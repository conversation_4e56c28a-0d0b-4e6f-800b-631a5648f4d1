describe("Login API", () => {
  it("should log in a user successfully", () => {
    cy.request({
      method: "POST",
      url: "http://localhost:5000/api/v1/auth/login", // Adjust the URL as needed
      body: {
        email: "<EMAIL>",
        password: "<PERSON><PERSON><PERSON>@123",
        deviceId: "1234567789675",
        deviceToken: "3455788",
        deviceType: "os",
      },
    }).then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property("token"); // Adjust based on actual response
    });
  });
});
