// eslint-disable-next-line n/no-path-concat
require('./signoz');
require('app-module-path').addPath(`${__dirname}/`);
require('./instrument.js');

const express = require('express');
const bodyParser = require('body-parser');
const http = require('http');
// const { host, httpPort } = require("config");
const { errorHandler } = require('./app/middleware');
const cors = require('cors');
const path = require('path');
const { sequelize } = require('./app/models/index');
const https = require('https');
const fs = require('fs');
const cron = require('node-cron');
const { checkPolicyExpiryAndSendEmail, checkRopaTentativeCompletionDateAndSendEmail, checkAssessmentsTentativeCompletionDateAndSendEmail, sendReminderForRequestAssigneeInDSR } = require('./app/utils/cron-jobs');
const packageJsonPath = require.resolve('./package.json');
const { version } = require(packageJsonPath);
const cookieParser = require('cookie-parser');
const compression = require('compression');
const helmet = require('helmet');
const Sentry = require('@sentry/node');
const { setupExpressErrorHandler } = require('@sentry/node');

// const { client } = require('./app/utils/minIO');
const axios = require('axios');

// ✅ Add this after initializing `app`
// ✅ Manual tracing setup
const { context, trace, propagation } = require('@opentelemetry/api');
const tracers = trace.getTracer('manual-span-test');
const tracer = trace.getTracer('http-middleware');

require('dotenv').config();

const app = express();

app.use((req, res, next) => {
  const span = tracer.startSpan(`${req.method} ${req.path}`, {
    attributes: { 'http.url': req.originalUrl }
  });
  res.once('finish', () => {
    span.setAttribute('http.status_code', res.statusCode);
    span.end();
  });
  next();
});

// Error Middleware
// app.use(errorHandler.methodNotAllowed);
// app.use(errorHandler.genericErrorHandler);

app.use(cookieParser());
// const path = require('path');
app.set(path.join(__dirname));
app.use(express.static(__dirname));

// DSR form landing
const dsrFormController = require('./app/controller/dsrForm');
app.get('/dsr/request-form/:customer_id', dsrFormController.renderDsrForm);
app.get('/dsr/guest-verify-email/:token', dsrFormController.verifyGuestEmail);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
//End of dsr form landing

const env = process.env.NODE_ENV || 'development';
app.use(cors());
app.use(compression()); // Compress all routes

// Add helmet to the middleware chain.
// Set CSP headers to allow our Bootstrap and jQuery to be served

app.use(
  helmet.contentSecurityPolicy({
    directives: {
      'script-src': ["'self'"]
    }
  })
);

const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];

app.use((req, res, next) => {
  const origin = req.headers.origin;

  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
    res.setHeader('Access-Control-Allow-Credentials', 'true');
  }
  // Request methods you wish to allow
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE');
  // Request headers you wish to allow
  res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type');
  // Pass to next layer of middleware
  next();
});

app.use(bodyParser.json({ limit: '2mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// ✅ Route that triggers a trace manually
app.get('/generate-span', async (req, res) => {
  const span = tracers.startSpan('custom-span-manual', {
    attributes: {
      'http.method': 'GET',
      'http.route': '/generate-span',
      'custom.info': 'tracing manually added'
    }
  });

  span.addEvent('manual-span-start');
  await new Promise(resolve => setTimeout(resolve, 100));
  span.addEvent('manual-span-finished');

  span.end();
  res.send('Manual span sent!');
});

//---->> Scheduler for Policy
// Schedule tasks to be run on the server.
cron.schedule('0 9 * * *', async () => {
  await checkPolicyExpiryAndSendEmail();
});
//---->> Scheduler for ROPA
// Schedule to run every day at 10 AM IST
cron.schedule('30 4 * * *', async () => {
  await checkRopaTentativeCompletionDateAndSendEmail();
});
//---->> Scheduler for Assessments
// Schedule to run every day at 10 AM IST
cron.schedule('30 4 * * *', async () => {
  await checkAssessmentsTentativeCompletionDateAndSendEmail();
});
//---->> Scheduler for DSR request . Remind request assignee
// Schedule to run every Wednesday at 2:00 AM
cron.schedule('0 2 * * 3', async () => {
  await sendReminderForRequestAssigneeInDSR();
});

let httpServer;
if (process.env.NODE_SSL == 'ssl') {
  httpServer = https
    .createServer(
      {
        key: fs.readFileSync(process.env.SSL_PRIV_KEY),
        cert: fs.readFileSync(process.env.SSL_CERT_KEY)
      },
      app
    )
    .listen(process.env.PORT, () => {
      console.info(`Server up successfully - port: ${process.env.PORT}`);
    });
} else {
  //   .createServer(
  //     {
  //       key: fs.readFileSync('/etc/letsencrypt/live/api.gotrust.tech/privkey.pem'),
  //       cert: fs.readFileSync('/etc/letsencrypt/live/api.gotrust.tech/fullchain.pem')
  //     },
  //     app,
  //   )
  //   .listen(process.env.PORT, () => {
  //     console.info(`Server up successfully - port: ${process.env.PORT}`);
  //   });
  // }else{
  httpServer = http.createServer(app.handle.bind(app)).listen(process.env.PORT, () => {
    console.info(`Server up successfully - port: ${process.env.PORT}`);
  });
}

// API routes
// const routes = require('./app')
// app.use('/api', require('./app/index'))

app.use('/health', async (req, res) => {
  const health = { postgres: false, minio: false, keycloak: false };

  try {
    // Check PostgreSQL (Sequelize)
    await sequelize.authenticate(); // Checks DB connection
    health.postgres = true;
  } catch (error) {
    console.error('PostgreSQL Health Check Failed:', error.message);
    health.postgres = false;
  }

  try {
    // Check MinIO or AWS S3
    let bucketExists;
    if (process.env.USE_MINIO === 'true') {
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.STORAGE_DOMAIN}/minio/health/live`,
        headers: {}
      };

      const test = await axios.request(config).then(response => {
        // console.log(JSON.stringify(response.data));
        // console.log('response', response);
        if (response.status === 200) {
          health.minio = true;
        }
      });

      // Check if MinIO is reachable by listing buckets
      // const bucketList = await client.listBuckets();
      // console.log('bucket list', bucketList);
      // console.log(`Checking MinIO bucket: ${process.env.BUCKET_NAME} at ${process.env.S3_ENDPOINT}`);
      // bucketExists = await client.bucketExists(process.env.BUCKET_NAME); // Check if bucket exists
      // health.minio = bucketExists;
    } else {
      const s3Params = { Bucket: process.env.BUCKET_NAME };
      bucketExists = await client.headBucket(s3Params).promise();
      health.minio = bucketExists;
    }
  } catch (error) {
    console.error('MinIO Health Check Failed:', error);
    health.minio = false;
  }

  try {
    // Check Keycloak (using Keycloak's /protocol/openid-connect/token endpoint)
    const keycloakResponse = await axios.post(
      `${process.env.KEYCLOAK_AUTH_SERVER_URL}/realms/${process.env.KEYCLOAK_REALM}/protocol/openid-connect/token`,
      new URLSearchParams({
        client_id: process.env.KEYCLOAK_CLIENT_ID,
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET,
        grant_type: 'client_credentials'
      }).toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
        // httpsAgent: new https.Agent({ rejectUnauthorized: false }), // Ignore SSL errors ///--------------->>>>>>>>>>>>>>>>>>>>>>
      }
    );
    health.keycloak = keycloakResponse.status === 200;
  } catch (error) {
    console.error('Keycloak Health Check Failed:', error);
    health.keycloak = false;
  }
  health.version = version;
  // Log the health object to confirm it's populated correctly
  console.log('Health Check Result:', health);

  // Send the response explicitly
  if (health.postgres && health.minio && health.keycloak) {
    return res.status(200).json({ status: 'UP', checks: health });
  } else {
    return res.status(503).json({ status: 'DOWN', checks: health });
  }
});

app.use('/api', require('./app/routes/index'));

app.get('/debug-sentry', function mainHandler(req, res) {
  throw new Error('My first Sentry error!!');
});

// app.use(Sentry.expressErrorMiddleware());
setupExpressErrorHandler(app);

process.on('unhandledRejection', err => {
  console.error('possibly unhandled rejection happened');
  console.error(err.message);
  Sentry.captureException(err);
  // enabledStackTrace && console.error(`stack: ${err.stack}`);
});
// console.log('connection', Sequelize);
// const con = sequelize.connectionManager
// const closeHandler = () => {
//   Object.values(db).forEach((connection) => connection.close());
//   httpServer.close(() => {
//     console.info("Server is stopped successfully");
//     process.exit(0);
//   });
// };

//-------------- Health check ----------------
// Health check route

const closeHandler = () => {
  // eslint-disable-next-line no-unused-expressions
  () => sequelize.close();
  httpServer.close(() => {
    console.info('Server is stopped successfully');
    process.exit(0);
  });
};

process.on('SIGTERM', closeHandler);
process.on('SIGINT', closeHandler);
