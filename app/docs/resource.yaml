openapi: 3.0.0
info:
  title: Resource API
  version: 1.0.0
paths:
  /api/v1/resources:
    get:
      tags:
        - Resource
      summary: Retrieve a list of resources for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
        - name: start_date
          in: query
          required: false
          schema:
            type: string
        - name: end_date
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of resources retrieved successfully
          content:
            application/json: {}

  /api/v1/user-resources:
    get:
      tags:
        - Resource
      summary: Retrieve resources associated with a specific user
      responses:
        '200':
          description: User resources retrieved successfully
          content:
            application/json: {}
