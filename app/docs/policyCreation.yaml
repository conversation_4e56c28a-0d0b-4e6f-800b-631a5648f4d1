openapi: 3.0.0
info:
  title: Policy Creation API
  version: 1.0.0
paths:
  /api/v1/policy-creation/continue:
    post:
      tags:
        - Policy Creation
      summary: Continue writing a policy based on provided text
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
                text:
                  type: string
                  example: 'Some text to continue writing.'
                policy_text:
                  type: string
                  example: 'Existing policy text.'
      responses:
        '200':
          description: Policy text continued successfully
          content:
            application/json: {}

  /api/v1/policy-creation/shorten:
    post:
      tags:
        - Policy Creation
      summary: Shorten the provided text for a policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
                text:
                  type: string
                  example: 'Text to shorten.'
                policy_text:
                  type: string
                  example: 'Existing policy text.'
      responses:
        '200':
          description: Text shortened successfully
          content:
            application/json: {}

  /api/v1/policy-creation/rewrite:
    post:
      tags:
        - Policy Creation
      summary: Rewrite the provided text for a policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
                text:
                  type: string
                  example: 'Text to rewrite.'
                policy_text:
                  type: string
                  example: 'Existing policy text.'
      responses:
        '200':
          description: Text rewritten successfully
          content:
            application/json: {}

  /api/v1/policy-creation/formalize:
    post:
      tags:
        - Policy Creation
      summary: Formalize the provided text for a policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
                text:
                  type: string
                  example: 'Text to formalize.'
                policy_text:
                  type: string
                  example: 'Existing policy text.'
      responses:
        '200':
          description: Text formalized successfully
          content:
            application/json: {}

  /api/v1/policy-creation/custom-prompt:
    post:
      tags:
        - Policy Creation
      summary: Use a custom prompt to generate text for a policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
                text:
                  type: string
                  example: 'Text to generate from prompt.'
                prompt:
                  type: string
                  example: 'Custom prompt for AI.'
                policy_text:
                  type: string
                  example: 'Existing policy text.'
      responses:
        '200':
          description: Text generated successfully
          content:
            application/json: {}

  /api/v1/policy-creation/fix-spelling:
    post:
      tags:
        - Policy Creation
      summary: Fix spelling in the provided text for a policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
                text:
                  type: string
                  example: 'Text with spelling errors.'
                policy_text:
                  type: string
                  example: 'Existing policy text.'
      responses:
        '200':
          description: Spelling fixed successfully
          content:
            application/json: {}

  /api/v1/policy-creation/templates:
    get:
      tags:
        - Policy Creation
      summary: Retrieve templates associated with a specific policy
      parameters:
        - name: policy_id
          in: query
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Templates retrieved successfully
          content:
            application/json: {}

  /api/v1/policy-creation/{id}:
    post:
      tags:
        - Policy Creation
      summary: Initiate the creation of a custom policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                policy_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Custom policy created successfully
          content:
            application/json: {}

    put:
      tags:
        - Policy Creation
      summary: Save a custom policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  example: 'Updated policy content.'
      responses:
        '200':
          description: Policy saved successfully
          content:
            application/json: {}

  /api/v1/policy-creation/{id}/submit:
    post:
      tags:
        - Policy Creation
      summary: Submit a policy for review
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                html_content:
                  type: string
                  example: '<p>Policy content in HTML format</p>'
      responses:
        '200':
          description: Policy submitted successfully
          content:
            application/json: {}
