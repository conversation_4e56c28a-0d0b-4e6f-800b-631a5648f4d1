paths:
  /api/v1/assessments:
    get:
      tags:
        - Assessments
      summary: Retrieve a list of assessments
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of assessments
          content:
            application/json: {}

  /api/v1/assessments/create:
    post:
      tags:
        - Assessments
      summary: Create a new assessment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assessment_name:
                  type: string
                  example: 'New Assessment'
                type:
                  type: string
                  example: 'LIA'
      responses:
        '200':
          description: Confirmation of assessment creation
          content:
            application/json: {}
