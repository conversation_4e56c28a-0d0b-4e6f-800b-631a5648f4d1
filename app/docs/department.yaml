openapi: 3.0.0
info:
  title: Department API
  version: 1.0.0
paths:
  /api/v1/department/{id}:
    get:
      tags:
        - Users
      summary: Retrieve details of a specific department
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Department details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - Users
      summary: Update details of a specific department
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'Updated Department Name'
                customer_id:
                  type: integer
                  example: 1
                group_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Department details updated successfully
          content:
            application/json: {}

  /api/v1/departments:
    get:
      tags:
        - Users
      summary: Retrieve a list of departments
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of departments retrieved successfully
          content:
            application/json: {}

  /api/v1/department:
    post:
      tags:
        - Users
      summary: Create a new department
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'New Department'
                customer_id:
                  type: integer
                  example: 1
                group_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Department created successfully
          content:
            application/json: {}
