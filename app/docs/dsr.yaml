openapi: 3.0.0
info:
  title: DSR API
  version: 1.0.0
paths:
  /api/v1/dsr/requests:
    get:
      tags:
        - DSR
      summary: Retrieve a list of DSR requests
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of DSR requests retrieved successfully
          content:
            application/json: {}

  /api/v1/dsr/requests/export:
    post:
      tags:
        - DSR
      summary: Export DSR requests to an Excel file
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: 'PENDING'
      responses:
        '200':
          description: DSR requests exported successfully
          content:
            application/json: {}

  /api/v1/dsr/requests/{id}:
    get:
      tags:
        - DSR
      summary: Retrieve details of a specific DSR request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: DSR request details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - DSR
      summary: Update a specific DSR request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: 'APPROVED'
      responses:
        '200':
          description: DSR request updated successfully
          content:
            application/json: {}

  /api/v1/dsr/requests/{id}/assign:
    post:
      tags:
        - DSR
      summary: Assign a DSR request to a user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                assignee_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: DSR request assigned successfully
          content:
            application/json: {}

  /api/v1/dsr/requests/{id}/approve-reject:
    post:
      tags:
        - DSR
      summary: Approve or reject a DSR request
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: 'APPROVED'
                reject_reason:
                  type: string
                  example: 'Not enough information'
      responses:
        '200':
          description: DSR request approved or rejected successfully
          content:
            application/json: {}
