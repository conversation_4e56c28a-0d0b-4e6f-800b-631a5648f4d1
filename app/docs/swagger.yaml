openapi: 3.0.0
info:
  title: Trustruler Backend API
  version: 1.0.0
servers:
  - url: http://localhost:4000
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
tags: 
  - name: Auth
    description: Endpoints for Auth    
  - name: Assessments
    description: Endpoints for managing assessments
  - name: Policies
    description: Endpoints for managing policies
  - name: Users
    description: Endpoints for user management
  - name: Dashboard
    description: Endpoints for dashboard functionalities
  - name: Vendors
    description: Endpoints for vendor management
  - name: Workflows
    description: Endpoints for workflow management
  - name: Workflow Steps
    description: Endpoints for managing workflow steps
  - name: Audit Logs
    description: Endpoints for managing audit logs
  - name: Controls
    description: Endpoints for managing controls
  - name: Departments
    description: Endpoints for managing departments
  - name: DSR
    description: Endpoints for managing DSR requests
  - name: Groups
    description: Endpoints for managing groups
  - name: Invoices
    description: Endpoints for managing invoices
  - name: Laws
    description: Endpoints for managing laws
  - name: Onboarding
    description: Endpoints for managing onboarding processes
  - name: PDA
    description: Endpoints for managing PDAs
  - name: Policy Category
    description: Endpoints for managing policy categories
  - name: Privacy Ops Assessment
    description: Endpoints for managing privacy operations assessments
  - name: Privacy Ops Document
    description: Endpoints for managing privacy operations documents
  - name: Process
    description: Endpoints for managing processes
  - name: Project
    description: Endpoints for managing projects
  - name: Resource
    description: Endpoints for managing resources
  - name: Role
    description: Endpoints for managing roles
  - name: Support
    description: Endpoints for managing support tickets
  - name: Table
    description: Endpoints for managing tables
paths:
  /api/v1/auth:
    $ref: './auth.yaml'
  /api/v1/assessments:
    $ref: './assessments.yaml'
  /api/v1/policy:
    $ref: './policy.yaml'
  /api/v1/policy-category:
    $ref: './policyCategory.yaml'
  /api/v1/privacy-assessment:
    $ref: './privacyOpsAssessment.yaml'
  /api/v1/privacy-documents:
    $ref: './privacyOpsDocument.yaml'
  /api/v1/process:
    $ref: './process.yaml'
  /api/v1/project:
    $ref: './project.yaml'
  /api/v1/resources:
    $ref: './resource.yaml'
  /api/v1/ticket:
    $ref: './support.yaml'
  /api/v1/vendor:
    $ref: './vendors.yaml'
  /api/v1/workflows:
    $ref: './workflows_v2.yaml'
  /api/v1/controls:
    $ref: './controls.yaml'
  /api/v1/audit:
    $ref: './audit_logs.yaml'
  /api/v1/customer:
    $ref: './customer.yaml'
  /api/v1/department:
    $ref: './department.yaml'
  /api/v1/dashboard:
    $ref: './dashboard.yaml'
  /api/v1/dsr:
    $ref: './dsr.yaml'
  /api/v1/group:
    $ref: './group.yaml'
  /api/v1/invoices:
    $ref: './invoices.yaml'
  /api/v1/laws:
    $ref: './law.yaml'
  /api/v1/onboarding:
    $ref: './onboarding.yaml'
  /api/v1/pda:
    $ref: './pda.yaml'
  /api/v1/vrm:
    $ref: './vrm.yaml'
  /api/v1/pia:
    $ref: './pia.yaml'
  /api/v1/lia:
    $ref: './lia.yaml'
  /api/v1/roles:
    $ref: './role.yaml'
  /api/v1/table:
    $ref: './table.yaml'
  /docs:
    get:
      summary: Get Swagger documentation
      responses:
        '200':
          description: Swagger documentation retrieved successfully
          content:
            application/json: {}
