openapi: 3.0.0
info:
  title: Project API
  version: 1.0.0
paths:
  /api/v1/client:
    post:
      tags:
        - Project
      summary: Register a new client
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                domain_name:
                  type: string
                  example: 'example.com'
                other_field:
                  type: string
                  example: 'Other data'
      responses:
        '200':
          description: Client registration successful
          content:
            application/json: {}

  /api/v1/client/{client_name}:
    get:
      tags:
        - Project
      summary: Retrieve details about a specific client
      parameters:
        - name: client_name
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Client details retrieved successfully
          content:
            application/json: {}
