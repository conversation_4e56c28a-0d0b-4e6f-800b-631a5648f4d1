openapi: 3.0.0
info:
  title: Controls API
  version: 1.0.0
paths:
  /api/v1/ropa/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for ROPA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}

  /api/v1/assessments/lia/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for LIA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}

  /api/v1/assessments/tia/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for TIA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}

  /api/v1/assessments/pda/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for PDA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}

  /api/v1/vrm/via/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for VIA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}

  /api/v1/vrm/vea/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for VEA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}
