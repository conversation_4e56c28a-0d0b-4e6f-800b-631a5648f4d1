openapi: 3.0.0
info:
  title: Invoices API
  version: 1.0.0
paths:
  /api/v1/invoices:
    get:
      tags:
        - Invoices
      summary: Retrieve a list of invoices for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: List of invoices retrieved successfully
          content:
            application/json: {}

  /api/v1/invoices/{id}:
    get:
      tags:
        - Invoices
      summary: Retrieve details of a specific invoice by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Invoice details retrieved successfully
          content:
            application/json: {}
