openapi: 3.0.0
info:
  title: Customer API
  version: 1.0.0
paths:
  /api/v1/customer/{id}:
    get:
      tags:
        - Users
      summary: Retrieve details of a specific customer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - Users
      summary: Update details of a specific customer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'Updated Customer Name'
                email:
                  type: string
                  example: '<EMAIL>'
                address:
                  type: string
                  example: '123 Main St'
                status:
                  type: string
                  example: 'active'
      responses:
        '200':
          description: Customer details updated successfully
          content:
            application/json: {}

  /api/v1/customers:
    get:
      tags:
        - Users
      summary: Retrieve a list of customers
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of customers retrieved successfully
          content:
            application/json: {}
