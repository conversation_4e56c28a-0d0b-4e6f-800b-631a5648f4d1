openapi: 3.0.0
info:
  title: Group API
  version: 1.0.0
paths:
  /api/v1/group:
    get:
      tags:
        - Users
      summary: Retrieve a list of groups for an organization
      parameters:
        - name: customer_id
          in: query
          required: true
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of groups retrieved successfully
          content:
            application/json: {}

    post:
      tags:
        - Users
      summary: Create a new group
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'New Group'
                customer_id:
                  type: integer
                  example: 1
                spoc_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Group created successfully
          content:
            application/json: {}

  /api/v1/group/{id}:
    put:
      tags:
        - Users
      summary: Update details of a specific group
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'Updated Group Name'
                customer_id:
                  type: integer
                  example: 1
                spoc_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Group details updated successfully
          content:
            application/json: {}
