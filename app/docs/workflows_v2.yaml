openapi: 3.0.0
info:
  title: Workflows API
  version: 1.0.0
paths:
  /api/v1/workflows:
    get:
      tags:
        - Workflows
      summary: Retrieve a list of workflows
      responses:
        '200':
          description: List of workflows
          content:
            application/json: {}

    post:
      tags:
        - Workflows
      summary: Create a new workflow
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                flowtype:
                  type: string
                  example: 'New Workflow'
      responses:
        '200':
          description: Confirmation of workflow creation
          content:
            application/json: {}
