openapi: 3.0.0
info:
  title: Support API
  version: 1.0.0
paths:
  /api/v1/ticket:
    post:
      tags:
        - Support
      summary: Create a new support ticket
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: 'Issue with product'
                description:
                  type: string
                  example: 'Detailed description of the issue.'
                priority:
                  type: string
                  example: 'High'
      responses:
        '200':
          description: Ticket created successfully
          content:
            application/json: {}

    get:
      tags:
        - Support
      summary: Retrieve a list of support tickets for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: status
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of support tickets retrieved successfully
          content:
            application/json: {}

  /api/v1/ticket/{id}:
    get:
      tags:
        - Support
      summary: Retrieve details of a specific support ticket
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Ticket details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - Support
      summary: Update details of a specific support ticket
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: 'Updated issue title'
                description:
                  type: string
                  example: 'Updated description of the issue.'
                priority:
                  type: string
                  example: 'Medium'
      responses:
        '200':
          description: Ticket updated successfully
          content:
            application/json: {}

  /api/v1/ticket/{id}/comment:
    post:
      tags:
        - Support
      summary: Add a comment to a specific support ticket
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  example: 'This is a comment on the ticket.'
      responses:
        '200':
          description: Comment added successfully
          content:
            application/json: {}

  /api/v1/ticket/{id}/history:
    get:
      tags:
        - Support
      summary: Retrieve the history of actions taken on a specific ticket
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Ticket history retrieved successfully
          content:
            application/json: {}

  /api/v1/ticket/dashboard:
    get:
      tags:
        - Support
      summary: Retrieve dashboard statistics for support tickets
      responses:
        '200':
          description: Dashboard statistics retrieved successfully
          content:
            application/json: {}

  /api/v1/ticket/priority/{priority}:
    get:
      tags:
        - Support
      summary: Retrieve tickets based on priority
      parameters:
        - name: priority
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Tickets retrieved successfully based on priority
          content:
            application/json: {}
