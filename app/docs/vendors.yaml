paths:
  /api/v1/vendor:
    get:
      tags:
        - Vendors
      summary: Retrieve a list of vendors for a customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of vendors
          content:
            application/json: {}

    post:
      tags:
        - Vendors
      summary: Create a new vendor
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                vendor_name:
                  type: string
                  example: 'New Vendor'
                email:
                  type: string
                  example: '<EMAIL>'
                phone:
                  type: string
                  example: '1234567890'
                country_code:
                  type: string
                  example: '+1'
      responses:
        '200':
          description: Confirmation of vendor creation
          content:
            application/json: {}
