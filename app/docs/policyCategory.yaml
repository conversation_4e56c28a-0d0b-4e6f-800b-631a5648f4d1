openapi: 3.0.0
info:
  title: Policy Category API
  version: 1.0.0
paths:
  /api/v1/policy-category:
    get:
      tags:
        - Policy Category
      summary: Retrieve a list of policy categories
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of policy categories retrieved successfully
          content:
            application/json: {}
