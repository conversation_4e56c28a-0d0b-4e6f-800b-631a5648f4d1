const fs = require("fs");
const yaml = require("js-yaml");
const path = require("path");

// Base swagger configuration
const baseSwagger = {
  openapi: "3.0.0",
  info: {
    title: "Trustruler Backend API",
    version: "1.0.0",
  },
  servers: [{ url: "http://localhost:5000" }, { url: "http://localhost:5600" }],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
      },
    },
  },
  tags: [
    { name: "Auth", description: "Endpoints for Auth" },
    { name: "Assessments", description: "Endpoints for managing assessments" },
    { name: "Policies", description: "Endpoints for managing policies" },
    { name: "Users", description: "Endpoints for user management" },
    {
      name: "Dashboard",
      description: "Endpoints for dashboard functionalities",
    },
    { name: "Vendors", description: "Endpoints for vendor management" },
    { name: "Workflows", description: "Endpoints for workflow management" },
    {
      name: "Workflow Steps",
      description: "Endpoints for managing workflow steps",
    },
    { name: "Audit Logs", description: "Endpoints for managing audit logs" },
    { name: "Controls", description: "Endpoints for managing controls" },
  ],
  paths: {},
};

// List of domain-specific YAML files
const domainFiles = [
  "auth.yaml",
  "assessments.yaml",
  "policies.yaml",
  "vendors.yaml",
  "workflows_v2.yaml",
  "controls.yaml",
  "audit_logs.yaml",
];

// Merge all domain files
domainFiles.forEach((file) => {
  try {
    const filePath = path.join(__dirname, file);
    const content = fs.readFileSync(filePath, "utf8");
    const doc = yaml.load(content);

    if (doc && doc.paths) {
      Object.assign(baseSwagger.paths, doc.paths);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
});

// Write the merged swagger file
const outputPath = path.join(__dirname, "swagger-merged.yaml");
const yamlStr = yaml.dump(baseSwagger, {
  indent: 2,
  lineWidth: -1,
});

fs.writeFileSync(outputPath, yamlStr, "utf8");
console.log("Swagger files merged successfully!");
