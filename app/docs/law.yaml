openapi: 3.0.0
info:
  title: Law API
  version: 1.0.0
paths:
  /api/v1/laws:
    get:
      tags:
        - Laws
      summary: Retrieve a list of relevant laws
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of laws retrieved successfully
          content:
            application/json: {}
