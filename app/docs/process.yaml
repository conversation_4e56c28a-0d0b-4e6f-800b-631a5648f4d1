openapi: 3.0.0
info:
  title: Process API
  version: 1.0.0
paths:
  /api/v1/process:
    post:
      tags:
        - Process
      summary: Create a new process
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'New Process'
                department_id:
                  type: integer
                  example: 1
                customer_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Process created successfully
          content:
            application/json: {}

  /api/v1/process/{process_id}:
    put:
      tags:
        - Process
      summary: Update details of a specific process
      parameters:
        - name: process_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'Updated Process Name'
      responses:
        '200':
          description: Process details updated successfully
          content:
            application/json: {}

  /api/v1/process/{department_id}:
    get:
      tags:
        - Process
      summary: Retrieve a list of processes for a specific department
      parameters:
        - name: department_id
          in: path
          required: true
          schema:
            type: integer
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of processes retrieved successfully
          content:
            application/json: {}
