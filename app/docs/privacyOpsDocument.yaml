openapi: 3.0.0
info:
  title: Privacy Ops Document API
  version: 1.0.0
paths:
  /api/v1/privacy-documents:
    get:
      tags:
        - Privacy Ops Document
      summary: Retrieve a list of policy documents for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: status
          in: query
          required: false
          schema:
            type: string
        - name: start_date
          in: query
          required: false
          schema:
            type: string
        - name: end_date
          in: query
          required: false
          schema:
            type: string
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of policy documents retrieved successfully
          content:
            application/json: {}

  /api/v1/privacy-documents/count:
    get:
      tags:
        - Privacy Ops Document
      summary: Count the number of policy documents
      responses:
        '200':
          description: Count of policy documents retrieved successfully
          content:
            application/json: {}
