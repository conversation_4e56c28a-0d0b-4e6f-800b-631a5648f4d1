openapi: 3.0.0
info:
  title: Privacy Ops Assessment API
  version: 1.0.0
paths:
  /api/v1/privacy-assessment/count:
    get:
      tags:
        - Privacy Ops Assessment
      summary: Count the number of assessments
      responses:
        '200':
          description: Count of assessments retrieved successfully
          content:
            application/json: {}

  /api/v1/privacy-assessment:
    get:
      tags:
        - Privacy Ops Assessment
      summary: Retrieve a list of assessments for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: search_key
          in: query
          required: false
          schema:
            type: string
        - name: is_assigned
          in: query
          required: false
          schema:
            type: boolean
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of assessments retrieved successfully
          content:
            application/json: {}
