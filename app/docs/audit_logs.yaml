openapi: 3.0.0
info:
  title: Audit Logs API
  version: 1.0.0
paths:
  /api/v1/audit/logs:
    get:
      tags:
        - Audit Logs
      summary: Retrieve all audit logs for policies associated with a customer
      responses:
        '200':
          description: List of audit logs
          content:
            application/json: {}

  /api/v1/audit/assessments:
    get:
      tags:
        - Audit Logs
      summary: Retrieve audit logs related to assessments
      responses:
        '200':
          description: List of audit logs for assessments
          content:
            application/json: {}

  /api/v1/audit/policy/{id}:
    get:
      tags:
        - Audit Logs
      summary: Retrieve audit logs for a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of audit logs for the policy
          content:
            application/json: {}

  /api/v1/audit/policy/{id}/logs:
    get:
      tags:
        - Audit Logs
      summary: Retrieve audit logs for a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of audit logs for the policy
          content:
            application/json: {}
