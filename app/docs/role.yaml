openapi: 3.0.0
info:
  title: Role API
  version: 1.0.0
paths:
  /api/v1/roles:
    get:
      tags:
        - Role
      summary: Retrieve a list of organization roles for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: customer_id
          in: query
          required: true
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
        - name: start_date
          in: query
          required: false
          schema:
            type: string
        - name: end_date
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of organization roles retrieved successfully
          content:
            application/json: {}

  /api/v1/roles/{role_id}/users:
    get:
      tags:
        - Role
      summary: Retrieve users associated with a specific role
      parameters:
        - name: role_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json: {}

  /api/v1/roles/assign:
    post:
      tags:
        - Role
      summary: Assign or unassign roles to users
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                check:
                  type: array
                  items:
                    type: integer
                uncheck:
                  type: array
                  items:
                    type: integer
                role_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Roles assigned or unassigned successfully
          content:
            application/json: {}

  /api/v1/roles/{role_id}:
    get:
      tags:
        - Role
      summary: Retrieve details of a specific role
      parameters:
        - name: role_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Role details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - Role
      summary: Update details of a specific role
      parameters:
        - name: role_id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                role_name:
                  type: string
                  example: 'Updated Role Name'
      responses:
        '200':
          description: Role details updated successfully
          content:
            application/json: {}

    post:
      tags:
        - Role
      summary: Create a new role
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                role_name:
                  type: string
                  example: 'New Role'
                customer_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Role created successfully
          content:
            application/json: {}
