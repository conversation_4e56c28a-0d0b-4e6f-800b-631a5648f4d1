openapi: 3.0.0
info:
  title: Onboarding API
  version: 1.0.0
paths:
  /api/v1/onboarding/industry-vertical:
    get:
      tags:
        - Onboarding
      summary: Retrieve a list of industry verticals
      responses:
        '200':
          description: List of industry verticals retrieved successfully
          content:
            application/json: {}

  /api/v1/onboarding/questionnaires:
    get:
      tags:
        - Onboarding
      summary: Retrieve a list of questionnaires
      parameters:
        - name: industry_vertical_id
          in: query
          required: true
          schema:
            type: integer
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: List of questionnaires retrieved successfully
          content:
            application/json: {}

  /api/v1/onboarding/answers:
    post:
      tags:
        - Onboarding
      summary: Submit answers to questionnaires
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                answers:
                  type: array
                  items:
                    type: object
                    properties:
                      question_id:
                        type: integer
                        example: 1
                      answer:
                        type: string
                        example: 'Sample answer'
      responses:
        '200':
          description: Answers submitted successfully
          content:
            application/json: {}

    put:
      tags:
        - Onboarding
      summary: Update answers to questionnaires
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                answers:
                  type: array
                  items:
                    type: object
                    properties:
                      question_id:
                        type: integer
                        example: 1
                      answer:
                        type: string
                        example: 'Updated answer'
      responses:
        '200':
          description: Answers updated successfully
          content:
            application/json: {}

  /api/v1/onboarding:
    post:
      tags:
        - Onboarding
      summary: Handle the onboarding process for users
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                service_id:
                  type: array
                  items:
                    type: integer
                ambition_id:
                  type: array
                  items:
                    type: integer
                questionnaires:
                  type: array
                  items:
                    type: object
                    properties:
                      question_id:
                        type: integer
                      status:
                        type: string
                package_id:
                  type: integer
      responses:
        '200':
          description: Onboarding completed successfully
          content:
            application/json: {}

  /api/v1/onboarding/state:
    get:
      tags:
        - Onboarding
      summary: Retrieve a list of states based on the country
      parameters:
        - name: country_id
          in: query
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: List of states retrieved successfully
          content:
            application/json: {}

  /api/v1/onboarding/country:
    get:
      tags:
        - Onboarding
      summary: Retrieve a list of countries
      responses:
        '200':
          description: List of countries retrieved successfully
          content:
            application/json: {}

  /api/v1/onboarding/language:
    get:
      tags:
        - Onboarding
      summary: Retrieve a list of languages
      responses:
        '200':
          description: List of languages retrieved successfully
          content:
            application/json: {}
