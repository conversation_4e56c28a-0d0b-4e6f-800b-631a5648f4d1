openapi: 3.0.0
info:
  title: VRM API
  version: 1.0.0
paths:
  /api/v1/vrm/vendor:
    get:
      tags:
        - Vendors
      summary: Retrieve a list of VRM vendors
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of VRM vendors
          content:
            application/json: {}

  /api/v1/vrm/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for VRM
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New VRM Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}
