paths:
  /api/v1/auth/login:
    post:
      tags:
        - Auth
      summary: User login
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: <EMAIL>
                deviceId:
                  type: string
                  example: '1234567789675'
                deviceToken:
                  type: string
                  example: '3455788'
                deviceType:
                  type: string
                  example: os
                password:
                  type: string
                  example: <PERSON><PERSON><PERSON>@123
      responses:
        '200':
          description: Successful response
          content:
            application/json: {}

  /api/v1/auth/send-otp:
    post:
      tags:
        - Auth
      summary: Send OTP to user's phone
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phone_no:
                  type: string
                  example: '987584849'
      responses:
        '200':
          description: Successful response
          content:
            application/json: {}

  /api/v1/auth/verify-otp:
    post:
      tags:
        - Auth
      summary: Verify the OTP sent to the user
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phone_no:
                  type: string
                  example: ''
                otp:
                  type: string
                  example: '998'
      responses:
        '200':
          description: Successful response
          content:
            application/json: {}

  /api/v1/auth/logout:
    post:
      tags:
        - Auth
      summary: User logout
      requestBody:
        content: {}
      responses:
        '200':
          description: Successful response
          content:
            application/json: {}
