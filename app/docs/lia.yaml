openapi: 3.0.0
info:
  title: LIA API
  version: 1.0.0
paths:
  /api/v1/lia/vendor:
    get:
      tags:
        - Vendors
      summary: Retrieve a list of LIA vendors
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of LIA vendors
          content:
            application/json: {}

  /api/v1/lia/controls:
    post:
      tags:
        - Controls
      summary: Add custom controls for LIA
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                control_name:
                  type: string
                  example: 'New LIA Control'
      responses:
        '200':
          description: Confirmation of control addition
          content:
            application/json: {}
