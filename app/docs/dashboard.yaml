openapi: 3.0.0
info:
  title: Dashboard API
  version: 1.0.0
paths:
  /api/v1/dashboard/update-password:
    put:
      tags:
        - Dashboard
      summary: Update user password
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: '<EMAIL>'
                password:
                  type: string
                  example: 'NewPassword123'
      responses:
        '200':
          description: Password updated successfully
          content:
            application/json: {}

  /api/v1/dashboard/update-organisation/{id}:
    put:
      tags:
        - Dashboard
      summary: Update organization details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'Updated Organization Name'
                email:
                  type: string
                  example: '<EMAIL>'
                address:
                  type: string
                  example: '456 Main St'
                status:
                  type: string
                  example: 'active'
      responses:
        '200':
          description: Organization details updated successfully
          content:
            application/json: {}

  /api/v1/dashboard/services:
    get:
      tags:
        - Dashboard
      summary: Retrieve a list of services
      responses:
        '200':
          description: List of services retrieved successfully
          content:
            application/json: {}

  /api/v1/dashboard/questionnaires:
    get:
      tags:
        - Dashboard
      summary: Retrieve a list of questionnaires
      parameters:
        - name: ambition_id
          in: query
          required: true
          schema:
            type: integer
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: List of questionnaires retrieved successfully
          content:
            application/json: {}

  /api/v1/dashboard/packages:
    get:
      tags:
        - Dashboard
      summary: Retrieve a list of packages
      responses:
        '200':
          description: List of packages retrieved successfully
          content:
            application/json: {}

  /api/v1/dashboard/packages/{id}:
    get:
      tags:
        - Dashboard
      summary: Retrieve details of a specific package
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Package details retrieved successfully
          content:
            application/json: {}

  /api/v1/dashboard/ambitions:
    get:
      tags:
        - Dashboard
      summary: Retrieve a list of ambitions
      responses:
        '200':
          description: List of ambitions retrieved successfully
          content:
            application/json: {}

  /api/v1/dashboard/sidebar:
    get:
      tags:
        - Dashboard
      summary: Retrieve sidebar resources for the user
      responses:
        '200':
          description: Sidebar resources retrieved successfully
          content:
            application/json: {}
