paths:
  /api/v1/policy:
    get:
      tags:
        - Policies
      summary: Retrieve a list of policies
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: status
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of policies
          content:
            application/json: {}

    post:
      tags:
        - Policies
      summary: Create a new policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'New Policy'
                description:
                  type: string
                  example: 'Policy description'
      responses:
        '200':
          description: Confirmation of policy creation
          content:
            application/json: {}
