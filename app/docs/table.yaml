openapi: 3.0.0
info:
  title: Table API
  version: 1.0.0
paths:
  /api/v1/table:
    post:
      tags:
        - Table
      summary: Retrieve and render a table based on provided data
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    type: object
                columns:
                  type: array
                  items:
                    type: string
                page:
                  type: integer
                  example: 1
                size:
                  type: integer
                  example: 10
                search_key:
                  type: string
                  example: 'search term'
                sort_by:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Table rendered successfully
          content:
            text/html: {}
