openapi: 3.0.0
info:
  title: Policy API
  version: 1.0.0
paths:
  /api/v1/policy:
    post:
      tags:
        - Policy
      summary: Create a new policy
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'New Policy'
                customer_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Policy created successfully
          content:
            application/json: {}

    get:
      tags:
        - Policy
      summary: Retrieve a list of policies for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: status
          in: query
          required: false
          schema:
            type: string
        - name: start_date
          in: query
          required: false
          schema:
            type: string
        - name: end_date
          in: query
          required: false
          schema:
            type: string
        - name: search
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
        - name: sort_order
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of policies retrieved successfully
          content:
            application/json: {}

  /api/v1/policy/{id}:
    get:
      tags:
        - Policy
      summary: Retrieve details of a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Policy details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - Policy
      summary: Update details of a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: 'Updated Policy Name'
                status:
                  type: string
                  example: 'ACTIVE'
      responses:
        '200':
          description: Policy details updated successfully
          content:
            application/json: {}

  /api/v1/policy/{id}/collaborator:
    post:
      tags:
        - Policy
      summary: Add a collaborator to a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                collaborator_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Collaborator added successfully
          content:
            application/json: {}

  /api/v1/policy/{id}/review:
    get:
      tags:
        - Policy
      summary: Retrieve review details for a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Review details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - Policy
      summary: Update review details for a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                result:
                  type: string
                  example: 'APPROVED'
                comment:
                  type: string
                  example: 'Looks good!'
      responses:
        '200':
          description: Review details updated successfully
          content:
            application/json: {}

  /api/v1/policy/{id}/documents:
    post:
      tags:
        - Policy
      summary: Upload documents related to a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                document:
                  type: string
                  example: 'document_url'
      responses:
        '200':
          description: Document uploaded successfully
          content:
            application/json: {}

    delete:
      tags:
        - Policy
      summary: Delete documents related to a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                url:
                  type: string
                  example: 'document_url'
      responses:
        '200':
          description: Document deleted successfully
          content:
            application/json: {}

  /api/v1/policy/{id}/download:
    get:
      tags:
        - Policy
      summary: Download documents related to a specific policy
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Document downloaded successfully
          content:
            application/json: {}
