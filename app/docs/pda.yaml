openapi: 3.0.0
info:
  title: PDA API
  version: 1.0.0
paths:
  /api/v1/pda:
    get:
      tags:
        - PDA
      summary: Retrieve a list of PDAs for a specific customer
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: size
          in: query
          required: false
          schema:
            type: integer
        - name: search
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of PDAs retrieved successfully
          content:
            application/json: {}

  /api/v1/pda/{id}:
    get:
      tags:
        - PDA
      summary: Retrieve details of a specific PDA
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: PDA details retrieved successfully
          content:
            application/json: {}

    put:
      tags:
        - PDA
      summary: Update details of a specific PDA
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: 'STARTED'
      responses:
        '200':
          description: PDA details updated successfully
          content:
            application/json: {}

  /api/v1/pda/{id}/start:
    post:
      tags:
        - PDA
      summary: Start the PDA process
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: PDA process started successfully
          content:
            application/json: {}

  /api/v1/pda/{id}/progress:
    get:
      tags:
        - PDA
      summary: Retrieve the progress of a specific PDA
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Progress of the PDA retrieved successfully
          content:
            application/json: {}

  /api/v1/pda/{id}/categories:
    get:
      tags:
        - PDA
      summary: Retrieve categories associated with a specific PDA
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Categories retrieved successfully
          content:
            application/json: {}

  /api/v1/pda/{id}/controls:
    get:
      tags:
        - PDA
      summary: Retrieve controls associated with a specific PDA
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Controls retrieved successfully
          content:
            application/json: {}

  /api/v1/pda/{id}/custom-controls:
    post:
      tags:
        - PDA
      summary: Add custom controls to a specific PDA
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: 'Custom Control Title'
                description:
                  type: string
                  example: 'Description of the custom control'
      responses:
        '200':
          description: Custom control added successfully
          content:
            application/json: {}
