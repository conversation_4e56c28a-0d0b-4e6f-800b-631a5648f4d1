<!DOCTYPE html>
<html
  lang="en"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" />
    <title></title>
    <!-- <link href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i,700,700i" rel="stylesheet"> -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,700;1,500&display=swap"
      rel="stylesheet"
    />
    <style>
      html,
      body {
        margin: 0 auto !important;
        padding: 0 !important;
        height: 100% !important;
        width: 100% !important;
        background: #f1f1f1;
      }
 
      /* What it does: Stops email clients resizing small text. */
      * {
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
      }
 
      /* What it does: Centers email on Android 4.4 */
      div[style*="margin: 16px 0"] {
        margin: 0 !important;
      }
 
      /* What it does: Stops Outlook from adding extra spacing to tables. */
      table,
      td {
        mso-table-lspace: 0pt !important;
        mso-table-rspace: 0pt !important;
      }
 
      /* What it does: Fixes webkit padding issue. */
      table {
        border-spacing: 0 !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
        margin: 0 auto !important;
      }
 
      /* What it does: Uses a better rendering method when resizing images in IE. */
      img {
        -ms-interpolation-mode: bicubic;
      }
 
      /* What it does: Prevents Windows 10 Mail from underlining links despite inline CSS. Styles for underlined links should be inline. */
      a {
        text-decoration: none;
      }
 
      /* What it does: A work-around for email clients meddling in triggered links. */
      *[x-apple-data-detectors],
        /* iOS */
        .unstyle-auto-detected-links *,
        .aBn {
        border-bottom: 0 !important;
        cursor: default !important;
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
      }
 
      /* What it does: Prevents Gmail from displaying a download button on large, non-linked images. */
      .a6S {
        display: none !important;
        opacity: 0.01 !important;
      }
 
      /* What it does: Prevents Gmail from changing the text color in conversation threads. */
      .im {
        color: inherit !important;
      }
 
      /* If the above doesn't work, add a .g-img class to any image in question. */
      img.g-img + div {
        display: none !important;
      }
 
      /* What it does: Removes right gutter in Gmail iOS app: https://github.com/TedGoas/Cerberus/issues/89  */
      /* Create one of these media queries for each additional viewport size you'd like to fix */
 
      /* iPhone 4, 4S, 5, 5S, 5C, and 5SE */
      @media only screen and (min-device-width: 320px) and (max-device-width: 374px) {
        u ~ div .email-container {
          min-width: 320px !important;
        }
      }
 
      /* iPhone 6, 6S, 7, 8, and X */
      @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
        u ~ div .email-container {
          min-width: 375px !important;
        }
      }
 
      /* iPhone 6+, 7+, and 8+ */
      @media only screen and (min-device-width: 414px) {
        u ~ div .email-container {
          min-width: 414px !important;
        }
      }
    </style>
 
    <style>
      .bg-gray {
        background: #f7f7f7;
      }
 
      .bg_white {
        background: #ffffff;
      }
 
      /* .email-section {
            padding: 1.8em 2.5em ;
        } */
 
      /*BUTTON*/
      .btn {
        padding: 10px 15px;
      }
 
      .borderWidth {
        border: 1px solid #f7f7f7;
        margin: 20px 0;
      }
 
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: "Poppins", sans-serif;
        color: #000000;
        margin-top: 0;
      }
 
      body {
        font-family: "Poppins", sans-serif;
        font-weight: 400;
        font-size: 15px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.4);
      }
 
      a {
        color: #f3a333;
      }
 
      table {
      }
 
      /*LOGO*/
 
      .logo h1 {
        margin: 0;
      }
 
      .logo h1 a img {
        max-width: 200px;
        /* color: #000;
    font-size: 20px;
    font-weight: 700;
    text-transform: uppercase;
    font-family: 'Poppins', sans-serif; */
      }
 
      /*HEADING SECTION*/
 
      .heading-section h2 {
        color: #000000;
        font-size: 24px;
        margin-top: 0;
        line-height: 1.4;
        margin-bottom: 10px;
        font-weight: 600;
      }
 
      .heading-section h3 {
        color: #000000;
        font-size: 20px;
        margin-top: 0;
        line-height: 1.4;
        margin-bottom: 10px;
        font-weight: 600;
      }
 
      .heading-section p {
        margin: 5px 0 30px;
        color: #000000;
        font-weight: 400;
        font-family: "Poppins", sans-serif;
      }
 
      /*VIDEO*/
      .img {
        width: 100%;
        height: auto;
        position: relative;
      }
    </style>
  </head>
 
  <body
    width="100%"
    style="
      margin: 0;
      padding: 0 !important;
      mso-line-height-rule: exactly;
      background-color: #f1f1f1;
    "
  >
    <center style="width: 100%; background-color: #f1f1f1">
      <div
        style="
          display: none;
          font-size: 1px;
          max-height: 0px;
          max-width: 0px;
          opacity: 0;
          overflow: hidden;
          mso-hide: all;
          font-family: 'Poppins', sans-serif;
        "
      >
        &zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;
      </div>
      <div style="max-width: 600px; margin: 0 auto;justify-content: center;" class="email-container">
        <table
          align="center"
          role="presentation"
          cellspacing="0"
          cellpadding="0"
          border="0"
          width="100%"
          style="margin: auto"
        >
          <!-- <tr>
                    <td class="bg_white logo"
                        style="padding: 0.5em 2.5em; text-align: center; border-bottom: 1px solid #8080802b;">
                        <h1><a href="javascript:void(0)" style="max-width: 150px; width:100%;"><img
                                    src="https://go-asset-management-profile-image.s3.us-east-2.amazonaws.com/icon.png" alt="logo"
                                    title="logo" border="0"></a></h1>
                    </td>
                </tr> -->
          <th
            style="
              padding: 15px;
              text-align: center;
              background: #000080;
              border-radius: 8px 8px 0px 0px;
             
            "
          ></th>
          <tr>
            <td class="bg_white">
              <table
                role="presentation"
                cellspacing="0"
                cellpadding="0"
                border="0"
                width="100%"
              >
                <tr>
                  <td
                    class="bg_white email-section"
                    style="padding: 1.8em 2.5em 0"
                  >
                    <div class="heading-section" style="tpadding: 0 30px">
                      <h2>You’re nearly there!</h2>
                      <p>Hi <%= name %>,</p>
 
                      <p>
                        To finish setting up your account and start using
                        GoTrust, confirm we’ve got the correct email for you.
                      </p>
                      <h3><%= otp %></h3>
                      <div class="borderWidth"></div>
                      <h4>Your GoTrust Account</h4>
 
                      <p>
                        Make things easier by using one account across all of
                        your GoTrust products.
                        <!-- <a href="#" style="color:#679BFF"> Learn more.</a> -->
                      </p>
                      <!-- <p style="margin:5px 0"><a style="color:#679BFF"
                                                href="https://play.google.com/store/apps/details?id=com.tekhsters.goshare&pli=1">play.google.com/store/apps/details?id=com.tekhsters.goshare&pli=1</a>
                                        </p>
                                        <p><a style="color:#679BFF"
                                                href="https://apps.apple.com/us/app/GoTrust/id6446066064?platform=iphone">apps.apple.com/us/app/GoTrust/id6446066064?platform=iphone</a>
                                        </p> -->
                      <div class="borderWidth"></div>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
 
          <tr>
            <td
              class="bg_white"
              style="padding: 0 2.5em 1em; text-align: center"
            >
              <div
                style="
                  color: #000080;
                  font-size: 37px;
                  font-family: 'Bricolage Grotesque', sans-serif;
                  font-weight: 700;
                  word-wrap: break-word;
                "
              >
                GoTrust
              </div>
              <p
                align="center"
                style="
                  font-size: 12px;
                  color: #979797;
                  font-family: 'Poppins', sans-serif;
                  text-align: center;
                  font-weight: normal;
                "
              >
                This message was sent to you by GoTrust
              </p>
            </td>
          </tr>
        </table>
      </div>
    </center>
  </body>
</html>