const jwt = require("jsonwebtoken");
// const otpGenerator = require("otp-generator");
const bcrypt = require("bcryptjs");
const salt = bcrypt.genSaltSync(10);
const db = require('../models/index').sequelize;
const { env } = require('../constant/environment');

async function signToken(data, time = 5184000) {

    //const secret = Buffer.from(process.env.JWT_SECRET, "base64").toString();
    //console.log(secret,"?????????????//",process.env.JWT_SECRET)

    return jwt.sign(data, env.SECRET_KEY, {
        expiresIn: time, // expires in 60 days
    });
}

async function jwtVerify(event) {
    return new Promise(async (resolve, reject) => {
        const token = event.headers.Authorization; // eslint-disable-line
        console.log('ciws', token);
        if (!token) return resolve({ status: 0, message: "Unauthorized" });

        //const secret = Buffer.from(process.env.JWT_SECRET, "base64");

        jwt.verify(token, process.env.JWT_SECRET, async (error, decoded) => {
            console.log("🚀 ~ file: helper.js:28 ~ jwt.verify ~ decoded", decoded)
            if (error) {
                console.log('test error', error);
                return resolve({ status: 0, message: "Invalid token" });
            }
            // const { DeviceToken } = await connectToDatabase();
            // const tokenValidate = await DeviceToken.findOne({
            //   where: { auth_token: token },
            // });
            // if (!tokenValidate) {
            //   return resolve({ status: 0, message: "Invalid token. Please login." });
            // }
            return resolve({ status: 1, data: decoded });
        });
    });
}
async function generateUniqueMpin(User, n) {
    console.log(n);
    let randomString = '';
    let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < n; i++) {
        randomString += characters.charAt(
            Math.floor(Math.random() * characters.length)
        );
    }

    const hash_mPin = await bcrypt.hash(randomString, salt);
    const checkAlreadyUseMpin = await User.count({
        where: { mpin: hash_mPin },
        raw: true,
    });

    if (checkAlreadyUseMpin > 0) {
        await generateUniqueMpin(Driver, 4);
    } else {
        return { mPin: randomString, hash_mPin: hash_mPin };
    }
}

async function authenticate(event) {
    return new Promise(async (resolve, reject) => {
        const token = event.headers.Authorization; // eslint-disable-line

        if (!token) return resolve({ status: 0, message: "Unauthorized" });

        //const secret = Buffer.from(process.env.JWT_SECRET, "base64");

        jwt.verify(token, process.env.JWT_SECRET, async (error, decoded) => {
            console.log("🚀 ~ file: helper.js:28 ~ jwt.verify ~ decoded", decoded)
            if (error) {
                return resolve({ status: 0, message: "Invalid token" });
            }
            if (decoded.userId == 0 || undefined || "") {
                return resolve({ status: 0, message: "user doesn't exist." });
            }
            const { User, Driver, Organization, OrganisationUsers, UserOrganisation, DeviceToken } =
                db.models;

            let tokenValidate = await DeviceToken.findOne({
                where: { auth_token: token },
            });
            if (!tokenValidate) {
                return resolve({ status: 0, message: "Invalid token. Please login." });
            } else {
                let model = User;
                if (decoded.role == "user") model = User;
                else if (decoded.role == "driver") model = Driver;
                let result = await model.findOne({
                    attributes: {
                        exclude: ["password", "access_token", "createdAt", "updatedAt"],
                    },
                    where: { id: decoded.userId },
                });
                if (result) {
                    result = JSON.parse(JSON.stringify(result));
                    if (result.status == false || result.status == 0) {
                        return resolve({
                            status: 0,
                            message: "Your account has been inactive by super admin.",
                        });
                    }

                    if (result.status == 2) {
                        return resolve({
                            status: 0,
                            message: "Your account has been deleted by super admin.",
                        });
                    }
                    let userOrgData = {
                    };
                    if (decoded.role == "driver") {
                        result.role = decoded.role;
                    } else if (decoded.role == "user") {
                        result.role = decoded.role;
                    }
                    if (decoded.role == "user" && decoded.role_id == 1) {
                        userOrgData = await Organization.findAll({
                            attributes: ["id", "name"],
                            where: { is_group: false }
                        });

                    } else if (decoded.role == "user" && decoded.role_id != 1) {
                        userOrgData = await UserOrganisation.findAll({
                            where: {
                                user_id: result.id,
                            },
                        })
                    }
                    if (decoded.role == "user" && decoded.role_id == 1 && userOrgData.length) {
                        userOrgData = userOrgData.map((ele) => ele.id);
                    } else if (decoded.role == "user" && decoded.role_id != 1 && userOrgData.length) {
                        userOrgData = userOrgData.map((ele) => ele.org_id || ele.organisation_id);
                    }
                    console.log("org_   ", userOrgData);
                    return resolve({
                        status: 1,
                        data: { userId: result.id, ...result, userOrgData, role_key: decoded.role_key ? decoded.role_key : 'DRIVER' },
                    });
                } else {
                    return resolve({ status: 0, message: "Session has been expired." });
                }
            }
        });
    });
}

function checkScore(value) {
    if (value == 1001) return 1001;
    else if (value > 0) return value >= 10 ? 10 : value;
    else if (value == 0) return 10;
    else return value > -10 ? value : -10;
}

async function generateOtp(digit) {
    var generatOtp = otpGenerator.generate(parseInt(digit), {
        lowerCaseAlphabets: false,
        upperCaseAlphabets: false,
        specialChars: false,
    });
    return generatOtp;
}

async function authenticateSocket(token) {
    return new Promise(async (resolve, reject) => {
        if (!token) return resolve({ status: 0, message: "Unauthorized" });

        if (token.length < 30 || token.length > 40) {
            return resolve({ status: 0, message: "Invalid token" });
        }

        const { User } = db.models;

        let result = await User.findOne({
            attributes: {
                exclude: ["id"],
            },
            where: { socket_token: token },
        });
        if (result) {
            result = JSON.parse(JSON.stringify(result));
            if (result.status == 1) {
                return resolve({
                    status: 1,
                    message: "Success",
                });
            } else {
                return resolve({ status: 0, message: "Session has been expired." });
            }
        } else {
            return resolve({ status: 0, message: "Session has been expired." });
        }
    });
}
function generatePassword() {
    let pass = '';
    let str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' +
        'abcdefghijklmnopqrstuvwxyz0123456789@#$';

    for (let i = 1; i <= 8; i++) {
        let char = Math.floor(Math.random()
            * str.length + 1);
        console.log(char, str.charAt(char))

        pass += str.charAt(char)
    }

    return pass;
}


function transform(input, args) {
    let exp, rounded, final, valu,
        suffixes = ['K', 'M', 'G', 'T', 'P', 'E'];

    if (isNaN(input)) {
        return input;
    }
    if (input === null) return null;
    if (input === 0) return input.toString();
    if (input < 9999) {
        rounded = Number(input).toFixed(1)
        return Number(rounded).toLocaleString('de-DE');
    }

    exp = Math.floor(Math.log(input) / Math.log(1000));
    valu = (input / Math.pow(1000, exp)).toFixed(args + 1).slice(0, (args * -1))
    final = Number(valu).toLocaleString('de-DE') + suffixes[exp - 1];
    return final;
}

function convertToDutch(val) {
    if (val == '1001' || val == 1001 || val == '1001.0') {
        return "N/A"
    }
    else {
        if (val) {
            val = parseFloat(val);
            val = parseFloat(val.toFixed(1));
            return val.toLocaleString('de-DE')
        }
        else
            return '0';
    }
}

function maskEmail(input) {
    // console.log(input);
    if (!input) return;
    else {
        var email = input;
        var parts = email.split("@");
        var name = parts[0];
        var result = name.charAt(0);
        for (var i = 1; i < name.length; i++) {
            result += "*";
        }
        result += name.charAt(name.length - 1);
        result += "@";
        var domain = parts[1];
        result += domain.charAt(0);
        var dot = domain.indexOf(".");
        for (var i = 1; i < dot; i++) {
            result += "*";
        }
        result += domain.substring(dot);
        return result;
    }

}

function makeHiricalTree(currentNode, OrgList, org_id, currentId) {
    if (!currentId) {
        currentNode.children = currentNode.children || new Array();
        return makeHiricalTree(currentNode, OrgList, org_id, currentNode.id)
    } else {
        for (var j = 0; j < OrgList.length; j++) {
            if (OrgList[j].parent_node == currentId) {
                currentNode.children.push(OrgList[j]);
            }
        }
    }
    if (currentNode.id == org_id) {
        return currentNode;
    }
}

function recursionForOrgIdAndUserId(menuList, organisationIds1) {
    return new Promise((resolve, reject) => {
        let organisationIds;
        if (organisationIds1 == undefined) {
            organisationIds = [];
        } else {
            organisationIds = organisationIds1;
        }

        for (let i = 0; i < menuList.length; i++) {
            let id1s = menuList[i].id;
            organisationIds.push(id1s);
            recursionForOrgIdAndUserId(menuList[i].children, organisationIds);

        }
        resolve(organisationIds);
    })
}


const getPagination = (page, size) => {
    const limit = size ? size : null;
    const offset = page ? (page - 1) * limit : 0;
    return { limit, offset };
};
const getPagination2 = (page, size) => {
    const limit = size ? size : 10;
    const offset = page ? (page - 1) * limit : 0;
    return { limit, offset };
};


module.exports = {
    signToken,
    authenticate,
    checkScore,
    generateOtp,
    authenticateSocket,
    generatePassword,
    transform,
    convertToDutch,
    maskEmail,
    makeHiricalTree,
    recursionForOrgIdAndUserId,
    getPagination,
    jwtVerify,
    generateUniqueMpin,
    getPagination2
};
