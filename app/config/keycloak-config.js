// var session = require('express-session');
var Keycloak = require('keycloak-connect');

let keycloak;

var keycloakConfig = {
    "realm": process.env.KEYCLOAK_REALM,
    "auth-server-url": process.env.KEYCLOAK_AUTH_SERVER_URL,
    "ssl-required": "external",
    "resource": process.env.KEYCLOAK_RESOURCE,
    "realmPublicKey": process.env.KEYCLOAK_REALM_PUBLIC_KEY,
    "bearer-only": true
};

function initKeycloak() {
    if (keycloak) {
        console.log("Returning existing Keycloak instance!");
        return keycloak;
    } else {
        console.log("Initializing Keycloak...");
        keycloak = new Keycloak({}, keycloakConfig);
        return keycloak;
    }
}

module.exports = {
    initKeycloak
};
