const { env } = require('../constant/environment');
require('dotenv').config();

module.exports = {
  development: {
    allowedOrigins: [
      'http://localhost:3000',
      'http://127.0.0.1:8080',
      'http://dev.gotrust.tech'
    ],
    username: process.env.DB_USERNAME_INFO,
    password: process.env.DB_PASSWORD_INFO,
    database: process.env.DB_DATABASE_INFO,
    host: process.env.WRITE_DB_HOST_INFO,
    dialect: process.env.DB_DIALECT || 'postgres',
    port: process.env.DB_PORT,
    max: 20, // maximum connection which PostgreSQL can initiate
    min: 0, // minimum connection which PostgreSQL can initiate
    acquire: 20000, // time required to reconnect
    idle: 20000, // get idle connection
    evict: 10000 // removes idle connections
  },

  // development: {
  //   username: 'postgres',
  //   password: 'root',
  //   database: 'postgres',
  //   host: '127.0.0.1',
  //   dialect: 'postgres'
  // },

  preprod: {
    allowedOrigins: ['http://preprod.gotrust.tech'],
    username: process.env.DB_USERNAME_INFO,
    password: process.env.DB_PASSWORD_INFO,
    database: process.env.DB_DATABASE_INFO,
    host: process.env.WRITE_DB_HOST_INFO,
    dialect: process.env.DB_DIALECT || 'postgres',
    port: process.env.DB_PORT,
    max: 20, // maximum connection which PostgreSQL can initiate
    min: 0, // minimum connection which PostgreSQL can initiate
    acquire: 20000, // time required to reconnect
    idle: 20000, // get idle connection
    evict: 10000, // removes idle connections
    // dialectOptions: {
    //   ssl: {
    //     require: true, // This will help you, but you might see a new error
    //     rejectUnauthorized: false // This line fixes the error
    //   }
    // }
  },
  production: {
    allowedOrigins: ['https://portal.gotrust.tech'],
    username: process.env.DB_USERNAME_INFO,
    password: process.env.DB_PASSWORD_INFO,
    database: process.env.DB_DATABASE_INFO,
    host: process.env.WRITE_DB_HOST_INFO,
    dialect: process.env.DB_DIALECT || 'postgres',
    port: process.env.DB_PORT,
    max: 20, // maximum connection which PostgreSQL can initiate
    min: 0, // minimum connection which PostgreSQL can initiate
    acquire: 20000, // time required to reconnect
    idle: 20000, // get idle connection
    evict: 10000, // removes idle connections
    // dialectOptions: {
    //   ssl: {
    //     require: true, // This will help you, but you might see a new error
    //     rejectUnauthorized: false // This line fixes the error
    //   }
    // }
  },
  test: {
    allowedOrigins: ['https://test.gotrust.tech'],
    username: process.env.DB_USERNAME_INFO,
    password: process.env.DB_PASSWORD_INFO,
    database: process.env.DB_DATABASE_INFO,
    host: process.env.WRITE_DB_HOST_INFO,
    dialect: process.env.DB_DIALECT || 'postgres',
    port: process.env.DB_PORT,
    max: 20, // maximum connection which PostgreSQL can initiate
    min: 0, // minimum connection which PostgreSQL can initiate
    acquire: 20000, // time required to reconnect
    idle: 20000, // get idle connection
    evict: 10000, // removes idle connections
    // dialectOptions: {
    //   ssl: {
    //     require: true, // This will help you, but you might see a new error
    //     rejectUnauthorized: false // This line fixes the error
    //   }
    // }
  }
};
