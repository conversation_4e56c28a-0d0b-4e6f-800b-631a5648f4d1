"use strict";

const user_attributes = {
    USER: ["id", "firstName", "lastName", "email", "status", "password", "phone", "profile_image", "country_code", "address", "color_code", "failed_login_count", "failed_login_at", "is_notifies", "tnc_accepted", "marketing_email_accepted", 'is_email_verified', 'mpin', 'role_id', 'group_access', 'customer_id','firstLogin']
}

const device_type = {
    ANDROID: 'ANDROID',
    IOS: 'IOS'
}

module.exports = Object.freeze({
    user_attributes,
    device_type,
})