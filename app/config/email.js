const { renderFile } = require('ejs');
var nodemailer = require('nodemailer');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { env } = require('../constant/environment');
const FormData = require('form-data');

require('dotenv').config();

const smtpConfig = {
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: process.env.SMTP_SECURE === 'true'
};

if (process.env.SMTP_USER && process.env.SMTP_PASS) {
  smtpConfig.auth = {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  };
}

const transporter = nodemailer.createTransport(smtpConfig);

//------------------------------------------------- Send Mail ------------------------------------------------------------------------------

async function sendMail(email, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL) {
  if (process.env.USE_CLIENT_EMAIL_SERVICE === 'true') {
    return sendMailViaAPI(email, sendData, subject, textTemplate, sender);
  } else {
    return sendMailViaSMTP(email, sendData, subject, textTemplate, sender);
  }
}

async function sendMailViaSMTP(email, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL) {
  try {
    return await new Promise((resolve, reject) => {
      renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
        if (err) {
          console.log(err);
        } else {
          // console.log("============================");
          // for (let key in sendData) {
          //     if (sendData[key] === undefined) {
          //         console.log(sendData[key] )
          //         throw new Error(`The value for ${key} is undefined.`);
          //     }
          // }
          const mainOptions = {
            from: process.env.SMTP_EMAIL,
            to: email,
            subject,
            html: dataTemplate
          };
          transporter.sendMail(mainOptions, (err, success) => {
            if (err) {
              console.log(err);
              return reject(err);
            }
            console.log('info', success);
            return resolve(true);
          });
        }
      });
    });
  } catch (error) {
    console.log('---Email Error--', error);
    return false;
  }
}

async function sendMailViaAPI(email, sendData, subject, textTemplate) {
  try {
    // Step 1: Get access token
    const tokenResponse = await axios.post(
      `${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/oauth2/token`, // Endpoint for fetching access token
      null, // Empty body for this request
      {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY // Include API key in headers
        },
        auth: {
          username: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_ID, // Basic Auth: username
          password: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_SECRET // Basic Auth: password
        }
      }
    );

    const accessToken = tokenResponse.data.access_token;
    console.log('tokenResponse---', accessToken);

    // Step 2: Send mail
    const renderPromise = new Promise((resolve, reject) => {
      renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
        if (err) reject(err);
        resolve(dataTemplate);
      });
    });
    const dataTemplate = await renderPromise;
    // Prepare the form data
    if (typeof email === 'string') {
      email = [email]; // Convert a string to an array
    }
    const formData = new FormData();
    formData.append('toEmailIds', email.join(',')); // Replace with actual recipient email
    formData.append('subject', subject);
    formData.append('emailBody', dataTemplate);
    formData.append('fromEmailId', process.env.CLIENT_EMAIL_SENDER);
    // Send the email using Axios
    const mailResponse = await axios.post(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/send`, formData, {
      headers: {
        'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
        Authorization: `Bearer ${accessToken}`,
        ...formData.getHeaders() // Include FormData headers
      }
    });

    console.log('mailResponse---', mailResponse.data);
    const emailRequestId = mailResponse.data.emailRequestId;

    setTimeout(async () => {
      // Step 3: Check mail status
      const statusResponse = await axios.get(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/status/${emailRequestId}`, {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
          Authorization: `Bearer ${accessToken}`
        }
      });
      console.log('API Mail Status:', statusResponse.data);
    }, 3000);

    return true;
  } catch (error) {
    console.log('API Mail Error:', error);
    return false;
  }
}

//----------------------------------------------------- Send MailV2 for mailing to multiple mail id ---------------------------------------------------------------------------------

async function sendMailV2(emails, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL) {
  if (process.env.USE_CLIENT_EMAIL_SERVICE === 'true') {
    return sendMailViaAPI(emails, sendData, subject, textTemplate, sender);
  } else {
    return sendMailV2ViaSMTP(emails, sendData, subject, textTemplate, sender);
  }
}

async function sendMailV2ViaSMTP(emails, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL) {
  try {
    return await new Promise((resolve, reject) => {
      renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
        if (err) {
          console.log(err);
        } else {
          const mainOptions = {
            from: sender,
            to: emails.join(','), // Convert array of emails to comma-separated string,
            subject,
            html: dataTemplate
          };
          transporter.sendMail(mainOptions, (err, success) => {
            if (err) {
              console.log(err);
              return reject(err);
            }
            console.log('info', success);
            return resolve(true);
          });
        }
      });
    });
  } catch (error) {
    console.log('---Email Error--', error);
    return false;
  }
}

// async function sendMailV2ViaAPI(emails, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL) {
//     try {
//         // Step 1: Get access token
//         const tokenResponse = await axios.post(
//             `${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/oauth2/token`, // Endpoint for fetching access token
//             null, // Empty body for this request
//             {
//                 headers: {
//                     'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY, // Include API key in headers
//                 },
//                 auth: {
//                     username: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_ID, // Basic Auth: username
//                     password: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_SECRET, // Basic Auth: password
//                 },
//             }
//         );

//         const accessToken = tokenResponse.data.access_token;
//         console.log("tokenResponse---", accessToken);

//         // Step 2: Render the email template
//         const renderPromise = new Promise((resolve, reject) => {
//             renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
//                 if (err) {
//                     console.log('Template Render Error:', err);
//                     return reject(err);
//                 }
//                 resolve(dataTemplate);
//             });
//         });

//         const dataTemplate = await renderPromise;

//         // Step 3: Prepare FormData to send via the API
//         const formData = new FormData();
//         formData.append('toEmailIds', emails.join(',')); // Comma-separated emails (supports multiple recipients)
//         formData.append('subject', subject);
//         formData.append('emailBody', dataTemplate);
//         formData.append('fromEmailId', sender);

//         // Step 4: Send the email using Axios with FormData
//         const mailResponse = await axios.post(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/send`, formData, {
//             headers: {
//                 'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
//                 'Authorization': `Bearer ${accessToken}`,
//                 ...formData.getHeaders(), // Include headers for FormData
//             },
//         });

//         const emailRequestId = mailResponse.data.emailRequestId;
//         console.log("mailResponse---", emailRequestId);

//         // Step 5: Check email status using the request ID
//         const statusResponse = await axios.get(
//             `${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/status/${emailRequestId}`,
//             {
//                 headers: {
//                     'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
//                     'Authorization': `Bearer ${accessToken}`,
//                     'Cookie': 'XSRF-TOKEN=ffa515de-85a5-4d74-a198-3c393fef26e5',
//                 },
//             }
//         );

//         console.log("API Mail Status:", statusResponse.data);
//         return statusResponse.data.status === 'sent'; // Return true if email is successfully sent
//     } catch (error) {
//         console.log('API Mail Error:', error);
//         return false;
//     }
// }

//--------------------------------------------------- sendMailWithAttach ----------------------------------------------------------------------------------------

async function sendMailWithAttach(emails, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL, folderName) {
  if (process.env.USE_CLIENT_EMAIL_SERVICE === 'true') {
    return sendMailWithAttachViaAPI(emails, sendData, subject, textTemplate, sender, folderName);
  } else {
    return sendMailWithAttachViaSMTP(emails, sendData, subject, textTemplate, sender, folderName);
  }
}

async function sendMailWithAttachViaAPI(email, sendData, subject, textTemplate, folderName) {
  try {
    // Step 1: Get access token
    const tokenResponse = await axios.post(
      `${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/oauth2/token`, // Endpoint for fetching access token
      null, // Empty body for this request
      {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY // Include API key in headers
        },
        auth: {
          username: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_ID, // Basic Auth: username
          password: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_SECRET // Basic Auth: password
        }
      }
    );

    const accessToken = tokenResponse.data.access_token;
    console.log('tokenResponse---', accessToken);

    // Step 2: Send mail
    const renderPromise = new Promise((resolve, reject) => {
      renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
        if (err) reject(err);
        resolve(dataTemplate);
      });
    });
    const dataTemplate = await renderPromise;
    // Prepare the form data
    const formData = new FormData();
    if (typeof email === 'string') {
      email = [email]; // Convert a string to an array
    }
    formData.append('toEmailIds', email.join(',')); // Replace with actual recipient email
    formData.append('subject', subject);
    formData.append('emailBody', dataTemplate);
    formData.append('fromEmailId', process.env.CLIENT_EMAIL_SENDER);

    // Check if the file exists
    if (fs.existsSync(folderName)) {
      const fileStream = fs.createReadStream(folderName);
      formData.append('attachment', fileStream);
      // fs.unlinkSync(folderName);
      fileStream.on('end', () => {
        // Safely delete the file after the stream ends
        fs.unlink(folderName, err => {
          if (err) {
            console.error('Error deleting the file:', err);
          } else {
            console.log('File successfully deleted.');
          }
        });
      });
    } else {
      console.log('File not found:', folderName);
    }

    // Send the email using Axios
    const mailResponse = await axios.post(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/send`, formData, {
      headers: {
        'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
        Authorization: `Bearer ${accessToken}`,
        ...formData.getHeaders() // Include FormData headers
      }
    });

    const emailRequestId = mailResponse.data.emailRequestId;

    setTimeout(async () => {
      // Step 3: Check mail status
      const statusResponse = await axios.get(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/status/${emailRequestId}`, {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
          Authorization: `Bearer ${accessToken}`
        }
      });
      console.log('API Mail Status:', statusResponse.data);
    }, 3000);

    return true;
  } catch (error) {
    console.log('API Mail Error:', error);
    return false;
  }
}

async function sendMailWithAttachViaSMTP(email, sendData, subject, textTemplate, folderName) {
  try {
    renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
      if (err) {
        console.log(err);
      } else {
        const mainOptions = {
          from: process.env.SMTP_EMAIL,
          to: email,
          subject,
          html: dataTemplate,
          attachments: [
            {
              path: folderName
            }
          ]
        };
        transporter.sendMail(mainOptions, (err, success) => {
          if (err) {
            console.log(err);
            return false;
          }
          if (fs.existsSync(folderName)) fs.unlinkSync(folderName);

          console.log('info', success);
          return true;
        });
      }
    });
  } catch (error) {
    console.log('---Email Error--', error);
    return false;
  }
}

//--------------------------------------------------------sendMailWithMultipleAttachments -------------------------------------------------------------------------------------

async function sendMailWithMultipleAttachments(emails, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL, folderNames) {
  if (process.env.USE_CLIENT_EMAIL_SERVICE === 'true') {
    return sendMailWithMultipleAttachmentsViaAPI(emails, sendData, subject, textTemplate, sender, folderNames);
  } else {
    return sendMailWithMultipleAttachmentsViaSMTP(emails, sendData, subject, textTemplate, folderNames);
  }
}

async function sendMailWithMultipleAttachmentsViaAPI(email, sendData, subject, textTemplate, folderNames) {
  try {
    // Step 1: Get access token
    const tokenResponse = await axios.post(
      `${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/oauth2/token`, // Endpoint for fetching access token
      null, // Empty body for this request
      {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY // Include API key in headers
        },
        auth: {
          username: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_ID, // Basic Auth: username
          password: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_SECRET // Basic Auth: password
        }
      }
    );

    const accessToken = tokenResponse.data.access_token;
    console.log('tokenResponse---', accessToken);

    // Step 2: Send mail
    const renderPromise = new Promise((resolve, reject) => {
      renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
        if (err) reject(err);
        resolve(dataTemplate);
      });
    });
    const dataTemplate = await renderPromise;
    // Prepare the form data
    const formData = new FormData();
    if (typeof email === 'string') {
      email = [email]; // Convert a string to an array
    }
    formData.append('toEmailIds', email.join(',')); // Replace with actual recipient email
    formData.append('subject', subject);
    formData.append('emailBody', dataTemplate);
    formData.append('fromEmailId', process.env.CLIENT_EMAIL_SENDER);

    // Iterate over folderNames array and attach each file
    for (const folderName of folderNames) {
      if (fs.existsSync(folderName)) {
        const fileStream = fs.createReadStream(folderName);
        formData.append('attachment', fileStream); // Ensure the key matches API expectations
        fs.unlinkSync(filePath);
      } else {
        console.log('File not found:', folderName);
      }
    }

    // Send the email using Axios
    const mailResponse = await axios.post(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/send`, formData, {
      headers: {
        'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
        Authorization: `Bearer ${accessToken}`,
        ...formData.getHeaders() // Include FormData headers
      }
    });

    const emailRequestId = mailResponse.data.emailRequestId;

    setTimeout(async () => {
      // Step 3: Check mail status
      const statusResponse = await axios.get(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/status/${emailRequestId}`, {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
          Authorization: `Bearer ${accessToken}`
        }
      });
      console.log('API Mail Status:', statusResponse.data);
    }, 3000);

    return true;
  } catch (error) {
    console.log('API Mail Error:', error);
    return false;
  }
}

async function sendMailWithMultipleAttachmentsViaSMTP(email, sendData, subject, textTemplate, attachments) {
  try {
    renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
      if (err) {
        console.log(err);
      } else {
        const mainOptions = {
          from: process.env.SMTP_EMAIL,
          to: email,
          subject,
          html: dataTemplate,
          attachments: attachments.map(filePath => ({ path: filePath }))
        };

        transporter.sendMail(mainOptions, (err, success) => {
          if (err) {
            console.log(err);
            return false;
          }

          attachments.forEach(filePath => {
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
            }
          });

          console.log('info', success);
          return true;
        });
      }
    });
  } catch (error) {
    console.log('---Email Error--', error);
    return false;
  }
}

//-----------------------------Send Multiple Attachments to multiple mails -------------------------------------------------------------------------

async function sendMailsWithMultipleAttachments(emails, sendData, subject, textTemplate, sender = process.env.SMTP_EMAIL, folderNames) {
  if (process.env.USE_CLIENT_EMAIL_SERVICE === 'true') {
    return sendMailsWithMultipleAttachmentsViaAPI(emails, sendData, subject, textTemplate, sender, folderNames);
  } else {
    return sendMailsWithMultipleAttachmentsViaSMTP(emails, sendData, subject, textTemplate, folderNames);
  }
}

async function sendMailsWithMultipleAttachmentsViaAPI(emails, sendData, subject, textTemplate, folderNames) {
  try {
    // Step 1: Get access token
    const tokenResponse = await axios.post(
      `${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/oauth2/token`, // Endpoint for fetching access token
      null, // Empty body for this request
      {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY // Include API key in headers
        },
        auth: {
          username: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_ID, // Basic Auth: username
          password: process.env.CLIENT_EMAIL_SERVICE_API_CLIENT_SECRET // Basic Auth: password
        }
      }
    );

    const accessToken = tokenResponse.data.access_token;
    console.log('tokenResponse---', accessToken);

    // Step 2: Send mail
    const renderPromise = new Promise((resolve, reject) => {
      renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
        if (err) reject(err);
        resolve(dataTemplate);
      });
    });
    const dataTemplate = await renderPromise;
    // Prepare the form data
    const formData = new FormData();
    if (typeof emails === 'string') {
      emails = [emails]; // Convert a string to an array
    }
    formData.append('toEmailIds', emails.join(',')); // Replace with actual recipient email
    formData.append('subject', subject);
    formData.append('emailBody', dataTemplate);
    formData.append('fromEmailId', process.env.CLIENT_EMAIL_SENDER);

    // Iterate over folderNames array and attach each file
    for (const folderName of folderNames) {
      if (fs.existsSync(folderName)) {
        const fileStream = fs.createReadStream(folderName);
        formData.append('attachment', fileStream); // Ensure the key matches API expectations
        fs.unlinkSync(filePath);
      } else {
        console.log('File not found:', folderName);
      }
    }

    // Send the email using Axios
    const mailResponse = await axios.post(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/send`, formData, {
      headers: {
        'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
        Authorization: `Bearer ${accessToken}`,
        ...formData.getHeaders() // Include FormData headers
      }
    });

    const emailRequestId = mailResponse.data.emailRequestId;

    setTimeout(async () => {
      // Step 3: Check mail status
      const statusResponse = await axios.get(`${process.env.CLIENT_EMAIL_SERVICE_API_BASE_URL}/email/status/${emailRequestId}`, {
        headers: {
          'x-api-key': process.env.CLIENT_EMAIL_SERVICE_X_API_KEY,
          Authorization: `Bearer ${accessToken}`
        }
      });
      console.log('API Mail Status:', statusResponse.data);
    }, 3000);

    return true;
  } catch (error) {
    console.log('API Mail Error:', error);
    return false;
  }
}

async function sendMailsWithMultipleAttachmentsViaSMTP(emails, sendData, subject, textTemplate, attachments) {
  try {
    renderFile(path.join(`app/public/mailTemplates/${textTemplate}`), sendData, (err, dataTemplate) => {
      if (err) {
        console.log(err);
      } else {
        const mainOptions = {
          from: process.env.SMTP_EMAIL,
          to: emails.join(','),
          subject,
          html: dataTemplate,
          attachments: attachments?.map(filePath => ({ path: filePath }))
        };

        transporter.sendMail(mainOptions, (err, success) => {
          if (err) {
            console.log(err);
            return false;
          }

          attachments.forEach(filePath => {
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
            }
          });

          console.log('info', success);
          return true;
        });
      }
    });
  } catch (error) {
    console.log('---Email Error--', error);
    return false;
  }
}

async function sendMailWithAttachV2(email, sendData, subject, textTemplate, buffer, sender = process.env.SMTP_EMAIL) {
  try {
    return new Promise(async (resolve, reject) => {
      const mailOptions = {
        from: sender,
        to: email,
        subject: subject,
        html: textTemplate,
        attachments: [
          {
            content: buffer,
            contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          }
        ]
      };
      transporter.sendMail(mailOptions, (err, success) => {
        if (err) {
          console.log(err);
          reject(error);
        }

        console.log('info', success);
        resolve(success);
      });
    });
  } catch (error) {
    return false;
  }
}

module.exports = {
  sendMail,
  sendMailV2,
  sendMailWithAttach,
  sendMailWithMultipleAttachments,
  sendMailsWithMultipleAttachments,
  sendMailWithAttachV2
};
