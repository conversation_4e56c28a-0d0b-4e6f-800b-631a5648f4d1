{"SIGNUP_SUCCESSFUL": "User registered successfully", "LOGIN_SUCCESSFUL": "User logged in successfully", "USER_REGISTERED_LOGIN": "User registered successfully. Please login to verify your account.", "API_SUCCESS": "Success", "LOGOUT_SUCCESSFUL": "User logged out successfully", "PASSWORD_UPDATED": "Password has been Updated!", "FORGOT_PASSWORD": "Reset link Sent on your mail", "NOT_MATCHED": "Not Matched Yet", "DELETED": "Deleted successfully.", "OTP_SENT": "Otp sent successfully", "EMAIL_VERIFIED": "Email successfully verified", "P_UPDATE": "Profile update successfully", "UPDATE_EMAIL": "Email update successfully", "TOKEN_EXPIRED": "Token Expired", "ALREADY_REGISTERED": "An account has already been created.", "REG_ALREADY_REGISTERED": "An account has already been created with this registration number.", "UPDATE_ERROR": "Error in updating data.", "API_ERROR": "Error in Api Execution.", "VALIDATION_ERROR": "Validation error.", "FAILED_TO_ADD": "Failed to Add Data.", "INVALID_CREDENTIALS": "Invalid Credentials", "EMAIL_FAILURE": "<PERSON><PERSON> not sent.", "EMAIL_ALREADY_EXISTS": "Email already exists.", "EMAIL_ALREADY_EXIST_IN_KEYCLOAK": "Email already exists in keyCloak", "USER_NOT_FOUND": "User Not Found", "UNAUTHORIZED": "Unauthorized Access.", "FAILED_TO_UPDATE": "Failed to Update.", "FAILED_TO_DELETE": "Failed to Delete Data.", "INTERNAL_SERVER_ERROR": "Internal server error", "INVALID_EMAIL": "invalid email id", "INVALID_OTP": "invalid otp", "SIGNUP_FAILED": "Your signUp failed", "EMAIL_NOT_VERIFIED": "Email is not verified", "INVALID_TOKEN": "Your token is invalid.", "EMAIL_v_FAILED": "Email verification is failed", "MISSING_TOKEN": "Missing token", "MISSING_P": "Missing parameter", "OTP_NOT_SEND": "Otp not send successfully", "OTP_EXPIRED": "Otp has expired", "NOT_FOUND": "Data not found", "TOTAL_LOGIN": "You've reached the maximum login limits. Please logout of other devices and login again.", "WRONG_PASS": "Incorrect password", "INVALID_ROUTE": "Invalid route", "MISSING_API_KEY": "Missing API key", "INVALID_API_KEY": "Api key is invalid", "LAWS_FETCHED": "Laws fetched successfully", "REGIONS_FETCHED": "Regions fetched successfully", "STATE_FETCHED": "State fetched successfully", "COUNTRY_FETCHED": "Country fetched successfully", "POLICY_ALREADY_EXISTS": "Policy with this name already exists.", "POLICY_ADDED": "Policy created successfully", "POLICY_FETCHED": "Policies fetched successfully", "DEPARTMENT_FETCHED": "Departments fetched successfully", "INDUSTRY_FETCHED": "Industry Vertical List fetched successfully", "ERROR_CREATING_POLICY": "Error while creating Policy", "ERROR_CREATING_ANSWERS": "Error while creating answers", "POLICY_NOT_FOUND": "Policy not found", "ERROR_IN_POLICYLIST": " Error in fetching Policy list", "ERROR_UPDATING_POLICY": "Error while updating policy", "POLICY_UPDATED": "Policy updated Successfully", "POLICY_NAME_EXIST": "Policy name Exist.", "DEPARTMENT_CREATED": "Departments created successfully", "ERROR_CREATING_DEPARTMENT": "Error while creating Department", "DEPARTMENT_NAME_EXIST": "Department name already exist", "PROCESS_CREATED": "Process created successfully", "ERROR_CREATING_PROCESS": "Error while creating Process", "PROCESS_NAME_EXIST": "Process name already exist", "ROPA_NOT_ASSIGNED": "ROPA not assigned", "ROPA_CANT_START": "ROPA is already performed and can not started", "ROPA_CANT_ASSIGN": " ROPA is already performed and can not assigned", "ROPA_FETCHED": "ROPA fetched successfully", "ROPA_YET_TO_START": " ROPA yet not started", "ROPA_CHANGES_REQUESTED": "ROPA already in change request", "CATEGORIES_NOT_FOUND": "Categories not found", "CATEGORIES_FETCHED": "Categories fetched successfully", "CONTROLS_NOT_FOUND": "Controls not found", "CONTROLS_FETCHED": "Controls fetched successfully", "ERROR_CREATING_CONTROL": "Error while creating Control", "CONTROL_CREATED": "Control created successfully", "ERROR_UPDATING_CONTROL": "Error while updating Control", "CONTROL_UPDATED": "Control updated successfully", "ERROR_CREATING_ROPA": "Error while creating ROPA", "ROPA_NOT_FOUND": "ROPA not found", "ROPA_STARTED": "ROPA started", "POLICY_COUNT_FETCHED": "Policy Count Fetched.", "DATA_NOT_FOUND": "Error in finding the Policies count.", "ERROR": "Something went wrong at the Server", "ERROR_CREATING_ANSWER": "Error while creating Answer", "ANSWER_CREATED_OR_UPDATED": "Answer saved successfully", "ERROR_UPDATING_ANSWER": "Error while updating Answer", "ROPA_ASSIGNED": "ROPA assigned successfully", "ROPA_UNDER_REVIEW": "ROPA is under review", "ROPA_COMPLETED": "ROPA is already completed", "ROPA_SUBMITTED": "ROPA is submitted successfully", "DOCUMENT_UPLOADED": "Documents uploaded successfully.", "S3_BUCKET_ERROR": "Error in S3 Bucket", "UPLOAD_FILE": "Upload File", "INVOICES_FETCHED": "Invoices fetched successfully", "ERROR_UPDATING_GROUPUSER": "Error in Updating Group Access", "DASHBOARD_FETCHED": "Dashboard fetched successfully", "ERROR_IN_AUDIT": "Error in Updating Audit Log", "ERROR_IN_DOCUMENT_UPLOAD": "Error in Uploading Documents", "AUDIT_DATA_NOT_FOUND": "Audit data not found", "AUDIT_LOG_FETCHED": "<PERSON><PERSON> Fetched.", "INVALID_ROPA_LEVEL": "Invalid ROPA Level", "QUESTIONS_NOT_FOUND": "Questions not found", "QUESTIONS_FETCHED": "Questions fetched successfully", "ERROR_ADDING_COLLABORATOR": "Error in adding Collaborator", "COLLABORATOR_ADDED": "Collaborator added successfully", "ROPA_NOT_STARTED": "ROPA not started", "AUDIT_ERROR": "Error in Auditing", "ARTIFACT_TYPES_NOT_FOUND": "Artifact Types not found", "ARTIFACT_TYPES_FETCHED": "Artifact Types fetched successfully", "INVALID_DATA": "Invalid Data", "INVALID_ARTIFACT_TYPE": "Invalid Artifact Type", "CONTROLS_UPLOADED": "Controls uploaded successfully", "INVALID_EXTRA_INPUT_TYPE": "Invalid Extra Input Type", "ALL_NOT_REVIEWED": "Review all the questions before submitting", "ROPA_NOT_SUBMITTED": "ROPA is not submitted for review yet", "ALL_NOT_ANSWERED": "Answer all the questions before submitting", "LIA_NOT_ASSIGNED": "LIA not assigned", "LIA_FETCHED": "LIA fetched successfully", "ERROR_CREATING_LIA": "Error while creating LIA", "LIA_NOT_FOUND": "LIA not found", "LIA_STARTED": "LIA started", "LIA_ASSIGNED": "LIA assigned successfully", "LIA_UNDER_REVIEW": "LIA is under review", "LIA_COMPLETED": "LIA is already completed", "LIA_SUBMITTED": "LIA is submitted successfully", "LIA_NOT_STARTED": "LIA not started", "INVALID_LIA_LEVEL": "Invalid LIA Level", "LIA_NOT_SUBMITTED": "LIA is not submitted for review yet", "LIA_NOT_COMPLETED": "LIA is not completed yet", "LIA_REVIEWER_ADDED": "LIA Reviewer Added", "TIA_NOT_ASSIGNED": "TIA not assigned", "TIA_FETCHED": "TIA fetched successfully", "ERROR_CREATING_TIA": "Error while creating TIA", "TIA_NOT_FOUND": "TIA not found", "TIA_STARTED": "TIA started", "TIA_ASSIGNED": "TIA assigned successfully", "TIA_UNDER_REVIEW": "TIA is under review", "TIA_COMPLETED": "TIA is already completed", "TIA_SUBMITTED": "TIA is submitted successfully", "TIA_NOT_STARTED": "TIA not started", "INVALID_TIA_LEVEL": "Invalid TIA Level", "TIA_NOT_SUBMITTED": "TIA is not submitted for review yet", "TIA_NOT_COMPLETED": "TIA is not completed yet", "TIA_REVIEWER_ADDED": "TIA Reviewer Added", "PIA_NOT_ASSIGNED": "PIA not assigned", "PIA_FETCHED": "PIA fetched successfully", "ERROR_CREATING_PIA": "Error while creating PIA", "PIA_NOT_FOUND": "PIA not found", "PIA_STARTED": "PIA started", "PIA_ASSIGNED": "PIA assigned successfully", "PIA_UNDER_REVIEW": "PIA is under review", "PIA_COMPLETED": "PIA is already completed", "PIA_SUBMITTED": "PIA is submitted successfully", "PIA_NOT_STARTED": "PIA not started", "INVALID_PIA_LEVEL": "Invalid PIA Level", "PIA_NOT_SUBMITTED": "PIA is not submitted for review yet", "PIA_NOT_COMPLETED": "PIA is not completed yet", "PIA_REVIEWER_ADDED": "PIA Reviewer Added", "PDA_FETCHED": "PDA fetched successfully", "ERROR_CREATING_PDA": "Error while creating PDA", "PDA_NOT_FOUND": "PDA not found", "PDA_STARTED": "PDA started", "PDA_ASSIGNED": "PDA assigned successfully", "PDA_UNDER_REVIEW": "PDA is under review", "PDA_COMPLETED": "PDA is already completed", "PDA_SUBMITTED": "PDA is submitted successfully", "PDA_NOT_COMPLETED": "PDA is not completed yet", "PDA_NOT_STARTED": "PDA not started", "PDA_REVIEWER_ADDED": "PDA Reviewer Added", "INVALID_PDA_LEVEL": "Invalid PDA Level", "PDA_NOT_SUBMITTED": "PDA is not submitted for review yet", "COLLABORATORS_NOT_FOUND": "Collaborators not found", "COLLABORATORS_FETCHED": "Collaborators fetched successfully", "ERROR_DELETING_COLLABORATOR": "Error in deleting Collaborator", "COLLABORATOR_UPDATED": "Collaborator updated successfully", "CANT_UPDATE": "Policy is in use : Can't update.", "ERROR_DELETING_CONTROL": "Error in deleting Control", "CONTROL_DELETED": "Control deleted successfully", "DOCUMENT_DELETED_ERROR": "Error in Deleting the Document", "DOCUMENT_DELETED": "Document Deleted", "DEPARTMENT_DOES_NOT_EXIST": "Department does not exist", "PROCESS_DOES_NOT_EXIST": "Process does not exist", "PROCESS_UPDATED": "Process Updated Successfully.", "PROGRESS FETCHED": "Progress fetched successfully", "ERROR_CREATING_VENDOR": " Error in creating <PERSON><PERSON><PERSON>", "VENDOR_ADDED": "<PERSON><PERSON><PERSON> created Successfully", "VENDOR_NOT_FOUND": "<PERSON><PERSON><PERSON> not Exist", "VENDOR_NAME_EXIST": "Vendor name already exist.", "ERROR_UPDATING_VENDOR": "Error in updating <PERSON><PERSON><PERSON>ail.", "VENDOR_UPDATED": " Vendor Updated successfully", "ERROR_UPDATING_ANSWERS": "Error in updating Answer.", "FAILED_TO_GET_AI_RESPONSE": "Failed to get AI response", "VENDOR_ROLE_NOT_EXIST": "V<PERSON><PERSON> role not found", "ROPA_NOT_COMPLETED": "Ropa not completed yet", "POLICY_DOWNLOADED": "Policy downloaded successfully", "ROPA_DOWNLOADED": "ROPA sent to mail successfully", "TIA_DOWNLOADED": "TIA sent to mail successfully", "LIA_DOWNLOADED": "LIA sent to mail successfully", "PIA_DOWNLOADED": "PIA sent to mail successfully", "PDA_DOWNLOADED": "PDA sent to mail successfully", "CUSTOMER_NOT_FOUND": "Customer name not found", "POLICY_NOT_IN_USE": " Policy document can't be downloaded because Policy not in use", "POLICY_ID_REQUIRED": "Policy Id is required", "TEMPLATES_NOT_FOUND": "Templates not found", "TEMPLATE_NOT_FOUND": "Temp<PERSON> not found", "TEMPLATES_FETCHED": "Templates fetched successfully", "TEMPLATE_FETCHED": "Template fetched successfully", "CUSTOM_POLICY_FETCHED": "Custom Policy fetched successfully", "FAILED_TO_CREATE_CUSTOM_POLICY": "Failed to create custom policy", "CUSTOM_POLICY_CREATED": "Custom Policy created successfully", "CANNOT_CREATE_POLICY": "Cannot create policy at this stage", "CUSTOM_POLICY_NOT_FOUND": "Custom Policy not found", "FAILED_TO_SAVE_POLICY": "Failed to save policy", "POLICY_SAVED": "Policy saved successfully", "FAILED_TO_UPLOAD_DOCUMENT": "Failed to upload document", "ERROR_CREATING_GROUP_USER": "Error in creating Group User", "ERROR_CREATING_ASSESSMENT": "Error in creating Assessment", "ASSESSMENT_LIST": "List of Assessments", "ASSESSMENT_CREATED": "Assessment created Successfully.", "ASSESSMENT_MAIL_NOT_TRIGGRED": "Assessment created Successfully But mail does not triggred.", "ASSESSMENT_UPDATED": "Assessment updated Successfully.", "TASK_OVERVIEW_FETCHED": "Task overview list fetched Successfully", "PROCESS_NOT_FOUND": "Process list can't fetched", "PROCESS_LIST": "Process list fetched successfully", "DEPARTMENT_NOT_FOUND": "Department list can't fetched", "DEPARTMENT_LIST": "Department list fetched successfully", "NOT_REGISTERED_ON_GOTRUST": "User is not registered on GoTrust", "INACTIVE_CUSTOMER": "Account is inactive. Someone will contact you soon.", "VENDOR_LIST_FETCHED": "<PERSON><PERSON><PERSON> List Fetched Successfully.", "FETCHING_ERROR": "List not fetched", "VENDOR_NOT_EXIST": "Vend<PERSON> does not exist", "VENDOR_FETHCED": "Vendor details fetched", "VIA_NOT_ASSIGNED": "VIA not assigned", "VIA_FETCHED": "VIA fetched successfully", "ERROR_CREATING_VIA": "Error while creating VIA", "VIA_NOT_FOUND": "VIA not found", "VIA_STARTED": "VIA started", "VIA_ASSIGNED": "VIA assigned successfully", "VIA_UNDER_REVIEW": "VIA is under review", "VIA_COMPLETED": "VIA is already completed", "VIA_SUBMITTED": "VIA is submitted successfully", "VIA_NOT_STARTED": "VIA not started", "INVALID_VIA_LEVEL": "Invalid VIA Level", "VIA_NOT_SUBMITTED": "VIA is not submitted for review yet", "VIA_NOT_COMPLETED": "VIA is not completed yet", "VIA_REVIEWER_ADDED": "VIA Reviewer Added", "VIA_DOWNLOADED": "VIA sent to mail successfully", "VEA_NOT_FOUND": "VEA not found", "VEA_STARTED": "VEA started", "VEA_ASSIGNED": "VEA assigned successfully", "VEA_UNDER_REVIEW": "VEA is under review", "VEA_COMPLETED": "VEA is already completed", "VEA_SUBMITTED": "VEA is submitted successfully", "VEA_NOT_STARTED": "VEA not started", "INVALID_VEA_LEVEL": "Invalid VEA Level", "VEA_NOT_SUBMITTED": "VEA is not submitted for review yet", "VEA_NOT_COMPLETED": "VEA is not completed yet", "VEA_REVIEWER_ADDED": "VEA Reviewer Added", "VEA_DOWNLOADED": "VEA sent to mail successfully", "RISK_NOT_FETCHED": "Error in loading the risk of internal assessment", "STATUS_NOT_FETCHED": "Error in loading the status of internal assessment", "VENDOR_TYPE_LIST_FETCHED": "Vendor type fetched Successfully", "MODEL_NOT_FOUND": "Invalid key or model name", "DOCUMENT_NOT_FOUND": "Document not found", "INVALID_KEY": "Key is Invalid", "ASSESSMENTS_RISK_COUNT": "Count of assessment according to risk", "ASSESSMENTS_NAME_COUNT": "Count of assessment according to name", "ASSESSMENTS_OWNER_COUNT": "Count of assessment according to owner", "VRM_RISK_COUNT": "Count of Vendos according to risk", "DASHBOARD_DATA_NOT_FOUND": "Error in fetching the dashboard data", "EMAIL_V_FAILED": "Email verification failed.", "QUESTIONNAIRES_FETCHED": "Questionnaires fetched successfully", "ERROR_CREATING_TOKEN": "Error while creating token", "ACCESS_TOKEN_CREATED": "Access Token created successfully", "USER_FETCHED": "User fetched successfully", "ROLE_FETCHED": "Role fetched successfully", "SIDEBAR_FETCHED": "Sidebar fetched successfully", "CUSTOMER_FETCHED": "Customer fetched successfully", "EMAIL_ALREADY_EXIST": "Email already exist", "ASSIGNED": "Role assignment and unassignment completed successfully", "ERROR_RESOURCE_DELETE": "Error while deleting resources", "ERROR_RESOURCE_UPDATE": "Error while updating resources", "USER_UPDATED": "User updated successfully", "ROLE_EXIST": "This role already exist.", "GROUP_FETCHED": "Group fetched successfully", "ROLE_NOT_FOUND": "Role not found", "GROUP_NAME_EXIST": "Group with this name already exists.", "ERROR_CREATING_GROUP": "Error while creating group", "GROUP_CREATED": "Group created successfully", "RESOURCES_NOT_FOUND": "Resources not found", "FORBIDDEN": "Access to the requested resource is forbidden", "NO_RESOURCES": "No resources allocated to this role", "LANGUAGE_FETCHED": "Language fetched successfully.", "GROUP_DOES_NOT_EXIST": "Group does not exist.", "ENTITY_FETCHED": "En<PERSON><PERSON> fetched successfully", "BUSINESS_UNIT_NOT_FOUND": "Business unit not found", "GROUP_UPDATED": "Group updated successfully", "UPLOAD_FAILED": " Uploading failed", "TICKET_RAISED": "Ticket raised Successfully", "TICKET_NOT_FETCHED": "Error in fetching the Ticket details", "TICKET_OPENED": "Ticket Opened Successfully", "TICKETS_FETCHED": "Tickets Fetched Successfully", "ERROR_IN_TICKET_DATA": "Error in Fetching the Tickets Stats", "TICKET_UPDATED": "Ticket Updated Successfully", "COMMENT_ERROR": "Error in creating Comment ob Ticket", "COMMENT_SUCCESS": "Commented On Ticket Successfully", "CUSTOMER_UPDATED": "Customer Updated Successfully", "ROLE_UPDATE_NOT_ALLOWED": "Role update not allowed", "SOMEONE_CONTACT_SOON": "Someone will contact you soon", "DPO_NOT_FOUND": "DPO Not found", "USER_VALIDATED": "User validated successfully", "ERROR_CREATING_DATA_SUBJECT": "Error in saving information of Data Subject", "ERROR_CREATING_REQUEST": "Error in creating the DSR Request", "REQUEST_CREATED": "DSR request is created", "WORKFLOW_NAME_EXIST": "Duplicate workflow. Please choose a different name", "ERROR_CREATING_WORKFLOW": "Error while creating workflow type", "WORKFLOW_CREATED": "Workflow created successfully", "WORKFLOW_DOES_NOT_EXIST": "Workflow does not exist", "WORKFLOW_NOT_FOUND": "Workflow list can't fetched", "WORKFLOW_FETCHED": "Workflow fetched successfully", "WORKFLOW_UPDATED": "Workflow updated successfully", "WORKFLOW_STEP_NAME_EXIST": "Step name already exist", "ERROR_CREATING_WORKFLOW_STEP": "Error while creating workflow step", "WORKFLOW_STEP_CREATED": "Workflow step created successfully", "WORKFLOW_STEP_DOES_NOT_EXIST": "Workflow step does not exist", "WORKFLOW_STEP_UPDATED": "Workflow step updated successfully", "ERROR_DELETING_WORKFLOW_STEP": "Error in deleting workflow step", "WORKFLOW_STEP_DELETED_SUCC": "Workflow step deleted successfully", "WORKFLOW_STEP_ALREADY_TOP": "Workflow step is already at the top", "TASK_NAME_EXIST": "Task name already exist", "ERROR_CREATING_TASK": "Error while creating Task", "TASK_CREATED": "Task created successfully", "TASK_DOES_NOT_EXIST": "Task does not exist", "TASK_NOT_FOUND": "Task list can't fetched", "TASK_FETCHED": "Task fetched successfully", "TASK_UPDATED": "Task updated successfully", "ERROR_DELETING_TASK": "Error in deleting task", "TASK_DELETED_SUCC": "Task deleted successfully", "RISK_MATRIX": " Risk Matrix fetched Successfully", "TEMPLATE_NOT_ADDED": "Template not added. Please try again", "LIST_FETCHED": "Template list fetched", "MITIGATION_COMPLETED": "Mitigation Completed", "DATA_SUBJECT_DOES_NOT_EXIST": "Data subject does not exist", "REQUEST_NOT_FOUND": "Request list can't fetched", "REQUEST_FETCHED": "Request fetched successfully", "REQUEST_DOES_NOT_EXIST": "Request does not exist", "REQUEST_UPDATED": "Request updated successfully", "REQUEST_TYPE_COUNT": "Count of request by the type", "REQUEST_STATUS_COUNT": "Count of request by the status", "FORM_NOT_FOUND": "Form not exist", "FORM_CREATE_ERROR": "Form does not created", "FORM_UPDATE_ERROR": "Form does not updated", "FORM_CREATED": "Form successfully created", "FORM_UPDATED": "Form successfully updated", "DATA_FETCHED": "Data fetched successfully", "SERVICE_ENTITY_FETCHED": "Service Entity fetched successfully", "MAIL_LIST_ERROR": "Error in fetching the list of mail template", "MAIL_LIST_FETCHED": "Mail template list fetched Successfully", "MAIL_CREATE_ERROR": "Error in creating mail template", "MAIL_UPDATED": "Mail template updated", "MAIL_DOES_NOT_EXIST": "Mail template does not exist", "MAIL_DELETE_ERROR": "Error in Deleting the Mail template", "MAIL_DELETE": "Mail template delete", "MAIL_UPDATE_ERROR": "Error in updating mail template", "MAIL_CREATED": "Mail template created", "EMAIL_SUCCESS": "<PERSON>ail sent successfully", "EMAIL_REQUIRED": "Email id is required", "MITIGATION_NOT_COMPLETED": "Mitigation not completed yet for Vendor", "MITIGATION_SAVED": "Mitigation saved", "EMAIL_NOT_FOUND": "<PERSON><PERSON> not found", "MITIGATION_PLAN_NOT_FILLED": "Please fill the mitigation plan for all controls", "LIST_ERROR": "Error in fetching List", "REGULATION_LIST_FETCHED": "List fetched successfully", "DUPLICATE_WEBHOOK": "Action name already exist", "WEBHOOK_CREATED": "Webhook created successfully", "ERROR_CREATING_WEBHOOK": "Webhook created successfully", "WEBHOOK_DELETED_ERROR": "Error in Deleting the webhook", "WEBHOOK_DELETED": "Webhook Deleted", "WEBHOOK_NOT_EXIST": "Webhook does not exist", "AUDIT_DOWNLOADED": "Audit downloaded successfully", "DATASUBJECT_NOT_MATCHED": "Data subject information does not match", "DSRINFO_DOES_NOT_EXIST": "Your information does not exist in our record", "OCR_NOT_MATTACHED": "Your identity information does not match in provided documents.", "ALREADY_EXIST_TEMPLATE": "Template Already Exist, Can't update .", "WORKFLOW_ID_REQUIRED": "Workflow id is required.", "UNSTRUCTURED_DATA_MANAGEMENT_FETCHED": "Unstructed Data Management fetched successfully", "DOCUMENT_LIST": "Document list fetched successfully", "FILE_FORMAT": "File Format fetched successfully", "SUMMARY_FETCHED": "Summary fetched successfully", "LOCATION_FETCHED": "Location fetched successfully", "VIEW_DETAILS_FETCHED": "View details fetched successfully", "ELEMENT_CATEGORIES_FETCHED": "Element categories fetched successfully", "DATA_MANAGEMENT_FETCHED": "Data Management fetched successfully", "ELEMENT_TYPES_FETCHED": "Element Types fetched successfully", "STRUCTURED_VIEW_LOCATION": "Structured View Location fetched successfully", "PROFILER_META_FETCHED": "Profiler <PERSON><PERSON> fetched successfully", "ERROR_ADDING_ACTION": "Action does not added", "ACTION_ADDED": "Action added successfully", "ACTION_LIST_FETCHED": "Action list fetched successfully", "ACTION_NOT_FOUND": "Action is not found", "ACTION_FETCHED": "Action fetched successfully", "ERROR_CREATING_DUTY": "Error in creating duty", "DUTY_CREATED": "Duty is created successfully", "DUTY_NOT_FOUND": "Duty not found", "DUTY_UPDATED": "Duty updated successfully", "STRUCTURED_SENSITIVITY_FETCHED": "Structured Sensitivity Data Fetched", "UNSTRUCTURED_SENSITIVITY_FETCHED": "Unstructured Sensitivity Data Fetched", "RESOURCE_LIST_NOT_FOUND": "Resource list not found", "RESOURCES_ADDED": "Resource is added successfully", "ERROR_CREATING_IMPROVEMENT": "Error in creating improvement", "IMPROVEMENT_CREATED": "Improvement is created successfully", "IMPROVEMENT_UPDATED": "Improvement updated successfully", "IMPROVEMENT_NOT_FOUND": "Improvement not found", "CATEGORY_NOT_FOUND": "Category not found", "CATEGORY_CREATE_ERROR": "Error in creating category", "CATEGORY_FETCHED": "Category fetched successfully", "ERROR_DELETING_FORM": "Error in deleting the form", "FORM_DELETED": "Form deleted successfully", "ERROR_DELETING_CATEGORY": "Error in deleting Categories", "TASK_STATUS": "Task Status is required", "FORM_FETCHED": "Form fetched successfully", "FORM_PUBLISHED": "Form published successfully", "FORM_NOT_PUBLISHED": "Error in publishing form", "FORM_ALREADY_PUBLISHED": "Form already exist", "DATA_UPDATED": "Data updated successfully", "DATA_DELETED": "Data deleted successfully", "DATA_CREATED": "Data created successfully", "ERROR_CREATING_INCIDENT": "Error in creating incident", "INCIDENT_CREATED": "Incident is created", "INCIDENT_NOT_FOUND": "Incident list can't fetched", "ASSESSMENT_NOT_FOUND": "Assessment does not exist", "ASSESSMENT_NOT_ASSIGNED": "Assessment is not assigned", "ASSESSMENT_STARTED": "Assessment started", "ASSESSMENT_UNDER_REVIEW": "Assessment is under review", "ASSESSMENT_COMPLETED": "Assessment Completed", "ASSESSMENT_DOWNLOADED": "Assessment sent to mail successfully", "ASSESSMENT_ASSIGNED": "Assessment assigned successfully", "ASSESSMENT_REVIEWER_ADDED": "Assessment Reviewer Added", "ALREADY_VERIFIED": "Email Already verified", "FORM_VERIFIED": "Form verified successfully", "DUPLICATE_QUESTION_TITLE": "Some questions appear more than once.", "ERROR_CREATING_DASHBOARD": "Error in creating dashboard", "DASHBOARD_CREATED": "Dashboard created successfully", "DASHBOARD_NOT_FOUND": "Dashboard not found", "DASHBOARD_UPDATED": "Dashboard updated successfully", "DASHBOARD_DELETED": "Dashboard deleted successfully", "DELETE_ERROR": "Error in deleting Element", "DASHBOARD_ALREADY_EXIST": "Dashboard already exist", "CANNOT_DELETE_DEPARTMENT": "A department cannot be deleted if it has a ROPA, Process, or sub-department", "CANNOT_DELETE_PROCESS": "A process cannot be deleted if it has a ROPA or sub-process", "DEPARTMENT_DELETED": "Department deleted successfully", "PROCESS_DELETED": "Process deleted successfully", "GROUP_DELETED": "Group deleted successfully", "CANNOT_DELETE_GROUP": "A group cannot be deleted if it has a department or sub-group", "ERROR_DELETING_GROUP_USER": "Error in deleting Group User", "ERROR_DELETING_CUSTOMER_REGULATIONS": "Error in deleting Customer Regulations", "ERROR_DELETING_CUSTOMER_BUSINESS_REQUIREMENTS": "Error in deleting Customer Business Requirements"}