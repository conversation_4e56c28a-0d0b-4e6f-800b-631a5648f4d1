{"SIGNUP_SUCCESSFUL": "Utente registrato con successo", "LOGIN_SUCCESSFUL": "Utente connesso con successo", "USER_REGISTERED_LOGIN": "Utente registrato con successo. Effettua il login per verificare il tuo account.", "API_SUCCESS": "Successo", "LOGOUT_SUCCESSFUL": "Utente disconnesso con successo", "PASSWORD_UPDATED": "Password aggiornata!", "FORGOT_PASSWORD": "Link di reset inviato alla tua email", "NOT_MATCHED": "Non ancora abbinato", "DELETED": "Eliminato con successo.", "OTP_SENT": "OTP inviato con successo", "EMAIL_VERIFIED": "Email verificata con successo", "P_UPDATE": "Profilo a<PERSON>rna<PERSON> con successo", "UPDATE_EMAIL": "Email a<PERSON> con <PERSON>o", "TOKEN_EXPIRED": "<PERSON><PERSON> scaduto", "ALREADY_REGISTERED": "È già stato creato un account.", "REG_ALREADY_REGISTERED": "È già stato creato un account con questo numero di registrazione.", "UPDATE_ERROR": "Errore nell'aggiornamento dei dati.", "API_ERROR": "Errore nell'esecuzione dell'API.", "VALIDATION_ERROR": "Errore di validazione.", "FAILED_TO_ADD": "Impossibile aggiungere i dati.", "INVALID_CREDENTIALS": "Credenziali non valide", "EMAIL_FAILURE": "Email non inviata.", "EMAIL_ALREADY_EXISTS": "Email già esistente.", "EMAIL_ALREADY_EXIST_IN_KEYCLOAK": "<PERSON>ail gi<PERSON> es<PERSON>ente in keyCloak", "USER_NOT_FOUND": "Utente non trovato", "UNAUTHORIZED": "Accesso non autorizzato.", "FAILED_TO_UPDATE": "Impossibile aggiornare.", "FAILED_TO_DELETE": "Impossibile eliminare i dati.", "INTERNAL_SERVER_ERROR": "Errore interno del server", "INVALID_EMAIL": "ID email non valido", "INVALID_OTP": "OTP non valido", "SIGNUP_FAILED": "La tua registrazione è fallita", "EMAIL_NOT_VERIFIED": "Email non verificata", "INVALID_TOKEN": "Il tuo token non è valido.", "EMAIL_v_FAILED": "La verifica dell'email è fallita", "MISSING_TOKEN": "Token mancante", "MISSING_P": "Parametro mancante", "OTP_NOT_SEND": "OTP non inviato con successo", "OTP_EXPIRED": "OTP scaduto", "NOT_FOUND": "Dati non trovati", "TOTAL_LOGIN": "Hai raggiunto il limite massimo di accessi. Disconnettiti dagli altri dispositivi e accedi nuovamente.", "WRONG_PASS": "Password errata", "INVALID_ROUTE": "Percorso non valido", "MISSING_API_KEY": "Chiave API mancante", "INVALID_API_KEY": "La chiave API non è valida", "LAWS_FETCHED": "<PERSON><PERSON>i recuperate con successo", "REGIONS_FETCHED": "Regioni recuperate con successo", "STATE_FETCHED": "Stato recuperato con successo", "COUNTRY_FETCHED": "Paese recuperato con successo", "POLICY_ALREADY_EXISTS": "Una policy con questo nome esiste già.", "POLICY_ADDED": "Policy creata con successo", "POLICY_FETCHED": "Policy recuperate con successo", "DEPARTMENT_FETCHED": "Dipartimenti recuperati con successo", "INDUSTRY_FETCHED": "Elenco verticali di settore recuperato con successo", "ERROR_CREATING_POLICY": "Errore durante la creazione della policy", "ERROR_CREATING_ANSWERS": "Errore durante la creazione delle risposte", "POLICY_NOT_FOUND": "Policy non trovata", "ERROR_IN_POLICYLIST": "Errore nel recupero dell'elenco delle policy", "ERROR_UPDATING_POLICY": "Errore durante l'aggiornamento della policy", "POLICY_UPDATED": "Policy aggiornata con successo", "POLICY_NAME_EXIST": "Nome della policy esistente.", "DEPARTMENT_CREATED": "Dipar<PERSON>nti creati con successo", "ERROR_CREATING_DEPARTMENT": "Errore durante la creazione del dipartimento", "DEPARTMENT_NAME_EXIST": "Nome del dipartimento già esistente", "PROCESS_CREATED": "Processo creato con successo", "ERROR_CREATING_PROCESS": "Errore durante la creazione del processo", "PROCESS_NAME_EXIST": "Nome del processo già esistente", "ROPA_NOT_ASSIGNED": "ROPA non assegnato", "ROPA_CANT_START": "ROPA è già stato eseguito e non può essere avviato", "ROPA_CANT_ASSIGN": "ROPA è già stato eseguito e non può essere assegnato", "ROPA_FETCHED": "ROPA recuperato con successo", "ROPA_YET_TO_START": "ROPA non ancora iniziato", "ROPA_CHANGES_REQUESTED": "ROPA già in richiesta di modifica", "CATEGORIES_NOT_FOUND": "Categorie non trovate", "CATEGORIES_FETCHED": "Categorie recuperate con successo", "CONTROLS_NOT_FOUND": "Controlli non trovati", "CONTROLS_FETCHED": "Controlli recuperati con successo", "ERROR_CREATING_CONTROL": "Errore durante la creazione del controllo", "CONTROL_CREATED": "<PERSON><PERSON> creato con successo", "ERROR_UPDATING_CONTROL": "Errore durante l'aggiornamento del controllo", "CONTROL_UPDATED": "<PERSON>lo aggiornato con successo", "ERROR_CREATING_ROPA": "Errore durante la creazione di ROPA", "ROPA_NOT_FOUND": "ROPA non trovato", "ROPA_STARTED": "ROPA avviato", "POLICY_COUNT_FETCHED": "Conteggio policy recuperato.", "DATA_NOT_FOUND": "Errore nel trovare il conteggio delle policy.", "ERROR": "Qualcosa è andato storto nel server", "ERROR_CREATING_ANSWER": "Errore durante la creazione della risposta", "ANSWER_CREATED_OR_UPDATED": "Risposta salvata con successo", "ERROR_UPDATING_ANSWER": "Errore durante l'aggiornamento della risposta", "ROPA_ASSIGNED": "ROPA assegnato con successo", "ROPA_UNDER_REVIEW": "ROPA è in revisione", "ROPA_COMPLETED": "ROPA è già completato", "ROPA_SUBMITTED": "ROPA inviato con successo", "DOCUMENT_UPLOADED": "Documenti caricati con successo.", "S3_BUCKET_ERROR": "Errore nel bucket S3", "UPLOAD_FILE": "Carica file", "INVOICES_FETCHED": "Fatture recuperate con successo", "ERROR_UPDATING_GROUPUSER": "Errore nell'aggiornamento dell'accesso al gruppo", "DASHBOARD_FETCHED": "Dashboard recuperata con successo", "ERROR_IN_AUDIT": "Errore nell'aggiornamento del log di audit", "ERROR_IN_DOCUMENT_UPLOAD": "Errore nel caricamento dei documenti", "AUDIT_DATA_NOT_FOUND": "Dati di audit non trovati", "AUDIT_LOG_FETCHED": "Log di audit recuperati.", "INVALID_ROPA_LEVEL": "Livello ROPA non valido", "QUESTIONS_NOT_FOUND": "Domande non trovate", "QUESTIONS_FETCHED": "Domande recuperate con successo", "ERROR_ADDING_COLLABORATOR": "Errore nell'aggiunta del collaboratore", "COLLABORATOR_ADDED": "Collaboratore aggiunto con successo", "ROPA_NOT_STARTED": "ROPA non avviato", "AUDIT_ERROR": "Errore nell'audit", "ARTIFACT_TYPES_NOT_FOUND": "Tipi di artefatto non trovati", "ARTIFACT_TYPES_FETCHED": "Tipi di artefatto recuperati con successo", "INVALID_DATA": "Dati non validi", "INVALID_ARTIFACT_TYPE": "Tipo di artefatto non valido", "CONTROLS_UPLOADED": "Controlli caricati con successo", "INVALID_EXTRA_INPUT_TYPE": "Tipo di input extra non valido", "ALL_NOT_REVIEWED": "<PERSON><PERSON><PERSON> tutte le domande prima di inviare", "ROPA_NOT_SUBMITTED": "ROPA non è ancora stato inviato per la revisione", "ALL_NOT_ANSWERED": "Rispondi a tutte le domande prima di inviare", "LIA_NOT_ASSIGNED": "LIA non assegnato", "LIA_FETCHED": "LIA recuperato con successo", "ERROR_CREATING_LIA": "Errore durante la creazione di LIA", "LIA_NOT_FOUND": "LIA non trovato", "LIA_STARTED": "LIA avviato", "LIA_ASSIGNED": "LIA assegnato con successo", "LIA_UNDER_REVIEW": "LIA è in revisione", "LIA_COMPLETED": "LIA è già completato", "LIA_SUBMITTED": "LIA inviato con successo", "LIA_NOT_STARTED": "LIA non avviato", "INVALID_LIA_LEVEL": "Livello LIA non valido", "LIA_NOT_SUBMITTED": "LIA non è ancora stato inviato per la revisione", "LIA_NOT_COMPLETED": "LIA non è ancora completato", "LIA_REVIEWER_ADDED": "Revisore LIA aggiunto", "TIA_NOT_ASSIGNED": "TIA non assegnato", "TIA_FETCHED": "TIA recuperato con successo", "ERROR_CREATING_TIA": "Errore durante la creazione di TIA", "TIA_NOT_FOUND": "TIA non trovato", "TIA_STARTED": "TIA avviato", "TIA_ASSIGNED": "TIA assegnato con successo", "TIA_UNDER_REVIEW": "TIA è in revisione", "TIA_COMPLETED": "TIA è già completato", "TIA_SUBMITTED": "TIA inviato con successo", "TIA_NOT_STARTED": "TIA non avviato", "INVALID_TIA_LEVEL": "Livello TIA non valido", "TIA_NOT_SUBMITTED": "TIA non è ancora stato inviato per la revisione", "TIA_NOT_COMPLETED": "TIA non è ancora completato", "TIA_REVIEWER_ADDED": "Revisore TIA aggiunto", "PIA_NOT_ASSIGNED": "PIA non assegnato", "PIA_FETCHED": "PIA recuperato con successo", "ERROR_CREATING_PIA": "Errore durante la creazione di PIA", "PIA_NOT_FOUND": "PIA non trovato", "PIA_STARTED": "PIA avviato", "PIA_ASSIGNED": "PIA assegnato con successo", "PIA_UNDER_REVIEW": "PIA è in revisione", "PIA_COMPLETED": "PIA è già completato", "PIA_SUBMITTED": "PIA inviato con successo", "PIA_NOT_STARTED": "PIA non avviato", "INVALID_PIA_LEVEL": "Livello PIA non valido", "PIA_NOT_SUBMITTED": "PIA non è ancora stato inviato per la revisione", "PIA_NOT_COMPLETED": "PIA non è ancora completato", "PIA_REVIEWER_ADDED": "Revisore PIA aggiunto", "PDA_FETCHED": "PDA recuperato con successo", "ERROR_CREATING_PDA": "Errore durante la creazione di PDA", "PDA_NOT_FOUND": "PDA non trovato", "PDA_STARTED": "PDA avviato", "PDA_ASSIGNED": "PDA assegnato con successo", "PDA_UNDER_REVIEW": "PDA è in revisione", "PDA_COMPLETED": "PDA è già completato", "PDA_SUBMITTED": "PDA inviato con successo", "PDA_NOT_COMPLETED": "PDA non è ancora completato", "PDA_NOT_STARTED": "PDA non avviato", "PDA_REVIEWER_ADDED": "Revisore PDA aggiunto", "INVALID_PDA_LEVEL": "Livello PDA non valido", "PDA_NOT_SUBMITTED": "PDA non è ancora stato inviato per la revisione", "COLLABORATORS_NOT_FOUND": "Collaboratori non trovati", "COLLABORATORS_FETCHED": "Collaboratori recuperati con successo", "ERROR_DELETING_COLLABORATOR": "Errore nell'eliminazione del collaboratore", "COLLABORATOR_UPDATED": "Collaboratore aggiornato con successo", "CANT_UPDATE": "Policy in uso: impossibile aggiornare.", "ERROR_DELETING_CONTROL": "Errore nell'eliminazione del controllo", "CONTROL_DELETED": "<PERSON><PERSON> eliminato con successo", "DOCUMENT_DELETED_ERROR": "Errore nell'eliminazione del documento", "DOCUMENT_DELETED": "Documento eliminato", "DEPARTMENT_DOES_NOT_EXIST": "Il dipartimento non esiste", "PROCESS_DOES_NOT_EXIST": "Il processo non esiste", "PROCESS_UPDATED": "Processo aggiornato con successo.", "PROGRESS FETCHED": "Progresso recuperato con successo", "ERROR_CREATING_VENDOR": "Errore nella creazione del fornitore", "VENDOR_ADDED": "<PERSON><PERSON><PERSON> creato con successo", "VENDOR_NOT_FOUND": "Fornitore non esistente", "VENDOR_NAME_EXIST": "Nome del fornitore già esistente.", "ERROR_UPDATING_VENDOR": "Errore nell'aggiornamento dei dettagli del fornitore.", "VENDOR_UPDATED": "Fornitore aggiornato con successo", "ERROR_UPDATING_ANSWERS": "Errore nell'aggiornamento della risposta.", "FAILED_TO_GET_AI_RESPONSE": "Impossibile ottenere la risposta dell'IA", "VENDOR_ROLE_NOT_EXIST": "<PERSON><PERSON><PERSON> del fornitore non trovato", "ROPA_NOT_COMPLETED": "ROPA non ancora completato", "POLICY_DOWNLOADED": "Policy scaricata con successo", "ROPA_DOWNLOADED": "ROPA inviato alla mail con successo", "TIA_DOWNLOADED": "TIA inviato alla mail con successo", "LIA_DOWNLOADED": "LIA inviato alla mail con successo", "PIA_DOWNLOADED": "PIA inviato alla mail con successo", "PDA_DOWNLOADED": "PDA inviato alla mail con successo", "CUSTOMER_NOT_FOUND": "Nome del cliente non trovato", "POLICY_NOT_IN_USE": "Il documento della policy non può essere scaricato perché la policy non è in uso", "POLICY_ID_REQUIRED": "ID della policy richiesto", "TEMPLATES_NOT_FOUND": "Template non trovati", "TEMPLATE_NOT_FOUND": "Template non trovato", "TEMPLATES_FETCHED": "Template recuperati con successo", "TEMPLATE_FETCHED": "Template recuperato con successo", "CUSTOM_POLICY_FETCHED": "Policy personalizzata recuperata con successo", "FAILED_TO_CREATE_CUSTOM_POLICY": "Impossibile creare la policy personalizzata", "CUSTOM_POLICY_CREATED": "Policy personalizzata creata con successo", "CANNOT_CREATE_POLICY": "Impossibile creare la policy in questa fase", "CUSTOM_POLICY_NOT_FOUND": "Policy personalizzata non trovata", "FAILED_TO_SAVE_POLICY": "Impossibile salvare la <PERSON>", "POLICY_SAVED": "Policy salvata con successo", "FAILED_TO_UPLOAD_DOCUMENT": "Impossibile caricare il documento", "ERROR_CREATING_GROUP_USER": "Errore nella creazione dell'utente del gruppo", "ERROR_CREATING_ASSESSMENT": "Errore nella creazione della valutazione", "ASSESSMENT_LIST": "Elenco delle valutazioni", "ASSESSMENT_CREATED": "Valutazione creata con successo.", "ASSESSMENT_MAIL_NOT_TRIGGRED": "Valutazione creata con successo ma l'email non è stata attivata.", "ASSESSMENT_UPDATED": "Valutazione aggiornata con successo.", "TASK_OVERVIEW_FETCHED": "Elenco panoramica attività recuperato con successo", "PROCESS_NOT_FOUND": "Impossibile recuperare l'elenco dei processi", "PROCESS_LIST": "Elenco processi recuperato con successo", "DEPARTMENT_NOT_FOUND": "Impossibile recuperare l'elenco dei dipartimenti", "DEPARTMENT_LIST": "Elenco dipartimenti recuperato con successo", "NOT_REGISTERED_ON_GOTRUST": "L'utente non è registrato su GoTrust", "INACTIVE_CUSTOMER": "Account inattivo. Qualcuno ti contatterà presto.", "VENDOR_LIST_FETCHED": "Elenco fornitori recuperato con successo.", "FETCHING_ERROR": "Elenco non recuperato", "VENDOR_NOT_EXIST": "Il fornitore non esiste", "VENDOR_FETHCED": "Dettagli del fornitore recuperati", "VIA_NOT_ASSIGNED": "VIA non assegnato", "VIA_FETCHED": "VIA recuperato con successo", "ERROR_CREATING_VIA": "Errore durante la creazione di VIA", "VIA_NOT_FOUND": "VIA non trovato", "VIA_STARTED": "VIA avviato", "VIA_ASSIGNED": "VIA assegnato con successo", "VIA_UNDER_REVIEW": "VIA è in revisione", "VIA_COMPLETED": "VIA è già completato", "VIA_SUBMITTED": "VIA inviato con successo", "VIA_NOT_STARTED": "VIA non avviato", "INVALID_VIA_LEVEL": "Livello VIA non valido", "VIA_NOT_SUBMITTED": "VIA non è ancora stato inviato per la revisione", "VIA_NOT_COMPLETED": "VIA non è ancora completato", "VIA_REVIEWER_ADDED": "Revisore VIA aggiunto", "VIA_DOWNLOADED": "VIA inviato alla mail con successo", "VEA_NOT_FOUND": "VEA non trovato", "VEA_STARTED": "VEA avviato", "VEA_ASSIGNED": "VEA assegnato con successo", "VEA_UNDER_REVIEW": "VEA è in revisione", "VEA_COMPLETED": "VEA è già completato", "VEA_SUBMITTED": "VEA inviato con successo", "VEA_NOT_STARTED": "VEA non avviato", "INVALID_VEA_LEVEL": "Livello VEA non valido", "VEA_NOT_SUBMITTED": "VEA non è ancora stato inviato per la revisione", "VEA_NOT_COMPLETED": "VEA non è ancora completato", "VEA_REVIEWER_ADDED": "Revisore VEA aggiunto", "VEA_DOWNLOADED": "VEA inviato alla mail con successo", "RISK_NOT_FETCHED": "Errore nel caricamento del rischio della valutazione interna", "STATUS_NOT_FETCHED": "Errore nel caricamento dello stato della valutazione interna", "VENDOR_TYPE_LIST_FETCHED": "Tipo di fornitore recuperato con successo", "MODEL_NOT_FOUND": "Chiave o nome del modello non valido", "DOCUMENT_NOT_FOUND": "Documento non trovato", "INVALID_KEY": "La chiave non è valida", "ASSESSMENTS_RISK_COUNT": "Conteggio delle valutazioni secondo il rischio", "ASSESSMENTS_NAME_COUNT": "Conteggio delle valutazioni secondo il nome", "ASSESSMENTS_OWNER_COUNT": "Conteggio delle valutazioni secondo il proprietario", "VRM_RISK_COUNT": "Conteggio dei fornitori secondo il rischio", "DASHBOARD_DATA_NOT_FOUND": "Errore nel recupero dei dati della dashboard", "EMAIL_V_FAILED": "Verifica dell'email fallita.", "QUESTIONNAIRES_FETCHED": "Questionari recuperati con successo", "ERROR_CREATING_TOKEN": "Errore durante la creazione del token", "ACCESS_TOKEN_CREATED": "Token di accesso creato con successo", "USER_FETCHED": "Utente recuperato con successo", "ROLE_FETCHED": "<PERSON><PERSON><PERSON> recuperato con successo", "SIDEBAR_FETCHED": "Barra laterale recuperata con successo", "CUSTOMER_FETCHED": "Cliente recuperato con successo", "EMAIL_ALREADY_EXIST": "Email gi<PERSON> esistente", "ASSIGNED": "Assegnazione e rimozione del ruolo completate con successo", "ERROR_RESOURCE_DELETE": "Errore durante l'eliminazione delle risorse", "ERROR_RESOURCE_UPDATE": "Errore durante l'aggiornamento delle risorse", "USER_UPDATED": "Utente aggiornato con successo", "ROLE_EXIST": "<PERSON>o ruolo esiste gi<PERSON>.", "GROUP_FETCHED": "Gruppo recuperato con successo", "ROLE_NOT_FOUND": "Ruolo non trovato", "GROUP_NAME_EXIST": "Un gruppo con questo nome esiste già.", "ERROR_CREATING_GROUP": "Errore durante la creazione del gruppo", "GROUP_CREATED": "Gruppo creato con successo", "RESOURCES_NOT_FOUND": "Risorse non trovate", "FORBIDDEN": "L'accesso alla risorsa richiesta è vietato", "NO_RESOURCES": "Nessuna risorsa allocata a questo ruolo", "LANGUAGE_FETCHED": "Lingua recuperata con successo.", "GROUP_DOES_NOT_EXIST": "Il gruppo non esiste.", "ENTITY_FETCHED": "Entità recuperata con successo", "BUSINESS_UNIT_NOT_FOUND": "Unità aziendale non trovata", "GROUP_UPDATED": "Gruppo aggiornato con successo", "UPLOAD_FAILED": "Caricamento fallito", "TICKET_RAISED": "Ticket creato con successo", "TICKET_NOT_FETCHED": "Errore nel recupero dei dettagli del ticket", "TICKET_OPENED": "Ticket aperto con successo", "TICKETS_FETCHED": "Ticket recuperati con successo", "ERROR_IN_TICKET_DATA": "Errore nel recupero delle statistiche dei ticket", "TICKET_UPDATED": "Ticket aggiornato con successo", "COMMENT_ERROR": "Errore nella creazione del commento sul ticket", "COMMENT_SUCCESS": "Commento sul ticket aggiunto con successo", "CUSTOMER_UPDATED": "Cliente aggiornato con successo", "ROLE_UPDATE_NOT_ALLOWED": "Aggiornamento del ruolo non consentito", "SOMEONE_CONTACT_SOON": "Qualcuno ti contatterà presto", "DPO_NOT_FOUND": "DPO non trovato", "USER_VALIDATED": "Utente validato con successo", "ERROR_CREATING_DATA_SUBJECT": "Errore nel salvataggio delle informazioni dell'interessato", "ERROR_CREATING_REQUEST": "Errore nella creazione della richiesta DSR", "REQUEST_CREATED": "Richiesta DSR creata", "WORKFLOW_NAME_EXIST": "Workflow duplicato. Scegli un nome diverso", "ERROR_CREATING_WORKFLOW": "Errore durante la creazione del tipo di workflow", "WORKFLOW_CREATED": "Workflow creato con successo", "WORKFLOW_DOES_NOT_EXIST": "Il workflow non esiste", "WORKFLOW_NOT_FOUND": "Impossibile recuperare l'elenco dei workflow", "WORKFLOW_FETCHED": "Workflow recuperato con successo", "WORKFLOW_UPDATED": "Workflow aggiornato con successo", "WORKFLOW_STEP_NAME_EXIST": "Il nome del passaggio esiste già", "ERROR_CREATING_WORKFLOW_STEP": "Errore durante la creazione del passaggio del workflow", "WORKFLOW_STEP_CREATED": "Passaggio del workflow creato con successo", "WORKFLOW_STEP_DOES_NOT_EXIST": "Il passaggio del workflow non esiste", "WORKFLOW_STEP_UPDATED": "Passaggio del workflow aggiornato con successo", "ERROR_DELETING_WORKFLOW_STEP": "Errore nell'eliminazione del passaggio del workflow", "WORKFLOW_STEP_DELETED_SUCC": "Passaggio del workflow eliminato con successo", "WORKFLOW_STEP_ALREADY_TOP": "Il passaggio del workflow è già in cima", "TASK_NAME_EXIST": "Il nome dell'attività esiste già", "ERROR_CREATING_TASK": "Errore durante la creazione dell'attività", "TASK_CREATED": "Attività creata con successo", "TASK_DOES_NOT_EXIST": "L'attività non esiste", "TASK_NOT_FOUND": "Impossibile recuperare l'elenco delle attività", "TASK_FETCHED": "Attività recuperata con successo", "TASK_UPDATED": "Attività aggiornata con successo", "ERROR_DELETING_TASK": "Errore nell'eliminazione dell'attività", "TASK_DELETED_SUCC": "Attività eliminata con successo", "RISK_MATRIX": "<PERSON><PERSON> di rischio recuperata con successo", "TEMPLATE_NOT_ADDED": "Template non aggiunto. Riprova", "LIST_FETCHED": "Elenco template recuperato", "MITIGATION_COMPLETED": "Mitigazione completata", "DATA_SUBJECT_DOES_NOT_EXIST": "L'interessato non esiste", "REQUEST_NOT_FOUND": "Impossibile recuperare l'elenco delle richieste", "REQUEST_FETCHED": "Richiesta recuperata con successo", "REQUEST_DOES_NOT_EXIST": "La richiesta non esiste", "REQUEST_UPDATED": "Richiesta aggiornata con successo", "REQUEST_TYPE_COUNT": "Conteggio delle richieste per tipo", "REQUEST_STATUS_COUNT": "Conteggio delle richieste per stato", "FORM_NOT_FOUND": "Mo<PERSON>lo non esistente", "FORM_CREATE_ERROR": "<PERSON><PERSON>lo non creato", "FORM_UPDATE_ERROR": "Modulo non aggiornato", "FORM_CREATED": "<PERSON><PERSON><PERSON> creato con successo", "FORM_UPDATED": "<PERSON><PERSON><PERSON> a<PERSON> con <PERSON>o", "DATA_FETCHED": "<PERSON>ti recuperati con successo", "SERVICE_ENTITY_FETCHED": "Entità del servizio recuperata con successo", "MAIL_LIST_ERROR": "Errore nel recupero dell'elenco dei template email", "MAIL_LIST_FETCHED": "Elenco template email recuperato con successo", "MAIL_CREATE_ERROR": "Errore nella creazione del template email", "MAIL_UPDATED": "Template email aggiornato", "MAIL_DOES_NOT_EXIST": "Il template email non esiste", "MAIL_DELETE_ERROR": "Errore nell'eliminazione del template email", "MAIL_DELETE": "Template email eliminato", "MAIL_UPDATE_ERROR": "Errore nell'aggiornamento del template email", "MAIL_CREATED": "Template email creato", "EMAIL_SUCCESS": "Email inviata con successo", "EMAIL_REQUIRED": "L'ID email è richiesto", "MITIGATION_NOT_COMPLETED": "Mitigazione non ancora completata per il fornitore", "MITIGATION_SAVED": "Mitigazione salvata", "EMAIL_NOT_FOUND": "Email non trovata", "MITIGATION_PLAN_NOT_FILLED": "Compila il piano di mitigazione per tutti i controlli", "LIST_ERROR": "Errore nel recupero dell'elenco", "REGULATION_LIST_FETCHED": "Elenco recuperato con successo", "DUPLICATE_WEBHOOK": "Il nome dell'azione esiste già", "WEBHOOK_CREATED": "Webhook creato con successo", "ERROR_CREATING_WEBHOOK": "Webhook creato con successo", "WEBHOOK_DELETED_ERROR": "Errore nell'eliminazione del webhook", "WEBHOOK_DELETED": "Webhook eliminato", "WEBHOOK_NOT_EXIST": "Il webhook non esiste", "AUDIT_DOWNLOADED": "Audit scaricato con successo", "DATASUBJECT_NOT_MATCHED": "Le informazioni dell'interessato non corrispondono", "DSRINFO_DOES_NOT_EXIST": "Le tue informazioni non esistono nei nostri record", "OCR_NOT_MATTACHED": "Le informazioni della tua identità non corrispondono nei documenti forniti", "ALREADY_EXIST_TEMPLATE": "Template g<PERSON><PERSON> esistente, impossibile aggiornare", "WORKFLOW_ID_REQUIRED": "L'ID del workflow è richiesto", "UNSTRUCTURED_DATA_MANAGEMENT_FETCHED": "Gestione dati non strutturati recuperata con successo", "DOCUMENT_LIST": "Elenco documenti recuperato con successo", "FILE_FORMAT": "Formato file recuperato con successo", "SUMMARY_FETCHED": "Riepilogo recuperato con successo", "LOCATION_FETCHED": "Posizione recuperata con successo", "VIEW_DETAILS_FETCHED": "Dettagli vista recuperati con successo", "ELEMENT_CATEGORIES_FETCHED": "Categorie degli elementi recuperate con successo", "DATA_MANAGEMENT_FETCHED": "Gestione dati recuperata con successo", "ELEMENT_TYPES_FETCHED": "Tipi di elementi recuperati con successo", "STRUCTURED_VIEW_LOCATION": "Posizione vista strutturata recuperata con successo", "PROFILER_META_FETCHED": "Meta profiler recuperato con successo", "ERROR_ADDING_ACTION": "Azione non aggiunta", "ACTION_ADDED": "Azione aggiunta con successo", "ACTION_LIST_FETCHED": "Elenco azioni recuperato con successo", "ACTION_NOT_FOUND": "Azione non trovata", "ACTION_FETCHED": "Azione recuperata con successo", "ERROR_CREATING_DUTY": "Errore nella creazione del dovere", "DUTY_CREATED": "<PERSON><PERSON> creato con successo", "DUTY_NOT_FOUND": "<PERSON>e non trovato", "DUTY_UPDATED": "Dovere aggiornato con successo", "STRUCTURED_SENSITIVITY_FETCHED": "Dati di sensibilità strutturati recuperati", "UNSTRUCTURED_SENSITIVITY_FETCHED": "Dati di sensibilità non strutturati recuperati", "RESOURCE_LIST_NOT_FOUND": "Elenco risorse non trovato", "RESOURCES_ADDED": "Risorsa aggiunta con successo", "ERROR_CREATING_IMPROVEMENT": "Errore nella creazione del miglioramento", "IMPROVEMENT_CREATED": "Miglioramento creato con successo", "IMPROVEMENT_UPDATED": "Miglioramento aggiornato con successo", "IMPROVEMENT_NOT_FOUND": "Miglioramento non trovato", "CATEGORY_NOT_FOUND": "Categoria non trovata", "CATEGORY_CREATE_ERROR": "Errore nella creazione della categoria", "CATEGORY_FETCHED": "Categoria recuperata con successo", "ERROR_DELETING_FORM": "Errore nell'eliminazione del modulo", "FORM_DELETED": "<PERSON><PERSON><PERSON> eliminato con <PERSON>o", "ERROR_DELETING_CATEGORY": "Errore nell'eliminazione delle categorie", "TASK_STATUS": "Lo stato dell'attività è richiesto", "FORM_FETCHED": "<PERSON><PERSON><PERSON> recuperato con successo", "FORM_PUBLISHED": "<PERSON><PERSON><PERSON> pubblicato con successo", "FORM_NOT_PUBLISHED": "Errore nella pubblicazione del modulo", "FORM_ALREADY_PUBLISHED": "Il modulo esiste già", "DATA_UPDATED": "<PERSON>ti aggiornati con successo", "DATA_DELETED": "Dati eliminati con successo", "DATA_CREATED": "<PERSON><PERSON> creati con successo", "ERROR_CREATING_INCIDENT": "Errore nella creazione dell'incidente", "INCIDENT_CREATED": "<PERSON><PERSON> creato", "INCIDENT_NOT_FOUND": "Impossibile recuperare l'elenco degli incidenti", "ASSESSMENT_NOT_FOUND": "La valutazione non esiste", "ASSESSMENT_NOT_ASSIGNED": "La valutazione non è assegnata", "ASSESSMENT_STARTED": "Valutazione iniziata", "ASSESSMENT_UNDER_REVIEW": "La valutazione è in revisione", "ASSESSMENT_COMPLETED": "Valutazione completata", "ASSESSMENT_DOWNLOADED": "Valutazione inviata per email con successo", "ASSESSMENT_ASSIGNED": "Valutazione assegnata con successo", "ASSESSMENT_REVIEWER_ADDED": "Revisore della valutazione aggiunto", "ALREADY_VERIFIED": "Email già verificata", "FORM_VERIFIED": "<PERSON><PERSON>lo verificato con successo", "DUPLICATE_QUESTION_TITLE": "Alcune domande appaiono più di una volta", "ERROR_CREATING_DASHBOARD": "Errore nella creazione della dashboard", "DASHBOARD_CREATED": "Dashboard creata con successo", "DASHBOARD_NOT_FOUND": "Dashboard non trovata", "DASHBOARD_UPDATED": "Dashboard aggiornata con successo", "DASHBOARD_DELETED": "Dashboard eliminata con successo", "DELETE_ERROR": "Errore nell'eliminazione dell'elemento", "DASHBOARD_ALREADY_EXIST": "La dashboard esiste già", "CANNOT_DELETE_DEPARTMENT": "Un dipartimento non può essere eliminato se ha un ROPA, processo o sotto-dipartimento", "CANNOT_DELETE_PROCESS": "Un processo non può essere eliminato se ha un ROPA o sotto-processo", "DEPARTMENT_DELETED": "Dipartimento eliminato con successo", "PROCESS_DELETED": "Processo eliminato con successo", "GROUP_DELETED": "Gruppo eliminato con successo", "CANNOT_DELETE_GROUP": "Un gruppo non può essere eliminato se ha un dipartimento o sotto-gruppo", "ERROR_DELETING_GROUP_USER": "Errore nell'eliminazione dell'utente del gruppo", "ERROR_DELETING_CUSTOMER_REGULATIONS": "Errore nell'eliminazione delle normative del cliente", "ERROR_DELETING_CUSTOMER_BUSINESS_REQUIREMENTS": "Errore nell'eliminazione dei requisiti aziendali del cliente", "REQUEST_ARCHIVED": "Richiesta archiviata con successo", "REQUEST_UNARCHIVED": "Richiesta non archiviata con successo", "DSR_REQUEST_ARCHIVING_FAILED": "Archiviazione della richiesta DSR fallita", "REQUEST_ALREADY_ARCHIVED": "Richiesta già archiviata", "MISSING_REQUIRED_FIELDS": "Campi obbligatori mancanti", "TASK_CREATION_FAILED": "Creazione dell'attività fallita", "TASK_UPDATED_SUCCESSFULLY": "Attività aggiornata con successo", "TASK_UPDATE_FAILED": "Aggiornamento dell'attività fallito", "PRIVACY_NOTICE_GENERATION_FAILED": "Generazione dell'informativa sulla privacy fallita", "PRIVACY_NOTICE_GENERATED": "Informativa sulla privacy generata con successo", "GENERATE_NOTICE_STARTED": "Generazione dell'informativa sulla privacy iniziata", "PRIVACY_NOTICE_NOT_FOUND": "Informativa sulla privacy non trovata", "FAILED_TO_UPDATE_URL": "Impossibile aggiornare l'URL", "USER_ALREADY_LOGGED_IN": "L'utente è già connesso", "PASSWORD_NOT_UPDATED": "Password non aggiornata", "CREDENTIALS_SENT": "Credenziali inviate con successo", "FORM_VERSION_CREATED": "Versione del modulo creata con successo", "VENDOR_LIST_NOT_FOUND": "Elenco fornitori non trovato", "ACTION_ALREADY_EXIST": "L'azione esiste già", "ADD_DATA_BREACH_NOTIFICATION": "Aggiungi notifica di violazione dei dati", "ALERT_SENT": "Avviso inviato", "AMBITION_CREATED": "Ambizione creata con successo", "ANSWER_CREATION_FAILED": "Creazione risposta fallita", "ANSWER_NOT_SAVED": "Risposta non salvata", "ASSESSMENT_ALREADY_EXIST": "La valutazione esiste già", "ASSESSMENT_NOT_ADDED": "Valutazione non aggiunta con successo", "ASSESSMENT_NOT_COMPLETED": "Valutazione non completata con successo", "ASSESSMENT_NOT_STARTED": "Valutazione non iniziata con successo", "ASSESSMENT_NOT_SUBMITTED": "Valutazione non inviata con successo", "ASSESSMENT_REVIEWED": "Valutazione rivista con successo", "ASSESSMENT_SUBMITTED": "Valutazione inviata con successo", "AUDIT_LOG_NOT_FOUND": "Log di audit non trovato", "BILLING_ADDRESS": "Indirizzo di fatturazione", "BILLING_ADDRESS_CREATE_ERROR": "Errore creazione indirizzo di fatturazione", "BLOGS_FETCHED": "Blog recuperati con successo", "BLOG_CATEGORIES_FETCHED": "Categorie blog recuperate con successo", "BLOG_FETCHED": "Blog recuperato con successo", "BUSINESS_REQUIREMENTS": "Requisiti aziendali", "BUSINESS_REQUIREMENTS_ADDED": "Requisiti aziendali aggiunti con successo", "BUSINESS_REQUIREMENTS_UPDATED": "Requisiti aziendali aggiornati con successo", "CATEGORY_ADDED": "Categoria aggiunta con successo", "CATEGORY_CREATION_FAILED": "Creazione categoria fallita", "CLIENT_DOES_NOT_EXIST": "Il cliente non esiste", "CLIENT_REGISTRATION_SUCCESSFUL": "Registrazione cliente completata con successo", "CONTROL_CREATION_FAILED": "Creazione controllo fallita", "CONTROL_FOUND": "<PERSON><PERSON> trovato", "CONTROL_NOT_FOUND": "Controllo non trovato", "CONTROL_NOT_UPDATED": "Controllo non aggiornato con successo", "CREATE_ADD_ACTION": "Crea aggiungi azione", "CUSTOMER_ASSESSMENTS_NOT_FOUND": "Valutazioni cliente non trovate", "CUSTOMER_CONTROL_CREATION_FAILED": "Creazione controllo cliente fallita", "CUSTOM_REGULATION_ADDED": "Regolamento personalizzato aggiunto con successo", "DASHBOARD_BREACH_COUNT": "Conteggio violazioni dashboard recuperato con successo", "DASHBOARD_BREACH_STATUS_DISTRIBUTION": "Distribuzione stato violazioni dashboard", "DASHBOARD_SEVERITY_DISTRIBUTION": "Distribuzione gravità dashboard", "DATA_FECTHED": "<PERSON><PERSON> recuperati", "DATA_SUBJECT_NOT_FOUND": "Soggetto dati non trovato", "DEPARTMENT_UPDATED": "Dipartimento aggiornato con successo", "DOCUMENTS_FETCHED": "Documenti recuperati con successo", "DOMAIN_NAME_ALREADY_EXIST": "Il nome dominio esiste già", "DSR_REQUEST_NOT_FOUND": "Richiesta DSR non trovata", "EMAIL_EXISTS": "L'email esiste già", "EMAIL_NOT_EXIST": "L'email non esiste", "EMBEDDINGS_CREATED": "Incorporamenti creati con successo", "ENTITY_NOT_FOUND": "Entità non trovata", "ERROR_CREATE_AMBITION": "Errore: crea ambizione", "ERROR_CREATE_QUESTIONNAIRES": "Errore: crea questionari", "ERROR_CREATE_ROLE": "Errore: crea ruolo", "ERROR_CREATE_SERVICE": "Errore: crea servizio", "ERROR_CREATING_AUDIT_LOG": "Errore: creazione log di audit", "ERROR_CREATING_BREACH_NOTIFICATION": "Errore: creazione notifica di violazione", "ERROR_CREATING_RISK": "Errore: creazione rischio", "ERROR_UPDATING_MASTER_CONTROL": "Errore: aggiornamento controllo principale", "ERROR_WHILE_DELETE_TOKEN": "Errore: durante eliminazione token", "FILE_NOT_IN_CORRECT_FORMAT": "File non nel formato corretto", "FILE_REQUIRED": "File richiesto", "FORM_DOES_NOT_EXIST": "Il modulo non esiste", "GENERATE_NOTICE_START": "Inizio generazione avviso", "GET_ACTION_LISTING": "Ottieni elenco azioni", "GROUP_NOT_FOUND": "Gruppo non trovato", "INCIDENT_DOES_NOT_EXIST": "L'incidente non esiste", "INCIDENT_UPDATED": "Incidente aggiornato con successo", "INTERNAL_ASSESSMENT_ERROR": "Errore valutazione interna", "INVALID_HEADER": "Intestazione non valida", "IP_LOOKUP_SUCCESS": "Ricerca IP completata con successo", "LIA_REVIEWED": "LIA rivista con successo", "LIST_NOT_FOUND": "Elenco non trovato", "MARKED_READ": "<PERSON><PERSON><PERSON><PERSON><PERSON> come letto", "NEW_FORM_VERSION_PUBLISHED": "Nuova versione modulo pubblicata", "NOT_PUBLISHED": "Non pubblicato", "NO_DATA_FOUND": "<PERSON><PERSON><PERSON> dato trovato", "NO_GROUPS_FOUND": "Nessun gruppo trovato", "OK": "Ok", "ONBOARDING_COMPLETED": "Onboarding completato con successo", "ORGANISATION_CREATED": "Organizzazione creata con successo", "ORGANISATION_NOT_FOUND": "Organizzazione non trovata", "ORGANISATION_UPDATE_ERROR": "Errore aggiornamento organizzazione", "OTP_NOT_GENERATE": "OTP non generato", "OTP_NOT_VERIFIED": "OTP non verificato", "PACKAGES_FETCHED": "<PERSON><PERSON><PERSON> recuperati con successo", "PACKAGE_DOES_NOT_EXIST": "Il pacchetto non esiste", "PARSING_ERROR": "Errore di analisi", "PASSWORD_NOT_MATCH": "Password non corrispondente", "PASSWORD_UPDATE_ERROR": "Errore aggiornamento password", "PDA_NOT_ASSIGNED": "PDA non assegnata con successo", "PDA_REVIEWED": "PDA rivista con successo", "PENDING_REQUEST_COUNT": "Conteggio richieste in sospeso recuperato con successo", "PIA_REVIEWED": "PIA rivista con successo", "POLICIES_FETCHED": "Politiche recuperate con successo", "POLICIES_NOT_FOUND": "Politiche non trovate", "PRIVACY_NOTICES_FETCHED": "Informative privacy recuperate con successo", "PRIVACY_NOTICES_NOT_FOUND": "Informative privacy non trovate", "PRIVACY_NOTICE_CREATED": "Informativa privacy creata con successo", "PRIVACY_NOTICE_NOT_CREATED": "Informativa privacy non creata con successo", "PRIVACY_NOTICE_NOT_UPDATE": "Informativa privacy non aggiornata", "PRIVACY_NOTICE_UPDATE": "Aggiornamento informativa privacy", "PROCESSES_CREATED_SUCCESSFULLY": "Processi creati con successo", "PROCESS_CREATION_FAILED": "Creazione processo fallita", "REGULATION_NOT_FOUND": "Regolamento non trovato", "REQUIREMENT_UPDATED": "<PERSON><PERSON><PERSON> a<PERSON> con <PERSON>o", "REVIEW_CREATED_OR_UPDATED": "Revisione creata o aggiornata con successo", "RISK_CREATED": "<PERSON><PERSON><PERSON> creato con successo", "RISK_NOT_FOUND": "Rischio non trovato", "RISK_UPDATED": "Rischio aggiornato con successo", "ROPA_ALREADY_STARTED": "ROPA già iniziata con successo", "ROPA_CREATION_FAILED": "Creazione ROPA fallita", "ROPA_REVIEWED": "ROPA rivista con successo", "ROPA_UPDATE_FAILED": "Aggiornamento ROPA fallito", "SERVER_ERROR": "Errore del server", "SERVICE_FETCHED": "<PERSON><PERSON><PERSON> recuperato con successo", "SOME_AMBITIONS_DO_NOT_EXIST": "Alcune ambizioni non esistono", "SOME_QUESTIONNAIRES_DO_NOT_EXIST": "Alcuni questionari non esistono", "SOME_SERVICES_DO_NOT_EXIST": "Alcuni servizi non esistono", "STEP_NOT_FOUND": "Passaggio non trovato", "STREAM_ERROR": "Errore di flusso", "SUBPROCESS_CREATION_FAILED": "Creazione sottoprocesso fallita", "TASK_FOUND": "Attività trovata", "TEMPLATE_ADDED": "Template aggiunto con successo", "TEMPLATE_CREATION_FAILED": "Creazione template fallita", "TEMPLATE_PUBLISHED": "Template pubblicato", "TIA_REVIEWED": "TIA rivista con successo", "TRANSACTION_FAILED": "Transazione fallita", "TYPES_FETCHED": "Tipi recuperati con successo", "TYPES_NOT_FOUND": "Tipi non trovati", "UPDATE_TIA_FAILED": "Aggiornamento TIA fallito", "UPDATE_VEA_FAILED": "Aggiornamento VEA fallito", "UPDATE_VIA_FAILED": "Aggiornamento VIA fallito", "USERS_NOT_FOUND": "Utenti non trovati", "VEA_NOT_ASSIGNED": "VEA non assegnata con successo", "VEA_REVIEWED": "VEA rivista con successo", "VENDOR_ASSESSMENTS_NOT_FOUND": "Valutazioni fornitori non trovate", "VENDOR_DETAILS_NOT_FOUND": "Dettagli fornitore non trovati", "REGULATION_ADDED": "Regolamento aggiunto con successo", "REGULATION_UPDATED": "Regolamento aggiornato con successo", "RESOURCES_FETCHED": "Risorse recuperate con successo", "ROLE_UPDATED": "<PERSON><PERSON><PERSON> a<PERSON> con successo", "ROPA_UPDATED": "ROPA aggiornata con successo", "SAVE_ERROR": "Errore nel salvataggio dei dati", "VIA_REVIEWED": "VIA rivista con successo", "DPO_ROLE_EXIST": "Non è possibile creare 'Responsabile della Protezione dei Dati', scegliere un altro nome di ruolo", "PROGRESS_FETCHED": "Progresso recuperato con successo", "ROPA_CREATED": "ROPA creata con successo"}