{"SIGNUP_SUCCESSFUL": "Usuario registrado exitosamente", "LOGIN_SUCCESSFUL": "Usuario logueado exitosamente", "USER_REGISTERED_LOGIN": "Usuario registrado exitosamente. Inicie sesión para verificar su cuenta.", "API_SUCCESS": "Éxito", "LOGOUT_SUCCESSFUL": "Usuario deslogueado exitosamente", "PASSWORD_UPDATED": "¡Contraseña actualizada!", "FORGOT_PASSWORD": "Enlace de restablecimiento enviado a su correo electrónico", "NOT_MATCHED": "Aún no coincide", "DELETED": "Eliminado exitosamente.", "OTP_SENT": "OTP enviado exitosamente", "EMAIL_VERIFIED": "Correo electrónico verificado exitosamente", "P_UPDATE": "Perfil actualizado exitosamente", "UPDATE_EMAIL": "Correo electrónico actualizado exitosamente", "TOKEN_EXPIRED": "Token expirado", "ALREADY_REGISTERED": "Ya se ha creado una cuenta.", "REG_ALREADY_REGISTERED": "Ya se ha creado una cuenta con este número de registro.", "UPDATE_ERROR": "<PERSON><PERSON>r al actualizar datos.", "API_ERROR": "Error en la ejecución de la API.", "VALIDATION_ERROR": "Error de validación.", "FAILED_TO_ADD": "Error al agregar datos.", "INVALID_CREDENTIALS": "Credenciales de inicio de sesión inválidas", "EMAIL_FAILURE": "Correo electrónico no enviado.", "EMAIL_ALREADY_EXISTS": "El correo electrónico ya existe.", "EMAIL_ALREADY_EXIST_IN_KEYCLOAK": "El correo electrónico ya existe en KeyCloak", "USER_NOT_FOUND": "Usuario no encontrado", "UNAUTHORIZED": "Acceso no autorizado.", "FAILED_TO_UPDATE": "<PERSON><PERSON><PERSON> al actualizar.", "FAILED_TO_DELETE": "Error al eliminar datos.", "INTERNAL_SERVER_ERROR": "Error interno del servidor", "INVALID_EMAIL": "Dirección de correo electrónico inválida", "INVALID_OTP": "OTP inválido", "SIGNUP_FAILED": "Su registro ha fallado", "EMAIL_NOT_VERIFIED": "El correo electrónico no está verificado", "INVALID_TOKEN": "Su token es inválido.", "EMAIL_v_FAILED": "La verificación del correo electrónico ha fallado", "MISSING_TOKEN": "Token faltante", "MISSING_P": "Parámetro faltante", "OTP_NOT_SEND": "OTP no enviado exitosamente", "OTP_EXPIRED": "OTP ha expirado", "NOT_FOUND": "Datos no encontrados", "TOTAL_LOGIN": "Ha alcanzado el número máximo de intentos de inicio de sesión. Cierre sesión en otros dispositivos e inicie sesión nuevamente.", "WRONG_PASS": "Contrase<PERSON>", "INVALID_ROUTE": "Ruta inválida", "MISSING_API_KEY": "Clave API faltante", "INVALID_API_KEY": "La clave API es inválida", "LAWS_FETCHED": "Leyes obtenidas exitosamente", "REGIONS_FETCHED": "Regiones obtenidas exitosamente", "STATE_FETCHED": "Estado obtenido exitosamente", "COUNTRY_FETCHED": "<PERSON><PERSON> obtenido exitosamente", "POLICY_ALREADY_EXISTS": "Ya existe una política con este nombre.", "POLICY_ADDED": "Política creada exitosamente", "POLICY_FETCHED": "Políticas obtenidas exitosamente", "DEPARTMENT_FETCHED": "Departamentos obtenidos exitosamente", "INDUSTRY_FETCHED": "Lista de verticales de industria obtenida exitosamente", "ERROR_CREATING_POLICY": "Error al crear política", "ERROR_CREATING_ANSWERS": "Error al crear respuestas", "POLICY_NOT_FOUND": "Política no encontrada", "ERROR_IN_POLICYLIST": "Error al obtener lista de políticas", "ERROR_UPDATING_POLICY": "Error al actualizar política", "POLICY_UPDATED": "Política actualizada exitosamente", "POLICY_NAME_EXIST": "El nombre de la política ya existe.", "DEPARTMENT_CREATED": "Departamentos creados exitosamente", "ERROR_CREATING_DEPARTMENT": "Error al crear departamento", "DEPARTMENT_NAME_EXIST": "El nombre del departamento ya existe", "PROCESS_CREATED": "Proceso creado exitosamente", "ERROR_CREATING_PROCESS": "Error al crear proceso", "PROCESS_NAME_EXIST": "El nombre del proceso ya existe", "ROPA_NOT_ASSIGNED": "ROPA no asignado", "ROPA_CANT_START": "ROPA ya se ha ejecutado y no se puede iniciar", "ROPA_CANT_ASSIGN": "ROPA ya se ha ejecutado y no se puede asignar", "ROPA_FETCHED": "ROPA obtenido exitosamente", "ROPA_YET_TO_START": "ROPA aún no iniciado", "ROPA_CHANGES_REQUESTED": "ROPA ya en solicitud de cambios", "CATEGORIES_NOT_FOUND": "Categorías no encontradas", "CATEGORIES_FETCHED": "Categorías obtenidas exitosamente", "CONTROLS_NOT_FOUND": "Controles no encontrados", "CONTROLS_FETCHED": "Controles obtenidos exitosamente", "ERROR_CREATING_CONTROL": "Error al crear control", "CONTROL_CREATED": "Control creado exitosamente", "ERROR_UPDATING_CONTROL": "Error al actualizar control", "CONTROL_UPDATED": "Control actualizado exitosamente", "ERROR_CREATING_ROPA": "Error al crear ROPA", "ROPA_NOT_FOUND": "ROPA no encontrado", "ROPA_STARTED": "ROPA iniciado", "POLICY_COUNT_FETCHED": "Recuento de políticas obtenido.", "DATA_NOT_FOUND": "Error al encontrar las políticas.", "ERROR": "Algo salió mal en el servidor", "ERROR_CREATING_ANSWER": "Error al crear respuesta", "ANSWER_CREATED_OR_UPDATED": "Respuesta guardada exitosamente", "ERROR_UPDATING_ANSWER": "<PERSON><PERSON>r al actualizar respuesta", "ROPA_ASSIGNED": "ROPA asignado exitosamente", "ROPA_UNDER_REVIEW": "ROPA está siendo revisado", "ROPA_COMPLETED": "ROPA ya está completado", "ROPA_SUBMITTED": "ROPA enviado exitosamente", "DOCUMENT_UPLOADED": "Documentos subidos exitosamente.", "S3_BUCKET_ERROR": "Error en el bucket S3", "UPLOAD_FILE": "Subir archivo", "INVOICES_FETCHED": "Facturas obtenidas exitosamente", "ERROR_UPDATING_GROUPUSER": "Error al actualizar acceso de grupo", "DASHBOARD_FETCHED": "Panel de control obtenido exitosamente", "ERROR_IN_AUDIT": "Error al actualizar registro de auditoría", "ERROR_IN_DOCUMENT_UPLOAD": "Error al subir documentos", "AUDIT_DATA_NOT_FOUND": "Datos de auditoría no encontrados", "AUDIT_LOG_FETCHED": "Registros de auditoría obtenidos.", "INVALID_ROPA_LEVEL": "Nivel ROPA inválido", "QUESTIONS_NOT_FOUND": "Preguntas no encontradas", "QUESTIONS_FETCHED": "Preguntas obtenidas exitosamente", "ERROR_ADDING_COLLABORATOR": "Error al agregar colaborador", "COLLABORATOR_ADDED": "Colaborador agregado exitosamente", "ROPA_NOT_STARTED": "ROPA no iniciado", "AUDIT_ERROR": "Error en procesos de auditoría", "ARTIFACT_TYPES_NOT_FOUND": "Tipos de artefactos no encontrados", "ARTIFACT_TYPES_FETCHED": "Tipos de artefactos obtenidos exitosamente", "INVALID_DATA": "<PERSON><PERSON>", "INVALID_ARTIFACT_TYPE": "Tipo de artefacto inválido", "CONTROLS_UPLOADED": "Controles subidos exitosamente", "INVALID_EXTRA_INPUT_TYPE": "Tipo de entrada extra inválido", "ALL_NOT_REVIEWED": "Revise todas las preguntas antes de enviar", "ROPA_NOT_SUBMITTED": "ROPA aún no enviado para revisión", "ALL_NOT_ANSWERED": "Responda todas las preguntas antes de enviar", "LIA_NOT_ASSIGNED": "LIA no asignado", "LIA_FETCHED": "LIA obtenido exitosamente", "ERROR_CREATING_LIA": "Error al crear LIA", "LIA_NOT_FOUND": "LIA no encontrado", "LIA_STARTED": "LIA iniciado", "LIA_ASSIGNED": "LIA asignado exitosamente", "LIA_UNDER_REVIEW": "LIA está siendo revisado", "LIA_COMPLETED": "LIA ya está completado", "LIA_SUBMITTED": "LIA enviado exitosamente", "LIA_NOT_STARTED": "LIA no iniciado", "INVALID_LIA_LEVEL": "Nivel LIA inválido", "LIA_NOT_SUBMITTED": "LIA aún no enviado para revisión", "LIA_NOT_COMPLETED": "LIA aún no completado", "LIA_REVIEWER_ADDED": "Revisor LIA agregado", "TIA_NOT_ASSIGNED": "TIA no asignado", "TIA_FETCHED": "TIA obtenido exitosamente", "ERROR_CREATING_TIA": "Error al crear TIA", "TIA_NOT_FOUND": "TIA no encontrado", "TIA_STARTED": "TIA iniciado", "TIA_ASSIGNED": "TIA asignado exitosamente", "TIA_UNDER_REVIEW": "TIA está siendo revisado", "TIA_COMPLETED": "TIA ya está completado", "TIA_SUBMITTED": "TIA enviado exitosamente", "TIA_NOT_STARTED": "TIA no iniciado", "INVALID_TIA_LEVEL": "Nivel TIA inválido", "TIA_NOT_SUBMITTED": "TIA aún no enviado para revisión", "TIA_NOT_COMPLETED": "TIA aún no completado", "TIA_REVIEWER_ADDED": "Revisor TIA agregado", "PIA_NOT_ASSIGNED": "PIA no asignado", "PIA_FETCHED": "PIA obtenido exitosamente", "ERROR_CREATING_PIA": "Error al crear PIA", "PIA_NOT_FOUND": "PIA no encontrado", "PIA_STARTED": "PIA iniciado", "PIA_ASSIGNED": "PIA asignado exitosamente", "PIA_UNDER_REVIEW": "PIA está siendo revisado", "PIA_COMPLETED": "PIA ya está completado", "PIA_SUBMITTED": "PIA enviado exitosamente", "PIA_NOT_STARTED": "PIA no iniciado", "INVALID_PIA_LEVEL": "Nivel PIA inválido", "PIA_NOT_SUBMITTED": "PIA aún no enviado para revisión", "PIA_NOT_COMPLETED": "PIA aún no completado", "PIA_REVIEWER_ADDED": "Revisor PIA agregado", "PDA_FETCHED": "PDA obtenido exitosamente", "ERROR_CREATING_PDA": "Error al crear PDA", "PDA_NOT_FOUND": "PDA no encontrado", "PDA_STARTED": "PDA iniciado", "PDA_ASSIGNED": "PDA asignado exitosamente", "PDA_UNDER_REVIEW": "PDA está siendo revisado", "PDA_COMPLETED": "PDA ya está completado", "PDA_SUBMITTED": "PDA enviado exitosamente", "PDA_NOT_COMPLETED": "PDA aún no completado", "PDA_NOT_STARTED": "PDA no iniciado", "PDA_REVIEWER_ADDED": "Revisor PDA agregado", "INVALID_PDA_LEVEL": "Nivel PDA inválido", "PDA_NOT_SUBMITTED": "PDA aún no enviado para revisión", "COLLABORATORS_NOT_FOUND": "Colaboradores no encontrados", "COLLABORATORS_FETCHED": "Colaboradores obtenidos exitosamente", "ERROR_DELETING_COLLABORATOR": "Error al eliminar colaborador", "COLLABORATOR_UPDATED": "Colaborador actualizado exitosamente", "CANT_UPDATE": "Política en uso: No se puede actualizar.", "ERROR_DELETING_CONTROL": "Error al eliminar control", "CONTROL_DELETED": "Control eliminado exitosamente", "DOCUMENT_DELETED_ERROR": "Error al eliminar el documento", "DOCUMENT_DELETED": "Documento eliminado", "DEPARTMENT_DOES_NOT_EXIST": "El departamento no existe", "PROCESS_DOES_NOT_EXIST": "El proceso no existe", "PROCESS_UPDATED": "Proceso actualizado exitosamente.", "PROGRESS FETCHED": "Progreso obtenido exitosamente", "ERROR_CREATING_VENDOR": "Error al crear proveedor", "VENDOR_ADDED": "Proveedor creado exitosamente", "VENDOR_NOT_FOUND": "El proveedor no existe", "VENDOR_NAME_EXIST": "El nombre del proveedor ya existe.", "ERROR_UPDATING_VENDOR": "Error al actualizar datos del proveedor.", "VENDOR_UPDATED": "Proveedor actualizado exitosamente", "ERROR_UPDATING_ANSWERS": "Error al actualizar respuesta.", "FAILED_TO_GET_AI_RESPONSE": "Error al obtener respuesta de IA", "VENDOR_ROLE_NOT_EXIST": "Rol de proveedor no encontrado", "ROPA_NOT_COMPLETED": "ROPA aún no completado", "POLICY_DOWNLOADED": "Política descargada exitosamente", "ROPA_DOWNLOADED": "ROPA enviado exitosamente al correo electrónico", "TIA_DOWNLOADED": "TIA enviado exitosamente al correo electrónico", "LIA_DOWNLOADED": "LIA enviado exitosamente al correo electrónico", "PIA_DOWNLOADED": "PIA enviado exitosamente al correo electrónico", "PDA_DOWNLOADED": "PDA enviado exitosamente al correo electrónico", "CUSTOMER_NOT_FOUND": "Nombre del cliente no encontrado", "POLICY_NOT_IN_USE": "El documento de política no se puede descargar porque la política no está en uso", "POLICY_ID_REQUIRED": "ID de política requerido", "TEMPLATES_NOT_FOUND": "Plantillas no encontradas", "TEMPLATE_NOT_FOUND": "Plantilla no encontrada", "TEMPLATES_FETCHED": "Plantillas obtenidas exitosamente", "TEMPLATE_FETCHED": "Plantilla obtenida exitosamente", "CUSTOM_POLICY_FETCHED": "Política personalizada obtenida exitosamente", "FAILED_TO_CREATE_CUSTOM_POLICY": "Error al crear política personalizada", "CUSTOM_POLICY_CREATED": "Política personalizada creada exitosamente", "CANNOT_CREATE_POLICY": "No se puede crear política en esta etapa", "CUSTOM_POLICY_NOT_FOUND": "Política personalizada no encontrada", "FAILED_TO_SAVE_POLICY": "Error al guardar política", "POLICY_SAVED": "Política guardada exitosamente", "FAILED_TO_UPLOAD_DOCUMENT": "Error al subir documento", "ERROR_CREATING_GROUP_USER": "Error al crear usuario de grupo", "ERROR_CREATING_ASSESSMENT": "Error al crear evaluación", "ASSESSMENT_LIST": "Lista de evaluaciones", "ASSESSMENT_CREATED": "Evaluación creada exitosamente.", "ASSESSMENT_MAIL_NOT_TRIGGRED": "Evaluación creada exitosamente pero correo electrónico no enviado.", "ASSESSMENT_UPDATED": "Evaluación actualizada exitosamente.", "TASK_OVERVIEW_FETCHED": "Lista de resumen de tareas obtenida exitosamente", "PROCESS_NOT_FOUND": "No se puede obtener la lista de procesos", "PROCESS_LIST": "Lista de procesos obtenida exitosamente", "DEPARTMENT_NOT_FOUND": "No se puede obtener la lista de departamentos", "DEPARTMENT_LIST": "Lista de departamentos obtenida exitosamente", "NOT_REGISTERED_ON_GOTRUST": "El usuario no está registrado en GoTrust", "INACTIVE_CUSTOMER": "La cuenta está inactiva. Alguien se pondrá en contacto con usted pronto.", "VENDOR_LIST_FETCHED": "Lista de proveedores obtenida exitosamente.", "FETCHING_ERROR": "Lista no obtenida", "VENDOR_NOT_EXIST": "El proveedor no existe", "VENDOR_FETHCED": "Datos del proveedor obtenidos", "VIA_NOT_ASSIGNED": "VIA no asignado", "VIA_FETCHED": "VIA obtenido exitosamente", "ERROR_CREATING_VIA": "Error al crear VIA", "VIA_NOT_FOUND": "VIA no encontrado", "VIA_STARTED": "VIA iniciado", "VIA_ASSIGNED": "VIA asignado exitosamente", "VIA_UNDER_REVIEW": "VIA está siendo revisado", "VIA_COMPLETED": "VIA ya está completado", "VIA_SUBMITTED": "VIA enviado exitosamente", "VIA_NOT_STARTED": "VIA no iniciado", "INVALID_VIA_LEVEL": "Nivel VIA inválido", "VIA_NOT_SUBMITTED": "VIA aún no enviado para revisión", "VIA_NOT_COMPLETED": "VIA aún no completado", "VIA_REVIEWER_ADDED": "Revisor VIA agregado", "VIA_DOWNLOADED": "VIA enviado exitosamente al correo electrónico", "VEA_NOT_FOUND": "VEA no encontrado", "VEA_STARTED": "VEA iniciado", "VEA_ASSIGNED": "VEA asignado exitosamente", "VEA_UNDER_REVIEW": "VEA está siendo revisado", "VEA_COMPLETED": "VEA ya está completado", "VEA_SUBMITTED": "VEA enviado exitosamente", "VEA_NOT_STARTED": "VEA no iniciado", "INVALID_VEA_LEVEL": "Nivel VEA inválido", "VEA_NOT_SUBMITTED": "VEA aún no enviado para revisión", "VEA_NOT_COMPLETED": "VEA aún no completado", "VEA_REVIEWER_ADDED": "Revisor VEA agregado", "VEA_DOWNLOADED": "VEA enviado exitosamente al correo electrónico", "RISK_NOT_FETCHED": "Error al cargar el riesgo de evaluación interna", "STATUS_NOT_FETCHED": "Error al cargar el estado de evaluación interna", "VENDOR_TYPE_LIST_FETCHED": "<PERSON><PERSON>o de proveedor obtenido exitosamente", "MODEL_NOT_FOUND": "Clave inválida o nombre de modelo", "DOCUMENT_NOT_FOUND": "Documento no encontrado", "INVALID_KEY": "La clave es inválida", "ASSESSMENTS_RISK_COUNT": "Número de evaluaciones por riesgo", "ASSESSMENTS_NAME_COUNT": "Número de evaluaciones por nombre", "ASSESSMENTS_OWNER_COUNT": "Número de evaluaciones por propietario", "VRM_RISK_COUNT": "Número de proveedores por riesgo", "DASHBOARD_DATA_NOT_FOUND": "Error al obtener datos del panel de control", "EMAIL_V_FAILED": "Verificación de correo electrónico fallida.", "QUESTIONNAIRES_FETCHED": "Cuestionarios obtenidos exitosamente", "ERROR_CREATING_TOKEN": "Error al crear token", "ACCESS_TOKEN_CREATED": "Token de acceso creado exitosamente", "USER_FETCHED": "Usuario obtenido exitosamente", "ROLE_FETCHED": "Rol obtenido exitosamente", "SIDEBAR_FETCHED": "Barra lateral obtenida exitosamente", "CUSTOMER_FETCHED": "Cliente obtenido exitosamente", "EMAIL_ALREADY_EXIST": "El correo electrónico ya existe", "ASSIGNED": "Asignación y revocación de roles completada exitosamente", "ERROR_RESOURCE_DELETE": "Error al eliminar recursos", "ERROR_RESOURCE_UPDATE": "<PERSON><PERSON><PERSON> al actualizar recursos", "USER_UPDATED": "Usuario actualizado exitosamente", "ROLE_EXIST": "Este rol ya existe.", "GROUP_FETCHED": "Grupo obtenido exitosamente", "ROLE_NOT_FOUND": "Rol no encontrado", "GROUP_NAME_EXIST": "Ya existe un grupo con este nombre.", "ERROR_CREATING_GROUP": "Error al crear grupo", "GROUP_CREATED": "Grupo creado exitosamente", "RESOURCES_NOT_FOUND": "Recursos no encontrados", "FORBIDDEN": "El acceso al recurso solicitado está prohibido", "NO_RESOURCES": "No hay recursos asignados a este rol", "LANGUAGE_FETCHED": "Idioma obtenido exitosamente.", "GROUP_DOES_NOT_EXIST": "El grupo no existe.", "ENTITY_FETCHED": "Entidad obtenida exitosamente", "BUSINESS_UNIT_NOT_FOUND": "Unidad de negocio no encontrada", "GROUP_UPDATED": "Grupo actualizado exitosamente", "UPLOAD_FAILED": "Error en la carga", "TICKET_RAISED": "Ticket creado exitosamente", "TICKET_NOT_FETCHED": "Error al obtener datos del ticket", "TICKET_OPENED": "Ticket abierto exitosamente", "TICKETS_FETCHED": "Tickets obtenidos exitosamente", "ERROR_IN_TICKET_DATA": "Error al obtener estadísticas del ticket", "TICKET_UPDATED": "Ticket actualizado exitosamente", "COMMENT_ERROR": "Error al crear comentario en el ticket", "COMMENT_SUCCESS": "Comentario del ticket publicado exitosamente", "CUSTOMER_UPDATED": "Cliente actualizado exitosamente", "ROLE_UPDATE_NOT_ALLOWED": "Actualización de rol no permitida", "SOMEONE_CONTACT_SOON": "Al<PERSON><PERSON> se <PERSON>r<PERSON> en contacto con usted pronto", "DPO_NOT_FOUND": "DPO no encontrado", "USER_VALIDATED": "Usuario validado exitosamente", "ERROR_CREATING_DATA_SUBJECT": "Error al guardar información del sujeto de datos", "ERROR_CREATING_REQUEST": "Error al crear solicitud DSR", "REQUEST_CREATED": "Solicitud DSR creada", "WORKFLOW_NAME_EXIST": "Flujo de trabajo duplicado. Elija un nombre diferente", "ERROR_CREATING_WORKFLOW": "Error al crear tipo de flujo de trabajo", "WORKFLOW_CREATED": "Flujo de trabajo creado exitosamente", "WORKFLOW_DOES_NOT_EXIST": "El flujo de trabajo no existe", "WORKFLOW_NOT_FOUND": "No se puede obtener la lista de flujos de trabajo", "WORKFLOW_FETCHED": "Flujo de trabajo obtenido exitosamente", "WORKFLOW_UPDATED": "Flujo de trabajo actualizado exitosamente", "WORKFLOW_STEP_NAME_EXIST": "El nombre del paso ya existe", "ERROR_CREATING_WORKFLOW_STEP": "Error al crear paso del flujo de trabajo", "WORKFLOW_STEP_CREATED": "Paso del flujo de trabajo creado exitosamente", "WORKFLOW_STEP_DOES_NOT_EXIST": "El paso del flujo de trabajo no existe", "WORKFLOW_STEP_UPDATED": "Paso del flujo de trabajo actualizado exitosamente", "ERROR_DELETING_WORKFLOW_STEP": "Error al eliminar paso del flujo de trabajo", "WORKFLOW_STEP_DELETED_SUCC": "Paso del flujo de trabajo eliminado exitosamente", "WORKFLOW_STEP_ALREADY_TOP": "El paso del flujo de trabajo ya está en la parte superior", "TASK_NAME_EXIST": "El nombre de la tarea ya existe", "ERROR_CREATING_TASK": "Error al crear tarea", "TASK_CREATED": "Tarea creada exitosamente", "TASK_DOES_NOT_EXIST": "La tarea no existe", "TASK_NOT_FOUND": "No se puede obtener la lista de tareas", "TASK_FETCHED": "Tarea obtenida exitosamente", "TASK_UPDATED": "Tarea actualizada exitosamente", "ERROR_DELETING_TASK": "Error al eliminar tarea", "TASK_DELETED_SUCC": "Tarea eliminada exitosamente", "RISK_MATRIX": "<PERSON><PERSON> r<PERSON>go obtenida exitosamente", "TEMPLATE_NOT_ADDED": "Plantilla no agregada. Inténtelo de nuevo", "LIST_FETCHED": "Lista de plantillas obtenida", "MITIGATION_COMPLETED": "Mitigación completada", "DATA_SUBJECT_DOES_NOT_EXIST": "El sujeto de datos no existe", "REQUEST_NOT_FOUND": "No se puede obtener la lista de solicitudes", "REQUEST_FETCHED": "Solicitud obtenida exitosamente", "REQUEST_DOES_NOT_EXIST": "La solicitud no existe", "REQUEST_UPDATED": "Solicitud actualizada exitosamente", "REQUEST_TYPE_COUNT": "Número de solicitudes por tipo", "REQUEST_STATUS_COUNT": "Número de solicitudes por estado", "FORM_NOT_FOUND": "El formulario no existe", "FORM_CREATE_ERROR": "Formulario no creado", "FORM_UPDATE_ERROR": "Formulario no actualizado", "FORM_CREATED": "Formulario creado exitosamente", "FORM_UPDATED": "Formulario actualizado exitosamente", "DATA_FETCHED": "Datos obtenidos exitosamente", "SERVICE_ENTITY_FETCHED": "Entidad de servicio obtenida exitosamente", "MAIL_LIST_ERROR": "Error al obtener la lista de plantillas de correo electrónico", "MAIL_LIST_FETCHED": "Lista de plantillas de correo electrónico obtenida exitosamente", "MAIL_CREATE_ERROR": "Error al crear plantilla de correo electrónico", "MAIL_UPDATED": "Plantilla de correo electrónico actualizada", "MAIL_DOES_NOT_EXIST": "La plantilla de correo electrónico no existe", "MAIL_DELETE_ERROR": "Error al eliminar la plantilla de correo electrónico", "MAIL_DELETE": "Plantilla de correo electrónico eliminada", "MAIL_UPDATE_ERROR": "Error al actualizar la plantilla de correo electrónico", "MAIL_CREATED": "Plantilla de correo electrónico creada", "EMAIL_SUCCESS": "Correo electrónico enviado exitosamente", "EMAIL_REQUIRED": "La dirección de correo electrónico es requerida", "MITIGATION_NOT_COMPLETED": "Mitigación aún no completada para el proveedor", "MITIGATION_SAVED": "Mitigación guardada", "EMAIL_NOT_FOUND": "Correo electrónico no encontrado", "MITIGATION_PLAN_NOT_FILLED": "Complete el plan de mitigación para todos los controles", "LIST_ERROR": "Error al obtener la lista", "REGULATION_LIST_FETCHED": "Lista obtenida exitosamente", "DUPLICATE_WEBHOOK": "El nombre de la acción ya existe", "WEBHOOK_CREATED": "Webhook creado exitosamente", "ERROR_CREATING_WEBHOOK": "Error al crear el webhook", "WEBHOOK_DELETED_ERROR": "Error al eliminar el webhook", "WEBHOOK_DELETED": "Webhook eliminado", "WEBHOOK_NOT_EXIST": "El webhook no existe", "AUDIT_DOWNLOADED": "Auditoría descargada exitosamente", "DATASUBJECT_NOT_MATCHED": "Los datos del interesado no coinciden", "DSRINFO_DOES_NOT_EXIST": "Su información no existe en nuestro registro", "OCR_NOT_MATTACHED": "Sus datos de identidad no coinciden en los documentos proporcionados", "ALREADY_EXIST_TEMPLATE": "La plantilla ya existe, no se puede actualizar", "WORKFLOW_ID_REQUIRED": "Se requiere el ID del flujo de trabajo", "UNSTRUCTURED_DATA_MANAGEMENT_FETCHED": "Gestión de datos no estructurados obtenida exitosamente", "DOCUMENT_LIST": "Lista de documentos obtenida exitosamente", "FILE_FORMAT": "Formato de archivo obtenido exitosamente", "SUMMARY_FETCHED": "Resumen obtenido exitosamente", "LOCATION_FETCHED": "Ubicación obtenida exitosamente", "VIEW_DETAILS_FETCHED": "Detalles de vista obtenidos exitosamente", "ELEMENT_CATEGORIES_FETCHED": "Categorías de elementos obtenidas exitosamente", "DATA_MANAGEMENT_FETCHED": "Gestión de datos obtenida exitosamente", "ELEMENT_TYPES_FETCHED": "Tipos de elementos obtenidos exitosamente", "STRUCTURED_VIEW_LOCATION": "Ubicación de vista estructurada obtenida exitosamente", "PROFILER_META_FETCHED": "Metadatos del perfilador obtenidos exitosamente", "ERROR_ADDING_ACTION": "Acción no agregada", "ACTION_ADDED": "Acción agregada exitosamente", "ACTION_LIST_FETCHED": "Lista de acciones obtenida exitosamente", "ACTION_NOT_FOUND": "Acción no encontrada", "ACTION_FETCHED": "Acción obtenida exitosamente", "ERROR_CREATING_DUTY": "Error al crear el deber", "DUTY_CREATED": "Deber creado exitosamente", "DUTY_NOT_FOUND": "Deber no encontrado", "DUTY_UPDATED": "Deber actualizado exitosamente", "STRUCTURED_SENSITIVITY_FETCHED": "Datos de sensibilidad estructurados obtenidos", "UNSTRUCTURED_SENSITIVITY_FETCHED": "Datos de sensibilidad no estructurados obtenidos", "RESOURCE_LIST_NOT_FOUND": "Lista de recursos no encontrada", "RESOURCES_ADDED": "Recurso agregado exitosamente", "ERROR_CREATING_IMPROVEMENT": "Error al crear la mejora", "IMPROVEMENT_CREATED": "Me<PERSON>ra creada exitosamente", "IMPROVEMENT_UPDATED": "Mejora actualizada exitosamente", "IMPROVEMENT_NOT_FOUND": "Mejora no encontrada", "CATEGORY_NOT_FOUND": "Categoría no encontrada", "CATEGORY_CREATE_ERROR": "Error al crear la categoría", "CATEGORY_FETCHED": "Categoría obtenida exitosamente", "ERROR_DELETING_FORM": "Error al eliminar el formulario", "FORM_DELETED": "Formulario eliminado exitosamente", "ERROR_DELETING_CATEGORY": "Error al eliminar las categorías", "TASK_STATUS": "El estado de la tarea es requerido", "FORM_FETCHED": "Formulario obtenido exitosamente", "FORM_PUBLISHED": "Formulario publicado exitosamente", "FORM_NOT_PUBLISHED": "Error al publicar el formulario", "FORM_ALREADY_PUBLISHED": "El formulario ya existe", "DATA_UPDATED": "Datos actualizados exitosamente", "DATA_DELETED": "Datos eliminados exitosamente", "DATA_CREATED": "Datos creados exitosamente", "ERROR_CREATING_INCIDENT": "Error al crear el incidente", "INCIDENT_CREATED": "<PERSON><PERSON> c<PERSON>o", "INCIDENT_NOT_FOUND": "No se puede obtener la lista de incidentes", "ASSESSMENT_NOT_FOUND": "La evaluación no existe", "ASSESSMENT_NOT_ASSIGNED": "La evaluación no está asignada", "ASSESSMENT_STARTED": "Evaluación iniciada", "ASSESSMENT_UNDER_REVIEW": "Evaluación en revisión", "ASSESSMENT_COMPLETED": "Evaluación completada", "ASSESSMENT_DOWNLOADED": "Evaluación enviada exitosamente por correo electrónico", "ASSESSMENT_ASSIGNED": "Evaluación asignada exitosamente", "ASSESSMENT_REVIEWER_ADDED": "Revisor de evaluación agregado", "ALREADY_VERIFIED": "Correo electrónico ya verificado", "FORM_VERIFIED": "Formulario verificado exitosamente", "DUPLICATE_QUESTION_TITLE": "Algunas preguntas aparecen más de una vez", "ERROR_CREATING_DASHBOARD": "Error al crear el panel de control", "DASHBOARD_CREATED": "Panel de control creado exitosamente", "DASHBOARD_NOT_FOUND": "Panel de control no encontrado", "DASHBOARD_UPDATED": "Panel de control actualizado exitosamente", "DASHBOARD_DELETED": "Panel de control eliminado exitosamente", "DELETE_ERROR": "Error al eliminar el elemento", "DASHBOARD_ALREADY_EXIST": "El panel de control ya existe", "CANNOT_DELETE_DEPARTMENT": "Un departamento no puede ser eliminado si tiene un ROPA, proceso o subdepartamento", "CANNOT_DELETE_PROCESS": "Un proceso no puede ser eliminado si tiene un ROPA o subproceso", "DEPARTMENT_DELETED": "Departamento eliminado exitosamente", "PROCESS_DELETED": "Proceso eliminado exitosamente", "GROUP_DELETED": "Grupo eliminado exitosamente", "CANNOT_DELETE_GROUP": "Un grupo no puede ser eliminado si tiene un departamento o subgrupo", "ERROR_DELETING_GROUP_USER": "Error al eliminar el usuario del grupo", "ERROR_DELETING_CUSTOMER_REGULATIONS": "Error al eliminar las regulaciones del cliente", "ERROR_DELETING_CUSTOMER_BUSINESS_REQUIREMENTS": "Error al eliminar los requisitos comerciales del cliente", "REQUEST_ARCHIVED": "Solicitud archivada exitosamente", "REQUEST_UNARCHIVED": "Solicitud desarchivada exitosamente", "DSR_REQUEST_ARCHIVING_FAILED": "Error al archivar la solicitud DSR", "REQUEST_ALREADY_ARCHIVED": "Solicitud ya archivada", "MISSING_REQUIRED_FIELDS": "Faltan campos requeridos", "TASK_CREATION_FAILED": "Error al crear la tarea", "TASK_UPDATED_SUCCESSFULLY": "Tarea actualizada exitosamente", "TASK_UPDATE_FAILED": "Error al actualizar la tarea", "PRIVACY_NOTICE_GENERATION_FAILED": "Error al generar el aviso de privacidad", "PRIVACY_NOTICE_GENERATED": "Aviso de privacidad generado exitosamente", "GENERATE_NOTICE_STARTED": "Generación del aviso de privacidad iniciada", "PRIVACY_NOTICE_NOT_FOUND": "Aviso de privacidad no encontrado", "FAILED_TO_UPDATE_URL": "Error al actualizar la URL", "USER_ALREADY_LOGGED_IN": "El usuario ya está conectado", "PASSWORD_NOT_UPDATED": "Contraseña no actualizada", "CREDENTIALS_SENT": "Credenciales enviadas exitosamente", "FORM_VERSION_CREATED": "Versión del formulario creada exitosamente", "VENDOR_LIST_NOT_FOUND": "Lista de proveedores no encontrada", "PROGRESS_FETCHED": "Progreso obtenido exitosamente", "ACTION_ALREADY_EXIST": "La acción ya existe", "ADD_DATA_BREACH_NOTIFICATION": "Agregar notificación de violación de datos", "ALERT_SENT": "Alerta enviada", "AMBITION_CREATED": "Ambición creada exitosamente", "ANSWER_CREATION_FAILED": "Falló la creación de respuesta", "ANSWER_NOT_SAVED": "Respuesta no guardada", "ASSESSMENT_ALREADY_EXIST": "La evaluación ya existe", "ASSESSMENT_NOT_ADDED": "Evaluación no agregada exitosamente", "ASSESSMENT_NOT_COMPLETED": "Evaluación no completada exitosamente", "ASSESSMENT_NOT_STARTED": "Evaluación no iniciada exitosamente", "ASSESSMENT_NOT_SUBMITTED": "Evaluación no enviada exitosamente", "ASSESSMENT_REVIEWED": "Evaluación revisada exitosamente", "ASSESSMENT_SUBMITTED": "Evaluación enviada exitosamente", "AUDIT_LOG_NOT_FOUND": "Registro de auditoría no encontrado", "BILLING_ADDRESS": "Dirección de facturación", "BILLING_ADDRESS_CREATE_ERROR": "Error al crear dirección de facturación", "BLOGS_FETCHED": "Blogs obtenidos exitosamente", "BLOG_CATEGORIES_FETCHED": "Categorías de blog obtenidas exitosamente", "BLOG_FETCHED": "Blog obtenido exitosamente", "BUSINESS_REQUIREMENTS": "Requisitos de negocio", "BUSINESS_REQUIREMENTS_ADDED": "Requisitos de negocio agregados exitosamente", "BUSINESS_REQUIREMENTS_UPDATED": "Requisitos de negocio actualizados exitosamente", "CATEGORY_ADDED": "Categoría agregada exitosamente", "CATEGORY_CREATION_FAILED": "Falló la creación de categoría", "CLIENT_DOES_NOT_EXIST": "El cliente no existe", "CLIENT_REGISTRATION_SUCCESSFUL": "Registro de cliente completado exitosamente", "CONTROL_CREATION_FAILED": "Falló la creación de control", "CONTROL_FOUND": "Control encontrado", "CONTROL_NOT_FOUND": "Control no encontrado", "CONTROL_NOT_UPDATED": "Control no actualizado exitosamente", "CREATE_ADD_ACTION": "<PERSON><PERSON>r agregar acción", "CUSTOMER_ASSESSMENTS_NOT_FOUND": "Evaluaciones de cliente no encontradas", "CUSTOMER_CONTROL_CREATION_FAILED": "Falló la creación de control de cliente", "CUSTOM_REGULATION_ADDED": "Regulación personalizada agregada exitosamente", "DASHBOARD_BREACH_COUNT": "Conteo de violaciones del panel obtenido exitosamente", "DASHBOARD_BREACH_STATUS_DISTRIBUTION": "Distribución de estado de violaciones del panel", "DASHBOARD_SEVERITY_DISTRIBUTION": "Distribución de severidad del panel", "DATA_FECTHED": "<PERSON><PERSON> obteni<PERSON>", "DATA_SUBJECT_NOT_FOUND": "Sujeto de datos no encontrado", "DEPARTMENT_UPDATED": "Departamento actualizado exitosamente", "DOCUMENTS_FETCHED": "Documentos obtenidos exitosamente", "DOMAIN_NAME_ALREADY_EXIST": "El nombre de dominio ya existe", "DSR_REQUEST_NOT_FOUND": "Solicitud DSR no encontrada", "EMAIL_EXISTS": "El email ya existe", "EMAIL_NOT_EXIST": "El email no existe", "EMBEDDINGS_CREATED": "Embeddings creados exitosamente", "ENTITY_NOT_FOUND": "Entidad no encontrada", "ERROR_CREATE_AMBITION": "Error: crear ambición", "ERROR_CREATE_QUESTIONNAIRES": "Error: crear cuestionarios", "ERROR_CREATE_ROLE": "Error: crear rol", "ERROR_CREATE_SERVICE": "Error: crear servicio", "ERROR_CREATING_AUDIT_LOG": "Error: creando registro de auditoría", "ERROR_CREATING_BREACH_NOTIFICATION": "Error: creando notificación de violación", "ERROR_CREATING_RISK": "Error: creando riesgo", "ERROR_UPDATING_MASTER_CONTROL": "Error: actualizando control maestro", "ERROR_WHILE_DELETE_TOKEN": "Error: al eliminar token", "FILE_NOT_IN_CORRECT_FORMAT": "Archivo no está en formato correcto", "FILE_REQUIRED": "Archivo requerido", "FORM_DOES_NOT_EXIST": "El formulario no existe", "GENERATE_NOTICE_START": "Generar aviso inicio", "GET_ACTION_LISTING": "Obtener listado de acciones", "GROUP_NOT_FOUND": "Grupo no encontrado", "INCIDENT_DOES_NOT_EXIST": "El incidente no existe", "INCIDENT_UPDATED": "Incidente actualizado exitosamente", "INTERNAL_ASSESSMENT_ERROR": "Error de evaluación interna", "INVALID_HEADER": "Encabezado in<PERSON>", "IP_LOOKUP_SUCCESS": "Búsqueda de IP completada exitosamente", "LIA_REVIEWED": "LIA revisada exitosamente", "LIST_NOT_FOUND": "Lista no encontrada", "MARKED_READ": "Marcado como leído", "NEW_FORM_VERSION_PUBLISHED": "Nueva versión de formulario publicada", "NOT_PUBLISHED": "No publicado", "NO_DATA_FOUND": "No se encontraron datos", "NO_GROUPS_FOUND": "No se encontraron grupos", "OK": "Ok", "ONBOARDING_COMPLETED": "Incorporación completada exitosamente", "ORGANISATION_CREATED": "Organización creada exitosamente", "ORGANISATION_NOT_FOUND": "Organización no encontrada", "ORGANISATION_UPDATE_ERROR": "Error de actualización de organización", "OTP_NOT_GENERATE": "OTP no generado", "OTP_NOT_VERIFIED": "OTP no verificado", "PACKAGES_FETCHED": "Paquetes obtenidos exitosamente", "PACKAGE_DOES_NOT_EXIST": "El paquete no existe", "PARSING_ERROR": "<PERSON><PERSON><PERSON> <PERSON>", "PASSWORD_NOT_MATCH": "Las contraseñas no coinciden", "PASSWORD_UPDATE_ERROR": "Error de actualización de contraseña", "PDA_NOT_ASSIGNED": "PDA no asignado exitosamente", "PDA_REVIEWED": "PDA revisado exitosamente", "PENDING_REQUEST_COUNT": "Conteo de solicitudes pendientes obtenido exitosamente", "PIA_REVIEWED": "PIA revisado exitosamente", "POLICIES_FETCHED": "Políticas obtenidas exitosamente", "POLICIES_NOT_FOUND": "Políticas no encontradas", "PRIVACY_NOTICES_FETCHED": "Avisos de privacidad obtenidos exitosamente", "PRIVACY_NOTICES_NOT_FOUND": "Avisos de privacidad no encontrados", "PRIVACY_NOTICE_CREATED": "Aviso de privacidad creado exitosamente", "PRIVACY_NOTICE_NOT_CREATED": "Aviso de privacidad no creado exitosamente", "PRIVACY_NOTICE_NOT_UPDATE": "Aviso de privacidad no actualizado", "PRIVACY_NOTICE_UPDATE": "Actualización de aviso de privacidad", "PROCESSES_CREATED_SUCCESSFULLY": "Procesos creados exitosamente", "PROCESS_CREATION_FAILED": "Falló la creación de proceso", "REGULATION_NOT_FOUND": "Regulación no encontrada", "REQUIREMENT_UPDATED": "Requisito actualizado exitosamente", "REVIEW_CREATED_OR_UPDATED": "Revisión creada o actualizada exitosamente", "RISK_CREATED": "R<PERSON>go creado exitosamente", "RISK_NOT_FOUND": "Riesgo no encontrado", "RISK_UPDATED": "Riesgo actualizado exitosamente", "ROPA_ALREADY_STARTED": "ROPA ya iniciado exitosamente", "ROPA_CREATION_FAILED": "Falló la creación de ROPA", "ROPA_REVIEWED": "ROPA revisado exitosamente", "ROPA_UPDATE_FAILED": "Falló la actualización de ROPA", "SERVER_ERROR": "Error del servidor", "SERVICE_FETCHED": "<PERSON><PERSON><PERSON> obtenido exitosamente", "SOME_AMBITIONS_DO_NOT_EXIST": "Algunas ambiciones no existen", "SOME_QUESTIONNAIRES_DO_NOT_EXIST": "Algunos cuestionarios no existen", "SOME_SERVICES_DO_NOT_EXIST": "Algunos servicios no existen", "STEP_NOT_FOUND": "Paso no encontrado", "STREAM_ERROR": "Error de transmisión", "SUBPROCESS_CREATION_FAILED": "Falló la creación de subproceso", "TASK_FOUND": "Tarea encontrada", "TEMPLATE_ADDED": "Plantilla agregada exitosamente", "TEMPLATE_CREATION_FAILED": "Falló la creación de plantilla", "TEMPLATE_PUBLISHED": "Plantilla publicada", "TIA_REVIEWED": "TIA revisada exitosamente", "TRANSACTION_FAILED": "Transacción falló", "TYPES_FETCHED": "Tipos obtenidos exitosamente", "TYPES_NOT_FOUND": "Tipos no encontrados", "UPDATE_TIA_FAILED": "Falló la actualización de TIA", "UPDATE_VEA_FAILED": "Falló la actualización de VEA", "UPDATE_VIA_FAILED": "Falló la actualización de VIA", "USERS_NOT_FOUND": "Usuarios no encontrados", "VEA_NOT_ASSIGNED": "VEA no asignado exitosamente", "VEA_REVIEWED": "VEA revisado exitosamente", "VENDOR_ASSESSMENTS_NOT_FOUND": "Evaluaciones de proveedor no encontradas", "VENDOR_DETAILS_NOT_FOUND": "Detalles de proveedor no encontrados", "REGULATION_ADDED": "Regulación agregada exitosamente", "REGULATION_UPDATED": "Regulación actualizada exitosamente", "RESOURCES_FETCHED": "Recursos obtenidos exitosamente", "ROLE_UPDATED": "Rol actualizado exitosamente", "ROPA_UPDATED": "ROPA actualizado exitosamente", "SAVE_ERROR": "Error al guardar datos", "VIA_REVIEWED": "VIA revisada exitosamente", "DPO_ROLE_EXIST": "No se puede crear 'Oficial de Protección de Datos', elija otro nombre de rol", "ROPA_CREATED": "ROPA creado exitosamente"}