{"SIGNUP_SUCCESSFUL": "Gebruiker succesvol geregistreerd", "LOGIN_SUCCESSFUL": "Gebruiker succesvol ingelogd", "USER_REGISTERED_LOGIN": "Gebruiker succesvol geregistreerd. Log in om uw account te verifiëren.", "API_SUCCESS": "Succes", "LOGOUT_SUCCESSFUL": "Gebruiker succesvol uitgelogd", "PASSWORD_UPDATED": "Wachtwoord is bijgewerkt!", "FORGOT_PASSWORD": "Reset link verzonden naar uw e-mail", "NOT_MATCHED": "Nog niet gema<PERSON>t", "DELETED": "Succesvol verwijderd.", "OTP_SENT": "OTP succesvol verzonden", "EMAIL_VERIFIED": "E-mail succesvol geverifieerd", "P_UPDATE": "Profiel succesvol bijgewerkt", "UPDATE_EMAIL": "E-mail succesvol bijgewerkt", "TOKEN_EXPIRED": "Token verlopen", "ALREADY_REGISTERED": "Er is al een account aangemaakt.", "REG_ALREADY_REGISTERED": "Er is al een account aangemaakt met dit registratienummer.", "UPDATE_ERROR": "Fout bij het bijwerken van gegevens.", "API_ERROR": "Fout bij API-uitvoering.", "VALIDATION_ERROR": "Validatiefout.", "FAILED_TO_ADD": "<PERSON><PERSON><PERSON><PERSON> van gegevens mislukt.", "INVALID_CREDENTIALS": "Ongeldige inloggegevens", "EMAIL_FAILURE": "E-mail niet verzonden.", "EMAIL_ALREADY_EXISTS": "E-mail bestaat al.", "EMAIL_ALREADY_EXIST_IN_KEYCLOAK": "E-mail bestaat al in keyCloak", "USER_NOT_FOUND": "Gebruiker niet gevonden", "UNAUTHORIZED": "Ongeautoriseer<PERSON>.", "FAILED_TO_UPDATE": "Bijwerken mislukt.", "FAILED_TO_DELETE": "Verwijderen van gegevens mislukt.", "INTERNAL_SERVER_ERROR": "Interne serverfout", "INVALID_EMAIL": "ongeldig e-mailadres", "INVALID_OTP": "ongeldige OTP", "SIGNUP_FAILED": "Uw registratie is mislukt", "EMAIL_NOT_VERIFIED": "E-mail is niet g<PERSON><PERSON>ieerd", "INVALID_TOKEN": "Uw token is ongeldig.", "EMAIL_v_FAILED": "E-mailverificatie is mislukt", "MISSING_TOKEN": "Ontbrekende token", "MISSING_P": "Ontbrekende parameter", "OTP_NOT_SEND": "OTP niet succesvol verzonden", "OTP_EXPIRED": "OTP is verlopen", "NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> niet gevonden", "TOTAL_LOGIN": "U heeft het maximum aantal inlogpogingen bereikt. Log uit van andere apparaten en log opnieuw in.", "WRONG_PASS": "Onju<PERSON> wachtwoord", "INVALID_ROUTE": "Ongeldige route", "MISSING_API_KEY": "Ontbrekende API-sleutel", "INVALID_API_KEY": "API-sleutel is ongeldig", "LAWS_FETCHED": "Wetten succesvol opgehaald", "REGIONS_FETCHED": "Regio's succesvol opgehaald", "STATE_FETCHED": "Staat succesvol opgehaald", "COUNTRY_FETCHED": "Land succesvol opgehaald", "POLICY_ALREADY_EXISTS": "<PERSON><PERSON><PERSON> met deze naam best<PERSON> al.", "POLICY_ADDED": "Beleid succesvol aangemaakt", "POLICY_FETCHED": "Beleidsregels succesvol opgehaald", "DEPARTMENT_FETCHED": "Afdelingen succesvol opgehaald", "INDUSTRY_FETCHED": "Industrie Verticale Lijst succesvol opgehaald", "ERROR_CREATING_POLICY": "Fout bij het aanma<PERSON> van beleid", "ERROR_CREATING_ANSWERS": "Fout bij het aanmaken van antwoorden", "POLICY_NOT_FOUND": "Beleid niet gevonden", "ERROR_IN_POLICYLIST": "Fout bij het op<PERSON><PERSON> van <PERSON>lijst", "ERROR_UPDATING_POLICY": "Fout bij het bijwerken van beleid", "POLICY_UPDATED": "Beleid succesvol bijgewerkt", "POLICY_NAME_EXIST": "Beleidsnaam bestaat al.", "DEPARTMENT_CREATED": "Afdelingen succesvol aangemaakt", "ERROR_CREATING_DEPARTMENT": "Fout bij het aanmaken van afdeling", "DEPARTMENT_NAME_EXIST": "Afdelingsnaam bestaat al", "PROCESS_CREATED": "Proces succesvol aangemaakt", "ERROR_CREATING_PROCESS": "Fout bij het aanmaken van proces", "PROCESS_NAME_EXIST": "Procesnaam bestaat al", "ROPA_NOT_ASSIGNED": "ROPA niet toege<PERSON>zen", "ROPA_CANT_START": "ROPA is al uitgevoerd en kan niet gestart worden", "ROPA_CANT_ASSIGN": "ROPA is al uitgevoerd en kan niet toegewezen worden", "ROPA_FETCHED": "ROPA succesvol opgehaald", "ROPA_YET_TO_START": "ROPA nog niet gestart", "ROPA_CHANGES_REQUESTED": "ROPA al in wijzigingsverzoek", "CATEGORIES_NOT_FOUND": "Categorieën niet gevonden", "CATEGORIES_FETCHED": "Categorieën succesvol opgehaald", "CONTROLS_NOT_FOUND": "Controles niet gevonden", "CONTROLS_FETCHED": "Controles succesvol opgehaald", "ERROR_CREATING_CONTROL": "Fout bij het aanmaken van controle", "CONTROL_CREATED": "Controle succesvol aangemaakt", "ERROR_UPDATING_CONTROL": "Fout bij het bijwerken van controle", "CONTROL_UPDATED": "Controle succesvol bijgewerkt", "ERROR_CREATING_ROPA": "Fout bij het aanmaken van ROPA", "ROPA_NOT_FOUND": "ROPA niet gevonden", "ROPA_STARTED": "ROPA gestart", "POLICY_COUNT_FETCHED": "Beleidstelling opgehaald.", "DATA_NOT_FOUND": "Fout bij het vinden van de beleidstellingen.", "ERROR": "Er ging iets mis op de server", "ERROR_CREATING_ANSWER": "Fout bij het aanmaken van antwoord", "ANSWER_CREATED_OR_UPDATED": "Antwoord succesvol opgeslagen", "ERROR_UPDATING_ANSWER": "Fout bij het bijwerken van antwoord", "ROPA_ASSIGNED": "ROPA succesvol toegewezen", "ROPA_UNDER_REVIEW": "ROPA wordt beoordeeld", "ROPA_COMPLETED": "ROPA is al voltooid", "ROPA_SUBMITTED": "ROPA is succesvol ingediend", "DOCUMENT_UPLOADED": "Documenten succesvol geüpload.", "S3_BUCKET_ERROR": "Fout in S3 Bucket", "UPLOAD_FILE": "Bestand uploaden", "INVOICES_FETCHED": "Facturen succesvol opgehaald", "ERROR_UPDATING_GROUPUSER": "Fout bij het bijwerken van g<PERSON>stoegang", "DASHBOARD_FETCHED": "Dashboard succesvol opgehaald", "ERROR_IN_AUDIT": "Fout bij het bijwerken van auditlog", "ERROR_IN_DOCUMENT_UPLOAD": "Fout bij het uploaden van documenten", "AUDIT_DATA_NOT_FOUND": "Auditgegevens niet gevonden", "AUDIT_LOG_FETCHED": "Auditlogs opge<PERSON>ald.", "INVALID_ROPA_LEVEL": "Ongeldig ROPA-niveau", "QUESTIONS_NOT_FOUND": "Vragen niet gevonden", "QUESTIONS_FETCHED": "Vragen succesvol opgehaald", "ERROR_ADDING_COLLABORATOR": "Fout bij het toevoegen van medewerker", "COLLABORATOR_ADDED": "Medewerker succesvol toegevoegd", "ROPA_NOT_STARTED": "ROPA niet gestart", "AUDIT_ERROR": "Fout bij auditprocessen", "ARTIFACT_TYPES_NOT_FOUND": "Artefacttypes niet gevonden", "ARTIFACT_TYPES_FETCHED": "Artefacttypes succesvol opgehaald", "INVALID_DATA": "Ongeldige gegevens", "INVALID_ARTIFACT_TYPE": "Ongeldig artefacttype", "CONTROLS_UPLOADED": "Controles succesvol geüpload", "INVALID_EXTRA_INPUT_TYPE": "Ongeldig extra invoertype", "ALL_NOT_REVIEWED": "Beoordeel alle vragen voordat u indient", "ROPA_NOT_SUBMITTED": "ROPA is nog niet ingediend voor beoordeling", "ALL_NOT_ANSWERED": "Beantwoord alle vragen voordat u indient", "LIA_NOT_ASSIGNED": "LIA niet toegewezen", "LIA_FETCHED": "LIA succesvol opgehaald", "ERROR_CREATING_LIA": "Fout bij het aanmaken van LIA", "LIA_NOT_FOUND": "LIA niet gevonden", "LIA_STARTED": "LIA gestart", "LIA_ASSIGNED": "LIA succesvol toegewezen", "LIA_UNDER_REVIEW": "LIA wordt beoordeeld", "LIA_COMPLETED": "LIA is al voltooid", "LIA_SUBMITTED": "LIA is succesvol ingediend", "LIA_NOT_STARTED": "LIA niet gestart", "INVALID_LIA_LEVEL": "Ongeldig LIA-niveau", "LIA_NOT_SUBMITTED": "LIA is nog niet ingediend voor beoordeling", "LIA_NOT_COMPLETED": "LIA is nog niet voltooid", "LIA_REVIEWER_ADDED": "LIA-be<PERSON><PERSON><PERSON> toe<PERSON>vo<PERSON>d", "TIA_NOT_ASSIGNED": "TIA niet toegewezen", "TIA_FETCHED": "TIA succesvol opgehaald", "ERROR_CREATING_TIA": "Fout bij het aanmaken van TIA", "TIA_NOT_FOUND": "TIA niet gevonden", "TIA_STARTED": "TIA gestart", "TIA_ASSIGNED": "TIA succesvol toegewezen", "TIA_UNDER_REVIEW": "TIA wordt beoordeeld", "TIA_COMPLETED": "TIA is al voltooid", "TIA_SUBMITTED": "TIA is succesvol ingediend", "TIA_NOT_STARTED": "TIA niet gestart", "INVALID_TIA_LEVEL": "Ongeldig TIA-niveau", "TIA_NOT_SUBMITTED": "TIA is nog niet ingediend voor beoordeling", "TIA_NOT_COMPLETED": "TIA is nog niet voltooid", "TIA_REVIEWER_ADDED": "TIA-be<PERSON><PERSON><PERSON> toegevoegd", "PIA_NOT_ASSIGNED": "PIA niet toegewezen", "PIA_FETCHED": "PIA succesvol opgehaald", "ERROR_CREATING_PIA": "Fout bij het aanmaken van PIA", "PIA_NOT_FOUND": "PIA niet gevonden", "PIA_STARTED": "PIA gestart", "PIA_ASSIGNED": "PIA succesvol toegewezen", "PIA_UNDER_REVIEW": "PIA wordt beoordeeld", "PIA_COMPLETED": "PIA is al voltooid", "PIA_SUBMITTED": "PIA is succesvol ingediend", "PIA_NOT_STARTED": "PIA niet gestart", "INVALID_PIA_LEVEL": "Ongeldig PIA-niveau", "PIA_NOT_SUBMITTED": "PIA is nog niet ingediend voor beoordeling", "PIA_NOT_COMPLETED": "PIA is nog niet voltooid", "PIA_REVIEWER_ADDED": "PIA-be<PERSON><PERSON><PERSON> toe<PERSON>vo<PERSON>d", "PDA_FETCHED": "PDA succesvol opgehaald", "ERROR_CREATING_PDA": "Fout bij het aanmaken van PDA", "PDA_NOT_FOUND": "PDA niet gevonden", "PDA_STARTED": "PDA gestart", "PDA_ASSIGNED": "PDA succesvol toegewezen", "PDA_UNDER_REVIEW": "PDA wordt beoordeeld", "PDA_COMPLETED": "PDA is al voltooid", "PDA_SUBMITTED": "PDA is succesvol ingediend", "PDA_NOT_COMPLETED": "PDA is nog niet voltooid", "PDA_NOT_STARTED": "PDA niet gestart", "PDA_REVIEWER_ADDED": "PDA-be<PERSON>delaar toegevoegd", "INVALID_PDA_LEVEL": "Ongeldig PDA-niveau", "PDA_NOT_SUBMITTED": "PDA is nog niet ingediend voor beoordeling", "COLLABORATORS_NOT_FOUND": "Medewerkers niet gevonden", "COLLABORATORS_FETCHED": "Medewerkers succesvol opgehaald", "ERROR_DELETING_COLLABORATOR": "Fout bij het verwijderen van medewerker", "COLLABORATOR_UPDATED": "Medewerker succesvol bijgewerkt", "CANT_UPDATE": "Beleid is in gebruik: Kan niet bijwerken.", "ERROR_DELETING_CONTROL": "Fout bij het verwijderen van controle", "CONTROL_DELETED": "Controle succesvol verwijderd", "DOCUMENT_DELETED_ERROR": "Fout bij het verwijderen van het document", "DOCUMENT_DELETED": "Document verwijderd", "DEPARTMENT_DOES_NOT_EXIST": "Afdeling bestaat niet", "PROCESS_DOES_NOT_EXIST": "Proces bestaat niet", "PROCESS_UPDATED": "Proces succesvol bijgewerkt.", "PROGRESS FETCHED": "Voortgang succesvol opgehaald", "ERROR_CREATING_VENDOR": "Fout bij het aanmaken van leverancier", "VENDOR_ADDED": "Leverancier succesvol aangemaakt", "VENDOR_NOT_FOUND": "Leverancier bestaat niet", "VENDOR_NAME_EXIST": "Leveranciers<PERSON>am bestaat al.", "ERROR_UPDATING_VENDOR": "Fout bij het bijwerken van leveranciersgegevens.", "VENDOR_UPDATED": "Leverancier succesvol bijgewerkt", "ERROR_UPDATING_ANSWERS": "Fout bij het bijwerken van antwoord.", "FAILED_TO_GET_AI_RESPONSE": "AI-<PERSON><PERSON> mis<PERSON>t", "VENDOR_ROLE_NOT_EXIST": "Leveranciersrol niet gevonden", "ROPA_NOT_COMPLETED": "ROPA nog niet voltooid", "POLICY_DOWNLOADED": "Beleid succesvol gedownload", "ROPA_DOWNLOADED": "ROPA succesvol naar e-mail verzonden", "TIA_DOWNLOADED": "TIA succesvol naar e-mail verzonden", "LIA_DOWNLOADED": "LIA succesvol naar e-mail verzonden", "PIA_DOWNLOADED": "PIA succesvol naar e-mail verzonden", "PDA_DOWNLOADED": "PDA succesvol naar e-mail verzonden", "CUSTOMER_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> niet gevonden", "POLICY_NOT_IN_USE": "Beleidsdocument kan niet gedownload worden omdat beleid niet in gebruik is", "POLICY_ID_REQUIRED": "Beleids-ID is vereist", "TEMPLATES_NOT_FOUND": "Sjablonen niet gevonden", "TEMPLATE_NOT_FOUND": "Sjabloon niet gevonden", "TEMPLATES_FETCHED": "Sjablonen succesvol opgehaald", "TEMPLATE_FETCHED": "Sjabloon succesvol opgehaald", "CUSTOM_POLICY_FETCHED": "Aangepast beleid succesvol opgehaald", "FAILED_TO_CREATE_CUSTOM_POLICY": "Aangepast beleid aan<PERSON>ken mislukt", "CUSTOM_POLICY_CREATED": "Aangepast beleid succesvol aangemaakt", "CANNOT_CREATE_POLICY": "Kan geen beleid a<PERSON> in deze fase", "CUSTOM_POLICY_NOT_FOUND": "Aangepast beleid niet gevonden", "FAILED_TO_SAVE_POLICY": "Beleid op<PERSON> mislukt", "POLICY_SAVED": "Beleid succesvol opgeslagen", "FAILED_TO_UPLOAD_DOCUMENT": "Document uploaden mislukt", "ERROR_CREATING_GROUP_USER": "Fout bij het aanmaken van groepsgebruiker", "ERROR_CREATING_ASSESSMENT": "Fout bij het aanma<PERSON> van beoordeling", "ASSESSMENT_LIST": "<PERSON><PERSON><PERSON> van beoordelingen", "ASSESSMENT_CREATED": "Beoordeling succesvol aangemaakt.", "ASSESSMENT_MAIL_NOT_TRIGGRED": "Beoordeling succesvol aangemaakt maar e-mail niet verzonden.", "ASSESSMENT_UPDATED": "Beoordeling succesvol bijgewerkt.", "TASK_OVERVIEW_FETCHED": "Taakoverzichtslijst succesvol opgehaald", "PROCESS_NOT_FOUND": "Procesli<PERSON>st kan niet op<PERSON> worden", "PROCESS_LIST": "Proceslijst succesvol opgehaald", "DEPARTMENT_NOT_FOUND": "A<PERSON>delingslijst kan niet opge<PERSON> worden", "DEPARTMENT_LIST": "Afdelingslijst succesvol opgehaald", "NOT_REGISTERED_ON_GOTRUST": "Gebruiker is niet geregistreerd op GoTrust", "INACTIVE_CUSTOMER": "Account is inactief. <PERSON><PERSON><PERSON> zal bin<PERSON> contact met u opnemen.", "VENDOR_LIST_FETCHED": "Leverancierslijst succesvol opgehaald.", "FETCHING_ERROR": "<PERSON><PERSON><PERSON> niet op<PERSON>", "VENDOR_NOT_EXIST": "Leverancier bestaat niet", "VENDOR_FETHCED": "Leveranciersgegevens opgehaald", "VIA_NOT_ASSIGNED": "VIA niet toegewezen", "VIA_FETCHED": "VIA succesvol opgehaald", "ERROR_CREATING_VIA": "Fout bij het aanmaken van VIA", "VIA_NOT_FOUND": "VIA niet gevonden", "VIA_STARTED": "VIA gestart", "VIA_ASSIGNED": "VIA succesvol toegewezen", "VIA_UNDER_REVIEW": "VIA wordt beoordeeld", "VIA_COMPLETED": "VIA is al voltooid", "VIA_SUBMITTED": "VIA is succesvol ingediend", "VIA_NOT_STARTED": "VIA niet gestart", "INVALID_VIA_LEVEL": "Ongeldig VIA-niveau", "VIA_NOT_SUBMITTED": "VIA is nog niet ingediend voor beoordeling", "VIA_NOT_COMPLETED": "VIA is nog niet voltooid", "VIA_REVIEWER_ADDED": "VIA-beoordelaar toegevoegd", "VIA_DOWNLOADED": "VIA succesvol naar e-mail verzonden", "VEA_NOT_FOUND": "VEA niet gevonden", "VEA_STARTED": "VEA gestart", "VEA_ASSIGNED": "VEA succesvol toegewezen", "VEA_UNDER_REVIEW": "VEA wordt beoordeeld", "VEA_COMPLETED": "VEA is al voltooid", "VEA_SUBMITTED": "VEA is succesvol ingediend", "VEA_NOT_STARTED": "VEA niet gestart", "INVALID_VEA_LEVEL": "Ongeldig VEA-niveau", "VEA_NOT_SUBMITTED": "VEA is nog niet ingediend voor beoordeling", "VEA_NOT_COMPLETED": "VEA is nog niet voltooid", "VEA_REVIEWER_ADDED": "VEA-beoordelaar toegevoegd", "VEA_DOWNLOADED": "VEA succesvol naar e-mail verzonden", "RISK_NOT_FETCHED": "Fout bij het laden van het risico van interne beoordeling", "STATUS_NOT_FETCHED": "Fout bij het laden van de status van interne beoordeling", "VENDOR_TYPE_LIST_FETCHED": "Leverancierstype succesvol opgehaald", "MODEL_NOT_FOUND": "Ongeldige sleutel of modelnaam", "DOCUMENT_NOT_FOUND": "Document niet gevonden", "INVALID_KEY": "Sleutel is ongeldig", "ASSESSMENTS_RISK_COUNT": "Aantal beoordelingen volgens risico", "ASSESSMENTS_NAME_COUNT": "Aantal beoordelingen volgens naam", "ASSESSMENTS_OWNER_COUNT": "Aantal beoordelingen volgens eigenaar", "VRM_RISK_COUNT": "Aantal leveranciers volgens risico", "DASHBOARD_DATA_NOT_FOUND": "Fout bij het op<PERSON><PERSON> van dashboardgegevens", "EMAIL_V_FAILED": "E-mailverificatie mislukt.", "QUESTIONNAIRES_FETCHED": "Vragenlijsten succesvol opgehaald", "ERROR_CREATING_TOKEN": "Fout bij het aanmaken van token", "ACCESS_TOKEN_CREATED": "Toegangstoken succesvol aangemaakt", "USER_FETCHED": "Gebruiker succesvol opgehaald", "ROLE_FETCHED": "Rol succesvol opgehaald", "SIDEBAR_FETCHED": "Zijbalk succesvol opgehaald", "CUSTOMER_FETCHED": "<PERSON><PERSON> succesvol opgehaald", "EMAIL_ALREADY_EXIST": "E-mail bestaat al", "ASSIGNED": "R<PERSON>oewi<PERSON>zing en -onttrekking succesvol voltooid", "ERROR_RESOURCE_DELETE": "Fout bij het verwij<PERSON><PERSON> van bronnen", "ERROR_RESOURCE_UPDATE": "Fout bij het bijwerken van bronnen", "USER_UPDATED": "Gebruiker succesvol bijgewerkt", "ROLE_EXIST": "Deze rol bestaat al.", "GROUP_FETCHED": "<PERSON><PERSON><PERSON> succesvol opgehaald", "ROLE_NOT_FOUND": "<PERSON><PERSON> niet gevonden", "GROUP_NAME_EXIST": "<PERSON><PERSON><PERSON> met deze naam best<PERSON> al.", "ERROR_CREATING_GROUP": "Fout bij het aanmaken van groep", "GROUP_CREATED": "G<PERSON>ep succesvol aangemaakt", "RESOURCES_NOT_FOUND": "Bronnen niet gevonden", "FORBIDDEN": "Toegang tot de gevraagde bron is verboden", "NO_RESOURCES": "<PERSON><PERSON> bronnen toegewezen aan deze rol", "LANGUAGE_FETCHED": "Taal succesvol opgehaald.", "GROUP_DOES_NOT_EXIST": "<PERSON><PERSON><PERSON> best<PERSON>t niet.", "ENTITY_FETCHED": "Entiteit succesvol opgehaald", "BUSINESS_UNIT_NOT_FOUND": "Bedrijfseenheid niet gevonden", "GROUP_UPDATED": "Groep succesvol bijgewerkt", "UPLOAD_FAILED": "Uploaden mislukt", "TICKET_RAISED": "Ticket succesvol aangemaakt", "TICKET_NOT_FETCHED": "Fout bij het op<PERSON><PERSON> van ticketgegevens", "TICKET_OPENED": "Ticket succesvol geopend", "TICKETS_FETCHED": "Tickets succesvol opgehaald", "ERROR_IN_TICKET_DATA": "Fout bij het op<PERSON><PERSON> van ticketstatistieken", "TICKET_UPDATED": "Ticket succesvol bijgewerkt", "COMMENT_ERROR": "Fout bij het aanmaken van opmerking bij ticket", "COMMENT_SUCCESS": "Succesvol opmerking geplaatst bij ticket", "CUSTOMER_UPDATED": "Klant succesvol bijgewerkt", "ROLE_UPDATE_NOT_ALLOWED": "Rolbijwerking niet toe<PERSON>", "SOMEONE_CONTACT_SOON": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> contact met u opnemen", "DPO_NOT_FOUND": "DPO niet gevonden", "USER_VALIDATED": "Gebruiker succesvol gevalideerd", "ERROR_CREATING_DATA_SUBJECT": "Fout bij het opslaan van informatie van betrokkene", "ERROR_CREATING_REQUEST": "Fout bij het aanmaken van DSR-verzoek", "REQUEST_CREATED": "DSR-verzoek is aangemaakt", "WORKFLOW_NAME_EXIST": "Duplicaat workflow. <PERSON><PERSON> een andere naam", "ERROR_CREATING_WORKFLOW": "Fout bij het aanmaken van workflowtype", "WORKFLOW_CREATED": "Workflow succesvol aangemaakt", "WORKFLOW_DOES_NOT_EXIST": "Workflow bestaat niet", "WORKFLOW_NOT_FOUND": "Workflowlijst kan niet op<PERSON> worden", "WORKFLOW_FETCHED": "Workflow succesvol opgehaald", "WORKFLOW_UPDATED": "Workflow succesvol bijgewerkt", "WORKFLOW_STEP_NAME_EXIST": "Stapnaam bestaat al", "ERROR_CREATING_WORKFLOW_STEP": "Fout bij het aanmaken van workflowstap", "WORKFLOW_STEP_CREATED": "Workflowstap succesvol aangemaakt", "WORKFLOW_STEP_DOES_NOT_EXIST": "Workflowstap bestaat niet", "WORKFLOW_STEP_UPDATED": "Workflowstap succesvol bijgewerkt", "ERROR_DELETING_WORKFLOW_STEP": "Fout bij het verwijderen van workflowstap", "WORKFLOW_STEP_DELETED_SUCC": "Workflowstap succesvol verwijderd", "WORKFLOW_STEP_ALREADY_TOP": "Workflowstap staat al bovenaan", "TASK_NAME_EXIST": "<PERSON><PERSON><PERSON><PERSON> bestaat al", "ERROR_CREATING_TASK": "Fout bij het aanma<PERSON> van taak", "TASK_CREATED": "Taak succesvol aangemaakt", "TASK_DOES_NOT_EXIST": "<PERSON><PERSON> bestaat niet", "TASK_NOT_FOUND": "Taak<PERSON>jst kan niet op<PERSON> worden", "TASK_FETCHED": "Taak succesvol opgehaald", "TASK_UPDATED": "Taak succesvol bijgewerkt", "ERROR_DELETING_TASK": "Fout bij het verwij<PERSON><PERSON> van taak", "TASK_DELETED_SUCC": "Taak succesvol verwijderd", "RISK_MATRIX": "Risicomatrix succesvol opgehaald", "TEMPLATE_NOT_ADDED": "Sjabloon niet toegevo<PERSON>d. Probeer opnieuw", "LIST_FETCHED": "Sjabloonlijst opgehaald", "MITIGATION_COMPLETED": "Mitigatie voltooid", "DATA_SUBJECT_DOES_NOT_EXIST": "Betrokkene bestaat niet", "REQUEST_NOT_FOUND": "Verzoeklijst kan niet opge<PERSON> worden", "REQUEST_FETCHED": "Verzoek succesvol opgehaald", "REQUEST_DOES_NOT_EXIST": "Verzoek bestaat niet", "REQUEST_UPDATED": "Verzoek succesvol bijgewerkt", "REQUEST_TYPE_COUNT": "Aantal verzoeken per type", "REQUEST_STATUS_COUNT": "Aantal verzoeken per status", "FORM_NOT_FOUND": "<PERSON><PERSON><PERSON> bestaat niet", "FORM_CREATE_ERROR": "<PERSON>ulier niet a<PERSON>", "FORM_UPDATE_ERROR": "Formulier niet bijgewerkt", "FORM_CREATED": "<PERSON>ulier succesvol aangemaakt", "FORM_UPDATED": "Formulier succesvol bijgewerkt", "DATA_FETCHED": "Gegevens succesvol opgehaald", "SERVICE_ENTITY_FETCHED": "Service-entiteit succesvol opgehaald", "MAIL_LIST_ERROR": "Fout bij het <PERSON><PERSON><PERSON> met e-mails<PERSON>blonen", "MAIL_LIST_FETCHED": "E-mailsjabloonlijst succesvol opgehaald", "MAIL_CREATE_ERROR": "Fout bij het aanmaken van e-mailsjabloon", "MAIL_UPDATED": "E-mailsjabloon bijgewerkt", "MAIL_DOES_NOT_EXIST": "E-mailsjabloon bestaat niet", "MAIL_DELETE_ERROR": "Fout bij het verwijderen van het e-mailsjabloon", "MAIL_DELETE": "E-mailsjabloon verwijderd", "MAIL_UPDATE_ERROR": "Fout bij het bijwerken van e-mailsjabloon", "MAIL_CREATED": "E-mailsjabloon aangemaakt", "EMAIL_SUCCESS": "E-mail succesvol verzonden", "EMAIL_REQUIRED": "E-mailadres is vereist", "MITIGATION_NOT_COMPLETED": "Mitigatie nog niet voltooid voor leverancier", "MITIGATION_SAVED": "Mitigatie opgeslagen", "EMAIL_NOT_FOUND": "E-mail niet gevonden", "MITIGATION_PLAN_NOT_FILLED": "Vul het mitigatieplan in voor alle controles", "LIST_ERROR": "Fout bij het op<PERSON><PERSON> van lijst", "REGULATION_LIST_FETCHED": "Lijst succesvol opgehaald", "DUPLICATE_WEBHOOK": "Actienaam bestaat al", "WEBHOOK_CREATED": "Webhook succesvol aangemaakt", "ERROR_CREATING_WEBHOOK": "Fout bij het aanmaken van webhook", "WEBHOOK_DELETED_ERROR": "Fout bij het verwij<PERSON><PERSON> van webhook", "WEBHOOK_DELETED": "Webhook verwijderd", "WEBHOOK_NOT_EXIST": "Webhook bestaat niet", "AUDIT_DOWNLOADED": "Audit succesvol gedownload", "DATASUBJECT_NOT_MATCHED": "<PERSON><PERSON><PERSON><PERSON> van betrok<PERSON>e komen niet overeen", "DSRINFO_DOES_NOT_EXIST": "Uw informatie bestaat niet in onze registratie", "OCR_NOT_MATTACHED": "Uw identiteitsgegevens komen niet overeen in de verstrekte documenten", "ALREADY_EXIST_TEMPLATE": "Sjabloon bestaat al, kan niet bijwerken", "WORKFLOW_ID_REQUIRED": "Workflow-id is vereist", "UNSTRUCTURED_DATA_MANAGEMENT_FETCHED": "Ongestructure<PERSON> g<PERSON><PERSON>r succesvol opgehaald", "DOCUMENT_LIST": "Documentenlijst succesvol opgehaald", "FILE_FORMAT": "Bestandsformaat succesvol opgehaald", "SUMMARY_FETCHED": "Samenvatting succesvol opgehaald", "LOCATION_FETCHED": "Locatie succesvol opgehaald", "VIEW_DETAILS_FETCHED": "Weergavedetails succesvol opgehaald", "ELEMENT_CATEGORIES_FETCHED": "Elementcategorieën succesvol opgehaald", "DATA_MANAGEMENT_FETCHED": "Gegevensbeheer succesvol opgehaald", "ELEMENT_TYPES_FETCHED": "Elementtypen succesvol opgehaald", "STRUCTURED_VIEW_LOCATION": "Gestructureerde weergavelocatie succesvol opgehaald", "PROFILER_META_FETCHED": "Profiler-metadata succesvol opgehaald", "ERROR_ADDING_ACTION": "<PERSON><PERSON> niet toe<PERSON>d", "ACTION_ADDED": "<PERSON><PERSON> succesvol toegevoegd", "ACTION_LIST_FETCHED": "Actielijst succesvol opgehaald", "ACTION_NOT_FOUND": "<PERSON><PERSON> niet gevonden", "ACTION_FETCHED": "<PERSON>ie succesvol opgehaald", "ERROR_CREATING_DUTY": "Fout bij het aanma<PERSON> van p<PERSON>t", "DUTY_CREATED": "Plicht succesvol aangemaakt", "DUTY_NOT_FOUND": "<PERSON><PERSON>t niet gevonden", "DUTY_UPDATED": "Plicht succesvol bijgewerkt", "STRUCTURED_SENSITIVITY_FETCHED": "Gestructureerde gevoeligheidsgegevens opgehaald", "UNSTRUCTURED_SENSITIVITY_FETCHED": "Ongestructureerde gevoeligheidsgegevens opgehaald", "RESOURCE_LIST_NOT_FOUND": "Resourcelijst niet gevonden", "RESOURCES_ADDED": "Resource succesvol toegevoegd", "ERROR_CREATING_IMPROVEMENT": "Fout bij het aanmaken van verbetering", "IMPROVEMENT_CREATED": "Verbetering succesvol aangemaakt", "IMPROVEMENT_UPDATED": "Verbetering succesvol bijgewerkt", "IMPROVEMENT_NOT_FOUND": "Verbetering niet gevonden", "CATEGORY_NOT_FOUND": "Categorie niet gevonden", "CATEGORY_CREATE_ERROR": "Fout bij het aanmaken van categorie", "CATEGORY_FETCHED": "Categorie succesvol opgehaald", "ERROR_DELETING_FORM": "Fout bij het verwij<PERSON><PERSON> van formulier", "FORM_DELETED": "Formulier succesvol verwijderd", "ERROR_DELETING_CATEGORY": "Fout bij het verwijderen van categorieën", "TASK_STATUS": "<PERSON>aks<PERSON><PERSON> is vereist", "FORM_FETCHED": "<PERSON>ulier succesvol opgehaald", "FORM_PUBLISHED": "Formulier succesvol gepubliceerd", "FORM_NOT_PUBLISHED": "Fout bij het publicere<PERSON> van formulier", "FORM_ALREADY_PUBLISHED": "<PERSON><PERSON><PERSON> best<PERSON> al", "DATA_UPDATED": "Gegevens succesvol bijgewerkt", "DATA_DELETED": "Gegevens succesvol verwijderd", "DATA_CREATED": "Gegevens succesvol aangemaakt", "ERROR_CREATING_INCIDENT": "Fout bij het aanmaken van incident", "INCIDENT_CREATED": "Incident aangemaakt", "INCIDENT_NOT_FOUND": "Incidentenlijst kan niet worden opgehaald", "ASSESSMENT_NOT_FOUND": "Beoordeling bestaat niet", "ASSESSMENT_NOT_ASSIGNED": "Beoordeling is niet toege<PERSON>zen", "ASSESSMENT_STARTED": "Beoordeling gestart", "ASSESSMENT_UNDER_REVIEW": "Beoordeling wordt beoordeeld", "ASSESSMENT_COMPLETED": "Beoordeling voltooid", "ASSESSMENT_DOWNLOADED": "Beoordeling succesvol naar e-mail verzonden", "ASSESSMENT_ASSIGNED": "Beoordeling succesvol toegewezen", "ASSESSMENT_REVIEWER_ADDED": "<PERSON><PERSON><PERSON><PERSON> van beoordel<PERSON> toegevoegd", "ALREADY_VERIFIED": "E-mail al geverifieerd", "FORM_VERIFIED": "Formulier succesvol geverifieerd", "DUPLICATE_QUESTION_TITLE": "Sommige vragen komen meer dan eens voor", "ERROR_CREATING_DASHBOARD": "Fout bij het aanmaken van dashboard", "DASHBOARD_CREATED": "Dashboard succesvol aangemaakt", "DASHBOARD_NOT_FOUND": "Dashboard niet gevonden", "DASHBOARD_UPDATED": "Dashboard succesvol bijgewerkt", "DASHBOARD_DELETED": "Dashboard succesvol verwijderd", "DELETE_ERROR": "Fout bij het verwijderen van element", "DASHBOARD_ALREADY_EXIST": "Dashboard bestaat al", "CANNOT_DELETE_DEPARTMENT": "Een afdeling kan niet worden verwijderd als deze een ROPA, proces of onderafdeling heeft", "CANNOT_DELETE_PROCESS": "Een proces kan niet worden verwijderd als het een ROPA of subproces heeft", "DEPARTMENT_DELETED": "Afdeling succesvol verwijderd", "PROCESS_DELETED": "Proces succesvol verwijderd", "GROUP_DELETED": "G<PERSON><PERSON> succesvol verwijderd", "CANNOT_DELETE_GROUP": "Een groep kan niet worden verwijderd als deze een afdeling of subgroep heeft", "ERROR_DELETING_GROUP_USER": "Fout bij het verwijderen van groepsgebruiker", "ERROR_DELETING_CUSTOMER_REGULATIONS": "Fout bij het verwijderen van klantregelgeving", "ERROR_DELETING_CUSTOMER_BUSINESS_REQUIREMENTS": "Fout bij het verwijderen van zakelijke vereisten van klant", "REQUEST_ARCHIVED": "Verzoek succesvol gearchiveerd", "REQUEST_UNARCHIVED": "Verzoek succesvol uit archief gehaald", "DSR_REQUEST_ARCHIVING_FAILED": "DSR-verzoek archiveren mislukt", "REQUEST_ALREADY_ARCHIVED": "Verzoek al gearchiveerd", "MISSING_REQUIRED_FIELDS": "Vereiste velden ontbreken", "TASK_CREATION_FAILED": "Taak aanma<PERSON> mislukt", "TASK_UPDATED_SUCCESSFULLY": "Taak succesvol bijgewerkt", "TASK_UPDATE_FAILED": "Taak bijwerken mislukt", "PRIVACY_NOTICE_GENERATION_FAILED": "<PERSON><PERSON><PERSON> van privacyverklaring mislukt", "PRIVACY_NOTICE_GENERATED": "Privacyverklaring succesvol gegenereerd", "GENERATE_NOTICE_STARTED": "<PERSON><PERSON><PERSON> van privacyverklaring gestart", "PRIVACY_NOTICE_NOT_FOUND": "Privacyverklaring niet gevonden", "FAILED_TO_UPDATE_URL": "URL bijwerken mislukt", "USER_ALREADY_LOGGED_IN": "Gebruiker is al ingelogd", "PASSWORD_NOT_UPDATED": "Wachtwoord niet bijgewerkt", "CREDENTIALS_SENT": "Inloggegevens succesvol verzonden", "FORM_VERSION_CREATED": "Formulierversie succesvol aangemaakt", "VENDOR_LIST_NOT_FOUND": "Leverancierslijst niet gevonden", "ACTION_ALREADY_EXIST": "<PERSON><PERSON> best<PERSON> al", "ADD_DATA_BREACH_NOTIFICATION": "Datalekmelding toevoegen", "ALERT_SENT": "Waarschuwing verzonden", "AMBITION_CREATED": "Ambitie succesvol aangemaakt", "ANSWER_CREATION_FAILED": "Antwoord aanmaken mislukt", "ANSWER_NOT_SAVED": "Antwoord niet opgeslagen", "ASSESSMENT_ALREADY_EXIST": "Beoordeling bestaat al", "ASSESSMENT_NOT_ADDED": "Beoordeling niet succesvol toegevoegd", "ASSESSMENT_NOT_COMPLETED": "Beoordeling niet succesvol voltooid", "ASSESSMENT_NOT_STARTED": "Beoordeling niet succesvol gestart", "ASSESSMENT_NOT_SUBMITTED": "Be<PERSON><PERSON>ing niet ingediend", "ASSESSMENT_REVIEWED": "Beoordeling succesvol beoordeeld", "ASSESSMENT_SUBMITTED": "Beoordeling succesvol ingediend", "AUDIT_LOG_NOT_FOUND": "Auditlog niet gevonden", "BILLING_ADDRESS": "Factu<PERSON><PERSON><PERSON>", "BILLING_ADDRESS_CREATE_ERROR": "Factuuradres a<PERSON> fout", "BLOGS_FETCHED": "Blogs succesvol opgehaald", "BLOG_CATEGORIES_FETCHED": "Blogcategorieën succesvol opgehaald", "BLOG_FETCHED": "Blog succesvol opgehaald", "BUSINESS_REQUIREMENTS": "Bedrijfsvereisten", "BUSINESS_REQUIREMENTS_ADDED": "Bedrijfsvereisten succesvol toegevoegd", "BUSINESS_REQUIREMENTS_UPDATED": "Bedrijfsvereisten succesvol bijgewerkt", "CATEGORY_ADDED": "Categorie succesvol toegevoegd", "CATEGORY_CREATION_FAILED": "Categorie aanmaken mislukt", "CLIENT_DOES_NOT_EXIST": "<PERSON><PERSON> best<PERSON>t niet", "CLIENT_REGISTRATION_SUCCESSFUL": "Klantregistratie succesvol voltooid", "CONTROL_CREATION_FAILED": "Controle aanmaken mislukt", "CONTROL_FOUND": "<PERSON>e gevonden", "CONTROL_NOT_FOUND": "<PERSON>e niet gevonden", "CONTROL_NOT_UPDATED": "Controle niet succesvol bijgewerkt", "CREATE_ADD_ACTION": "<PERSON>ie a<PERSON> toe<PERSON>n", "CUSTOMER_ASSESSMENTS_NOT_FOUND": "Klantbeoordelingen niet gevonden", "CUSTOMER_CONTROL_CREATION_FAILED": "Klantcontrole aanmaken mislukt", "CUSTOM_REGULATION_ADDED": "Aangepaste regelgeving succesvol toegevoegd", "DASHBOARD_BREACH_COUNT": "Dashboard lektellingen succesvol opgehaald", "DASHBOARD_BREACH_STATUS_DISTRIBUTION": "Dashboard lekstatus verdeling", "DASHBOARD_SEVERITY_DISTRIBUTION": "Dashboard ernst verdeling", "DATA_FECTHED": "<PERSON><PERSON><PERSON><PERSON>", "DATA_SUBJECT_NOT_FOUND": "Betrokkene niet gevonden", "DEPARTMENT_UPDATED": "Afdeling succesvol bijgewerkt", "DOCUMENTS_FETCHED": "Documenten succesvol opgehaald", "DOMAIN_NAME_ALREADY_EXIST": "<PERSON><PERSON><PERSON><PERSON> bestaat al", "DSR_REQUEST_NOT_FOUND": "DSR-verzoek niet gevonden", "EMAIL_EXISTS": "E-mail bestaat al", "EMAIL_NOT_EXIST": "E-mail bestaat niet", "EMBEDDINGS_CREATED": "Embeddings succesvol aangemaakt", "ENTITY_NOT_FOUND": "Entiteit niet gevonden", "ERROR_CREATE_AMBITION": "Fout: ambitie a<PERSON>", "ERROR_CREATE_QUESTIONNAIRES": "Fout: vragenlijsten aanmaken", "ERROR_CREATE_ROLE": "Fout: rol a<PERSON>", "ERROR_CREATE_SERVICE": "Fout: service aanmaken", "ERROR_CREATING_AUDIT_LOG": "Fout: auditlog aanmaken", "ERROR_CREATING_BREACH_NOTIFICATION": "Fout: lekmelding aanmaken", "ERROR_CREATING_RISK": "Fout: risico a<PERSON>", "ERROR_UPDATING_MASTER_CONTROL": "Fout: hoofdcontrole bijwerken", "ERROR_WHILE_DELETE_TOKEN": "Fout: tij<PERSON><PERSON> token verwijderen", "FILE_NOT_IN_CORRECT_FORMAT": "<PERSON><PERSON> niet in juiste formaat", "FILE_REQUIRED": "<PERSON><PERSON> vereist", "FORM_DOES_NOT_EXIST": "<PERSON><PERSON><PERSON> bestaat niet", "GENERATE_NOTICE_START": "Kennisgeving genereren start", "GET_ACTION_LISTING": "Actielijst ophalen", "GROUP_NOT_FOUND": "<PERSON><PERSON><PERSON> niet gevonden", "INCIDENT_DOES_NOT_EXIST": "Incident bestaat niet", "INCIDENT_UPDATED": "Incident succesvol bijgewerkt", "INTERNAL_ASSESSMENT_ERROR": "Interne beoordelingsfout", "INVALID_HEADER": "Ongeldige header", "IP_LOOKUP_SUCCESS": "IP-opzoeken succesvol voltooid", "LIA_REVIEWED": "LIA succesvol beoordeeld", "LIST_NOT_FOUND": "<PERSON><PERSON>st niet gevonden", "MARKED_READ": "Gemarkeerd als gelezen", "NEW_FORM_VERSION_PUBLISHED": "Nieuwe formulierversie gepubliceerd", "NOT_PUBLISHED": "<PERSON><PERSON>", "NO_DATA_FOUND": "<PERSON><PERSON> g<PERSON> gevonden", "NO_GROUPS_FOUND": "<PERSON><PERSON> gro<PERSON>en gevonden", "OK": "<PERSON><PERSON>", "ONBOARDING_COMPLETED": "Onboarding succesvol voltooid", "ORGANISATION_CREATED": "Organisatie succesvol aangemaakt", "ORGANISATION_NOT_FOUND": "Organisatie niet gevonden", "ORGANISATION_UPDATE_ERROR": "Organisatie bijwerken fout", "OTP_NOT_GENERATE": "OTP niet gege<PERSON>d", "OTP_NOT_VERIFIED": "OTP niet g<PERSON>", "PACKAGES_FETCHED": "Pak<PERSON><PERSON> succesvol opgehaald", "PACKAGE_DOES_NOT_EXIST": "Pak<PERSON> bestaat niet", "PARSING_ERROR": "<PERSON>rserf<PERSON>", "PASSWORD_NOT_MATCH": "Wachtwoord komt niet overeen", "PASSWORD_UPDATE_ERROR": "Wachtwoord bijwerken fout", "PDA_NOT_ASSIGNED": "PDA niet succesvol toegewezen", "PDA_REVIEWED": "PDA succesvol beoordeeld", "PENDING_REQUEST_COUNT": "Aantal openstaande verzoeken succesvol opgehaald", "PIA_REVIEWED": "PIA succesvol beoordeeld", "POLICIES_FETCHED": "Beleid succesvol opgehaald", "POLICIES_NOT_FOUND": "Beleid niet gevonden", "PRIVACY_NOTICES_FETCHED": "Privacyverklaringen succesvol opgehaald", "PRIVACY_NOTICES_NOT_FOUND": "Privacyverklaringen niet gevonden", "PRIVACY_NOTICE_CREATED": "Privacyverklaring succesvol aangemaakt", "PRIVACY_NOTICE_NOT_CREATED": "Privacyverklaring niet succesvol aangemaakt", "PRIVACY_NOTICE_NOT_UPDATE": "Privacyverklaring niet bijgewerkt", "PRIVACY_NOTICE_UPDATE": "Privacyverklaring bijgewerkt", "PROCESSES_CREATED_SUCCESSFULLY": "Processen succesvol aangemaakt", "PROCESS_CREATION_FAILED": "Proces aan<PERSON>ken mislukt", "REGULATION_NOT_FOUND": "Regelgeving niet gevonden", "REQUIREMENT_UPDATED": "Vereiste succesvol bijgewerkt", "REVIEW_CREATED_OR_UPDATED": "Beoordeling succesvol aangemaakt of bijgewerkt", "RISK_CREATED": "Risico succesvol aangemaakt", "RISK_NOT_FOUND": "Risico niet gevonden", "RISK_UPDATED": "Risico succesvol bijgewerkt", "ROPA_ALREADY_STARTED": "ROPA al succesvol gestart", "ROPA_CREATION_FAILED": "ROPA aanmaken mislukt", "ROPA_REVIEWED": "ROPA succesvol beoordeeld", "ROPA_UPDATE_FAILED": "ROPA bijwerken mislukt", "SERVER_ERROR": "Serverfout", "SERVICE_FETCHED": "Service succesvol opgehaald", "SOME_AMBITIONS_DO_NOT_EXIST": "Sommige ambities bestaan niet", "SOME_QUESTIONNAIRES_DO_NOT_EXIST": "Sommige vragenlijsten bestaan niet", "SOME_SERVICES_DO_NOT_EXIST": "Sommige services bestaan niet", "STEP_NOT_FOUND": "Stap niet gevonden", "STREAM_ERROR": "Streamfout", "SUBPROCESS_CREATION_FAILED": "Subproces aanmaken mislukt", "TASK_FOUND": "<PERSON>ak gevonden", "TEMPLATE_ADDED": "Sjabloon succesvol toegevoegd", "TEMPLATE_CREATION_FAILED": "Sjabloon aanmaken mislukt", "TEMPLATE_PUBLISHED": "Sjabloon gepubliceerd", "TIA_REVIEWED": "TIA succesvol beoordeeld", "TRANSACTION_FAILED": "Transactie mislukt", "TYPES_FETCHED": "Typen succesvol opgehaald", "TYPES_NOT_FOUND": "<PERSON>n niet gevonden", "UPDATE_TIA_FAILED": "TIA bijwerken mislukt", "UPDATE_VEA_FAILED": "VEA bijwerken mislukt", "UPDATE_VIA_FAILED": "VIA bijwerken mislukt", "USERS_NOT_FOUND": "Gebruikers niet gevonden", "VEA_NOT_ASSIGNED": "VEA niet succesvol toegewezen", "VEA_REVIEWED": "VEA succesvol beoordeeld", "VENDOR_ASSESSMENTS_NOT_FOUND": "Leveranciersbeoordelingen niet gevonden", "VENDOR_DETAILS_NOT_FOUND": "Leveranciersdetails niet gevonden", "REGULATION_ADDED": "Regelgeving succesvol toegevoegd", "REGULATION_UPDATED": "Regelgeving succesvol bijgewerkt", "RESOURCES_FETCHED": "Bronnen succesvol opgehaald", "ROLE_UPDATED": "Rol succesvol bijgewerkt", "ROPA_UPDATED": "ROPA succesvol bijgewerkt", "SAVE_ERROR": "Fout bij op<PERSON><PERSON> van <PERSON>", "VIA_REVIEWED": "VIA succesvol beoordeeld", "DPO_ROLE_EXIST": "'Functionaris voor Gegevensbescherming' kan niet worden aangemaakt, kies een andere rol<PERSON>am", "PROGRESS_FETCHED": "Voortgang succesvol opgehaald", "ROPA_CREATED": "ROPA succesvol aangemaakt"}