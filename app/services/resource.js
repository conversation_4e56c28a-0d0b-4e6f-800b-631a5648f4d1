/**
 * Retrieves a list of records from a model with an associated model without counting the total number of records.
 * @param {Object} model - The main model to retrieve records from.
 * @param {Object} model1 - The associated model.
 * @param {Object} condition - The condition to filter the records.
 * @param {Object} condition1 - The condition to filter the associated model records.
 * @param {Array} attributes - The attributes to include in the result.
 * @param {Array} attributes1 - The attributes to include in the result of the associated model.
 * @param {number} limit - The maximum number of records to retrieve.
 * @param {number} offset - The number of records to skip.
 * @param {Array} order - The order in which the records should be sorted.
 * @returns {Promise<Array|boolean>} - The list of records or false if an error occurs.
 */
exports.getCustomerResources = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
    try {
        let list = await model.findAll({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: {
                model: model1,
                ...condition1 !== undefined && {
                    where: condition1
                },
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                required: true
            },
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },

        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('erro', error);
        return false
    }
};