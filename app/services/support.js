/**
 * Retrieves a list of records from a model with two associated models, with pagination and sorting options.
 * @param {Object} model - The main model to retrieve records from.
 * @param {Object} model1 - The first associated model.
 * @param {Object} model2 - The second associated model.
 * @param {Object} condition - The condition to filter the records.
 * @param {Object} condition1 - The condition to filter the first associated model records.
 * @param {Object} condition2 - The condition to filter the second associated model records.
 * @param {Array} attributes - The attributes to include in the result.
 * @param {Array} attributes1 - The attributes to include in the result of the first associated model.
 * @param {Array} attributes2 - The attributes to include in the result of the second associated model.
 * @param {number} limit - The maximum number of records to retrieve.
 * @param {number} offset - The number of records to skip.
 * @param {Array} order - The order in which the records should be sorted.
 * @returns {Promise<Array|boolean>} - The list of records or false if an error occurs.
 */

const Sequelize = require('sequelize');

exports.getListWithMultipleAssociate = async (model, model1, model2,alias2 , model3, alias3 ,model4 , condition, condition1, condition2, condition3, condition4 , attributes, attributes1, attributes2, attributes3, attributes4, limit, offset, order) => {
    try {
      let list = await model.findAndCountAll({
        ...condition !== undefined && {
          where: condition
        },
        ...attributes !== undefined && {
          attributes
        },
        include: [
          {
            model: model1,
            ...condition1 !== undefined && {
              where: condition1
            },
            ...attributes1 !== undefined && {
              attributes: attributes1
            },
            required: false
          },
          {
            model: model2,
            as: alias2,
            ...condition2 !== undefined && {
              where: condition2
            },
            ...attributes2 !== undefined && {
              attributes: attributes2
            },
            required: false
          },
          {
            model: model3,
            as: alias3,
            ...condition3 !== undefined && {
              where: condition3
            },
            ...attributes3 !== undefined && {
              attributes: attributes3
            },
            required: false
          },
          {
            model: model4,
            ...condition4 !== undefined && {
              where: condition4
            },
            ...attributes4 !== undefined && {
              attributes: attributes4
            },
            required: false
          },
        //   // Additional includes for reporter and assignee
        //   {
        //     model: model2, // Assuming User model is imported
        //     as: 'Reporter',
        //     attributes: ['id', 'firstName', 'lastName'],
        //     required: false,
        //     ...condition2 !== undefined && { where: { id: Sequelize.col(`ticket.reporter_id`) } } // Assuming 'Ticket' is the alias for the model
        // },
        // {
        //     model: model2, // Assuming User model is imported
        //     as: 'Assignee',
        //     attributes: ['id', 'firstName', 'lastName'],
        //     required: false,
        //     ...condition2 !== undefined && { where: { id: Sequelize.col(`ticketks.assignee_id`) } } // Assuming 'Ticket' is the alias for the model
        // },
        ],
        ...limit !== undefined && {
          limit
        },
        ...offset !== undefined && {
          offset
        },
        ...order !== undefined && {
          order
        },
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;
  
    } catch (error) {
      console.log("eeeeeeee", error)
      return false
    }
  };


  // exports.getListGroupBy = async (model, condition, attributes, groupBy) => {
  //   try {
  //     let list = await model.findAll({
  //       where: condition,
  //       attributes: attributes ? attributes : ['status', [Sequelize.fn('COUNT', 'id'), 'count']],
  //       group: groupBy ? groupBy : ['status']
  //     });
  //     return list ? JSON.parse(JSON.stringify(list)) : false;
  //   } catch (error) {
  //     console.log('error', error);
  //     return false;
  //   }
  // };
  exports.getListGroupBy = async (model, condition, attributes, groupBy) => {
    try {
      let list = await model.findAll({
        where: condition,
        attributes: [`${attributes}`, [Sequelize.fn('COUNT', 'id'), 'count']],
        group: groupBy
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
      console.log('error', error);
      return false;
    }
  };

  /**
 * Retrieves a list of records from a model with two associated models, with pagination and sorting options.
 * @param {Object} model - The main model to retrieve records from.
 * @param {Object} model1 - The first associated model.
 * @param {Object} model2 - The second associated model.
 * @param {Object} condition - The condition to filter the records.
 * @param {Object} condition1 - The condition to filter the first associated model records.
 * @param {Object} condition2 - The condition to filter the second associated model records.
 * @param {Array} attributes - The attributes to include in the result.
 * @param {Array} attributes1 - The attributes to include in the result of the first associated model.
 * @param {Array} attributes2 - The attributes to include in the result of the second associated model.
 * @param {number} limit - The maximum number of records to retrieve.
 * @param {number} offset - The number of records to skip.
 * @param {Array} order - The order in which the records should be sorted.
 * @returns {Promise<Array|boolean>} - The list of records or false if an error occurs.
 */

exports.getTicketData = async (model, model1,alias1 , model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
    try {
      let list = await model.findAndCountAll({
        ...condition !== undefined && {
          where: condition
        },
        ...attributes !== undefined && {
          attributes
        },
        include: [
          {
            model: model1,
            as: alias1,
            ...condition1 !== undefined && {
              where: condition1
            },
            ...attributes1 !== undefined && {
              attributes: attributes1
            },
            required: false
          },
          {
            model: model2,
            ...condition2 !== undefined && {
              where: condition2
            },
            ...attributes2 !== undefined && {
              attributes: attributes2
            },
            required: false
          },
        ],
        ...limit !== undefined && {
          limit
        },
        ...offset !== undefined && {
          offset
        },
        ...order !== undefined && {
          order
        },
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;
  
    } catch (error) {
      console.log("eeeeeeee", error)
      return false
    }
  };