const sequelize = require('sequelize');

/**
 * Retrieves a list of records from a model with pagination and sorting options.
 * @param {Object} model - The model to retrieve records from.
 * @param {Object} condition - The condition to filter the records.
 * @param {Array} attributes - The attributes to include in the result.
 * @param {number} limit - The maximum number of records to retrieve.
 * @param {number} offset - The number of records to skip.
 * @param {Array} order - The order in which the records should be sorted.
 * @returns {Promise<Array|boolean>} - The list of records or false if an error occurs.
 */

exports.getOneRecord = async (model, condition, attributes, limit, offset, order) => {
  try {
    const list = await model.findOne({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      }

    });
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    return false;
  }
};

exports.getAllRecord = async (model, condition, attributes, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },

    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getThreeNestedDataById = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findOne({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: {
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false,
        include: [
          {
            model: model2,
            ...condition2 !== undefined && {
              where: condition2
            },
            ...attributes2 !== undefined && {
              attributes: attributes2
            },
            required: false
          },
        ]
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getMultiAssocData = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, limit, offset, order) => {
  try {
      const list = await model.findAndCountAll({
          attributes: attributes,
          where: condition,
          include: [{
              model: model1,
              ...attributes1 !== undefined && {
                  attributes: attributes1
              },
              ...condition1 !== undefined && {
                  where: condition1
              },
              required: true
          },
          {
              model: model2,
              attributes: attributes2,
              ...condition2 !== undefined && {
                  where: condition2
              },
              include: [{
                  model: model3,
                  // as: 'answered_by',
                  attributes: attributes3,
                  ...condition3 !== undefined && {
                      where: condition3
                  },
                  required: true
              }],
              required: true
          },
          {
            model: model4,
            attributes: attributes4,
            ...condition4 !== undefined && {
                where: condition4
            },
            required: false
          }
          
          ],
          ...limit !== undefined && {
            limit
          },
          ...offset !== undefined && {
            offset
          },
          ...order !== undefined && {
            order
          },
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
      console.log('error', error);
      return false
  }
}

exports.getMultiAssocDataById = async (model, model1, model2, model3, model4, model5, condition, condition1, condition2, condition3, condition4, condition5, attributes, attributes1, attributes2, attributes3, attributes4, attributes5, limit, offset, order) => {
  try {
      const list = await model.findOne({
          attributes: attributes,
          where: condition,
          include: [{
              model: model1,
              ...attributes1 !== undefined && {
                  attributes: attributes1
              },
              ...condition1 !== undefined && {
                  where: condition1
              },
              required: false
          },
          {
              model: model2,
              attributes: attributes2,
              ...condition2 !== undefined && {
                  where: condition2
              },
              include: [{
                  model: model3,
                  // as: 'answered_by',
                  attributes: attributes3,
                  ...condition3 !== undefined && {
                      where: condition3
                  },
                  required: false
              }],
              required: false
          },
          {
            model: model4,
            ...attributes1 !== undefined && {
                attributes: attributes4
            },
            ...condition1 !== undefined && {
                where: condition4
            },
            required: false
          },
          {
            model: model5,
            ...attributes1 !== undefined && {
                attributes: attributes5
            },
            ...condition1 !== undefined && {
                where: condition5
            },
            required: false
          },
          
          ],
          ...limit !== undefined && {
            limit
          },
          ...offset !== undefined && {
            offset
          },
          ...order !== undefined && {
            order
          },
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
      console.log('error', error);
      return false
  }
}



exports.getMultiAssocDataByIdV2 = async (model, model1, model2, model3, model4, model5, condition, condition1, condition2, condition3, condition4, condition5, attributes, attributes1, attributes2, attributes3, attributes4, attributes5, limit, offset, order) => {
  try {
      const list = await model.findOne({
          attributes: attributes,
          where: condition,
          include: [{
              model: model1,
              ...attributes1 !== undefined && {
                  attributes: attributes1
              },
              ...condition1 !== undefined && {
                  where: condition1
              },
              required: false
          },
          {
              model: model2,
              attributes: attributes2,
              ...condition2 !== undefined && {
                  where: condition2
              },
              // include: [{
              //     model: model3,
              //     // as: 'answered_by',
              //     attributes: attributes3,
              //     ...condition3 !== undefined && {
              //         where: condition3
              //     },
              //     required: false
              // }],
              // required: false
          },
          {
            model: model4,
            ...attributes1 !== undefined && {
                attributes: attributes4
            },
            ...condition1 !== undefined && {
                where: condition4
            },
            required: false
          },
          {
            model: model5,
            ...attributes1 !== undefined && {
                attributes: attributes5
            },
            ...condition1 !== undefined && {
                where: condition5
            },
            required: false
          },
          {
            model: model3,
            ...attributes3 !== undefined && {
                attribute3: attributes3
            },
            ...condition3 !== undefined && {
                where: condition3
            },
            required: false
          },
          
          ],
          ...limit !== undefined && {
            limit
          },
          ...offset !== undefined && {
            offset
          },
          ...order !== undefined && {
            order
          },
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
      console.log('error', error);
      return false
  }
}




exports.getSingleLeftJoinData = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    let list = await model.findOne({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: {
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getSingleLeftJoinDataThree = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findOne({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [{
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      },
      {
        model: model2,
        ...condition2 !== undefined && {
            where: condition2
        },
        attributes2: attributes2,
        required: false
      }
    
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getMultiLeftJoinData = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [{
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      }],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getMultiLeftJoinData2 = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [{
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      },
      {
        model: model2,
        ...condition2 !== undefined && {
            where: condition2
        },
        ...attributes2 !== undefined && {
          attributes: attributes2
        },
        required: false
      }
    
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};


exports.getMultiLeftJoinWithoutCount = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [{
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      }],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getMultiLeftJoinWithoutCountAndGroupBy = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order , groupBy) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      group: groupBy ? groupBy : ['DSRAuditLog.id', 'DSRAuditLog.action', 'DSRAuditLog.step', 'DSRAuditLog.createdAt', 'User.firstName', 'User.lastName'],
      include: [{
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      }],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};


// exports.getMultiLeftJoinWithoutCountAndGroupBy = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order, groupBy) => {
//   try {
//     // Determine attributes to include, ensuring compatibility with GROUP BY
//     const mainModelAttributes = attributes || groupBy || ['step']; // Fallback to grouped columns if no attributes provided
//     const associationAttributes = attributes1 || []; // Default to empty if no attributes for associated model
//     const groupAttributes = groupBy || ['step']; // Default groupBy to 'step'

//     // Merge all attributes into the GROUP BY clause
//     const allGroupAttributes = [
//       ...groupAttributes,
//       ...(attributes ? attributes : []), // Include additional selected attributes
//     ];

//     const list = await model.findAll({
//       where: condition || undefined,
//       attributes: mainModelAttributes, // Attributes for the main model
//       group: allGroupAttributes, // Ensure all selected columns are grouped
//       include: [{
//         model: model1,
//         where: condition1 || undefined,
//         attributes: associationAttributes, // Attributes for the associated model
//         required: false,
//       }],
//       limit: limit || undefined,
//       offset: offset || undefined,
//       order: order || undefined,
//     });

//     return list ? JSON.parse(JSON.stringify(list)) : false;

//   } catch (error) {
//     console.error('Error in getMultiLeftJoinWithoutCountAndGroupBy:', error);
//     return false;
//   }
// };


exports.getMultiLeftJoinWithGroupByAndAggregation = async (model, model1, condition, condition1, attributes, attributes1, groupBy) => {
  try {
    const list = await model.findAll({
      ...condition && { where: condition },
      attributes: [
        'step',
        // [model.sequelize.fn('COUNT', '*'), 'step_count'],
        [
          model.sequelize.fn(
            'JSON_AGG',
            model.sequelize.fn(
              'JSON_BUILD_OBJECT',
              'id',
              model.sequelize.col('DSRAuditLog.id'),
              'action',
              model.sequelize.col('DSRAuditLog.action'),
              'createdAt',
              model.sequelize.col('DSRAuditLog.createdAt'),
              'User',
              model.sequelize.fn(
                'JSON_BUILD_OBJECT',
                'firstName',
                model.sequelize.col('User.firstName'),
                'lastName',
                model.sequelize.col('User.lastName')
              )
            )
          ),
          'grouped_data'
        ]
      ],
      include: [
        {
          model: model1,
          as: 'User',
          ...condition1 && { where: condition1 },
          attributes: [], // Attributes are not needed because they are included in the aggregation
          required: false,
        }
      ],
      ...groupBy && { group: groupBy },
    });

    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
};



exports.dsrTaskListing = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      
      include: [{
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false
      },
      {
        model: model2,
        ...condition2 !== undefined && {
          where: condition2
        },
        ...attributes2 !== undefined && {
          attributes: attributes2
        },
        required: false
      },
      {
        model: model3,
        ...condition3 !== undefined && {
          where: condition3
        },
        ...attributes3 !== undefined && {
          attributes: attributes3
        },
        required: false
      },
    
    ],
      
      
      
      
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getSentEmails = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [{
          model: model1,
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false,
        },
        {
          model: model2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false,
        },
        
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.bulkUpdate = async (model, condition, updateData, transaction) => {
  try {
    // Perform the update based on the condition, decrementing the 'order' field by 1 using Sequelize.literal
    const result = await model.update(updateData, {
      where: condition, // Condition for records to be updated (order > deletedOrder)
      transaction, // Ensure the update runs within the provided transaction
      returning: true // Returning the updated records to check the result
    });

    // Log the result to check the number of rows affected
    console.log("Number of rows affected:", result[0]);

    // Return true if at least one row was affected
    return result[0] > 0;
  } catch (error) {
    console.log("Error in bulkUpdate:", error);
    return false; // Return false if an error occurs
  }
};

exports.getSingleCrossJoinData = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    let list = await model.findOne({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: {
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: true
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};


