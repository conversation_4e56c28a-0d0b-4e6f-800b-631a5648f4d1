const fs = require('fs');
const readline = require('readline');


/**
 * Retrieves a list of departments with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of departments with their associated models, or false if an error occurs.
 */


exports.getVendorList = async (model, model1, model2, model3, model4, model5, condition, condition1, condition2, condition3, condition4, condition5, attributes, attributes1, attributes2, attributes3, attributes4, attributes5, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,//VendorDetail
                    include: [
                        {
                            model: model3,//User
                            as: 'Created',
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                            required: false
                        },
                        {
                            model: model3,//User
                            as: 'Updated',
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3,
                            },
                            required: false
                        },
                        {
                            model: model3, // User
                            as: 'Assign',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model3, // User
                            as: 'Review',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model4,//Department
                            attributes: attributes4,
                            ...condition4 !== undefined && {
                                where: condition4
                            },
                            required: false
                        },
                        {
                            model: model5,//Group
                            attributes: attributes5,
                            ...condition5 !== undefined && {
                                where: condition5
                            },
                            required: false
                        }
                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: true
                },
                {
                    model: model2,//Customer
                    as: 'Customer',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                },
                {
                    model: model2,//Customer
                    as: 'Vendor',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                },


            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            }
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}


exports.getVendorsDetails = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,//VendorDetail
                    include: [
                        {
                            model: model2,//User
                            as: 'Created',
                            attributes: attributes2,
                            ...condition2 !== undefined && {
                                where: condition2
                            },
                            required: false
                        },
                        {
                            model: model2,//User
                            as: 'Updated',
                            attributes: attributes2,
                            ...condition2 !== undefined && {
                                where: condition2,
                            },
                            required: false
                        },
                        {
                            model: model2, // User
                            as: 'Assign',
                            attributes: attributes2,
                            where: condition2 || undefined,
                            required: false
                        },
                        {
                            model: model2, // User
                            as: 'Review',
                            attributes: attributes2,
                            where: condition2 || undefined,
                            required: false
                        },

                        // {
                        //     model: model5,//Group
                        //     attributes: attributes5,
                        //     ...condition5 !== undefined && {
                        //         where: condition5
                        //     },
                        //     required: false
                        // }
                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: true
                },
                // {
                //     model: model2,//Customer
                //     as: 'Customer',
                //     attributes: attributes2,
                //     ...condition2 !== undefined && {
                //         where: condition2
                //     },
                // },
                {
                    model: model3,//Customer
                    as: 'Vendor',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }


            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            }
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}



exports.getVendor = async (model, model1, condition, condition1, attributes, attributes1,) => {
    try {
        const data = await model.findOne({ //Vendor mapping table
            where: condition,
            include: [
                {
                    model: model1,//Customer
                    as: 'Customer',
                    attributes: attributes1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                },
                {
                    model: model1,//Customer
                    as: 'Vendor',
                    attributes: attributes1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                },
            ],
            attributes: attributes,
            required: false
        });
        return data ? JSON.parse(JSON.stringify(data)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}


exports.getVendorDetail = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, limit, offset, order) => {
    try {
        const list = await model.findOne({
            where: condition,
            include: [
                {
                    model: model1, // VendorDetail
                    include: [
                        {
                            model: model3, // User
                            as: 'Created',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model3, // User
                            as: 'Updated',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model3, // User
                            as: 'Assign',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model3, // User
                            as: 'Review',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model3, // User
                            as: 'VendorPOC',
                            attributes: attributes3,
                            where: condition3 || undefined,
                            required: false
                        },
                        {
                            model: model4, // Department
                            include: [
                                {
                                    model: model3, // User
                                    // as: 'Created',
                                    attributes: attributes3,
                                    where: condition3 || undefined,
                                    required: false
                                }
                            ],
                            attributes: attributes4,
                            where: condition4 || undefined,
                            required: false
                        }
                    ],
                    attributes: attributes1,
                    where: condition1 || undefined,
                    required: false
                },
                {
                    model: model2, // Customer as 'Customer'
                    as: 'Customer',
                    attributes: attributes2,
                    where: condition2 || undefined
                },
                {
                    model: model2, // Customer as 'Vendor'
                    as: 'Vendor',
                    attributes: attributes2,
                    where: condition2 || undefined
                },
            ],
            attributes: attributes,
            limit: limit || undefined,
            offset: offset || undefined,
            order: order || undefined
        });

        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}



exports.getListWithMultipleAssociates = async (model, model1, model2, alias2, model3, alias3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
    try {
        let list = await model.findAndCountAll({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    required: true,
                },
                {
                    model: model2,
                    as: alias2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: true
                },
                {
                    model: model3,
                    as: alias3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    required: true // left join
                },
                //      {
                //     model: model4,
                //     as: alias4,
                //     ...condition4 !== undefined && {
                //       where: condition4
                //     },
                //     ...attributes4 !== undefined && {
                //       attributes: attributes4
                //     },
                //     required: true // left join
                //   }
            ],
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
            subQuery: false
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log("errorr", error)
        return false
    }
};





exports.getList = async (model, model1, model2, alias2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
    try {
        let list = await model.findAndCountAll({ // vendor mapping
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1, // vendorDetail
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    include: [
                        {
                            model: model3, // department
                            // as: alias3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                            ...attributes3 !== undefined && {
                                attributes: attributes3
                            },
                            required: false // left join
                        },
                    ],
                    required: true,
                },
                {
                    model: model2,// Customer
                    as: alias2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: true
                },
            ],
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
            subQuery: false
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log("errorr", error)
        return false
    }
};

exports.getList2 = async (model, model1, model2, alias2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, limit, offset, order) => {
    try {
        let list = await model.findAndCountAll({ // vendor mapping
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1, // vendorDetail
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    include: [
                        {
                            model: model3, // department
                            // as: alias3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                            ...attributes3 !== undefined && {
                                attributes: attributes3
                            },
                            include: [
                                {
                                    model: model4, //user
                                    // as: alias3,
                                    ...condition4 !== undefined && {
                                        where: condition4
                                    },
                                    ...attributes4 !== undefined && {
                                        attributes: attributes4
                                    },
                                    required: true // left join
                                },
                            ],
                            required: false // left join
                        },
                    ],
                    required: true,
                },
                {
                    model: model2,// Customer
                    as: alias2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: true
                },
            ],
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
            subQuery: false
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log("errorr", error)
        return false
    }
};



exports.getControlsWithCategoryAndMitigation = async (model, model1, model2, model3, model4, model5, condition, condition1, condition2, condition3, condition4, condition5, attributes, attributes1, attributes2, attributes3, attributes4, attributes5, order) => {
    try {
        const list = await model.findAll({
            attributes: attributes,
            where: condition,
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false
            },
            {
                model: model2,
                attributes: attributes2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            },
            {
                model: model4,
                ...attributes4 !== undefined && {
                    attributes: attributes4
                },
                ...condition4 !== undefined && {
                    where: condition4
                },
                required: false
            },
            {
                model: model5,
                as: 'ReviewVEA',
                ...attributes5 !== undefined && {
                    attributes: attributes5
                },
                ...condition5 !== undefined && {
                    where: condition5
                },
                required: false
            },
            ],
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

exports.getListWithMultipleAssociates2 = async (model, model2, model4, alias4 , model5 , alias5 , model6 , alias6 ,model7 , condition, condition2, condition4,condition5, condition6,condition7 , attributes, attributes2, attributes4,attributes5,attributes6, attributes7 , limit, offset, order) => {
    try {
      let list = await model.findAndCountAll({
        ...condition !== undefined && {
          where: condition
        },
        ...attributes !== undefined && {
          attributes
        },
        include: [
          {
            model: model2,
            ...condition2 !== undefined && {
              where: condition2
            },
            ...attributes2 !== undefined && {
              attributes: attributes2
            },
            required: false,
          },
          {
            model: model4,
            as: alias4 ,
            ...condition4 !== undefined && {
              where: condition4
            },
            ...attributes4 !== undefined && {
              attributes: attributes4
            },
            required: true
          },
          {
            model: model5,
            as: alias5,
            ...condition5 !== undefined && {
              where: condition5
            },
            ...attributes5 !== undefined && {
              attributes: attributes5
            },
            required: true 
          },
             {
            model: model6,
            as: alias6,
            ...condition6 !== undefined && {
              where: condition6
            },
            ...attributes6 !== undefined && {
              attributes: attributes6
            },
            required: true 
          },
          {
            model: model7,
            // as: alias6,
            ...condition7 !== undefined && {
              where: condition7
            },
            ...attributes7 !== undefined && {
              attributes: attributes7
            },
            required: true 
          }
        ],
        ...limit !== undefined && {
          limit
        },
        ...offset !== undefined && {
          offset
        },
        ...order !== undefined && {
          order
        },
        subQuery: false
      });
      return list ? JSON.parse(JSON.stringify(list)) : false;
  
    } catch (error) {
      console.log("errorr", error)
      return false
    }
  };

  exports.getCategorieswiseControls = async (model, model1, condition, condition1, attributes, attributes1, order) => {
    try {
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: {
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false, // Change to true if you need an inner join
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
  
    } catch (error) {
        console.log( error);
        return false;
    }
  }