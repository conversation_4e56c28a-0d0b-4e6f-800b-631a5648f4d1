const { Op, Sequelize } = require('sequelize');

exports.getCustomerDetails = async (model1, model2, model3, model4, condition1, condition2, condition3, condition4, attributes1, attributes2, attributes3, attributes4) => {
    try {
        // (User, Customer, CustomerResources, Resources
        const list = await model1.findOne({
            ...condition1 !== undefined && {
                where: condition1
            },
            ...attributes1 !== undefined && {
                attributes: attributes1
            },
            include: {
                model: model2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                ...attributes2 !== undefined && {
                    attributes: attributes2
                },
                required: true,
                include: {
                    model: model3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    required: false,
                    include: {
                        model: model4,
                        ...condition4 !== undefined && {
                            where: condition4
                        },
                        ...attributes4 !== undefined && {
                            attributes: attributes4
                        },
                        required: true,
                    },
                },
            },
            // subQuery: true,
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        return false
    }
}
