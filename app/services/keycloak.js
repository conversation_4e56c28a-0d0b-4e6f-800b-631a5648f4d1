const token_endpoint = process.env.KEYCLOAK_TOKEN_ENDPOINT;
const client_id = process.env.KEYCLOAK_CLIENT_ID;
const client_secret = process.env.KEYCLOAK_CLIENT_SECRET;
const user_endpoint = process.env.KEYCLOAK_USER_ENDPOINT;
const axios = require('axios');
const qs = require('qs');


exports.resetPassword = async (data) => {
  const { userId, password } = data;
  try {
    const res = await axios.post(
      token_endpoint,
      new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: client_id,
        client_secret: client_secret
      }),
      { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
    );
     if (res.status !== 200 || !res.data?.access_token) {
       console.error('Failed to get admin token:', res.data);
       return false;
     }
    const adminToken = res.data.access_token;
    const updatePasswordRes = await axios.put(
      `${process.env.KEYCLOAK_AUTH_SERVER_URL}/admin/realms/${process.env.KEYCLOAK_REALM}/users/${userId}/reset-password`,
      {
        type: 'password',
        temporary: false,
        value: password
      },
      {
        headers: {
          Authorization: `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    if (![200, 204].includes(updatePasswordRes.status)) {
      console.log('Error updating password', updatePasswordRes.data);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error getting admin token:', error);
    return false;
  }
};

exports.getToken = async () => {
    try {
        const response = await fetch(token_endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `client_id=${client_id}&client_secret=${client_secret}&grant_type=client_credentials`
        });
        if (!response.ok) {
            console.log('response:', response);
            throw new Error(`HTTP error! status: ${response}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error fetching token:', error);
        return false;
    }
}

exports.checkUserCredsV2 = async (userData) => {
  try {
    let data = {
      client_id: process.env.KEYCLOAK_CLIENT_ID,
      client_secret: process.env.KEYCLOAK_CLIENT_SECRET,
      grant_type: process.env.KEYCLOAK_FRONTEND_GRANT_TYPE || 'password',
      username: userData.email,
      password: userData.password
    };
    if (userData.otp) {
      data['totp'] = userData.otp;
    }

    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: process.env.KEYCLOAK_TOKEN_ENDPOINT,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: qs.stringify(data)
    };

    const response = await axios.request(config);
    console.log('well', response.data);
    return { status: true, statusCode: response.status, data: response.data };
  } catch (error) {
    console.error('Error checking user credentials:', error.response.data);
    return { status: false, message: error.response.data, statusCode: error.response.status };
  }
};

exports.checkUserCreds = async (userData) => {
    try {
        let data = {
            'client_id': process.env.KEYCLOAK_FRONTEND_CLIENT_ID,
            'client_secret': process.env.KEYCLOAK_FRONTEND_CLIENT_SECRET,
            'grant_type': process.env.KEYCLOAK_FRONTEND_GRANT_TYPE,
            'username': userData.email,
            'password': userData.password,
        };
        if (userData.otp) {
            data['totp'] = userData.otp;
        }

        const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: process.env.KEYCLOAK_TOKEN_ENDPOINT,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        };
        
        const response = await axios.request(config);
        console.log('well', response.data);
        return { status: true, statusCode: response.status, data: response.data };
        
    } catch (error) {
        return { status: false, message: error.response.data, statusCode: error.response.status };
    }
}

exports.createUser = async (token, user) => {
    try {
        const response = await fetch(user_endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(user)
        });
        if (!response.ok) {
            console.log('response:', response);
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response;
    } catch (error) {
        console.error('Error creating user:', error);
        return false;
    }
}
