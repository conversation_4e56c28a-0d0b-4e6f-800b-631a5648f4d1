const { Op, Sequelize } = require('sequelize');

exports.getUserDetails = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3) => {
    try {
        // User, Organization, OrganisationResources, Resources
        const list = await model.findOne({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    required: true,
                },
                {
                    model: model2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: false,
                },
                {
                    model: model3,
                    as: 'Department',
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    required: false,
                }
            ]
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of records from a model with three associated models, with pagination and sorting options.
 * @param {Object} model - The main model to retrieve records from.
 * @param {Object} model1 - The first associated model.
 * @param {Object} model2 - The second associated model.
 * @param {Object} model3 - The third associated model.
 * @param {Object} model4 - The model associated with model3.
 * @param {Object} condition - The condition to filter the records.
 * @param {Object} condition1 - The condition to filter the first associated model records.
 * @param {Object} condition2 - The condition to filter the second associated model records.
 * @param {Object} condition3 - The condition to filter the third associated model records.
 * @param {Object} condition4 - The condition to filter the fourth associated model records.
 * @param {Array} attributes - The attributes to include in the result.
 * @param {Array} attributes1 - The attributes to include in the result of the first associated model.
 * @param {Array} attributes2 - The attributes to include in the result of the second associated model.
 * @param {Array} attributes3 - The attributes to include in the result of the third associated model.
 * @param {Array} attributes4 - The attributes to include in the result of the fourth associated model.
 * @param {number} limit - The maximum number of records to retrieve.
 * @param {number} offset - The number of records to skip.
 * @param {Array} order - The order in which the records should be sorted.
 * @returns {Promise<Array|boolean>} - The list of records or false if an error occurs.
 */
exports.getUserListWithCount = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, limit, offset, order) => {
    try {
        let list = await model.findAndCountAll({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    required: true
                },
                {
                    model: model2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: true
                },
                {
                    model: model3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    required: true,
                    include: [
                        {
                            model: model4,
                            ...condition4 !== undefined && {
                                where: condition4
                            },
                            ...attributes4 !== undefined && {
                                attributes: attributes4
                            },
                            required: true
                        }
                    ]
                },
            ],
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
            subQuery: false,
            distinct: true,
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log("eeeeeeee", error)
        return false
    }
};
