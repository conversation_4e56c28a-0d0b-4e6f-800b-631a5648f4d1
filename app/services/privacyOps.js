/**
 * Retrieves role details based on the provided parameters.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first nested model to include in the query.
 * @param {Object} model2 - The second nested model to include in the query.
 * @param {Object} condition - The condition to apply on the main model.
 * @param {Object} condition1 - The condition to apply on the first nested model.
 * @param {Object} condition2 - The condition to apply on the second nested model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first nested model.
 * @param {Array} attributes2 - The attributes to select from the second nested model.
 * @returns {Object|boolean} - The role details if found, otherwise false.
 */

exports.getDetailWithMultiAssociate = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...(condition !== undefined && {
        where: condition
      }),
      ...(attributes !== undefined && {
        attributes
      }),
      include: [
        {
          model: model1,
          as: 'Approver',
          ...(condition1 !== undefined && {
            where: condition1
          }),
          ...(attributes1 !== undefined && {
            attributes: attributes1
          }),
          required: false
        },
        {
          model: model1,
          as: 'Owner',
          ...(condition1 !== undefined && {
            where: condition1
          }),
          ...(attributes1 !== undefined && {
            attributes: attributes1
          }),
          required: false
        },
        {
          model: model2,
          ...(condition2 !== undefined && {
            where: condition2
          }),
          ...(attributes2 !== undefined && {
            attributes: attributes2
          }),
          required: false
        }
      ],
      ...(limit !== undefined && {
        limit
      }),
      ...(offset !== undefined && {
        offset
      }),
      ...(order !== undefined && {
        order
      })
      // subQuery:false
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('erro', error);
    return false;
  }
};

exports.getListWith4Models = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...(condition !== undefined && {
        where: condition
      }),
      ...(attributes !== undefined && {
        attributes
      }),
      include: {
        model: model1,
        ...(condition1 !== undefined && {
          where: condition1
        }),
        ...(attributes1 !== undefined && {
          attributes: attributes1
        }),
        required: false,
        include: {
          model: model2,
          // as: 'RegulationBusiRequirementMappings',
          ...(condition2 !== undefined && {
            where: condition2
          }),
          ...(attributes2 !== undefined && {
            attributes: attributes2
          }),
          required: false,
          include: {
            model: model3,
            // as: 'Regulations',
            ...(condition3 !== undefined && {
              where: condition3
            }),
            ...(attributes3 !== undefined && {
              attributes: attributes3
            }),
            required: false
          }
        },
        raw: true
        // distinct: true,
        // subQuery: false
      },
      // raw: true,
      subQuery: true,
      ...(limit !== undefined && {
        limit
      }),
      ...(offset !== undefined && {
        offset
      }),
      ...(order !== undefined && {
        order
      })
    });
    // console.log('list', list[0].UCFBusinessRequirement.RegulationBusiRequirementMappings);
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('eeeeeeee', error);
    return false;
  }
};

exports.getListWith4ModelsV2 = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
  try {
    console.log(attributes3);
    let list = await model.findAll({
      ...(condition !== undefined && {
        where: condition
      }),
      ...(attributes !== undefined && {
        attributes
      }),
      include: [
        {
          model: model1,
          ...(condition1 !== undefined && {
            where: condition1
          }),
          ...(attributes1 !== undefined && {
            attributes: attributes1
          }),
          required: false,
          raw: true
          // distinct: true,
          // subQuery: false
        },
        {
          model: model2,
          // as: 'RegulationBusiRequirementMappings',
          ...(condition2 !== undefined && {
            where: condition2
          }),
          ...(attributes2 !== undefined && {
            attributes: attributes2
          }),
          required: false,
          include: {
            model: model3,
            // as: 'Regulations',
            ...(condition3 !== undefined && {
              where: condition3
            }),
            ...(attributes3 !== undefined && {
              attributes: attributes3
            }),
            required: false
          }
        }
      ],
      // raw: true,
      subQuery: true,
      ...(limit !== undefined && {
        limit
      }),
      ...(offset !== undefined && {
        offset
      }),
      ...(order !== undefined && {
        order
      })
    });
    // console.log('list', list[0].UCFBusinessRequirement.RegulationBusiRequirementMappings);
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('eeeeeeee', error);
    return false;
  }
};
