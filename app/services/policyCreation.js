require('dotenv').config();
const { default: axios } = require('axios');

exports.continueWriting = async (industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text) => {
    try {
        const response = await axios.post(
            process.env.OPENROUTER_BASE_URL,
            {
                model: process.env.OPENROUTER_AI_MODEL, 
                max_tokens: 1000,
                temperature: 0,
                system: `You are a highly experienced privacy expert with many years of experience in drafting policies based on the latest laws and practices. Your task is to work on a policy related to data laws. Don't give out any cautionary statements or greetings. Provide the policy text or section that addresses the task description.`,
                messages: [
                    {
                        role: 'user',
                        content: `Here is the current policy text:
        
                        <policy_text>
                        ${policy_text}
                        </policy_text>
                    
                        Your specific task for this policy is as follows:

                        <task_description>
                        I work at a ${industry_vertical} company named "${customer_name}" located in ${country}. I am working on a ${policy_name} policy with description: ${policy_desc}. Please continue writing the following text:
                        <section>
                        ${text}
                        </section>
                        </task_description>
                    
                        Approach the task with the following guidelines:
                        1. Analyze the current policy text and the specific task requirements.
                        2. Ensure consistency with the rest of the policy.
                        3. Only change the text under <section> tag. Don't modify the rest of the policy.
                        4. Structure it logically and cover all necessary aspects of data protection.
                        5. Always adhere to the latest data protection laws and best practices in the industry.
                        6. Use clear, precise language that is easily understandable by the intended audience, clearly explaining any legal jargon used.
                        7. Always structure the output logically with smooth transitions between ideas.
                        
                        Formatting Requirements (Mandatory):
                        - Every paragraph must be wrapped inside <p> tags.
                        - Important terms, legal obligations, and key concepts must be highlighted using <strong> tags wherever appropriate.
                            Examples: "personal data", "data retention", "lawful basis", "data protection rights", "Data Protection Officer", "consent", "obligations", etc.
                        - If a term or phrase is critical for understanding obligations, rights, or responsibilities, it must be marked with <strong>.
                        - Lists must use proper <ul> and <li> tags.
                        - Your output must feel professional, structured, and easy to scan visually.
                        - Do not overuse <strong>. Only apply it where necessary to emphasize the importance.
                        - Always ensure smooth logical flow and transitions between ideas.
                        - Only modify the text inside the <section> tag. Do not alter any other parts of the policy.

                        Follow these instructions strictly for every output.
                    
                        Provide your output directly, without any introductory statements, greetings, or cautionary remarks. Your response should be the policy text that addresses the task description. Provide it without the <section> tag.`
                    }
                ]
            },

            {
                headers: {
                    Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        // console.log(response.data);
        return response.data; // Assuming the function should return the message object
    } catch (error) {
        console.error('Failed to get AI response:', error);
        return false;
    }
};


exports.shortenText = async (industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text) => {
    try {
        const response = await axios.post(
            process.env.OPENROUTER_BASE_URL,
            {
                model: process.env.OPENROUTER_AI_MODEL, 
                max_tokens: 1000,
                temperature: 0,
                system: "You are a highly experienced privacy expert with many years of experience in drafting policies based on the latest laws and practices. Your task is to work on a policy related to data laws. Don't give out any cautionary statements or greetings. Provide the policy text or section that addresses the task description.",
                messages: [
                    {
                        role: 'user',
                        content: `Here is the current policy text:

                        <policy_text>
                        ${policy_text}
                        </policy_text>

                        Your specific task for this policy is as follows:

                        <task_description>
                        I work at a ${industry_vertical} company named "${customer_name}" located in ${country}. I am working on a ${policy_name} policy with description: ${policy_desc}. Please shorten the following text:
                            <section>
                                ${text}.
                            </section>
                        </task_description>

                        Approach the task with the following guidelines:
                        1. Analyze the current policy text and the specific task requirements.
                        2. Ensure consistency with the rest of the policy, maintaining all crucial information while improving conciseness.
                        3. Only change the text under <section> tag. Don't modify the rest of the policy.
                        4. Structure it logically and cover all necessary aspects of data protection.
                        5. Always adhere to the latest data protection laws and best practices in the industry.
                        6. Use clear, precise language that is easily understandable by the intended audience, clearly explaining any legal jargon used.
                        7. Always return the text in HTML format with proper tags used for formatting. New paragraphs should be in <p> tags.

                        Formatting Requirements (Mandatory):
                        - Every paragraph must be wrapped inside <p> tags.
                        - Important terms, legal obligations, and key concepts must be highlighted using <strong> tags wherever appropriate.
                            Examples: "personal data", "data retention", "lawful basis", "data protection rights", "Data Protection Officer", "consent", "obligations", etc.
                        - If a term or phrase is critical for understanding obligations, rights, or responsibilities, it must be marked with <strong>.
                        - Lists must use proper <ul> and <li> tags.
                        - Your output must feel professional, structured, and easy to scan visually.
                        - Do not overuse <strong>. Only apply it where necessary to emphasize the importance.
                        - Always ensure smooth logical flow and transitions between ideas.
                        - Only modify the text inside the <section> tag. Do not alter any other parts of the policy.

                        Follow these instructions strictly for every output.

                        Provide your output directly, without any introductory statements, greetings, or cautionary remarks. Your response should be the policy text that addresses the task description. Provide it without the <section> tag.`
                    },

                    {
                        role: 'assistant',
                        content: 'Sure, here is the required policy text:'
                        
                    }
                ]
            },

            {
                headers: {
                    Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        // console.log(response.data);
        return response.data; // Assuming the function should return the message object

    } catch (error) {
        console.error('Failed to get AI response:', error);
        return false;
    }
};


exports.rewriteText = async (industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text) => {
    try {
        const response = await axios.post(
            process.env.OPENROUTER_BASE_URL,
            {
                model: process.env.OPENROUTER_AI_MODEL, 
                max_tokens: 1000,
                temperature: 0,
                system: "You are a highly experienced privacy expert with many years of experience in drafting policies based on the latest laws and practices. Your task is to work on a policy related to data laws. Don't give out any cautionary statements or greetings. Provide the policy text or section that addresses the task description.",
                messages: [
                    {
                        role: 'user',
                        content: `Here is the current policy text:

                        <policy_text>
                        ${policy_text}
                        </policy_text>

                        Your specific task for this policy is as follows:

                        <task_description>
                        I work at a ${industry_vertical} company named "${customer_name}" located in ${country}. I am working on a ${policy_name} policy with description: ${policy_desc}. Please rewrite the following text:
                            <section>
                                ${text}.
                            </section>
                        </task_description>

                        Approach the task with the following guidelines:
                        1. Analyze the current policy text and the specific task requirements.
                        2. Ensure consistency with the rest of the policy.
                        3. The rewritten content should should the same meaning as the original text. It should have a better language and structuring.
                        4. Only change the text under <section> tag. Don't modify the rest of the policy.
                        5. Structure it logically and cover all necessary aspects of data protection.
                        6. Always adhere to the latest data protection laws and best practices in the industry.
                        7. Use clear, precise language that is easily understandable by the intended audience, clearly explaining any legal jargon used.
                        8. Always return the text in HTML format with proper tags used for formatting. New paragraphs should be in <p> tags.

                        Formatting Requirements (Mandatory):
                        - Every paragraph must be wrapped inside <p> tags.
                        - Important terms, legal obligations, and key concepts must be highlighted using <strong> tags wherever appropriate.
                            Examples: "personal data", "data retention", "lawful basis", "data protection rights", "Data Protection Officer", "consent", "obligations", etc.
                        - If a term or phrase is critical for understanding obligations, rights, or responsibilities, it must be marked with <strong>.
                        - Lists must use proper <ul> and <li> tags.
                        - Your output must feel professional, structured, and easy to scan visually.
                        - Do not overuse <strong>. Only apply it where necessary to emphasize the importance.
                        - Always ensure smooth logical flow and transitions between ideas.
                        - Only modify the text inside the <section> tag. Do not alter any other parts of the policy.

                        Follow these instructions strictly for every output.

                        Provide your output directly, without any introductory statements, greetings, or cautionary remarks. Your response should be the policy text that addresses the task description. Provide it without the <section> tag.`
                    },

                    {
                        role: 'assistant',
                        content: 'Sure, here is the required policy text:'
                        
                    }
                ]
            },

            {
                headers: {
                    Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        return response.data; // Assuming the function should return the message object
    } catch (error) {
        console.error('Failed to get AI response:', error);
        return false;
    }
};


exports.formalizeText = async (industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text) => {
    try {

        const response = await axios.post(
            process.env.OPENROUTER_BASE_URL,
            {
                model: process.env.OPENROUTER_AI_MODEL, 
                max_tokens: 1000,
                temperature: 0,
                system: "You are a highly experienced privacy expert with many years of experience in drafting policies based on the latest laws and practices. Your task is to work on a policy related to data laws. Don't give out any cautionary statements or greetings. Provide the policy text or section that addresses the task description.",
                messages: [
                    {
                        role: 'user',
                        content: `Here is the current policy text:

                        <policy_text>
                        ${policy_text}
                        </policy_text>
                        
                        Your specific task for this policy is as follows:
                        
                        <task_description>
                        I work at a ${industry_vertical} company named "${customer_name}" located in ${country}. I am working on a ${policy_name} policy with description: ${policy_desc}. Please formalize the following text:
                            <section>
                                ${text}.
                            </section>
                        </task_description>
                        
                        Approach the task with the following guidelines:
                        1. Analyze the current policy text and the specific task requirements.
                        2. Ensure consistency with the rest of the policy and use a formal language.
                        3. Only change the text under <section> tag. Don't modify the rest of the policy.
                        4. Structure it logically and cover all necessary aspects of data protection.
                        5. Always adhere to the latest data protection laws and best practices in the industry.
                        6. Use clear, precise language that is easily understandable by the intended audience, clearly explaining any legal jargon used.
                        7. Always return the text in HTML format with proper tags used for formatting. New paragraphs should be in <p> tags.
                        
                        Formatting Requirements (Mandatory):
                        - Every paragraph must be wrapped inside <p> tags.
                        - Important terms, legal obligations, and key concepts must be highlighted using <strong> tags wherever appropriate.
                            Examples: "personal data", "data retention", "lawful basis", "data protection rights", "Data Protection Officer", "consent", "obligations", etc.
                        - If a term or phrase is critical for understanding obligations, rights, or responsibilities, it must be marked with <strong>.
                        - Lists must use proper <ul> and <li> tags.
                        - Your output must feel professional, structured, and easy to scan visually.
                        - Do not overuse <strong>. Only apply it where necessary to emphasize the importance.
                        - Always ensure smooth logical flow and transitions between ideas.
                        - Only modify the text inside the <section> tag. Do not alter any other parts of the policy.

                        Follow these instructions strictly for every output.

                        Provide your output directly, without any introductory statements, greetings, or cautionary remarks. Your response should be the policy text that addresses the task description. Provide it without the <section> tag.`
                    },

                    {
                        role: 'assistant',
                        content: 'Sure, here is the required policy text:'
                        
                    }
                ]
            },

            {
                headers: {
                    Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        return response.data; // Assuming the function should return the message object
    } catch (error) {
        console.error('Failed to get AI response:', error);
        return false;
    }
};


exports.customPrompt = async (industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, prompt, text) => {
    try {

        const response = await axios.post(
            process.env.OPENROUTER_BASE_URL,
            {
                model: process.env.OPENROUTER_AI_MODEL, 
                max_tokens: 1000,
                temperature: 0,
                system: "You are a highly experienced privacy expert with many years of experience in drafting policies based on the latest laws and practices. Your task is to work on a policy related to data laws. Don't give out any cautionary statements or greetings. Provide the policy text or section that addresses the task description.",
                messages: [
                    {
                        role: 'user',
                        content: `Here is the current policy text:

                        <policy_text>
                        ${policy_text}
                        </policy_text>
                        
                        Your specific task for this policy is as follows:
                        
                        <task_description>
                        I work at a ${industry_vertical} company named "${customer_name}" located in ${country}. I am working on a ${policy_name} policy with description: ${policy_desc}.
                            <prompt>
                                ${prompt}
                            </prompt>
                            <section>
                                ${text}.
                            </section>
                        </task_description>
                        
                        Approach the task with the following guidelines:
                        1. Analyze the current <policy_text> and the <prompt>.
                        2. Ensure consistency with the rest of the policy.
                        3. If text is provided under <section> tag, only modify and return the given content. Don't modify the rest of the policy.
                        4. Create a whole new policy only when no content is given in <policy_text>.
                        5. Structure it logically and cover all necessary aspects of data protection.
                        6. Always adhere to the latest data protection laws and best practices in the industry.
                        7. Use clear, precise language that is easily understandable by the intended audience, clearly explaining any legal jargon used.
                        8. Always return the text in HTML format with proper tags used for formatting. New paragraphs should be in <p> tags.
                        
                        Formatting Requirements (Mandatory):
                        - Every paragraph must be wrapped inside <p> tags.
                        - Important terms, legal obligations, and key concepts must be highlighted using <strong> tags wherever appropriate.
                            Examples: "personal data", "data retention", "lawful basis", "data protection rights", "Data Protection Officer", "consent", "obligations", etc.
                        - If a term or phrase is critical for understanding obligations, rights, or responsibilities, it must be marked with <strong>.
                        - Lists must use proper <ul> and <li> tags.
                        - Your output must feel professional, structured, and easy to scan visually.
                        - Do not overuse <strong>. Only apply it where necessary to emphasize the importance.
                        - Always ensure smooth logical flow and transitions between ideas.
                        - Only modify the text inside the <section> tag. Do not alter any other parts of the policy.

                        Follow these instructions strictly for every output.

                        Provide your output directly, without any introductory statements, greetings, or cautionary remarks. Your response should be the policy text that addresses the task description. Provide it without the <section> tag.`
                    },

                    {
                        role: 'assistant',
                        content: 'Sure, here is the required policy text:'
                        
                    }
                ]
            },

            {
                headers: {
                    Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        return response.data; // Assuming the function should return the message object
    } catch (error) {
        console.error('Failed to get AI response:', error);
        return false;
    }
};


exports.fixSpelling = async (industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text) => {
    try {

        const response = await axios.post(
            process.env.OPENROUTER_BASE_URL,
            {
                model: process.env.OPENROUTER_AI_MODEL, 
                max_tokens: 1000,
                temperature: 0,
                system: "You are a highly experienced privacy expert with many years of experience in drafting policies based on the latest laws and practices. Your task is to work on a policy related to data laws. Don't give out any cautionary statements or greetings. Provide the policy text or section that addresses the task description.",   
                messages: [
                    {
                        role: 'user',
                        content: `Here is the current policy text:

                        <policy_text>
                        ${policy_text}
                        </policy_text>

                        Your specific task for this policy is as follows:

                        <task_description>
                        I work at a ${industry_vertical} company named "${customer_name}" located in ${country}. I am working on a ${policy_name} policy with description: ${policy_desc}. Please fix the spelling and grammer in the following text:
                            <section>
                                ${text}.
                            </section>
                        </task_description>

                        Approach the task with the following guidelines:
                        1. Analyze the current policy text and the specific task requirements.
                        2. Ensure consistency with the rest of the policy.
                        3. Only change the text under <section> tag. Don't modify the rest of the policy.
                        4. For spelling fixes, correct any errors while preserving the original meaning.
                        5. Structure it logically and cover all necessary aspects of data protection.
                        6. Always adhere to the latest data protection laws and best practices in the industry.
                        7. Use clear, precise language that is easily understandable by the intended audience, clearly explaining any legal jargon used.
                        8. Always return the text in HTML format with proper tags used for formatting. New paragraphs should be in <p> tags.

                        Formatting Requirements (Mandatory):
                        - Every paragraph must be wrapped inside <p> tags.
                        - Important terms, legal obligations, and key concepts must be highlighted using <strong> tags wherever appropriate.
                            Examples: "personal data", "data retention", "lawful basis", "data protection rights", "Data Protection Officer", "consent", "obligations", etc.
                        - If a term or phrase is critical for understanding obligations, rights, or responsibilities, it must be marked with <strong>.
                        - Lists must use proper <ul> and <li> tags.
                        - Your output must feel professional, structured, and easy to scan visually.
                        - Do not overuse <strong>. Only apply it where necessary to emphasize the importance.
                        - Always ensure smooth logical flow and transitions between ideas.
                        - Only modify the text inside the <section> tag. Do not alter any other parts of the policy.

                        Follow these instructions strictly for every output.

                        Provide your output directly, without any introductory statements, greetings, or cautionary remarks. Your response should be the policy text that addresses the task description. Provide it without the <section> tag.`
                    },

                    {
                        role: 'assistant',
                        content: 'Sure, here is the required policy text:'
                        
                    }
                ]
            },

            {
                headers: {
                    Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        return response.data; // Assuming the function should return the message object
    } catch (error) {
        console.error('Failed to get AI response:', error);
        return false;
    }
};