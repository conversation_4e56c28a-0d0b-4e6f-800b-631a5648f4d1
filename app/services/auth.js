const { Op, Sequelize } = require('sequelize');
const db = require('../models/index');

exports.AddUserDetail = async (model, data) => {
  try {
    const addUserInfo = await model.create(data);

    return addUserInfo ? JSON.parse(JSON.stringify(addUserInfo)) : false;
  } catch (error) {
    console.log('cool', error.message);
    console.error('AddUserDetail>>>>>>>>>', error);
    return false;
  }
};

exports.dynamicAddRequest = async (model, data) => {
  try {
    const addedFriendInfo = await model.create(data);
    return addedFriendInfo ? JSON.parse(JSON.stringify(addedFriendInfo)) : false;
  } catch (error) {
    console.error('AddFriendRequest>>>>>>>>>', error);
    return false;
  }
};

exports.BulkData = async (model, data, transaction) => {
  try {
    if (!Array.isArray(data)) {
      throw new Error('Data must be an array');
    }
    const addUserInfo = await model.bulkCreate(data, { transaction });

    return addUserInfo ? JSON.parse(JSON.stringify(addUserInfo)) : false;
  } catch (error) {
    console.error('Error in Bulk Data Insert', error);
    return false;
  }
};

exports.GetUserDetail = async (model, condition, attributes) => {
  try {
    const getUserDetail = await model.findOne({
      where: condition,
      ...(attributes !== undefined && {
        attributes
      })
    });

    return getUserDetail ? JSON.parse(JSON.stringify(getUserDetail)) : false;
  } catch (error) {
    console.error('GetUserDetail>>>>>>>>>', error);
    return false;
  }
};

exports.UpdateUser = async (model, data, condition, transaction) => {
  try {
    const updateUser = await model.update(data, { where: condition });

    return updateUser ? JSON.parse(JSON.stringify(updateUser)) : false;
  } catch (error) {
    console.error('UpdateUser>>>>>>>>>', error);
    return false;
  }
};

exports.GetData = async (query, data) => {
  try {
    const getData = await db.sequelize.query(query, {
      replacements: data,
      type: db.sequelize.QueryTypes.SELECT
    });
    return getData ? JSON.parse(JSON.stringify(getData)) : false;
  } catch (error) {
    console.error('GetData>>>>>>>>>', error);
    return false;
  }
};

exports.GetAllUserDetail = async (model, condition, limit, offset) => {
  try {
    const getUserDetail = await model.findAll({
      ...(condition !== undefined && {
        where: condition
      }),
      limit,
      offset
    });

    return getUserDetail ? JSON.parse(JSON.stringify(getUserDetail)) : false;
  } catch (error) {
    console.error('GetUserDetail>>>>>>>>>', error);
    return false;
  }
};

exports.deleteFriendRequest = async (model, condition) => {
  try {
    const deleteTempReq = await model.destroy({
      where: condition,
      force: true
    });

    return deleteTempReq;
  } catch (error) {
    console.error('GetFriends>>>>>>>>>', error);
    return false;
  }
};

exports.getUserDetails = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3) => {
  try {
    const list = await model.findOne({
      ...(condition !== undefined && {
        where: condition
      }),
      ...(attributes !== undefined && {
        attributes
      }),
      include: [
        {
          model: model1,
          ...(condition1 !== undefined && {
            where: condition1
          }),
          ...(attributes1 !== undefined && {
            attributes: attributes1
          }),
          required: false
        },
        {
          model: model2,
          ...(condition2 !== undefined && {
            where: condition2
          }),
          ...(attributes2 !== undefined && {
            attributes: attributes2
          })
        },
        {
          model: model3,
          ...(condition3 !== undefined && {
            where: condition3
          }),
          ...(attributes3 !== undefined && {
            attributes: attributes3
          }),
          required: false
        }
      ]
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('eeeeeeee', error);
    return false;
  }
};
