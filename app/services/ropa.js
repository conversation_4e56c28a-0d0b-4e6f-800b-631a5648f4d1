const fs = require('fs');
const readline = require('readline');
const axios = require('axios');
const xlsx = require('xlsx');
const path = require('path');

/**
 * Retrieves a list of departments with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of departments with their associated models, or false if an error occurs.
 */
exports.getDepartmentsROPA = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model2,
                            attributes: attributes2,
                            ...condition2 !== undefined && {
                                where: condition2
                            },
                        }

                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: true
                },
                {
                    model: model2,
                    as: 'AssignedTo',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                },
                {
                    model: model2,
                    as: 'Approver',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                }
            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            }
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of processes with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of processes with their associated models, or false if an error occurs.
 */
exports.getProcessesROPA = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, order) => {
    try {
        const list = await model.findAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model2,
                            attributes: attributes2,
                            ...condition2 !== undefined && {
                                where: condition2
                            },
                        }
                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: true
                },
                {
                    model: model2,
                    as: 'AssignedTo',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                },
                {
                    model: model2,
                    as: 'Approver',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                }
            ],
            attributes: attributes,
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of processing activities assigned to an employee based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} model3 - The third associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Object} condition3 - The condition to filter the third associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} attributes3 - The attributes to select from the third associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of processing activities assigned to an employee, or false if an error occurs.
 */
exports.getEmployeeROPA = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model3,
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                        }

                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: false
                },
                {
                    model: model2,
                    include: [
                        {
                            model: model3,
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                        },
                        {
                            model: model1,
                            attributes: attributes1,
                            where: condition1,
                            required: true,
                            include: [{
                                model: model3,
                                attributes: attributes3,
                                ...condition3 !== undefined && {
                                    where: condition3
                                },
                            }]
                        }
                    ],
                    attributes: attributes2,
                    where: condition2,
                    required: false
                },
                {
                    model: model3,
                    as: 'AssignedTo',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: false
                },
                {
                    model: model3,
                    as: 'Approver',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: false
                }
            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            }
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}



exports.getEmployeeROPAWithDocs = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model3,
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                            required: false
                        }

                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: false
                },
                {
                    model: model2,
                    include: [
                        {
                            model: model3,
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                            required: false
                        },
                        {
                            model: model1,
                            attributes: attributes1,
                            where: condition1,
                            required: true,
                            include: [{
                                model: model3,
                                attributes: attributes3,
                                ...condition3 !== undefined && {
                                    where: condition3
                                },
                                required: false
                                
                            }]
                        }
                    ],
                    attributes: attributes2,
                    where: condition2,
                    required: false
                },
                {
                    model: model3,
                    as: 'AssignedTo',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: false
                },
                {
                    model: model3,
                    as: 'Approver',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: false
                },
                {
                    model: model4,
                    // as: 'RopaDocs',
                    attributes: attributes4,
                    ...condition4 !== undefined && {
                        where: condition4
                    },
                    required: false
                },
                
            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
            subQuery: false
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}


/**
 * Retrieves a list of controls with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} model3 - The third associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Object} condition3 - The condition to filter the third associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} attributes3 - The attributes to select from the third associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of controls with their associated models, or false if an error occurs.
 */
exports.getControls = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, order) => {
    try {
        const list = await model.findAll({
            attributes: attributes,
            where: condition,
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false
            },
            {
                model: model2,
                attributes: attributes2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            }],
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

exports.getAllRopaControls = async (model, model1, model2, condition, condition1, condition2,  attributes, attributes1, attributes2, order) => {
  try {
    const list = await model.findAll({
      attributes: attributes,
      where: condition,
      include: [
        {
          model: model1,
          ...(attributes1 !== undefined && {
            attributes: attributes1
          }),
          ...(condition1 !== undefined && {
            where: condition1
          }),
          required: false
        },
        {
          model: model2,
          attributes: attributes2,
          ...(condition2 !== undefined && {
            where: condition2
          }),
          required: false
        }
      ],
      order: order
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('error', error);
    return false;
  }
};


exports.getControlsWithCategory = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, order) => {
    try {
        const list = await model.findAll({  // customer controls
            attributes: attributes,
            where: condition,
            include: [{
                model: model1, // controls
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false
            },
            {
                model: model2, // answers
                attributes: attributes2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                include: [{
                    model: model3, //user
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            },
            {
                model: model4, //category
                ...attributes4 !== undefined && {
                    attributes: attributes4
                },
                ...condition4 !== undefined && {
                    where: condition4
                },
                required: false
            },
            ],
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of controls with reviews.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} model3 - The third associated model to include in the query.
 * @param {Object} model4 - The fourth associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Object} condition3 - The condition to filter the third associated model.
 * @param {Object} condition4 - The condition to filter the fourth associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} attributes3 - The attributes to select from the third associated model.
 * @param {Array} attributes4 - The attributes to select from the fourth associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of controls with their associated models, or false if an error occurs.
 */
exports.getControlsWithReview = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, order) => {
    try {
        const list = await model.findAll({
            attributes: attributes,
            where: condition,
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false
            },
            {
                model: model2,
                attributes: attributes2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            },
            {
                model: model4,
                attributes: attributes4,
                ...condition4 !== undefined && {
                    where: condition4
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            }],
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves the ROPA for the given ROPA ID.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @returns {Object|boolean} - The ROPA for the given ROPA ID, or false if an error occurs.
 */
exports.getROPA = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3) => {
    try {
        const head = await model.findOne({
            where: condition,
            include: [
                {
                    model: model1,
                    attributes: attributes1,
                    where: condition1,
                    required: false,
                    include: [{
                        model: model3,
                        attributes: attributes3,
                        where: condition3,
                        required: false
                    }]
                },
                {
                    model: model2,
                    attributes: attributes2,
                    where: condition2,
                    include: [{
                        model: model1,
                        attributes: attributes1,
                        where: condition1,
                        required: true,
                        include: [{
                            model: model3,
                            attributes: attributes3,
                            where: condition3,
                            required: false
                        }]
                    }],
                    required: false
                },
                {
                    model: model3,
                    as: 'AssignedTo',
                    attributes: attributes3,
                    where: condition3,
                    required: false
                },
                {
                    model: model3,
                    as: 'Approver',
                    attributes: attributes3,
                    where: condition3,
                    required: false
                }
            ],
            attributes: attributes
        });
        return head ? JSON.parse(JSON.stringify(head)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

exports.getROPAWithAssignee = async (model, model1, condition, condition1, attributes, attributes1, order) => {
    try {
        const head = await model.findOne({
            where: condition,
            include: [
                {
                    model: model1,
                    attributes: attributes1,
                    where: condition1,
                    as: 'AssignedTo',
                    required: false
                }
            ],
            attributes: attributes,
            order: order
        });
        return head ? JSON.parse(JSON.stringify(head)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }

}

exports.getAnswers = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, order) => {
    try {
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
            },
            {
                model: model2,
                ...attributes2 !== undefined && {
                    attributes: attributes2
                },
                ...condition2 !== undefined && {
                    where: condition2
                },
                required: true,
            }],
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getAnswers', error);
        return false
    }
}

exports.getControlsWithAnswersAndReviews = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
    try {
        let list = await model.findAndCountAll({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    required: false
                },
                {
                    model: model2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: false
                },
            ],
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log("eeeeeeee", error)
        return false
    }
};

exports.getRopaThirdParty = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, order) => {
    try {
        // Answers, CustomerControls, ROPA, Departments
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: {
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true,
                    include: {
                        model: model3,
                        ...attributes3 !== undefined && {
                            attributes: attributes3
                        },
                        ...condition3 !== undefined && {
                            where: condition3
                        },
                        required: true,
                    },
                },
                ]
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getAnswers', error);
        return false
    }
}

exports.getDepartmentBasicInfo = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, order) => {
    try {
        const list = await model.findOne({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true
                },
                {
                    model: model3,
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: true
                },
                {
                    model: model4,
                    ...attributes4 !== undefined && {
                        attributes: attributes4
                    },
                    ...condition4 !== undefined && {
                        where: condition4
                    },
                    required: false
                }]
            }],
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getDepartmentBasicInfo', error);
        return false
    }
}

exports.getProcessBasicInfo = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, order) => {
    try {
        const list = await model.findOne({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true,
                    include: [{
                        model: model3,
                        ...attributes3 !== undefined && {
                            attributes: attributes3
                        },
                        ...condition3 !== undefined && {
                            where: condition3
                        },
                        required: true
                    },
                    {
                        model: model4,
                        ...attributes4 !== undefined && {
                            attributes: attributes4
                        },
                        ...condition4 !== undefined && {
                            where: condition4
                        },
                        required: true
                    }]
                },
                {
                    model: model3,
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: true
                },
                {
                    model: model1,
                    as: 'children',
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    required: false
                }]
            }],
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getProcessBasicInfo', error);
        return false
    }
}

exports.getRopaDpiaReuqirement = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, order) => {
    try {
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: {
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: {
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition1 !== undefined && {
                        where: condition2
                    },
                    required: true,
                }
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getProcessBasicInfo', error);
        return false
    }
}

exports.validateHeaders = async (filePath, requiredHeaders) => {
    try {
        const fileStream = fs.createReadStream(filePath);
        let headers = [];

        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        for await (const line of rl) {
            headers = line.split(',');
            // Only need to read the first line for headers
            break;
        }

        for (let header of requiredHeaders) {
            if (!headers.includes(header)) {
                return { isValid: false, missingHeader: header };
            }
        }

        fileStream.close();

        return { isValid: true };
    } catch (error) {
        console.log('error', error);
        return false;
    }
}

exports.getCollaborators = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, order) => {
    try {
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true
                }]
            },
            ],
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getCollaborators', error);
        return false
    }
}
exports.makeApiCall = async (url, data, headers, retries = 3) => {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`Attempt ${attempt} to call API...`);
        const response = await axios.post(url, data, { headers });
        console.log("Response:", response.data);
        return response.data; // Success — exit function
      } catch (error) {
        console.error(
          `Error on attempt ${attempt}:`,
          error.response ? error.response.data : error.message
        );
        if (attempt === retries) {
          console.error("Max retries reached. API call failed.");
        }
      }
    }
  };
  exports.addApiCall = async (url, data, headers)=>{
    try{
        const response = await axios.post(url, data, { headers });
        if(!response.data){
            return false
        }
        return response.data;
    }
    catch(error){
        console.error(`Error:`,error.response ? error.response.data : error.message);
        return false
    }
  }

  exports.getApiCall = async (url, headers)=>{
    try{
        const config = { headers };
        const response = await axios.get(url, config );
        if(!response.data){
            return false
        }
        return response.data;
    }
    catch(error){
        console.error(`Error:`,error.response ? error.response.data : error.message);
        return false
    }
  }
  exports.updateApiCall = async (url, data, headers)=>{
    try{
        const response = await axios.patch(url, data, { headers });
        if(!response.data){
            return false
        }
        return response.data;
    }
    catch(error){
        console.error(`Error:`,error);
        return false
    }
  }
  exports.privacyNoticeWebhookCall = async (url, data, headers) => {
  
      try {
        const response = await axios.post(url, data, { headers });
        console.log("Response:", response.data);
        return response.data; // Success — exit function
      } catch (error) {
        console.error(
          error.response ? error.response.data : error.message
        )
      }
  };

  /**
 * Extracts process hierarchy from structured data
 * @param {Array} rawData - Raw data from Excel sheet as array of arrays
 * @returns {Array} Structured process hierarchy
 */
exports.extractProcessHierarchy=async (rawData)=> {
    // Find header rows dynamically
    let categoryRow = -1;
    let questionRow = -1;
    let dataStartRow = -1;
    
    // Identify key rows by structure rather than specific text
    for (let i = 0; i < Math.min(10, rawData.length); i++) { // Check first 10 rows
      const row = rawData[i];
      
      // Skip empty rows
      if (!row || row.length < 2) continue;
      
      // Category row typically has multiple non-empty cells with text
      // and is followed by a row with more detailed text (questions)
      const nonEmptyCells = row.filter(cell => cell && cell.toString().trim() !== '').length;
      
      if (nonEmptyCells >= 3 && categoryRow === -1) {
        // Check if next row has more non-empty cells (likely questions)
        if (i + 1 < rawData.length) {
          const nextRow = rawData[i + 1];
          const nextRowNonEmptyCells = nextRow.filter(cell => cell && cell.toString().trim() !== '').length;
          
          if (nextRowNonEmptyCells >= nonEmptyCells) {
            categoryRow = i;
            questionRow = i + 1;
            
            // Find first data row (typically has values in columns A and B)
            for (let j = questionRow + 1; j < rawData.length; j++) {
              if (rawData[j] && rawData[j][0] && rawData[j][1] && 
                  rawData[j][0].toString().trim() !== '' && 
                  rawData[j][1].toString().trim() !== '') {
                dataStartRow = j;
                break;
              }
            }
            
            break;
          }
        }
      }
    }
    
    // If we couldn't identify the structure, try an alternative approach
    if (categoryRow === -1 || questionRow === -1 || dataStartRow === -1) {
      console.log("Using alternative structure detection method");
      
      // Look for rows with content in column A and B as potential data rows
      for (let i = 0; i < rawData.length; i++) {
        const row = rawData[i];
        if (row && row[0] && row[1] && 
            row[0].toString().trim() !== '' && 
            row[1].toString().trim() !== '') {
          
          // Assume the two rows before this are category and question rows
          if (i >= 2) {
            categoryRow = i - 2;
            questionRow = i - 1;
            dataStartRow = i;
            break;
          }
        }
      }
    }
    
    // If we still couldn't identify the structure, return empty result
    if (categoryRow === -1 || questionRow === -1 || dataStartRow === -1) {
      console.error("Could not identify the file structure");
      return [];
    }
    
    console.log(`Detected structure: Category row=${categoryRow}, Question row=${questionRow}, Data start row=${dataStartRow}`);
    
    // Extract categories and their column spans
    const categories = [];
    let currentCategory = null;
    let startCol = 0;
    
    for (let col = 2; col < rawData[categoryRow].length; col++) {
      const cell = rawData[categoryRow][col];
      
      if (cell && cell.toString().trim() !== '') {
        // If we have a current category, add it with its span
        if (currentCategory) {
          categories.push({
            name: currentCategory,
            startCol: startCol,
            endCol: col - 1
          });
        }
        
        // Start a new category
        currentCategory = cell;
        startCol = col;
      }
    }
    
    // Add the last category
    if (currentCategory) {
      categories.push({
        name: currentCategory,
        startCol: startCol,
        endCol: rawData[categoryRow].length - 1
      });
    }
    // Extract questions for each category
    const categoryQuestions = categories.map(category => {
      const questions = [];
      
      for (let col = category.startCol; col <= category.endCol; col++) {
        const question = rawData[questionRow][col];
        if (question && question.toString().trim() !== '') {
          questions.push({
            text: question,
            column: col
          });
        }
      }
      
      return {
        ...category,
        questions
      };
    });
    
    // Now extract processes and subprocesses
    const processes = [];
    let currentProcess = null;
    let currentSubProcess = null;
    
    for (let row = dataStartRow; row < rawData.length; row++) {
      const rowData = rawData[row];
      
      // Skip empty rows
      if (!rowData || !rowData[0] && !rowData[1]) continue;
      
      // Check for process (column 0)
      if (rowData[0] && rowData[0].toString().trim() !== '') {
        // Save previous process
        if (currentProcess) {
          processes.push(currentProcess);
        }
        
        // Create new process
        currentProcess = {
          name: rowData[0],
          subProcesses: []
        };
        currentSubProcess = null;
      }
      
      // Check for subprocess (column 1)
      if (rowData[1] && rowData[1].toString().trim() !== '') {
        // Skip if no current process
        if (!currentProcess) continue;
        
        // Create new subprocess
        currentSubProcess = {
          name: rowData[1],
          categories: []
        };
        
        // Add subprocess to current process
        currentProcess.subProcesses.push(currentSubProcess);
        
        // Extract data for each category and question
        categoryQuestions.forEach(category => {
          const categoryData = {
            name: category.name,
            questions: []
          };
          
          category.questions.forEach(question => {
            const answer = rowData[question.column] || '';
            
            categoryData.questions.push({
              text: question.text,
              answer: answer.toString()
            });
          });
          
          currentSubProcess.categories.push(categoryData);
        });
      }
    }
    
    // Add the last process
    if (currentProcess) {
      processes.push(currentProcess);
    }
    
    return processes;
  };
  exports.extractProcessHierarchyV2 = async (rawData)=> {
    // Find header rows dynamically
    let categoryRow = -1;
    let questionRow = -1;
    let dataStartRow = -1;
    
    // Identify key rows by structure rather than specific text
    for (let i = 0; i < Math.min(10, rawData.length); i++) { // Check first 10 rows
      const row = rawData[i];
      
      // Skip empty rows
      if (!row || row.length < 2) continue;
      
      // Category row typically has multiple non-empty cells with text
      // and is followed by a row with more detailed text (questions)
      const nonEmptyCells = row.filter(cell => cell && cell.toString().trim() !== '').length;
      
      if (nonEmptyCells >= 3 && categoryRow === -1) {
        // Check if next row has more non-empty cells (likely questions)
        if (i + 1 < rawData.length) {
          const nextRow = rawData[i + 1];
          const nextRowNonEmptyCells = nextRow.filter(cell => cell && cell.toString().trim() !== '').length;
          
          if (nextRowNonEmptyCells >= nonEmptyCells) {
            categoryRow = i;
            questionRow = i + 1;
            
            // Find first data row (typically has values in columns A and B)
            for (let j = questionRow + 1; j < rawData.length; j++) {
              if (rawData[j] && rawData[j][0] && rawData[j][1] && 
                  rawData[j][0].toString().trim() !== '' && 
                  rawData[j][1].toString().trim() !== '') {
                dataStartRow = j;
                break;
              }
            }
            
            break;
          }
        }
      }
    }
    
    // If we couldn't identify the structure, try an alternative approach
    if (categoryRow === -1 || questionRow === -1 || dataStartRow === -1) {
      console.log("Using alternative structure detection method");
      
      // Look for rows with content in column A and B as potential data rows
      for (let i = 0; i < rawData.length; i++) {
        const row = rawData[i];
        if (row && row[0] && row[1] && 
            row[0].toString().trim() !== '' && 
            row[1].toString().trim() !== '') {
          
          // Assume the two rows before this are category and question rows
          if (i >= 2) {
            categoryRow = i - 2;
            questionRow = i - 1;
            dataStartRow = i;
            break;
          }
        }
      }
    }
    
    // If we still couldn't identify the structure, return empty result
    if (categoryRow === -1 || questionRow === -1 || dataStartRow === -1) {
      console.error("Could not identify the file structure");
      return [];
    }
    
    console.log(`Detected structure: Category row=${categoryRow}, Question row=${questionRow}, Data start row=${dataStartRow}`);
    const hasSubprocessColumn = (() => {
        // Check if there's a header for subprocess in column B
        if (rawData[questionRow-1] && rawData[questionRow-1][1]) {
          const headerB = rawData[questionRow-1][1].toString().trim().toLowerCase();
          return headerB.includes('sub') || headerB.includes('child') || headerB.includes('function');
        }
        
        // Check if column B is consistently used for subprocesses
        let filledColumnBCount = 0;
        let totalRowsChecked = 0;
        
        for (let row = dataStartRow; row < Math.min(dataStartRow + 5, rawData.length); row++) {
          if (rawData[row]) {
            totalRowsChecked++;
            if (rawData[row][1] && rawData[row][1].toString().trim() !== '') {
              filledColumnBCount++;
            }
          }
        }
        
        // If column B is used in most rows, it's likely for subprocesses
        return totalRowsChecked > 0 && filledColumnBCount / totalRowsChecked > 0.3;
      })();
    // Extract categories and their column spans
    const categories = [];
    let currentCategory = null;
    let startCol = 0;
    let i ;

    hasSubprocessColumn ? i=2 : i=0;

    for (let col = i; col < rawData[categoryRow].length; col++) {
      const cell = rawData[categoryRow][col];
      
      if (cell && cell.toString().trim() !== '') {
        // If we have a current category, add it with its span
        if (currentCategory) {
          categories.push({
            name: currentCategory,
            startCol: startCol,
            endCol: col - 1
          });
        }
        
        // Start a new category
        currentCategory = cell;
        startCol = col;
      }
    }
    
    // Add the last category
    if (currentCategory) {
      categories.push({
        name: currentCategory,
        startCol: startCol,
        endCol: rawData[categoryRow].length - 1
      });
    }
    // Extract questions for each category
    const categoryQuestions = categories.map(category => {
      const questions = [];
      
      for (let col = category.startCol; col <= category.endCol; col++) {
        const question = rawData[questionRow][col];
        if (question && question.toString().trim() !== '') {
          questions.push({
            text: question,
            column: col
          });
        }
      }
      
      return {
        ...category,
        questions
      };
    });
    // Now extract processes and subprocesses
    const processes = [];
    let currentProcess = null;
    let currentSubProcess = null;
    
    for (let row = dataStartRow; row < rawData.length; row++) {
      const rowData = rawData[row];
      
      // Skip empty rows
      if (!rowData || !rowData[0] && !rowData[1]) continue;
      
      // Check for process (column 0)
      if (rowData[0] && rowData[0].toString().trim() !== '') {
        // Save previous process
        if (currentProcess) {
          processes.push(currentProcess);
        }
        
        // Create new process
        currentProcess = {
          name: rowData[0],
          subProcesses: []
        };
        currentSubProcess = null;
      }
      
      // Check for subprocess (column 1)
      if (hasSubprocessColumn && rowData[1] && rowData[1].toString().trim() !== '') {
        // Skip if no current process
        if (!currentProcess) continue;
        
        // Create new subprocess
        currentSubProcess = {
          name: rowData[1],
          categories: []
        };
        
        // Add subprocess to current process
        currentProcess.subProcesses.push(currentSubProcess);
        
        // Extract data for each category and question
        categoryQuestions.forEach(category => {
          const categoryData = {
            name: category.name,
            questions: []
          };
          
          category.questions.forEach(question => {
            const answer = rowData[question.column] || '';
            
            categoryData.questions.push({
              text: question.text,
              answer: answer.toString()
            });
          });
          
          currentSubProcess.categories.push(categoryData);
        });
      }
      else{
        currentProcess = {
            name: rowData[0].toString().trim(),
            isChild: false,
            categories: []
          };
          
          // Extract data for each category and question
          categoryQuestions.forEach(category => {
            const categoryData = {
              name: category.name,
              questions: []
            };
            
            category.questions.forEach(question => {
              const answer = rowData[question.column] || '';
              
              categoryData.questions.push({
                text: question.text,
                answer: answer.toString()
              });
            });
            
            currentProcess.categories.push(categoryData);
          });
          processes.push(currentProcess);
          currentProcess = null;
        }
    }
    
    // Add the last process
    if (currentProcess) {
      processes.push(currentProcess);
    }
    
    return processes;
  };