const {validate} = require("../helper/requestValidator");
const schema = require("../validation/auth");
const connectToDatabase = require('../database/index')
const AuthController = require("../controller/auth");
const HttpStatus = require("http-status");
const CustomResponse = require('../response/index');
const {generateUniqueMpin, signToken} = require('../config/helper')
const jwt = require("jsonwebtoken");
const {sendMail} = require('../config/email')
const bcrypt = require("bcryptjs");
const {sendSms} = require('../config/sms')
const moment = require("moment");
const {USER_ROLE} = require("../config/constant");
const {Op} = require("sequelize");
const {Json} = require("sequelize/lib/utils");
const salt = bcrypt.genSaltSync(10);
const UserRepo = require('../database/repository/UserRepo')
const OrganizationRepo = require('../database/repository/OrganizationRepo')
const TokenService = require('../jwt/TokenService')
const ApplicationConstant = require('../constant/ApplicationConstant')
const commonService = require("./common");

const userLogin = async (req) => {
    // try {
    //     // validation
    //     const checkReq = validate(req, schema.login);
    //     if (!checkReq.status) {
    //         return checkReq.data;
    //     }
    //     const login = await AuthController.login(req, res);
    //     return login.data;
    //
    // } catch (error) {
    //     console.log("error", error);
    //     const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'INTERNAL_SERVER_ERROR'}, INTERNAL_SERVER_ERROR).data;
    //     return res.send(responseMessage);
    // }

    return await AuthController.login(req)
}

const userLogout = async (req, res) => {
    AuthController.logout(req, res)
}

const userForgetPassword = async (req, res) => {
    AuthController.forgotPassword(req, res)
}

const verifyOtp = async (req, res) => {
    AuthController.verifyOtp(req, res)
}

const resetPassword = async (req, res) => {
    AuthController.resetPassword(req, res)
}

const resendOTP = async (req, res) => {
    AuthController.resendOtp(req, res);
}

const profileUpdate = async (req, res) => {
    AuthController.updateProfile(req, res)
}

const verifyEmail = async (req, res) => {
    var responseMessage;
    try {
        if (!req.query || !req.query.token) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({
                    message: 'Email Verification Token is missing'
                })
            }
        }
        const {...tokenData} = await TokenService.decodeEmailVerificationToken(req.query.token)

        if (!tokenData) {
            const responseMessage = {
                statusCode: 400,
                message: 'Invalid Verification Token'
            }

            res.send(responseMessage)
        }
        const userData = await UserRepo.updateUserEmailVerifiedStatus(true, tokenData.userId, tokenData.email)

        if (!userData) {
            responseMessage = {
                statusCode: 400,
                body: JSON.stringify({
                    message: 'Error occurred while updating the verification status of Email'
                })
            }
            res.send(responseMessage)

        }
        responseMessage = {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Email verified successfully'
            })
        }
        res.send(responseMessage)

    } catch (error) {
        console.log('Error occurred in UserService.js method:- verifyEmail')
        console.log('Error Details:- ', error)
        const responseMessage = {
            statusCode: 500,
            body: JSON.stringify({
                message: 'Something went wrong'
            })
        }
        res.send(responseMessage)
    }
}

const sendVerificationMail = async (req, res) => {
    try {
        const {...tokenData} = await TokenService
            .decodeLoginAuthenticationToken(req.headers.authorization)

        if (!tokenData) {
            const responseMessage = {
                statusCode: 401,
                body: JSON.stringify({message: 'Invalid token'}),
            };
            res.send(responseMessage);
        }

        const userData = await UserRepo.findUserById(tokenData.userId)

        if (userData.is_email_verified) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "Email is already Verified"}),
            };
            return res.status(400).send(responseMessage).end()
        }
        const applicationBaseUrl = ApplicationConstant.APPLICATION_BASE_URL
        let baseVerificationLink = applicationBaseUrl + '/users/verify-mail?token='
        const verificationToken = await signToken({
            email: userData.email,
            userId: userData.id,
        }, 86400);

        const current_year = moment().year();
        const subject = "Verfication Mail";
        const textTemplate = "verification-mail.ejs";
        baseVerificationLink += verificationToken;
        const sendData = {
            name: `${userData.firstName} ${userData.lastName}`,
            current_year,
            portal_name: 'Go Asset',
            verificationLink: baseVerificationLink
        };

        await sendMail(userData.email, sendData, subject, textTemplate);
        const responseMessage = {
            statusCode: 200,
            body: JSON.stringify({message: "Verification Mail Sent"}),
        };
        res.send(responseMessage)

    } catch (error) {
        const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'INTERNAL_SERVER_ERROR'}, HttpStatus.INTERNAL_SERVER_ERROR).data;
        return res.send(responseMessage);
    }
}


const getMpins = async (req, res) => {
    try {
        const {User} = await connectToDatabase();
        const mpinArr = []
        const unique_m_pin1 = await generateUniqueMpin(User, 4);
        const unique_m_pin2 = await generateUniqueMpin(User, 4);
        const unique_m_pin3 = await generateUniqueMpin(User, 4);
        const unique_m_pin4 = await generateUniqueMpin(User, 4);
        mpinArr.push(unique_m_pin1.mPin)
        mpinArr.push(unique_m_pin2.mPin)
        mpinArr.push(unique_m_pin3.mPin)
        mpinArr.push(unique_m_pin4.mPin)
        const responseMessage = {
            statusCode: 200,
            body: JSON.stringify({
                data: mpinArr,
                message: "M-pin generated successfully. "
            }),
        };
        res.send(responseMessage)
    } catch (error) {
        const responseMessage = {
            statusCode: error.statusCode || 500,
            body: "Something went wrong",
        };
        res.send(responseMessage)
    }
}

const updateMpins = async (req, res) => {
    try {
        let body = req.body
        const token = req.headers.authorization.split(' ')[1];
        let {...tokenData} = await jwt.decode(token, process.env.JWT_SECRET);
        const userId = tokenData.userId;
        if (!userId) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "Invalid requested API url.You need to make api url with driver id."}),
            };
            res.send(responseMessage);
        }
        if (userId) {
            if (!Number.isInteger(Number(userId))) {
                const responseMessage = {
                    statusCode: 400,
                    body: JSON.stringify({message: "Invalid driver id."}),
                };
                res.send(responseMessage)
            }
        }

        if (!body?.mpinCode) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "mpinCode is required."}),
            };
            res.send(responseMessage)
        }
        const {User} = await connectToDatabase();
        let userObj = await User.findOne({
            where: {id: userId}
        })
        if (userObj) {
            const hash_mPin = await bcrypt.hash(body?.mpinCode, salt);
            await userObj.update({mpin: hash_mPin});
            if (userObj.email && userObj.email != null) {
                const current_year = moment().year();
                const sendData = {
                    name: `${userObj.firstName} ${userObj.lastName}`,
                    mpin: body?.mpinCode,
                    current_year,
                    portal_name: 'Go Asset'
                };
                const subject = " - New mPin";
                const textTemplate = "password-mpin-email.ejs";
                await sendMail(userObj.email, sendData, subject, textTemplate);
            }
            const message_text = `Let’s get you back in to Go Asset! In order to reset your mPin, your new mPin mentioned below! ` + `Your mPin is ${body?.mpinCode}`
            if (userObj.phone && userObj.phone != null && userObj.country_code && userObj.country_code != null) {
                const phone_number = '+' + userObj.country_code + userObj.phone;
                await sendSms(phone_number, message_text);
            }
            if (userObj.phone && userObj.phone != null && userObj.country_code == null) {
                const phone_number = userObj.phone;
                await sendSms(phone_number, message_text);
            }
            const responseMessage = {
                statusCode: 200,
                body: JSON.stringify({
                    message: "Driver's m-pin updated successfully."
                }),
            };
            res.send(responseMessage)
        } else {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "User not found."}),
            };
            res.send(responseMessage)
        }

    } catch (error) {
        return {
            statusCode: error.statusCode || 500,
            body: "Something went wrong",
        };
    }
}

const getProfile = async (req, res) => {
    try {
        const token = req.headers.authorization.split(' ')[1];
        const {...tokenData} = await jwt.decode(token, process.env.JWT_SECRET);
        if (!tokenData) {
            const responseMessage = {
                statusCode: 401,
                body: JSON.stringify({message: 'Invalid token'}),
            };
            res.send(responseMessage);
        }

        const {User} = await connectToDatabase();

        let userData = await User.findOne({
            attributes: [
                'firstName', 'lastName', 'address', 'email',
                'email', 'profile_image', 'language', 'country_code', 'phone'
            ],
            where: {id: tokenData.userId}
        });

        if (userData) {
            userData = JSON.parse(JSON.stringify(userData));
            userData.org_id = null;
            userData.organization_name = null;
            const responseMessage = {
                statusCode: 200,
                body: JSON.stringify({
                    message: "Profile fetched successfully.",
                    data: userData,
                }),
            };
            res.send(responseMessage)
        } else {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "Invalid Admin Id!"}),
            };
            res.send(responseMessage)
        }
    } catch (error) {
        console.log(error);
        const responseMessage = {
            statusCode: error.statusCode || 500,
            body: "Something went wrong in update profile API.",
        };
        res.send(responseMessage)
    }
}

const validateMpins = async (req, res) => {
    try {
        const token = req.headers.authorization.split(' ')[1]
        const {...tokenData} = await jwt.decode(token, process.env.JWT_SECRET)
        if (!tokenData) {
            const responseMessage = {
                statusCode: 401,
                body: JSON.stringify({message: authData["message"]}),
            };
            res.send(responseMessage)
        }
        let body = req.body
        let userId = tokenData.userId
        if (!userId || userId === 0) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "Invalid requested API url.You need to make api url with driver id."}),
            };
            res.send(responseMessage)
        }
        if (userId) {
            if (!Number.isInteger(Number(userId))) {
                const responseMessage = {
                    statusCode: 400,
                    body: JSON.stringify({message: "Invalid user id."}),
                };
                res.send(responseMessage)
            }
        }
        const mpin = body.mpin

        if (!mpin) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "mpinCode is required."}),
            };
            res.send(responseMessage)
        }
        const {User} = await connectToDatabase();
        let userObject = await User.findOne({
            attributes: ['id', 'mpin'],
            where: {id: userId}
        })
        if (userObject) {
            const hash_mPin = await bcrypt.hash(mpin, salt);
            if (bcrypt.compareSync(mpin, userObject.mpin)) {
                const responseMessage = {
                    statusCode: 200,
                    body: JSON.stringify({
                        status: true,
                        message: "User's m-pin validated successfully."
                    }),
                };
                res.send(responseMessage)
            } else {
                const resonseMessage = {
                    statusCode: 200,
                    body: JSON.stringify({
                        status: false,
                        message: "Incorrect mPin."
                    }),
                };
                res.send(resonseMessage)
            }

        } else {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "User not found."}),
            };
            res.send(responseMessage)
        }

    } catch (error) {
        const responseMessage = {
            statusCode: error.statusCode || 500,
            body: "Something went wrong",
        };
        res.send(responseMessage)
    }
}

const sendPhoneNumberVerificationLink = async (req, res) => {
    try {
        const {...tokenData} = await TokenService
            .decodeLoginAuthenticationToken(req.headers.authorization)

        if (!tokenData) {
            const responseMessage = {
                statusCode: 401,
                body: JSON.stringify({message: 'Invalid token'}),
            };
            res.send(responseMessage);
        }

        const userData = await UserRepo.findUserById(tokenData.userId)

        if (userData.is_phone_number_verified) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({message: "Phone number is already Verified"}),
            };
            res.send(responseMessage)
        }
        const applicationBaseUrl = ApplicationConstant.APPLICATION_BASE_URL
        let baseVerificationLink = applicationBaseUrl + '/users/verify-mail?token='
        const verificationToken = await signToken({
            email: userData.email,
            userId: userData.id,
            phone: userData.phone
        }, 86400);
        const verificationLink = baseVerificationLink + verificationToken
        const message = 'Click here'
        message.link(verificationLink)

        await sendSms(userData.phone, message);
        const responseMessage = {
            statusCode: 200,
            body: JSON.stringify({message: "Verification Mail Sent"}),
        };
        res.send(responseMessage)

    } catch (error) {
        const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'INTERNAL_SERVER_ERROR'}, INTERNAL_SERVER_ERROR).data;
        return res.send(responseMessage);
    }
}

const verifyPhoneNumberVerificationLink = async (req, res) => {
    var responseMessage;
    try {
        if (!req.query || !req.query.token) {
            const responseMessage = {
                statusCode: 400,
                body: JSON.stringify({
                    message: 'Phone Verification Token is missing'
                })
            }
        }
        const {...tokenData} = await TokenService.decodeEmailVerificationToken(req.query.token)

        if (!tokenData) {
            const responseMessage = {
                statusCode: 400,
                message: 'Invalid Verification Token'
            }

            res.send(responseMessage)
        }
        const userData = await UserRepo
            .updateUserPhoneNumberVerifiedStatus(true, tokenData.userId, tokenData.email, tokenData.phone)

        if (!userData) {
            responseMessage = {
                statusCode: 400,
                body: JSON.stringify({
                    message: 'Error occurred while updating the verification status of Phone number'
                })
            }
            res.send(responseMessage)

        }
        responseMessage = {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Phone number verified successfully'
            })
        }
        res.send(responseMessage)

    } catch (error) {
        console.log('Error occurred in UserService.js method:- verifyEmail')
        console.log('Error Details:- ', error)
        const responseMessage = {
            statusCode: 500,
            body: JSON.stringify({
                message: 'Something went wrong'
            })
        }
        res.send(responseMessage)
    }
}

const signup = async (req, res) => {
    AuthController.signup(req, res)
}

const updateOrganization = async (req, res) => {
    try {

        let body = req.body
        const organisationId = req.query.organisationId;
        const {Organization, Group} = await connectToDatabase();

        // check organization email
        const checkOrgEmail = await commonService.findByCondition(Organization, {email: body.email.toLowerCase()});
        if (checkOrgEmail) {
            const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'ALREADY_REGISTERED'}, HttpStatus.CONFLICT);
            res.send(responseMessage)
        }

        // update organization
        const organisationData = await commonService.updateData(Organization, body, {id: organisationId});
        if (!organisationData) {
            const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'SAVE_ERROR'}, HttpStatus.BAD_REQUEST);
            res.send(responseMessage)
        }

        // update parent group name
        const updateGroup = await commonService.updateData(Group, {name: body.name}, {
            org_id: Number(organisationId),
            parent_id: null,
            name: {[Op.not]: 'Unassigned Assets'}
        })
        if (!updateGroup[1]) {
            const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'SAVE_ERROR'}, HttpStatus.BAD_REQUEST);
            res.send(responseMessage)
        }

        const responseMessage = CustomResponse.customRes(req, true, {
            msgCode: "SIGNUP_SUCCESSFUL",
            data: organisationData[1]
        }, HttpStatus.OK);

        res.send(responseMessage)
    } catch (error) {
        console.log(error);
        const responseMessage = CustomResponse.customRes(req, false, {msgCode: 'INTERNAL_SERVER_ERROR'}, HttpStatus.INTERNAL_SERVER_ERROR);
        res.send(responseMessage)
    }
}

module.exports = {
    userLogin,
    userLogout,
    userForgetPassword,
    verifyOtp,
    resetPassword,
    resendOTP,
    profileUpdate,
    getMpins,
    updateMpins,
    validateMpins,
    getProfile,
    sendVerificationMail,
    verifyEmail,
    sendPhoneNumberVerificationLink,
    verifyPhoneNumberVerificationLink,
    signup,
    updateOrganization
}