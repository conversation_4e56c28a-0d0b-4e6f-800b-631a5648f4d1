const { Op, Sequelize } = require('sequelize');

exports.getRoleDetails = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2) => {
    try {
        // User, Organization, OrganisationResources, Resources
        const list = await model.findOne({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes: attributes
            },
            include: {
                model: model1,
                ...condition1 !== undefined && {
                    where: condition1
                },
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                required: false,
                include: {
                    model: model2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: false
                },
            },
            // subQuery: false,
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        return false
    }
}

/**
 * Retrieves role details based on the provided parameters.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first nested model to include in the query.
 * @param {Object} condition - The condition to apply on the main model.
 * @param {Object} limit - The limit to apply on the main model. 
 * @param {Object} offset - The offset to apply on the main model.
 * @param {Object} order - The order to apply on the main model.
 * @returns {Object|boolean} - The role details if found, otherwise false.
 */

exports.getRolesWithCreators = async (model, model1, condition, limit, offset, order) => {
    try {
        const roles = await model.findAndCountAll({
            where: condition,
            limit,
            offset,
            order,
            include: [{
                model: model1,
                as: 'createdByUser',
                attributes: ['firstName', 'lastName'],
            }],
        });
        return roles ? JSON.parse(JSON.stringify(roles)) : false;
    } catch (error) {
        return false;
    }
};
