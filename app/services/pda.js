const fs = require('fs');
const readline = require('readline');


/**
 * Retrieves a list of departments with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of departments with their associated models, or false if an error occurs.
 */
exports.getDepartmentsPDA = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model2,
                            attributes: attributes2,
                            ...condition2 !== undefined && {
                                where: condition2
                            },
                        }

                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: true
                },
                {
                    model: model2,
                    as: 'AssignedTo',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                },
                {
                    model: model2,
                    as: 'Approver',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                }
            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            }
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

exports.getControlsWithAnswersAndReviews = async (model, model1, model2,alias2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
    try {
        let list = await model.findAndCountAll({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes
            },
            include: [
                {
                    model: model1,
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    required: false
                },
                {
                    model: model2,
                    as: 'ReviewPDA',
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    required: false
                },
            ],
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log("eeeeeeee", error)
        return false
    }
};


/**
 * Retrieves a list of processes with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of processes with their associated models, or false if an error occurs.
 */
exports.getProcessesPDA = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, order) => {
    try {
        const list = await model.findAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model2,
                            attributes: attributes2,
                            ...condition2 !== undefined && {
                                where: condition2
                            },
                        }
                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: true
                },
                {
                    model: model2,
                    as: 'AssignedTo',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                },
                {
                    model: model2,
                    as: 'Approver',
                    attributes: attributes2,
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: false
                }
            ],
            attributes: attributes,
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of processing activities assigned to an employee based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} model3 - The third associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Object} condition3 - The condition to filter the third associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} attributes3 - The attributes to select from the third associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of processing activities assigned to an employee, or false if an error occurs.
 */
exports.getEmployeePDA = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
    try {
        const list = await model.findAndCountAll({
            where: condition,
            include: [
                {
                    model: model1,
                    include: [
                        {
                            model: model3,
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                        }

                    ],
                    attributes: attributes1,
                    where: condition1,
                    required: false
                },
                {
                    model: model2,
                    include: [
                        {
                            model: model3,
                            attributes: attributes3,
                            ...condition3 !== undefined && {
                                where: condition3
                            },
                        },
                        {
                            model: model1,
                            attributes: [],
                            where: condition1,
                            required: true
                        }
                    ],
                    attributes: attributes2,
                    where: condition2,
                    required: false
                },
                {
                    model: model3,
                    as: 'AssignedTo',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: false
                },
                {
                    model: model3,
                    as: 'Approver',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: false
                }
            ],
            attributes: attributes,
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            }
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of controls with their associated models based on the provided conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} model3 - The third associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Object} condition3 - The condition to filter the third associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} attributes3 - The attributes to select from the third associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of controls with their associated models, or false if an error occurs.
 */
exports.getControls = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, order) => {
    try {
        const list = await model.findAll({
            attributes: attributes,
            where: condition,
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false
            },
            {
                model: model2,
                attributes: attributes2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            }],
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves a list of controls with reviews.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} model3 - The third associated model to include in the query.
 * @param {Object} model4 - The fourth associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Object} condition3 - The condition to filter the third associated model.
 * @param {Object} condition4 - The condition to filter the fourth associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @param {Array} attributes3 - The attributes to select from the third associated model.
 * @param {Array} attributes4 - The attributes to select from the fourth associated model.
 * @param {Array} order - The order in which the results should be sorted.
 * @returns {Array|boolean} - The list of controls with their associated models, or false if an error occurs.
 */
exports.getControlsWithReview = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, order) => {
    try {
        const list = await model.findAll({
            attributes: attributes,
            where: condition,
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: false
            },
            {
                model: model2,
                as: 'ReviewPDA',
                attributes: attributes2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            },
            {
                model: model4,
                attributes: attributes4,
                ...condition4 !== undefined && {
                    where: condition4
                },
                include: [{
                    model: model3,
                    // as: 'answered_by',
                    attributes: attributes3,
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                }],
                required: false
            }],
            order: order
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('error', error);
        return false
    }
}

/**
 * Retrieves the PDA for the given PDA ID.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first associated model to include in the query.
 * @param {Object} model2 - The second associated model to include in the query.
 * @param {Object} condition - The condition to filter the main model.
 * @param {Object} condition1 - The condition to filter the first associated model.
 * @param {Object} condition2 - The condition to filter the second associated model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first associated model.
 * @param {Array} attributes2 - The attributes to select from the second associated model.
 * @returns {Object|boolean} - The PDA for the given PDA ID, or false if an error occurs.
 */
exports.getPDA = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3) => {
    try {
        const head = await model.findOne({
            where: condition,
            include: [
                {
                    model: model1,
                    attributes: attributes1,
                    where: condition1,
                    required: false,
                    include: [{
                        model: model3,
                        attributes: attributes3,
                        where: condition3,
                        required: true
                    }]
                },
                {
                    model: model2,
                    attributes: attributes2,
                    where: condition2,
                    include: [{
                        model: model1,
                        attributes: attributes1,
                        where: condition1,
                        required: true,
                        include: [{
                            model: model3,
                            attributes: attributes3,
                            where: condition3,
                            required: true
                        }]
                    }],
                    required: false
                },
                {
                    model: model3,
                    as: 'AssignedTo',
                    attributes: attributes3,
                    where: condition3,
                    required: false
                },
                {
                    model: model3,
                    as: 'Approver',
                    attributes: attributes3,
                    where: condition3,
                    required: false
                }
            ],
            attributes: attributes
        });
        return head ? JSON.parse(JSON.stringify(head)) : false;
    } catch (error) {
        console.log('error', error);
        return false
    }
}

exports.getAnswers = async (model, model1, condition, condition1, attributes, attributes1, order) => {
    try {
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: {
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getAnswers', error);
        return false
    }
}

exports.getPdaThirdParty = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, order) => {
    try {
        // Answers, pdaCustomerControls, PDA, pdaDepartments
        const list = await model.findAll({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: {
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true,
                    include: {
                        model: model3,
                        ...attributes3 !== undefined && {
                            attributes: attributes3
                        },
                        ...condition3 !== undefined && {
                            where: condition3
                        },
                        required: true,
                    },
                },
                ]
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getAnswers', error);
        return false
    }
}

exports.getDepartmentBasicInfo = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3, attributes4, order) => {
    try {
        const list = await model.findOne({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true
                },
                {
                    model: model3,
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: true
                },
                {
                    model: model4,
                    ...attributes4 !== undefined && {
                        attributes: attributes4
                    },
                    ...condition4 !== undefined && {
                        where: condition4
                    },
                    required: false
                }]
            }],
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getDepartmentBasicInfo', error);
        return false
    }
}

exports.getProcessBasicInfo = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, order) => {
    try {
        const list = await model.findOne({
            ...attributes !== undefined && {
                attributes: attributes
            },
            ...condition !== undefined && {
                where: condition
            },
            include: [{
                model: model1,
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                ...condition1 !== undefined && {
                    where: condition1
                },
                required: true,
                include: [{
                    model: model2,
                    ...attributes2 !== undefined && {
                        attributes: attributes2
                    },
                    ...condition2 !== undefined && {
                        where: condition2
                    },
                    required: true,
                    include: [{
                        model: model3,
                        ...attributes3 !== undefined && {
                            attributes: attributes3
                        },
                        ...condition3 !== undefined && {
                            where: condition3
                        },
                        required: true
                    }]
                },
                {
                    model: model3,
                    ...attributes3 !== undefined && {
                        attributes: attributes3
                    },
                    ...condition3 !== undefined && {
                        where: condition3
                    },
                    required: true
                },
                {
                    model: model1,
                    as: 'children',
                    ...attributes1 !== undefined && {
                        attributes: attributes1
                    },
                    ...condition1 !== undefined && {
                        where: condition1
                    },
                    required: false
                }]
            }],
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        console.log('getProcessBasicInfo', error);
        return false
    }
}

exports.validateHeaders = async (filePath, requiredHeaders) => {
    try {
        const fileStream = fs.createReadStream(filePath);
        let headers = [];

        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        for await (const line of rl) {
            headers = line.split(',');
            // Only need to read the first line for headers
            break;
        }

        for (let header of requiredHeaders) {
            if (!headers.includes(header)) {
                return { isValid: false, missingHeader: header };
            }
        }

        fileStream.close();

        return { isValid: true };
    } catch (error) {
        console.log('error', error);
        return false;
    }
}