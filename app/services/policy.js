/**
 * Retrieves user details based on specified conditions and attributes.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first model to include in the query.
 * @param {Object} model2 - The second model to include in the query.
 * @param {Object} condition - The condition to apply on the main model.
 * @param {Object} condition1 - The condition to apply on the first included model.
 * @param {Object} condition2 - The condition to apply on the second included model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first included model.
 * @param {Array} attributes2 - The attributes to select from the second included model.
 * @returns {Object|boolean} - The user details if found, otherwise false.
 */


// const { Op, Sequelize } = require('sequelize');

exports.getListAssociateWithoutCount = async (model, model1,alias1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: {
        model: model1,
        as: alias1 ,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false // left join
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },

    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};


exports.getListWithMultipleAssociate = async (model, model1, model2, model3, condition, condition1, condition2, condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false
        },
        {
          model: model2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false
        },
        {
          model: model3,
          ...condition3 !== undefined && {
            where: condition3
          },
          ...attributes3 !== undefined && {
            attributes: attributes3
          },
          required: false
        },
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log("eeeeeeee", error)
    return false
  }
};


exports.getListWithMultipleAssociateV2 = async (model, model1, model2, model3, model4, alias4, model5, alias5, model6, alias6, condition, condition1, condition2, condition3, condition4, condition5, condition6, attributes, attributes1, attributes2, attributes3, attributes4, attributes5, attributes6, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false
        },
        {
          model: model2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false
        },
        {
          model: model3,
          ...condition3 !== undefined && {
            where: condition3
          },
          ...attributes3 !== undefined && {
            attributes: attributes3
          },
          required: false
        },
        {
          model: model4,
          as: alias4,
          ...condition4 !== undefined && {
            where: condition4
          },
          ...attributes4 !== undefined && {
            attributes: attributes4
          },
          required: false
        },
        {
          model: model5,
          as: alias5,
          ...condition5 !== undefined && {
            where: condition5
          },
          ...attributes5 !== undefined && {
            attributes: attributes5
          },
          required: false
        },
        {
          model: model6,
          as: alias6,
          ...condition6 !== undefined && {
            where: condition6
          },
          ...attributes6 !== undefined && {
            attributes: attributes6
          },
          required: false
        },
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log("eeeeeeee", error)
    return false
  }
};



exports.getListWithMultipleAssociates = async (model, model1, model2, model3, model4, condition, condition1, condition2, condition3, condition4, attributes, attributes1, attributes2, attributes3,attributes4, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false
        },
        {
          model: model2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false
        },
        {
          model: model3,
          ...condition3 !== undefined && {
            where: condition3
          },
          ...attributes3 !== undefined && {
            attributes: attributes3
          },
          required: false
        },
        {
          model: model4,
          ...condition4 !== undefined && {
            where: condition4
          },
          ...attributes4 !== undefined && {
            attributes: attributes4
          },
          required: false
        }
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      subQuery: false
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log("errorr", error)
    return false
  }
};

exports.getListWithMultipleAssociates2 = async (model, model1, model2, model3,alias,alias1,alias2,alias3,condition, condition1, condition2,  condition3, attributes, attributes1, attributes2, attributes3, limit, offset, order) => {
  try {
    
    let list = await model.findAndCountAll({
      
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          as: alias1, // Add the alias for User model
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false
        },
        {
          model: model2,
          as: alias2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false
        },
       
        {
          model: model3,
          as: alias3,
          separate: true,
          ...condition3 !== undefined && {
            where: condition3
          },
          ...attributes3!== undefined && {
            attributes: attributes3
          },
          required: false
        }
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      subQuery: false
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log("errorr", error)
    return false
  }
};

exports.getListWithMultipleAssociates3 = async (model, model1, model2,alias,alias1,alias2,condition, condition1, condition2,  attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    
    let list = await model.findAndCountAll({
      
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          as: alias1, // Add the alias for User model
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false
        },
        {
          model: model2,
          as: alias2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false
        },
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      subQuery: false
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log("errorr", error)
    return false
  }
};

exports.getListAssociateWithTwoColumns = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      // where: condition !== undefined ? condition : {},
      // attributes: attributes !== undefined ? attributes : [],
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          as: 'assignee',
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false // left join
          // model: model1,
          // as: 'Assignee',
          // attributes: ['id'], // Select only the 'id' column for assignee
          // required: false // left join
        },
        {
          model: model1,
          as: 'approver',
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false // left join
          // model:model1,
          // as: 'Approver',
          // attributes: ['id'], // Select only the 'id' column for approver
          // required: false // left join
        },
        {
          model: model2,
          ...condition2 !== undefined && {
            where: condition1
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false // left join
          // model:model1,
          // as: 'Approver',
          // attributes: ['id'], // Select only the 'id' column for approver
          // required: false // left join
        }
      ],
      limit: limit !== undefined ? limit : null,
      offset: offset !== undefined ? offset : null,
      order: order !== undefined ? order : []
    });

    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('error', error);
    return false;
  }
};

const sequelize = require('sequelize');

// exports.findAndCount = async (Policy, Department, condition) => {
//   try {
//     const data = await Policy.findAll({
//       attributes: [
//         [sequelize.col('Department.name'), 'departmentName'],
//         [sequelize.fn('COUNT', sequelize.col('Policy.id')), 'count']
//       ],
//       include: [
//         {
//           model: Department,
//           where: { deletedAt: null }, // Join condition between Policy and Department
//           attributes: [] // Exclude all attributes from Department model
//         }
//       ],
//       where: condition,
//       group: ['Department.name'], // Group by department name
//       raw: true
//     });
//     return data ? JSON.parse(JSON.stringify(data)) : false;
//   } catch (error) {
//     return false;
//   }
// };


exports.findAndCount = async (model, model1, condition ,condition1 , attributes , groupBy) => {
  try {
    const data = await model.findAll({
      attributes: attributes,
      include: [
        {
          model: model1,
          where: condition1, 
          attributes: [] 
        }
      ],
      where: condition,
      group: groupBy, // Group by department name
      raw: true
    });
    return data ? JSON.parse(JSON.stringify(data)) : false;
  } catch (error) {
    return false;
  }
};

/**
 * Retrieves a list of records from a model with a group by clause.
 * @param {Object} model - The model to retrieve records from.
 * @param {Object} condition - The condition to filter the records.
 * @param {Array} attributes - The attributes to include in the result.
 * @param {number} limit - The maximum number of records to retrieve.
 * @param {number} offset - The number of records to skip.
 * @param {Array} order - The order in which the records should be sorted.
 * @returns {Promise<Array|boolean>} - The list of records or false if an error occurs.
 */
exports.getListWithGroup = async (model, condition, attributes, limit, offset, order) => {
  try {
    let list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      group: ['status'],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },

    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};


//count the policy 
// const policyCounts = await Policy.findAll({
//     attributes: [
//       'status',
//       [sequelize.fn('COUNT', sequelize.col('Policy.id')), 'count']
//     ],
//     include: [
//       {
//         model: Departments,
//         where: {
//           id: {
//             [Op.in]: department_ids
//           }
//         },
//         attributes: ['name']
//       }
//     ],
//     where: {
//       department_id: {
//         [Op.in]: department_ids
//       }
//     },
//     group: ['Policy.status','Department.name'],
//     raw: true
//   });


exports.getListGroupBy = async (model, condition, attributes, groupBy) => {
  try {
    let list = await model.findAll({
      where: condition,
      attributes: attributes ? attributes : ['status', [sequelize.fn('COUNT', 'id'), 'count']],
      group: groupBy ? groupBy : ['status']
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;
  } catch (error) {
    console.log('error', error);
    return false;
  }
};

// const policyCounts = await Policy.findAll({
//   attributes: ['status', [db.fn('COUNT', 'id'), 'count']],
//   group: ['status']
// });

const { Op } = require('sequelize');

exports.getList = async (model ,condition , attributes) => {
  try {
    const userList = await model.findAll({
      attributes: attributes,
      where: condition
    });

    return userList ? userList : false;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
};

exports.getListWithMultiAssociate = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: [
        {
          model: model1,
          ...condition1 !== undefined && {
            where: condition1
          },
          ...attributes1 !== undefined && {
            attributes: attributes1
          },
          required: false
        },
        {
          model: model2,
          ...condition2 !== undefined && {
            where: condition2
          },
          ...attributes2 !== undefined && {
            attributes: attributes2
          },
          required: false
        },
      ],
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log("eeeeeeee", error)
    return false
  }
};