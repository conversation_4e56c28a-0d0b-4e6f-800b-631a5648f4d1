/**
 * Retrieves role details based on the provided parameters.
 * @param {Object} model - The main model to query.
 * @param {Object} model1 - The first nested model to include in the query.
 * @param {Object} model2 - The second nested model to include in the query.
 * @param {Object} condition - The condition to apply on the main model.
 * @param {Object} condition1 - The condition to apply on the first nested model.
 * @param {Object} condition2 - The condition to apply on the second nested model.
 * @param {Array} attributes - The attributes to select from the main model.
 * @param {Array} attributes1 - The attributes to select from the first nested model.
 * @param {Array} attributes2 - The attributes to select from the second nested model.
 * @returns {Object|boolean} - The role details if found, otherwise false.
 */

exports.getListAssociateWithCount = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    let list = await model.findAndCountAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: {
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        as: 'parent',
        required: false
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      // subQuery:false
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};

exports.getBusinessUnitData = async (model, model1, condition, condition1, attributes, attributes1, limit, offset, order) => {
  try {
    const list = await model.findAll({
      ...condition !== undefined && {
        where: condition
      },
      ...attributes !== undefined && {
        attributes
      },
      include: {
        model: model1,
        ...condition1 !== undefined && {
          where: condition1
        },
        ...attributes1 !== undefined && {
          attributes: attributes1
        },
        required: false // left join
      },
      ...limit !== undefined && {
        limit
      },
      ...offset !== undefined && {
        offset
      },
      ...order !== undefined && {
        order
      },
      group: ['Group.id', 'Group.name', 'Group.parent_id', 'Group.customer_id', 'Group.user_id', 'Group.spoc_id', 'Group.status', 'Group.createdAt', 'Group.updatedAt', 'Group.deletedAt'],
      subQuery: false
    });
    return list ? JSON.parse(JSON.stringify(list)) : false;

  } catch (error) {
    console.log('erro', error);
    return false
  }
};
