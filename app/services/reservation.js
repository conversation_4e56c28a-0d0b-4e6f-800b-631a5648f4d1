const { Op, Sequelize } = require('sequelize');

exports.getReservationList = async (model, model1, model2, model3, model4, model5, condition, condition1, condition2, condition3, condition4, condition5, attributes, attributes1, attributes2, attributes3, attributes4, attributes5, limit, offset, order) => {
    try {
        // User, Organization, OrganisationResources, Resources
        const list = await model.findAndCountAll({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes: attributes
            },
            include: {
                model: model1,
                ...condition1 !== undefined && {
                    where: condition1
                },
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                required: true,
                include: [
                    {
                        model: model2,
                        ...condition2 !== undefined && {
                            where: condition2
                        },
                        ...attributes2 !== undefined && {
                            attributes: attributes2
                        },
                        required: false
                    },
                    {
                        model: model3,
                        ...condition3 !== undefined && {
                            where: condition3
                        },
                        ...attributes3 !== undefined && {
                            attributes: attributes3
                        },
                        required: false
                    },
                    {
                        model: model4,
                        ...condition4 !== undefined && {
                            where: condition4
                        },
                        ...attributes4 !== undefined && {
                            attributes: attributes4
                        },
                        required: false
                    },
                    {
                        model: model5,
                        ...condition5 !== undefined && {
                            where: condition5
                        },
                        ...attributes5 !== undefined && {
                            attributes: attributes5
                        },
                        required: false
                    },
                ]
            },
            ...limit !== undefined && {
                limit
            },
            ...offset !== undefined && {
                offset
            },
            ...order !== undefined && {
                order
            },
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        return false
    }
}


exports.getReservationDetails = async (model, model1, model2, condition, condition1, condition2, attributes, attributes1, attributes2) => {
    try {
        const list = await model.findOne({
            ...condition !== undefined && {
                where: condition
            },
            ...attributes !== undefined && {
                attributes: attributes
            },
            include: [
                {
                model: model1,
                ...condition1 !== undefined && {
                    where: condition1
                },
                ...attributes1 !== undefined && {
                    attributes: attributes1
                },
                required: true,
            },
            {
                model: model2,
                ...condition2 !== undefined && {
                    where: condition2
                },
                ...attributes2 !== undefined && {
                    attributes: attributes2
                },
                required: true,
            },
        ]
        });
        return list ? JSON.parse(JSON.stringify(list)) : false;

    } catch (error) {
        return false
    }
}