module.exports = (sequelize, type) => {
    const VendorType = sequelize.define('VendorType', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        type: {
            type: type.STRING,
            allowNull: true
        }
    },
    {
        tableName: 'vendor_type',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['type']
            }
        ]
    });

    VendorType.associate = (models) => {
        VendorType.hasMany(models.VendorDetail, { foreignKey: 'type_id' });
    };

    return VendorType;
};