module.exports = (sequelize, type) => {
    const UserAmbitions = sequelize.define('UserAmbitions', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ambition_id: {
            type: type.INTEGER,
            allowNull: true
        },
        user_id: {
            type: type.INTEGER,
            allowNull: false
        },
    },
        {
            tableName: 'user_ambitions',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    UserAmbitions.associate = function (models) {
        UserAmbitions.belongsTo(models.Ambitions, { foreignKey: 'ambition_id', as: 'ambitions' });
        UserAmbitions.belongsTo(models.User, { foreignKey: 'user_id', as: 'users' });
    }
    return UserAmbitions;
};