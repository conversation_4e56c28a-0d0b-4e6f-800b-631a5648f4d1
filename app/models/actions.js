module.exports = (sequelize, type) => {
  const Actions = sequelize.define(
    'Actions',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      title: {
        type: type.STRING,
        allowNull: true
      },
      description: {
        type: type.TEXT,
        allowNull: true
      },
      assigned_by: {
        type: type.INTEGER,
        allowNull: true
      },
      assigned_to: {
        type: type.INTEGER,
        allowNull: true
      },
      duty_id: {
        type: type.INTEGER,
        allowNull: true
      },
      assigned_date: {
        type: type.DATE,
        allowNull: true
      },
      deadline_date: {
        type: type.DATE,
        allowNull: true
      },
      regulation_id: {
        type: type.INTEGER,
        allowNull: true
      },
      status: {
        type: type.ENUM('Open', 'Closed'),
        defaultValue: 'Open',
        allowNull: true
      },
      customer_id: {
        type: type.INTEGER,
        allowNull: true
      },
      custom_busi_requirement_id: {
        type: type.INTEGER,
        allowNull: true
      },
      entity_id: {
        type: type.INTEGER,
        allowNull: true
      },
      data_breach_id: {
        type: type.INTEGER,
        allowNull: true
      },
      action_type: {
        type: type.ENUM('DPO_RUNBOOK', 'DATA_BREACH_MANAGEMENT'),
        defaultValue: 'DPO_RUNBOOK',
        allowNull: true
      }
    },
    {
      tableName: 'actions',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['duty_id']
        },
        {
          fields: ['assigned_by']
        },
        {
          fields: ['assigned_to']
        },
        {
          fields: ['regulation_id']
        }
      ]
    }
  );

  Actions.associate = function (models) {
    Actions.belongsTo(models.User, { as: 'AssignedTo', foreignKey: 'assigned_to', constraints: false });
    Actions.belongsTo(models.User, { as: 'AssignedBy', foreignKey: 'assigned_by', constraints: false });
    Actions.belongsTo(models.CustomerBusinessRequirements, { foreignKey: 'custom_busi_requirement_id', targetKey: 'id', constraints: false });
  };

  return Actions;
};
