module.exports = (sequelize, type) => {
    const PendingVerification = sequelize.define("PendingVerification",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            firstName: {
                type: type.STRING, allowNull: true,
            },
            lastName: {
                type: type.STRING, allowNull: true,
            },         
            email: {
                type: type.STRING, allowNull: false
            },
            phone: {
                type: type.STRING, allowNull: true, default: null,
            },
            otp: {
                type: type.INTEGER, allowNull: true, defaultValue: null,
            },       
            request_id: {
                type: type.INTEGER, allowNull: true
            },
            status:{
                type: type.ENUM('PENDING','COMPLETED'),
                defaultValue:'PENDING',
                allowNull:false
            }
                     
        }, {
        tableName: 'pending_verification',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['email']
            },
           
        ]
    });
  
    return PendingVerification;
};