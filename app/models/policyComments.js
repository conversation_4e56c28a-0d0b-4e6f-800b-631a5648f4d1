module.exports = (sequelize, DataTypes) => {
    const PolicyComments = sequelize.define('PolicyComments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        comment: {
            type: DataTypes.STRING,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        policy_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'PolicyComments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['user_id']
            },
            {
                fields: ['policy_id']
            }
        ]

    });
    PolicyComments.associate = function (models) {
        PolicyComments.belongsTo(models.Policy, { foreignKey: 'policy_id' });
        // PolicyComments.hasMany(models.AuditLog, { foreignKey: 'policy_comment_id' , targetKey:'type_id'});
        // PolicyComments.hasMany(models.AuditLog, { foreignKey: 'policy_comment_id', targetKey: 'action_by_id' });
        // PolicyComments.belongsTo(models.AuditLog, { foreignKey: 'id', targetKey: 'comment_id' })
    }

    return PolicyComments;
};