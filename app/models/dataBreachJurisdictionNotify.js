module.exports = (sequelize, DataTypes) => {
    const DataBreachJurisdictionNotify = sequelize.define('DataBreachJurisdictionNotify', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        data_breach_management_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        applicable_jurisdiction_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        have_you_filled_up_the_branch_form: {
            type: DataTypes.ENUM('YES', 'NO'),
            allowNull: true
        },
        breach_identification_no: {
            type: DataTypes.STRING,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        }
    }, {
        tableName: 'data_breach_jurisdiction_notify',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['applicable_jurisdiction_id']
            }
        ]
    });
 
    DataBreachJurisdictionNotify.associate = function (models) {
        DataBreachJurisdictionNotify.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        //DataBreachManagement.belongsTo(models.User, { foreignKey: 'handled_by', constraints: false  });
       // DataBreachManagement.hasMany(models.DataBreachDocument, { foreignKey: 'data_breach_id', constraints: false  });
    };
 
    return DataBreachJurisdictionNotify;
};
 