module.exports = (sequelize, type) => {
    const Packages = sequelize.define('Packages', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
        price: {
            type: type.INTEGER,
            allowNull: true
        },
        // title: {
        //     type: type.STRING,
        //     allowNull: true
        // },
        description: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
    },
        {
            tableName: 'packages',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    Packages.associate = function (models) {
        Packages.hasMany(models.PackagesInfo, { foreignKey: 'package_id', constraints: false  });
    }
    return Packages;
};