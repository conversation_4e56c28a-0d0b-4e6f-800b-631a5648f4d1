module.exports = (sequelize, DataTypes) => {
    const CustomPolicy = sequelize.define('CustomPolicy', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        policy_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        content: {
            type: DataTypes.JSON,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'custom_policy',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['policy_id']
            },
            {
                fields: ['customer_id']
            }
        ]

    });
    CustomPolicy.associate = function (models) {
        CustomPolicy.belongsTo(models.Policy, { foreignKey: 'policy_id', constraints: false  });
        CustomPolicy.belongsTo(models.Customer, { foreignKey: 'customer_id', constraints: false  });
    }

    return CustomPolicy;
};