module.exports = (sequelize, DataTypes) => {
    const AssessmentCustomerControls = sequelize.define('AssessmentCustomerControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // customer_id: {
        //     type: DataTypes.INTEGER,
        //     allowNull: true
        // },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        assessment_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
      },
        embedding: {
            type: DataTypes.ARRAY(DataTypes.FLOAT),
            allowNull: true
        },
        is_automated: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        }

    }, {
        tableName: 'assessment_customer_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });
    AssessmentCustomerControls.associate = function (models) {
        AssessmentCustomerControls.belongsTo(models.AssessmentControls, { foreignKey: 'question_id' });
        AssessmentCustomerControls.belongsTo(models.AssessmentCategory, { foreignKey: 'category_id' });
        AssessmentCustomerControls.belongsTo(models.CustomerAssessment, { foreignKey: 'assessment_id' , targetKey:'id' });
        AssessmentCustomerControls.hasOne(models.AssessmentAnswers, { foreignKey: 'customer_question_id' });
        AssessmentCustomerControls.belongsTo(models.AssessmentCustomerControls, { as: 'Parent', foreignKey: 'parent_id' });
        AssessmentCustomerControls.hasMany(models.AssessmentCustomerControls, { as: 'Children', foreignKey: 'parent_id' });
        AssessmentCustomerControls.hasOne(models.ReviewAssessment, { foreignKey: 'customer_question_id' , as: 'ReviewAssessment'  })
        AssessmentCustomerControls.belongsTo(models.AssessmentTemplate, { foreignKey: 'template_id'  });

    };
    return AssessmentCustomerControls;
}