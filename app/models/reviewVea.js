module.exports = (sequelize, DataTypes) => {
    const ReviewVEA = sequelize.define('ReviewVEA', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            unique: true
        },
        accurate_information: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        comments: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        reviewer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        risk_score: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        mitigation_plan: {
            type: DataTypes.JSON,
            allowNull: true
        },
        attachment:{
            type: DataTypes.STRING,
            allowNull: true
        }
    }, {
        tableName: 'review_vea',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_question_id']
            },
            {
                fields: ['reviewer_id']
            }
        ]
    });

    ReviewVEA.associate = function (models) {
        ReviewVEA.belongsTo(models.User, { foreignKey: 'reviewer_id', constraints: false  });
        ReviewVEA.belongsTo(models.VeaCustomerControls, { foreignKey: 'customer_question_id', constraints: false  });
    };

    return ReviewVEA;
}
