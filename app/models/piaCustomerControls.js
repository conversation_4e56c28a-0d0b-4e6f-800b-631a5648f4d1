module.exports = (sequelize, DataTypes) => {
    const piaCustomerControls = sequelize.define('piaCustomerControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // customer_id: {
        //     type: DataTypes.INTEGER,
        //     allowNull: true
        // },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        pia_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },

    }, {
        tableName: 'pia_customer_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    piaCustomerControls.associate = function (models) {
        piaCustomerControls.belongsTo(models.piaControls, { foreignKey: 'question_id', constraints: false  });
        piaCustomerControls.belongsTo(models.piaCategory, { foreignKey: 'category_id', constraints: false  });
        piaCustomerControls.belongsTo(models.CustomerAssessments, { foreignKey: 'pia_id' , targetKey: 'id', constraints: false  });
        piaCustomerControls.hasOne(models.piaAnswers, { foreignKey: 'customer_question_id', constraints: false  });
        piaCustomerControls.belongsTo(models.piaCustomerControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        piaCustomerControls.hasMany(models.piaCustomerControls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
        piaCustomerControls.hasOne(models.ReviewPIA, { foreignKey: 'customer_question_id' , as: 'ReviewPIA', constraints: false  })
    };

    return piaCustomerControls;
}