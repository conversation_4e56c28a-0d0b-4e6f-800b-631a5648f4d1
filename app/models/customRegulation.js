module.exports = (sequelize, DataTypes) => {
  const CustomerRegulations = sequelize.define(
    'CustomerRegulations',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      regulation_ids: {
        type: DataTypes.ARRAY(DataTypes.INTEGER),
        allowNull: false
      },
      assign_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      entity_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'custom_regulations',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['customer_id']
        }
      ]
    }
  );

  CustomerRegulations.associate = function (models) {
    // CustomerRegulations.hasMany(models.UCFCategory, { foreignKey: 'busi_requirement_id', targetKey: 'id', constraints: false });
    CustomerRegulations.hasMany(models.RegulationsV2, { foreignKey: 'regulation_id', targetKey: 'id', constraints: false });
  };

  return CustomerRegulations;
};
