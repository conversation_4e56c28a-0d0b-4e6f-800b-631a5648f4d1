module.exports = (sequelize, DataTypes) => {
    const ReviewAssessment = sequelize.define('ReviewAssessment', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            unique: true
        },
        accurate_information: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        comments: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        reviewer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        risk_score: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        mitigation_plan: {
            type: DataTypes.JSON,
            allowNull: true
        },
        attachment:{
            type: DataTypes.STRING,
            allowNull: true
        }
    }, {
        tableName: 'review_assessment',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_question_id']
            },
            {
                fields: ['reviewer_id']
            }
        ]
    });

    ReviewAssessment.associate = function (models) {
        ReviewAssessment.belongsTo(models.User, { foreignKey: 'reviewer_id', constraints: false  });
        ReviewAssessment.belongsTo(models.AssessmentCustomerControls, { foreignKey: 'customer_question_id', constraints: false  });
    };

    return ReviewAssessment;
}