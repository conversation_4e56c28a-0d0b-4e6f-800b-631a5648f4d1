module.exports = (sequelize, DataTypes) => {
    const DsJointParty = sequelize.define('DsJointParty', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        data_subject_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            // references: {
            //     model: 'data_subject', // refers to table name
            //     key: 'id'
            // }
        },
        first_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        last_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        unique_identification_type: {
            type: DataTypes.ENUM('PASSPORT', 'DRIVING_LICENSE', 'NATIONAL_ID', 'OTHER'),
            allowNull: false
        },
        unique_identification_number: {
            type: DataTypes.STRING,
            allowNull: false
        },
        address_1: {
            type: DataTypes.STRING,
            allowNull: false
        },
        address_2: {
            type: DataTypes.STRING,
            allowNull: true
        },
        secondary_address: {
            type: DataTypes.STRING,
            allowNull: true
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        state_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        city: {
            type: DataTypes.STRING,
            allowNull: false
        },
        postal_code: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        relationship: {
            type: DataTypes.ENUM('CUSTOMER','FORMER_CUSTOMER', 'CONTRACTOR', 'EMPLOYEE', 'JOB_APPLICANT', 'OTHER'),
            allowNull: false
        },          
        email: {
            type: DataTypes.STRING,
            allowNull: false
        },
        phone_no: {
            type: DataTypes.BIGINT,
            allowNull: false
        },
        dob: {
            type: DataTypes.DATE,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
    }, {
        tableName: 'dsr_joint_party',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['data_subject_id']
            }
        ]
    });

    DsJointParty.associate = function (models) {
        DsJointParty.belongsTo(models.DataSubject, { foreignKey: 'data_subject_id' });
        DsJointParty.belongsTo(models.country, { foreignKey: 'country_id' });
        DsJointParty.belongsTo(models.state, { foreignKey: 'state_id' });
        // DsJointParty.belongsTo(models.City, { foreignKey: 'city', constraints: false });
    };

    return DsJointParty;
};
