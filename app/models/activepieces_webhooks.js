module.exports = (sequelize, DataTypes) => {
    const ActivepiecesWebhooks = sequelize.define('ActivepiecesWebhooks', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        action_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        activepieces_workflow_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
    }, {
        tableName: 'activepieces_webhooks',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['action_name']
            }
        ]
    });

    // ActivepiecesWebhooks.associate = function (models) {
   
    // };

    return ActivepiecesWebhooks;
};
