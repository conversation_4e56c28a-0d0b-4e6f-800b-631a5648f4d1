module.exports = (sequelize, DataTypes) => {
  const ROPA = sequelize.define(
    'ROPA',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      department_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      process_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      start_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      end_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      assigned_to: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      approver: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      risks: {
        type: DataTypes.ENUM('Low', 'Medium', 'High'),
        allowNull: true
      },
      progress: {
        type: DataTypes.DOUBLE,
        default: 0
      },
      status: {
        type: DataTypes.ENUM('Yet to Start', 'Started', 'Under Review', 'Change Request', 'Completed'),
        allowNull: true
        // default: 'Yet to Start'
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      group_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      is_already_performed: {
        type: DataTypes.BOOLEAN
        // default: true
      },
      tentative_completion_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      template_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      is_automated: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      recurrence_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      version: {
        type: DataTypes.INTEGER,
        defaultValue: 1
      },
      parent_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      },
      prefilled_next_version: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
      }
    },
    {
      tableName: 'ropa',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['department_id']
        },
        {
          fields: ['process_id']
        },
        {
          fields: ['assigned_to']
        },
        {
          fields: ['approver']
        },
        {
          fields: ['risks']
        },
        {
          fields: ['status']
        }
      ]
    }
  );
  ROPA.associate = function (models) {
    ROPA.belongsTo(models.Departments, {
      foreignKey: 'department_id'
    });
    ROPA.belongsTo(models.Processes, {
      foreignKey: 'process_id'
    });
    ROPA.belongsTo(models.Group, {
      foreignKey: 'group_id'
    });
    ROPA.hasMany(models.CustomerControls, {
      foreignKey: 'ropa_id'
    });
    ROPA.belongsTo(models.User, {
      as: 'AssignedTo',
      foreignKey: 'assigned_to'
    });
    ROPA.belongsTo(models.User, {
      as: 'Approver',
      foreignKey: 'approver'
    });
    ROPA.hasMany(models.RopaDocuments, {
      foreignKey: 'ropa_id'
    });
  };
  return ROPA;
};
