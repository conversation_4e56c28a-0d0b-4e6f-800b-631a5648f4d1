const { Sequelize } = require('sequelize'); // Import Sequelize
module.exports = (sequelize, type) => {
    const UserRolePrivileges = sequelize.define('UserRolePrivileges', {
        user_role_priviledge_id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            // defaultValue: Sequelize.literal("nextval('user_role_privileges_user_role_priviledge_id_seq')") // Fetch next ID
        },
        role_id: {
            type: type.INTEGER,
            allowNull: true
        },
        resource_id: {
            type: type.INTEGER,
            allowNull: true
        },
        has_read: {
            type: type.BOOLEAN,
            allowNull: false,
            defaultValue: '1'
        },
        has_write: {
            type: type.BOOLEAN,
            allowNull: false,
            defaultValue: '0'
        },
        // org_user_role_id:{
        //     type: type.INTEGER,
        //     allowNull: true
        // }
    },
        {
            tableName: 'user_role_privileges',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['role_id']
                },
                {
                    fields: ['resource_id']
                }
            ]
        });
    UserRolePrivileges.associate = function (models) {
        UserRolePrivileges.belongsTo(models.Role, { foreignKey: 'role_id', constraints: false  });
        UserRolePrivileges.belongsTo(models.Resources, { foreignKey: 'resource_id', constraints: false  });
    }
    return UserRolePrivileges;
};