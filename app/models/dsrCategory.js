module.exports = (sequelize, DataTypes) => {
    const DSRCategory = sequelize.define('DSRCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        form_id:{
            type:DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        // ropa_level: {
        //     type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
        //     allowNull: false
        // },
    }, {
        tableName: 'dsr_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['name']
            }
        ]
    });

    DSRCategory.associate = function (models) {
        DSRCategory.hasMany(models.DSRControls, { foreignKey: 'category_id', constraints: false });
    };

    return DSRCategory;
}