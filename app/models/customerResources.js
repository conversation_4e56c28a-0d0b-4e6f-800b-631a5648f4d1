const { Sequelize } = require('sequelize'); // Import Sequelize
module.exports = (sequelize, type) => {
    const CustomerResources = sequelize.define('CustomerResources', {
        customer_resource_id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            // defaultValue: Sequelize.literal("nextval('customer_resources_customer_resource_id_seq')") // Fetch next ID
        },
        resource_id: {  // 2
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true
        },
        access_type: {
            type: type.INTEGER,
            allowNull: true
        }
    },
        {
            tableName: 'customer_resources',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['resource_id']
                },
                {
                    fields: ['customer_id']
                }
            ]
        });
    CustomerResources.associate = function (models) {
        CustomerResources.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        CustomerResources.belongsTo(models.Resources, { foreignKey: 'resource_id' });
    }
    return CustomerResources;
};