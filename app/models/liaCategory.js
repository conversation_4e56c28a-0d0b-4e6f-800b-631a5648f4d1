module.exports = (sequelize, DataTypes) => {
    const liaCategory = sequelize.define('liaCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        lia_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
    }, {
        tableName: 'lia_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['lia_level']
            }
        ]
    });

    liaCategory.associate = function (models) {
        liaCategory.hasMany(models.liaControls, { foreignKey: 'category_id', constraints: false  });
        liaCategory.hasMany(models.liaCollaborator, { foreignKey: 'category_id', constraints: false  });
    };

    return liaCategory;
};