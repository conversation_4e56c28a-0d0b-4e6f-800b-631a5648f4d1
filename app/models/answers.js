module.exports = (sequelize, type) => {
  const Answers = sequelize.define(
    'Answers',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      customer_question_id: {
        type: type.INTEGER,
        allowNull: false,
        unique: true
      },
      answer: {
        type: type.ARRAY(type.TEXT),
        allowNull: false
      },
      attachment_link: {
        type: type.STRING,
        allowNull: true
      },
      raw_url: {
        type: type.BOOLEAN,
        allowNull: true
      },
      answered_by: {
        type: type.INTEGER,
        allowNull: false
      },
      extra_answer: {
        type: type.ARRAY(type.STRING),
        allowNull: true
      },
      is_automated: {
        type: type.BOOLEAN,
        allowNull: true
      }
    },
    {
      tableName: 'answers',
      freezeTableName: true,
      timestamps: true,
      paranoid: true,
      indexes: [
        // Add this option
        {
          fields: ['customer_question_id'] // Index customer_question_id
        },
        {
          fields: ['answered_by'] // Index answered_by
        }
      ]
    }
  );
  Answers.associate = function (models) {
    Answers.belongsTo(models.CustomerControls, { foreignKey: 'customer_question_id', onDelete: 'CASCADE' });
    Answers.belongsTo(models.User, { foreignKey: 'answered_by' });
  };
  return Answers;
};
