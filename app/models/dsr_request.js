module.exports = (sequelize, DataTypes) => {
    const DsrRequest = sequelize.define('DsrRequest', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        dsr_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
        data_subject_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        request_type: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        description: {
            type: DataTypes.STRING,
            allowNull: true
        },
        request_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        completion_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        is_internal_request: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        dsr_return_preference: {
            type: DataTypes.ENUM('EMAIL', 'POST'),
            allowNull: true
        },
        status: {
            type: DataTypes.ENUM('APPROVED', 'REJECTED', 'PENDING', 'REJECTED_IN_PROGRESS', 'COMPLETED', 'ARCHIVED'),
            defaultValue: 'PENDING'
        },
        previous_status: {
            type: DataTypes.ENUM('APPROVED', 'REJECTED', 'PENDING', 'REJECTED_IN_PROGRESS', 'COMPLETED', 'ARCHIVED'),
            defaultValue: 'PENDING'
        },
        assignee_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        assigned_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        deadline_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        extended: {
            type: DataTypes.ENUM('NO', 'YES'),
            defaultValue: 'NO'
        },
        workflow_step_id: { 
            type: DataTypes.INTEGER,
            allowNull: true
        },
        reject_reason: { 
            type: DataTypes.TEXT,
            allowNull: true
        },
        data_discovery: { 
            type: DataTypes.ENUM('VERIFIED'),
            defaultValue: 'VERIFIED'
        },
        business_unit: { 
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_acknowledge_mail_sent: {
            type: DataTypes.ENUM('NO', 'YES'),
            defaultValue: 'NO'
        },
        // is_ocr_done: {
        //     type: DataTypes.ENUM('NO', 'YES'),
        //     defaultValue: 'NO'
        // },
        first_verification :{
            type: DataTypes.BOOLEAN,
            defaultValue:false,
            allowNull: true
        },
        second_verification :{
            type: DataTypes.BOOLEAN,
            defaultValue:false,
            allowNull: true
        }
    }, {
        tableName: 'dsr_request',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['data_subject_id']
            }
        ]
    });

    DsrRequest.associate = function (models) {
        DsrRequest.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        DsrRequest.belongsTo(models.User, { foreignKey: 'assignee_id' });
        DsrRequest.belongsTo(models.DsrRequestType, { foreignKey: 'request_type' });
        DsrRequest.belongsTo(models.DataSubject, { foreignKey: 'data_subject_id' });
        DsrRequest.hasMany(models.RequestDocument, { foreignKey: 'dsr_request_id' });
        DsrRequest.hasMany(models.CustomRequestTask,{foreignKey:'request_id'})


    };

    return DsrRequest;
};