module.exports = (sequelize, DataTypes) => {
    const ViaControls = sequelize.define('ViaControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
        industry_vertical_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },

    }, {
        tableName: 'via_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    ViaControls.associate = function (models) {
        ViaControls.belongsTo(models.ViaCategory, { foreignKey: 'category_id', constraints: false  });
        ViaControls.hasMany(models.ViaCustomerControls, { foreignKey: 'question_id', constraints: false  });
        ViaControls.belongsTo(models.ViaControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        ViaControls.hasMany(models.ViaControls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
    };

    return ViaControls;
};