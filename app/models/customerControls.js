module.exports = (sequelize, DataTypes) => {
  const CustomerControls = sequelize.define(
    'CustomerControls',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      question_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      category_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      ropa_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      parent_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      is_custom: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        default: false
      },
      title: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      artifact_type: {
        type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
        allowNull: true
      },
      is_attachment: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        default: false
      },
      question: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      fields: {
        type: DataTypes.ARRAY(DataTypes.JSON),
        allowNull: true
      },
      extra_input: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      extra_input_type: {
        type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
        allowNull: true
      },
      extra_input_fields: {
        type: DataTypes.ARRAY(DataTypes.JSON),
        allowNull: true
      },
      endpoint: {
        type: DataTypes.STRING,
        allowNull: true
      },
      embedding: {
        type: DataTypes.ARRAY(DataTypes.FLOAT),
        allowNull: true
      },
      is_automated: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      }
    },
    {
      tableName: 'customer_controls',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['category_id']
        },
        {
          fields: ['question_id']
        },
        {
          fields: ['ropa_id']
        },
        {
          fields: ['parent_id']
        }
      ]
    }
  );

  CustomerControls.associate = function (models) {
    CustomerControls.belongsTo(models.Controls, { foreignKey: 'question_id' });
    CustomerControls.belongsTo(models.Category, { foreignKey: 'category_id' });
    CustomerControls.belongsTo(models.ROPA, { foreignKey: 'ropa_id' });
    CustomerControls.hasOne(models.Answers, { foreignKey: 'customer_question_id', onDelete: 'CASCADE' });
    CustomerControls.belongsTo(models.CustomerControls, { as: 'Parent', foreignKey: 'parent_id' });
    CustomerControls.hasMany(models.CustomerControls, { as: 'Children', foreignKey: 'parent_id' });
    CustomerControls.hasOne(models.ReviewROPA, { foreignKey: 'customer_question_id' });
  };

  return CustomerControls;
};
