module.exports = (sequelize, type) => {
    const PackagesInfo = sequelize.define('PackagesInfo', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        package_id: {
            type: type.INTEGER,
            allowNull: false
        },
        title: {
            type: type.STRING,
            allowNull: true
        },
        description: {
            type: type.TEXT,
            allowNull: true
        },
    },
        {
            tableName: 'packages_info',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    PackagesInfo.associate = function (models) {
        PackagesInfo.belongsTo(models.Packages, { foreignKey: 'package_id' });
    }
    return PackagesInfo;
};