module.exports = (sequelize, type) => {
    const GrafanaDashboardList = sequelize.define("GrafanaDashboardList",
        {
            id: {
                type: type.INTEGER, 
                primaryKey: true, 
                autoIncrement: true,
            },
            name: {
                type: type.ENUM('STRUCTURED','UNSTRUCTURED'),
                allowNull: true,
            },
           url: {
                type: type.STRING,
                allowNull: true,
            },
            customer_id:{
                type: type.INTEGER,
                allowNull: true,
            }
        },
        {
            tableName: 'grafana',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
        }
    );
    return GrafanaDashboardList;
}