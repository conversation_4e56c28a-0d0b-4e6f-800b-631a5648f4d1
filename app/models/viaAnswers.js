module.exports = (sequelize, type) => {
    const ViaAnswers = sequelize.define('ViaAnswers', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: type.INTEGER,
            allowNull: false,
            unique: true
        },
        answer: {
            type: type.ARRAY(type.STRING),
            allowNull: false
        },
        attachment_link: {
            type: type.STRING,
            allowNull: true
        },
        raw_url: {
            type: type.BOOLEAN,
            allowNull: true
        },
        answered_by: {
            type: type.INTEGER,
            allowNull: false
        },
        extra_answer: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
    },
        {
            tableName: 'via_answers',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_question_id']
                },
                {
                    fields: ['answered_by']
                }
            ]
        });
        ViaAnswers.associate = function (models) {
            ViaAnswers.belongsTo(models.ViaCustomerControls, { foreignKey: 'customer_question_id', constraints: false  });
            ViaAnswers.belongsTo(models.User, { foreignKey: 'answered_by', constraints: false  });
    }
    return ViaAnswers;
}