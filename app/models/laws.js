module.exports = (sequelize, type) => {
    const Laws = sequelize.define('Laws', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
    },
        {
            tableName: 'laws',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    // Laws.associate = (models) => {
    //     // Laws.hasMany(models.Policy, { foreignKey: 'relevant_law_id' });
    // }
    return Laws;
};