module.exports = (sequelize, type) => {
  const DSRAuditLog = sequelize.define(
    'DSRAuditLog',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      type: {
        type: type.ENUM('DSR'),
        allowNull: false
      },
      type_id: {
        //dsr_id
        type: type.INTEGER,
        allowNull: true
      },
      action: {
        type: type.STRING,
        allowNull: true
      },
      action_by_id: {
        type: type.INTEGER,
        allowNull: false
      },
      step: {
        type: type.INTEGER,
        allowNull: true
      },
      chat: {
        type: type.BOOLEAN,
        allowNull: true
      },
      // dept_id: {
      //     type: type.INTEGER,
      //     allowNull: true
      // },
      // flag: {
      //     type: type.BOOLEAN,
      //     allowNull: false,
      //     defaultValue: false
      // },
      customer_id: {
        type: type.INTEGER,
        allowNull: true
      }
      // comment_id: {
      //     type: type.INTEGER,
      //     allowNull: true
      // },
    },
    {
      tableName: 'dsr_audit_log',
      freezeTableName: true,
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['type_id'] // Index type_id
        },
        {
          fields: ['action_by_id'] // Index action_by_id
        },
        {
          fields: ['customer_id'] // Index customer_id
        }
      ]
    }
  );
  DSRAuditLog.associate = function (models) {
    DSRAuditLog.belongsTo(models.User, { foreignKey: 'action_by_id' });
    // DSRAuditLog.belongsTo(models.Departments, { foreignKey: 'dept_id', constraints: false });
    // DSRAuditLog.belongsTo(models.PolicyComments, { foreignKey: 'action_by_id', targetKey: 'user_id', constraints: false });
    DSRAuditLog.belongsTo(models.Customer, { foreignKey: 'customer_id' });
    // DSRAuditLog.belongsTo(models.PolicyComments, { foreignKey: 'comment_id', constraints: false });
  };
  return DSRAuditLog;
};
