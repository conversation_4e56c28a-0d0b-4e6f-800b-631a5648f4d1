module.exports = (sequelize, type) => {
    const AssessmentAnswers = sequelize.define('AssessmentAnswers', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: type.INTEGER,
            allowNull: false,
            unique: true
        },
        answer: {
            type: type.ARRAY(type.STRING),
            allowNull: false
        },
        attachment_link: {
            type: type.STRING,
            allowNull: true
        },
        raw_url: {
            type: type.BOOLEAN,
            allowNull: true
        },
        answered_by: {
            type: type.INTEGER,
            allowNull: false
        },
        extra_answer: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
        is_automated: {
            type: type.BOOLEAN,
            allowNull: true
        }
    },
        {
            tableName: 'assessment_answers',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_question_id']
                },
                {
                    fields: ['answered_by']
                }
            ]
        });
        AssessmentAnswers.associate = function (models) {
            AssessmentAnswers.belongsTo(models.AssessmentCustomerControls, { foreignKey: 'customer_question_id' });
            AssessmentAnswers.belongsTo(models.User, { foreignKey: 'answered_by' });
    }
    return AssessmentAnswers;
}