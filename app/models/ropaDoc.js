module.exports = (sequelize, DataTypes) => {
    const RopaDocuments = sequelize.define('RopaDocuments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ropa_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        version_no: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        doc_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        doc_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        document: {
            type: DataTypes.STRING,
            allowNull: true
        },
        created_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        end_date: {
            type: DataTypes.DATE,
            allowNull: true
        }
    }, {
        tableName: 'ropa_docs',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['ropa_id']
            },
            {
                fields: ['version_no']
            },
            {
                fields: ['doc_id']
            },
            {
                fields: ['doc_name']
            }
        ]
    });
    RopaDocuments.associate = function (models) {
        RopaDocuments.belongsTo(models.ROPA, { foreignKey: 'ropa_id', targetKey: 'id', constraints: false  });
        // RopaDocuments.belongsTo(models.ROPA, { foreignKey: 'ropa_id', constraints: false  });
    }
    return RopaDocuments;
};