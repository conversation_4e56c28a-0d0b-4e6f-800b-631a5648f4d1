const { DEVICE_TYPE } = require('../constant/common');

module.exports = (sequelize, type) => {
    const DeviceToken = sequelize.define('DeviceToken', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: type.INTEGER,
            allowNull: true
        },
        device_id: {
            type: type.STRING,
            allowNull: true
        },
        device_token: {
            type: type.STRING,
            allowNull: true
        },
        device_type: {
            type: type.ENUM(DEVICE_TYPE.MPIN_ANDROID, DEVICE_TYPE.MPIN_IOS, DEVICE_TYPE.WEB, DEVICE_TYPE.IOS, DEVICE_TYPE.ANDRIOD),
            allowNull: true
        },
        role: {
            type: type.STRING,
            allowNull: true
        },
        auth_token: {
            type: type.STRING,
            allowNull: true
        }
    },
        {
            tableName: 'device_tokens',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['user_id']
                },
                {
                    fields: ['device_id']
                },
                {
                    fields: ['device_token']
                },
                {
                    fields: ['device_type']
                },
                {
                    fields: ['role']
                },
                {
                    fields: ['auth_token']
                }
            ]
        });
    return DeviceToken;
};