module.exports = (sequelize, type) => {
    const tiaAnswers = sequelize.define('tiaAnswers', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: type.INTEGER,
            allowNull: false,
            unique: true
        },
        answer: {
            type: type.ARRAY(type.STRING),
            allowNull: false
        },
        attachment_link: {
            type: type.STRING,
            allowNull: true
        },
        raw_url: {
            type: type.BOOLEAN,
            allowNull: true
        },
        answered_by: {
            type: type.INTEGER,
            allowNull: false
        },
        extra_answer: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
    },
        {
            tableName: 'tia_answers',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_question_id']
                },
                {
                    fields: ['answered_by']
                }
            ]
        });
    tiaAnswers.associate = function (models) {
        tiaAnswers.belongsTo(models.tiaCustomerControls, { foreignKey: 'customer_question_id', constraints: false  });
        tiaAnswers.belongsTo(models.User, { foreignKey: 'answered_by', constraints: false  });
    }
    return tiaAnswers;
}