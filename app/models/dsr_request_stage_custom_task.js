module.exports = (sequelize, DataTypes) => {
    const CustomRequestTask = sequelize.define('CustomRequestTask', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        stage_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            
        },
        workflow_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            
        },
        request_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false
        },
        guidance_text: {
            type: DataTypes.STRING,
            allowNull: true
        },
        reminder_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        task_note: {
            type: DataTypes.STRING,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        start_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        due_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        completion_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        requirement: {
            type: DataTypes.JSON,
            allowNull: true
        },
        assignee_id: {
            type: DataTypes.ARRAY(DataTypes.INTEGER),
            allowNull: true
        },
        progress: {
            type: DataTypes.ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'REJECTED'),
            defaultValue: 'NOT_STARTED'
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        activepieces_automation_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
        activepieces_completed_task: {
            type: DataTypes.STRING,
            allowNull: true
        },
        due_days: {
            type: DataTypes.INTEGER,
            allowNull: true
        }
    }, {
        tableName: 'dsr_custom_request_task',
        timestamps: true,
        paranoid: true
    });

    CustomRequestTask.associate = function (models) {
        CustomRequestTask.belongsTo(models.RequestTypeStages, { foreignKey: 'stage_id', targetKey: 'id', constraints: false });
        CustomRequestTask.belongsTo(models.RequestTask, { foreignKey: 'request_id', targetKey: 'id', constraints: false });
        CustomRequestTask.hasMany(models.TaskDocument, { foreignKey: 'task_id', targetKey: 'id', constraints: false })
        CustomRequestTask.belongsTo(models.Departments, { foreignKey: 'department_id', targetKey: 'id', constraints: false });
        
    };

    return CustomRequestTask;
};
