module.exports = (sequelize, DataTypes) => {
    const tiaCustomerControls = sequelize.define('tiaCustomerControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // customer_id: {
        //     type: DataTypes.INTEGER,
        //     allowNull: true
        // },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        tia_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },

    }, {
        tableName: 'tia_customer_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    tiaCustomerControls.associate = function (models) {
        tiaCustomerControls.belongsTo(models.tiaControls, { foreignKey: 'question_id', constraints: false });
        tiaCustomerControls.belongsTo(models.tiaCategory, { foreignKey: 'category_id', constraints: false });
        tiaCustomerControls.belongsTo(models.CustomerAssessments, { foreignKey: 'tia_id' , targetKey:'id', constraints: false });
        tiaCustomerControls.hasOne(models.tiaAnswers, { foreignKey: 'customer_question_id', constraints: false });
        tiaCustomerControls.belongsTo(models.tiaCustomerControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false });
        tiaCustomerControls.hasMany(models.tiaCustomerControls, { as: 'Children', foreignKey: 'parent_id', constraints: false });
        tiaCustomerControls.hasOne(models.ReviewTIA, { foreignKey: 'customer_question_id' , as: 'ReviewTIA', constraints: false })
    };

    return tiaCustomerControls;
}