module.exports = (sequelize, DataTypes) => {
    const TaskPrivacyNotice = sequelize.define('TaskPrivacyNotice', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        taskId: {
            type: DataTypes.STRING,
            allowNull: false
        },
        status: {
            type: DataTypes.ENUM('PROCESSING','FAILED','COMPLETED'),
            allowNull: false,
            defaultValue: 'PROCESSING'
        },
        content: {
            type: DataTypes.JSONB,
            allowNull: true
        }      
    }, {
        tableName: 'task_privacy_notice',
        timestamps: true,
        paranoid: true,
        indexed: [
           
            {
                fields: ['taskId']
            }
            
        ]        

    });

    return TaskPrivacyNotice;
};