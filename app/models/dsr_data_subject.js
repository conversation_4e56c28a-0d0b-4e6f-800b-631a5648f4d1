module.exports = (sequelize, DataTypes) => {
    const DataSubject = sequelize.define('DataSubject', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        is_data_subject: {
            type: DataTypes.BOOLEAN,
            allowNull: false
        },
        first_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        last_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        unique_identification_type: {
            type: DataTypes.ENUM('PASSPORT', 'DRIVING_LICENSE', 'NATIONAL_ID', 'OTHER'),
            allowNull: true
        },
        unique_identification_number: {
            type: DataTypes.STRING,
            allowNull: true
        },
        address_1: {
            type: DataTypes.STRING,
            allowNull: true
        },
        address_2: {
            type: DataTypes.STRING,
            allowNull: true
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        state_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        city: {
            type: DataTypes.STRING,
            allowNull: true
        },
        postal_code: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        relationship: {
            type: DataTypes.ENUM('CUSTOMER','FORMER_CUSTOMER', 'CONTRACTOR', 'EMPLOYEE', 'JOB_APPLICANT', 'OTHER'),
            allowNull: false
        },
        email: {
            type: DataTypes.STRING,
            allowNull: false
        },
        is_email_verified: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        phone_no: {
            type: DataTypes.STRING,
            allowNull: true
        },
        dob: {
            type: DataTypes.DATEONLY,
            allowNull: true
        },     
        third_party_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        third_party_practice_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        third_party_email: {
            type: DataTypes.STRING,
            allowNull: true
        },
        third_party_contact_number: {
            type: DataTypes.STRING,
            allowNull: true
        },
        second_address_1: {
            type: DataTypes.STRING,
            allowNull: true
        },
        second_address_2: {
            type: DataTypes.STRING,
            allowNull: true
        },
        second_country_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        second_state_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        second_city: {
            type: DataTypes.STRING,
            allowNull: true
        },
        second_postal_code: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
    }, {
        tableName: 'dsr_data_subject',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['unique_identification_number']
            },
            {
                fields: ['email']
            }
        ]
    });

    DataSubject.associate = function (models) {
        DataSubject.belongsTo(models.country, { foreignKey: 'country_id' });
        DataSubject.belongsTo(models.state, { foreignKey: 'state_id' });
        DataSubject.hasMany(models.RequestDocument, { foreignKey: 'dsr_data_subject_id'  });
    };

    return DataSubject;
};
