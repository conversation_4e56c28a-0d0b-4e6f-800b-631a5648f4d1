module.exports = (sequelize, DataTypes) => {
    const VeaCollaborator = sequelize.define('VeaCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        vea_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'vea_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['vea_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['vea_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    VeaCollaborator.associate = function (models) {
        VeaCollaborator.belongsTo(models.VendorAssessments, { foreignKey: 'vea_id' , targetKey: 'id' });
        VeaCollaborator.belongsTo(models.User, { foreignKey: 'user_id' });
        VeaCollaborator.belongsTo(models.VeaCategory, { foreignKey: 'category_id' });
    };

    return VeaCollaborator;
}
