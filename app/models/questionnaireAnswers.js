module.exports = (sequelize, type) => {
    const QuestionnairesAnswers = sequelize.define('QuestionnairesAnswers', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: type.INTEGER,
            allowNull: false
        },
        answer: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
        other: {
            type: type.BOOLEAN,
            allowNull: true
        },
        other_answer: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true
        },
        user_id: {
            type: type.INTEGER,
            allowNull: true
        },

    },
        {
            tableName: 'questionnaires_answers',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['question_id']
                },
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['user_id']
                }
            ]
        });
    QuestionnairesAnswers.associate = function (models) {
        QuestionnairesAnswers.belongsTo(models.Questionnaires, { foreignKey: 'question_id', constraints: false  });
    }
    return QuestionnairesAnswers;
}