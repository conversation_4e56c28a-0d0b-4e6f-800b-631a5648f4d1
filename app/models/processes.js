module.exports = (sequelize, type) => {
    const Processes = sequelize.define('Processes', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
        parent_id: {
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false
        },
        department_id: {
            type: type.INTEGER,
            allowNull: true
        },
        spoc_id: {
            type: type.INTEGER,
            allowNull: true
        },
    },
        {
            tableName: 'processes',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['department_id']
                },
                {
                    fields: ['spoc_id']
                }
            ]
        });
    Processes.associate = function (models) {
        Processes.belongsTo(models.User, { foreignKey: 'spoc_id' });
        Processes.belongsTo(models.Departments, { foreignKey: 'department_id' });
        Processes.belongsTo(models.Processes, { as: 'parent', foreignKey: 'parent_id' });
        Processes.hasMany(models.Processes, { as: 'children', foreignKey: 'parent_id' });
        Processes.hasOne(models.ROPA, { foreignKey: 'process_id' });
        Processes.hasMany(models.User, { foreignKey: 'processes_id' });
    }
    return Processes;
}
