module.exports = (sequelize, DataTypes) => {
  const UCFCustomCategory = sequelize.define(
    'UCFCustomCategory',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      category_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      category_no: {
        type: DataTypes.STRING,
        allowNull: true
      },
      category_name: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      required_docs: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'ucf_custom_control_category',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['category_id']
        }
      ]
    }
  );

  UCFCustomCategory.associate = function (models) {
    UCFCustomCategory.hasMany(models.UCFControl, { foreignKey: 'category_id' });
    UCFCustomCategory.belongsTo(models.UCFCategory, { foreignKey: 'category_id', targetKey: 'id' });
  };

  return UCFCustomCategory;
};
