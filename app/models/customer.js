const { Sequelize } = require('sequelize'); // Import Sequelize
module.exports = (sequelize, type) => {
    const Customer = sequelize.define('Customer', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            // defaultValue: Sequelize.literal("nextval('customer_id_seq')") // Fetch next ID
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
        country_code: {
            type: type.STRING,
            allowNull: true,
            default: null,
        },
        status: {
            type: type.ENUM('active', 'inactive', 'archived'),
            defaultValue: "inactive",
            allowNull: true
        },
        customer_type: {
            type: type.ENUM('VENDOR', 'CUSTOMER', 'CUSTOMER_VENDOR'),
            defaultValue: "CUSTOMER"
        },
        // org_id: {
        //     type: type.INTEGER,
        //     allowNull: true
        // },
        // org_category: {
        //     type: type.STRING,
        //     allowNull: true,
        //     defaultValue: null
        // },
        is_active: {
            type: type.BOOLEAN,
            allowNull: true,
            defaultValue: true
        },
        // parent_node: {
        //     type: type.INTEGER,
        //     allowNull: true
        // },
        // is_archived: {
        //     type: type.BOOLEAN,
        //     allowNull: true,
        //     defaultValue: false
        // },
        // archived_date: {
        //     type: 'TIMESTAMP',
        //     allowNull: true,
        //     defaultValue: null
        // },
        // start_date: {
        //     type: type.DATE,
        //     allowNull: true
        // },
        // reseller: {
        //     type: type.STRING,
        //     allowNull: true
        // },
        // is_group: {
        //     type: type.BOOLEAN,
        //     allowNull: true
        // },
        is_deleted: {
            type: type.BOOLEAN,
            allowNull: true,
            defaultValue: false
        },
        email: {
            type: type.STRING,
            allowNull: true,
            unique: 'email'
        },
        address: {
            type: type.STRING,
            allowNull: true
        },
        address_secondary: {
            type: type.STRING,
            allowNull: true
        },
        city: {
            type: type.STRING,
            allowNull: true
        },
        state: {
            type: type.STRING,
            allowNull: true
        },
        postal_code: {
            type: type.STRING,
            allowNull: true
        },
        country: {
            type: type.STRING,
            allowNull: true
        },
        business_size: {
            type: type.STRING,
            allowNull: true
        },
        industry_vertical: {
            type: type.INTEGER,
            allowNull: true
        }
    },
        {
            tableName: 'customer',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['name']
                },
                {
                    fields: ['email']
                }
            ]
        });
    Customer.associate = (models) => {
        Customer.hasMany(models.Policy, { foreignKey: 'customer_id', constraints: false  });
        Customer.hasMany(models.VendorsMapping, { foreignKey: 'customer_id' , as: 'Customer' , constraints: false  });
        Customer.hasMany(models.VendorsMapping, { foreignKey: 'vendor_id' , as: 'Vendor' , constraints: false  });
        Customer.hasMany(models.User, { foreignKey: 'customer_id', constraints: false  });
        Customer.hasMany(models.Role, { foreignKey: 'customer_id', constraints: false  });
        Customer.hasMany(models.CustomerResources, { foreignKey: 'customer_id', constraints: false  });
        Customer.hasMany(models.CustomerAssessments, { foreignKey: 'customer_id', constraints: false  });
        Customer.hasMany(models.DsrRequest, { foreignKey: 'customer_id', constraints: false  });
        Customer.hasMany(models.VendorList, { foreignKey: 'customer_id', constraints: false  })
    }
    return Customer;
};
