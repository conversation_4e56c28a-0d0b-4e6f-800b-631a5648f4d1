module.exports = (sequelize, type) => {
    const Ambitions = sequelize.define('Ambitions', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
        description: {
            type: type.STRING,
            allowNull: true
        },
    },
        {
            tableName: 'ambitions',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });

    return Ambitions;

};