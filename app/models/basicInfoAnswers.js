module.exports = (sequelize, DataTypes) => {
    const BasicInfoAnswers = sequelize.define('BasicInfoAnswers', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ropa_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            // unique: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        answer: {
            type: DataTypes.ARRAY(DataTypes.STRING),
            allowNull: false
        },
        answered_by: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'basic_info_answers',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                // unique: false,
                fields: ['ropa_id']
            },
            {
                fields: ['question_id']
            },
            {
                fields: ['answered_by']
            }
        ],
        // uniqueKeys: {
        //     unique_ropa_question: {
        //         fields: ['ropa_id', 'question_id']
        //     }
        // }
    });

    BasicInfoAnswers.associate = function (models) {
        BasicInfoAnswers.belongsTo(models.BasicInfoQuestions, { foreignKey: 'question_id', constraints: false });
        BasicInfoAnswers.belongsTo(models.ROPA, { foreignKey: 'ropa_id', constraints: false });
        BasicInfoAnswers.belongsTo(models.User, { foreignKey: 'answered_by', constraints: false });
    };

    return BasicInfoAnswers;
}
