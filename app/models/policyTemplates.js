module.exports = (sequelize, type) => {
    const PolicyTemplates = sequelize.define("PolicyTemplates",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            template_id: {
                type: type.INTEGER,
                allowNull: true,
            },
            policy_category_id: {
                type: type.INTEGER,
                allowNull: true,
            }
        },
        {
            tableName: 'policy_templates',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['template_id']
                },
                {
                    fields: ['policy_category_id']
                }
            ]
        }
    );
    PolicyTemplates.associate = function (models) {
        PolicyTemplates.belongsTo(models.PolicyCategory, { foreignKey: 'policy_category_id', constraints: false  });
        PolicyTemplates.belongsTo(models.Templates, { foreignKey: 'template_id', constraints: false  });
    };
    return PolicyTemplates;
}
