module.exports = (sequelize, DataTypes) => {
    const VeaControls = sequelize.define('VeaControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
        industry_vertical_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        risks: {
            type: DataTypes.ENUM('Low', 'Medium', 'High'),
            allowNull: true
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        }

    }, {
        tableName: 'vea_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    VeaControls.associate = function (models) {
        VeaControls.belongsTo(models.VeaCategory, { foreignKey: 'category_id' });
        VeaControls.hasMany(models.VeaCustomerControls, { foreignKey: 'question_id' });
        VeaControls.belongsTo(models.VeaControls, { as: 'Parent', foreignKey: 'parent_id' });
        VeaControls.hasMany(models.VeaControls, { as: 'Children', foreignKey: 'parent_id' });
        VeaControls.belongsTo(models.Customer, {foreignKey: 'customer_id' });
        VeaControls.belongsTo(models.VeaTemplate, {foreignKey: 'template_id' });
    };

    return VeaControls;
};
