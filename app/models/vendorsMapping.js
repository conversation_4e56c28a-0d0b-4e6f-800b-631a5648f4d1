module.exports = (sequelize, type) => {
    const VendorsMapping = sequelize.define('VendorsMapping', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        vendor_id: {
            type: type.INTEGER,
            allowNull: true,
            // references: {
            //     model: 'customer', // referencing to the 'Customer' table
            //     key: 'id'
            // },
            // onUpdate: 'CASCADE',
            // onDelete: 'SET NULL'
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false,
            // references: {
            //     model: 'customer', // references the 'customer' table
            //     key: 'id'
            // },
            // onUpdate: 'CASCADE',
            // onDelete: 'CASCADE'
        },
        
        // name: {
        //     type: type.STRING,
        //     allowNull: false
        // },
        status: {
            type: type.ENUM('active', 'inactive', 'archived'),
            defaultValue: 'active',
            allowNull: true
        }
    },
    {
        tableName: 'vendor_customer_mapping',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['vendor_id']
            }
        ]
    });

    VendorsMapping.associate = (models) => {
        VendorsMapping.belongsTo(models.Customer, { foreignKey: 'customer_id', as: 'Customer', constraints: false }); // Link to Customer
        VendorsMapping.belongsTo(models.Customer, { foreignKey: 'vendor_id' , as: 'Vendor', constraints: false}); // Self-referencing association
        VendorsMapping.hasOne(models.VendorDetail, { foreignKey: 'vendor_id', constraints: false });
    };

    return VendorsMapping;
};