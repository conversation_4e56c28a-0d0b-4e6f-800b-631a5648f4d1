module.exports = (sequelize, DataTypes) => {
    const ViaCollaborator = sequelize.define('ViaCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        via_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'via_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['via_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['via_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    ViaCollaborator.associate = function (models) {
        ViaCollaborator.belongsTo(models.VendorAssessments, { foreignKey: 'via_id' , targetKey: 'id', constraints: false });
        ViaCollaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        ViaCollaborator.belongsTo(models.ViaCategory, { foreignKey: 'category_id', constraints: false  });
    };

    return ViaCollaborator;
}
