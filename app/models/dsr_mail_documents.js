module.exports = (sequelize, DataTypes) => {
    const DsrMailDocuments = sequelize.define('DsrMailDocuments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        original_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        dsr_sent_mail_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        dsr_request_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'dsr_mail_documents',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['dsr_request_id']
            }
        ]

    });
    DsrMailDocuments.associate = function (models) {
        DsrMailDocuments.belongsTo(models.DsrRequest, { foreignKey: 'dsr_request_id', constraints: false  });
    }

    return DsrMailDocuments;
};