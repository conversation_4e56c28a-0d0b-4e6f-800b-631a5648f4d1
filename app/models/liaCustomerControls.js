module.exports = (sequelize, DataTypes) => {
    const liaCustomerControls = sequelize.define('liaCustomerControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // customer_id: {
        //     type: DataTypes.INTEGER,
        //     allowNull: true
        // },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        lia_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },

    }, {
        tableName: 'lia_customer_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    liaCustomerControls.associate = function (models) {
        liaCustomerControls.belongsTo(models.liaControls, { foreignKey: 'question_id', constraints: false  });
        liaCustomerControls.belongsTo(models.liaCategory, { foreignKey: 'category_id', constraints: false });
        liaCustomerControls.belongsTo(models.CustomerAssessments, { foreignKey: 'lia_id' , targetKey:'id', constraints: false  });
        liaCustomerControls.hasOne(models.liaAnswers, { foreignKey: 'customer_question_id', constraints: false  });
        liaCustomerControls.belongsTo(models.liaCustomerControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        liaCustomerControls.hasMany(models.liaCustomerControls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
        liaCustomerControls.hasOne(models.ReviewLIA, { foreignKey: 'customer_question_id', as: 'ReviewLIA', constraints: false  })
    };

    return liaCustomerControls;
}