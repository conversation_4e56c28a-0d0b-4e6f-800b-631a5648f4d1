module.exports = (sequelize, DataTypes) => {
  const CustomerBusinessRequirements = sequelize.define(
    'CustomerBusinessRequirements',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      group_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      control_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      regulation_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      busi_req_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      applicability: {
        type: DataTypes.ENUM('YES', 'NO', 'N/A'),
        defaultValue: 'NO'
      },
      compliance_status: {
        type: DataTypes.ENUM('COMPLIANT', 'PARTIAL_COMPLIANT', 'NON_COMPLIANT', 'N/A'),
        defaultValue: 'NON_COMPLIANT'
      },
      observation: {
        type: DataTypes.ARRAY(DataTypes.TEXT),
        allowNull: true
      },
      document_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      reference_no: {
        type: DataTypes.STRING,
        allowNull: true
      },
      business_requirement: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      articles: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true
      },
      is_custom: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      }
    },
    {
      tableName: 'custom_business_requirement',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['busi_req_id']
        }
      ]
    }
  );

  CustomerBusinessRequirements.associate = function (models) {
    CustomerBusinessRequirements.belongsTo(models.UCFCustomControl, { foreignKey: 'control_id', targetKey: 'id', constraints: false });
    CustomerBusinessRequirements.belongsTo(models.RegulationsV2, { foreignKey: 'regulation_id', targetKey: 'id', constraints: false });
    CustomerBusinessRequirements.belongsTo(models.UCFBusinessRequirement, { foreignKey: 'busi_req_id', constraints: false });
    CustomerBusinessRequirements.belongsTo(models.Policy, { foreignKey: 'document_id', targetKey: 'id', constraints: false });
    // CustomerBusinessRequirements.belongsTo(models.RegulationBusiRequirementMapping, { foreignKey: 'busi_requirement_id', constraints: false });
    // CustomerBusinessRequirements.belongsToMany(models.Duties, { foreignKey: 'custom_busi_requirement_id', as: 'Duties', constraints: false });
    // CustomerBusinessRequirements.belongsToMany(models.Actions, { foreignKey: 'custom_busi_requirement_id', as: 'Actions', constraints: false });
    // CustomerBusinessRequirements.belongsToMany(models.Improvement, { foreignKey: 'custom_busi_requirement_id', as: 'Improvement', constraints: false });
  };

  return CustomerBusinessRequirements;
};
