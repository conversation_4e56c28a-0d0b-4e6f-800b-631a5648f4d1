module.exports = (sequelize, DataTypes) => {
    const AssessmentCategory = sequelize.define('AssessmentCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        level: {
            type: DataTypes.ENUM('Department', 'Process'),
            allowNull: false
        },
        assessment_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        },
        key:{
            type:DataTypes.STRING,
            allowNull:true
        },
        template_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        }
    }, {
        tableName: 'assessment_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['level']
            }
        ]
    });
    AssessmentCategory.associate = function (models) {
        AssessmentCategory.hasMany(models.AssessmentControls, { foreignKey: 'category_id', constraints: false });
        AssessmentCategory.belongsTo(models.AssessmentTemplate, { foreignKey: 'template_id', constraints: false });
        AssessmentCategory.belongsTo(models.Assessments,{foreignKey: 'assessment_id', constraints: false})
        AssessmentCategory.hasMany(models.AssessmentCollaborator, { foreignKey: 'category_id', constraints: false });

    };
    return AssessmentCategory;
};