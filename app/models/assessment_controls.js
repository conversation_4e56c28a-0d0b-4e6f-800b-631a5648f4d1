module.exports = (sequelize, DataTypes) => {
    const AssessmentControls = sequelize.define('AssessmentControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
        industry_vertical_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        assessment_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        },
        key:{
            type:DataTypes.STRING,
            allowNull:true
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        embedding: {
            type: DataTypes.ARRAY(DataTypes.FLOAT),
            allowNull: true
        }

    }, {
        tableName: 'assessment_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });
    AssessmentControls.associate = function (models) {
        AssessmentControls.belongsTo(models.AssessmentCategory, { foreignKey: 'category_id'  });
        AssessmentControls.hasMany(models.AssessmentCustomerControls, { foreignKey: 'question_id'  });
        AssessmentControls.belongsTo(models.AssessmentControls, { as: 'Parent', foreignKey: 'parent_id'  });
        AssessmentControls.hasMany(models.AssessmentControls, { as: 'Children', foreignKey: 'parent_id'  });
        AssessmentControls.belongsTo(models.IndustryVertical, { foreignKey: 'industry_vertical_id'  });
        AssessmentControls.belongsTo(models.AssessmentTemplate, { foreignKey: 'template_id'  });
        AssessmentControls.belongsTo(models.Assessments, { foreignKey: 'assessment_id'  });

    };
    return AssessmentControls;
};
