module.exports = (sequelize, type) => {
    const Blogs = sequelize.define('Blogs', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: type.STRING,
            allowNull: false
        },
        description: {
            type: type.STRING,
            allowNull: false
        },
        content: {
            type: type.TEXT,
            allowNull: false
        },
        image: {
            type: type.STRING,
            allowNull: false
        },
        category: {
            type: type.STRING,
            allowNull: false
        },
        author_name: {
            type: type.STRING,
            allowNull: false
        }
    },
        {
            tableName: 'blogs',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['category']
                }
            ]
        });

    return Blogs;

};