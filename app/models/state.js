module.exports = (sequelize, DataTypes) => {
    const state = sequelize.define('state', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        state_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
    },
        {
            tableName: 'state',
            timestamps: true,
            paranoid: true,
            freezeTableName: true,
            indexes: [
                {
                    fields: ['country_id']
                }
            ]
        });

    return state;
};
