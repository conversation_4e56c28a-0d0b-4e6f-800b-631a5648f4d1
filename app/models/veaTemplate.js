module.exports = (sequelize, DataTypes) => {
    const VeaTemplate = sequelize.define('VeaTemplate', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        temp_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        content: {
            type: DataTypes.JSON,
            allowNull: true
        },
        thumbnail: {
            type: DataTypes.STRING,
            allowNull: true,
            default: "https://go-asset-management-profile-image.s3.us-east-2.amazonaws.com/privacy-policy-template.jpeg"
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },

    }, {
        tableName: 'vea_template',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            }
        ]
    });

    VeaTemplate.associate = function (models) {
        VeaTemplate.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        
    };

    return VeaTemplate;
};