const { type } = require(".");

module.exports = (sequelize, DataTypes) => {
    const DataBreachAction = sequelize.define('DataBreachAction', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        data_breach_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false
        },        
        description: {
            type: DataTypes.STRING,
            allowNull: true
        },
        assignee_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        assigned_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        deadline_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        
    }, {
        tableName: 'data_breach_action',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['title']
            }
        ]
    });

    DataBreachAction.associate = function (models) {
        DataBreachAction.belongsTo(models.Customer, { foreignKey: 'customer_id'  });
        DataBreachAction.belongsTo(models.User, { foreignKey: 'assignee_id'  });
        DataBreachAction.belongsTo(models.DataBreachManagement, { foreignKey: 'data_breach_id'  })
    };

    return DataBreachAction;
};