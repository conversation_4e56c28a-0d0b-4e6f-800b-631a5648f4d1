module.exports = (sequelize, DataTypes) => {
    const ViaCategory = sequelize.define('ViaCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        via_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
    }, {
        tableName: 'via_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['via_level']
            }
        ]
    });

    ViaCategory.associate = function (models) {
        ViaCategory.hasMany(models.ViaControls, { foreignKey: 'category_id' });
        ViaCategory.hasMany(models.ViaCollaborator, { foreignKey: 'category_id' });
    };

    return ViaCategory;
};