module.exports = (sequelize, DataTypes) => {
    const VendorAssessments = sequelize.define('VendorAssessments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        assessment_type:{
            type: DataTypes.ENUM('via', 'vea'),
            allowNull: true
        },
        assessment_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        vendor_id:{ // vendor mapping id that means, customer <--> vendor mapping id
            type: DataTypes.INTEGER,
            allowNull: false
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        approver:{
            type: DataTypes.INTEGER,
            allowNull: false
        },
        assigned_to:{
            type: DataTypes.INTEGER,
            allowNull: false
        },
        start_date:{
            type: DataTypes.DATE,
            allowNull: true
        },
        end_date:{
            type: DataTypes.DATE,
            allowNull: true
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        risks: {
            type: DataTypes.ENUM('Low', 'Medium', 'High'),
            allowNull: true
        },
        risk_score: {
            type: DataTypes.DOUBLE,
            allowNull: true
        },
        progress:{
            type: DataTypes.DOUBLE,
            default: 0
        },
        status: {
            type: DataTypes.ENUM('Yet to Start', 'Started', 'Under Review', 'Changes Requested', 'Completed'),
            default: 'Yet to Start'
        },
        entity_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        },
        owner_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_automated: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        recurrence_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        version: {
            type: DataTypes.INTEGER,
            defaultValue: 1
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_latest: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        }
    }, {
        tableName: 'vendor_assessments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['vendor_id']
            }
        ]
    });

    VendorAssessments.associate = function (models) {
        VendorAssessments.hasMany(models.Regions, { foreignKey: 'region_id' });
        // VendorAssessments.belongsTo(models.Assessments, { foreignKey: 'assessment_id' , targetKey: 'id', constraints: false  });
        VendorAssessments.belongsTo(models.Departments, { foreignKey: 'department_id' });
        // VendorAssessments.belongsTo(models.Processes, { foreignKey: 'process_id', constraints: false  });

        VendorAssessments.belongsTo(models.User, { as: 'AssignedTo', foreignKey: 'assigned_to' });
        VendorAssessments.belongsTo(models.User, { as: 'Approver', foreignKey: 'approver' });
        VendorAssessments.belongsTo(models.Group, { foreignKey: 'entity_id' });
        VendorAssessments.belongsTo(models.User, { as: 'Owner', foreignKey: 'owner_id' });
        VendorAssessments.belongsTo(models.VendorsMapping, { foreignKey: 'vendor_id' });

        VendorAssessments.belongsTo(models.ViaCollaborator, { foreignKey: 'via_id' });

        VendorAssessments.belongsTo(models.ViaCustomerControls, { foreignKey: 'via_id' });
        VendorAssessments.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        VendorAssessments.belongsTo(models.VeaTemplate, { foreignKey: 'template_id', targetKey: 'id' });
        VendorAssessments.belongsTo(models.Customer, { as: 'Vendor', foreignKey: 'vendor_id' });
        VendorAssessments.belongsTo(models.Departments, { as: 'Departments', foreignKey: 'department_id' });
        
    };

    return VendorAssessments;
}