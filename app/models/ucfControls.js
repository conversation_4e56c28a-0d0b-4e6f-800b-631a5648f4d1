module.exports = (sequelize, DataTypes) => {
    const UCFControl = sequelize.define('UCFControl', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        control_no: {
            type: DataTypes.STRING,
            allowNull: false
        },
        control_description: {
            type: DataTypes.TEXT,
            allowNull: false
        },
        in_scope_regulations: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        
    }, {
        tableName: 'ucf_control',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    UCFControl.associate = function (models) {
        UCFControl.belongsTo(models.UCFCategory, { foreignKey: 'category_id', targetKey: 'id', constraints: false });
        UCFControl.hasMany(models.UCFBusinessRequirement, { foreignKey: 'control_id', constraints: false })
    };

    return UCFControl;
}