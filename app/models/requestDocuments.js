module.exports = (sequelize, DataTypes) => {
    const RequestDocument = sequelize.define('RequestDocument', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        original_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        dsr_data_subject_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        dsr_request_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        document_type: {
            type: DataTypes.ENUM('identification_documents', 'letter_of_authority'),
            allowNull: false
        },
    }, {
        tableName: 'RequestDocuments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['dsr_data_subject_id']
            }
        ]

    });
    RequestDocument.associate = function (models) {
        RequestDocument.belongsTo(models.DataSubject, { foreignKey: 'dsr_data_subject_id', constraints: false  });
    }

    return RequestDocument;
};