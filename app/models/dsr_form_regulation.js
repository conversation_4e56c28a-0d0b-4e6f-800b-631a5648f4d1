module.exports = (sequelize, DataTypes) => {
    const DsrFormRegulations = sequelize.define('DsrFormRegulations', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        form_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        regulation_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'dsr_form_regulations',
        timestamps: false
    });

    return DsrFormRegulations;
};