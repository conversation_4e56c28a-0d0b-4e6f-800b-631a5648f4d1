module.exports = (sequelize, DataTypes) => {
    const piaCollaborator = sequelize.define('piaCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        pia_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'pia_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['pia_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['pia_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    piaCollaborator.associate = function (models) {
        piaCollaborator.belongsTo(models.CustomerAssessments, { foreignKey: 'pia_id' , targetKey: 'id', constraints: false });
        piaCollaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        piaCollaborator.belongsTo(models.piaCategory, { foreignKey: 'category_id', constraints: false  });
    };

    return piaCollaborator;
}
