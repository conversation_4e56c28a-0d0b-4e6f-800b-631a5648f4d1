module.exports = (sequelize, DataTypes) => {
  const UCFBusinessRequirement = sequelize.define(
    'UCFBusinessRequirement',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      control_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      reference_no: {
        type: DataTypes.STRING,
        allowNull: false
      },
      business_requirement: {
        type: DataTypes.TEXT,
        allowNull: false
      }
    },
    {
      tableName: 'ucf_business_requirement',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['control_id']
        }
      ]
    }
  );

  UCFBusinessRequirement.associate = function (models) {
    UCFBusinessRequirement.belongsTo(models.UCFControl, { foreignKey: 'control_id', targetKey: 'id' });
    UCFBusinessRequirement.hasOne(models.CustomerBusinessRequirements, { foreignKey: 'busi_req_id', targetKey: 'id' });
    UCFBusinessRequirement.hasMany(models.RegulationBusiRequirementMapping, { foreignKey: 'busi_requirement_id' });
  };

  return UCFBusinessRequirement;
};
