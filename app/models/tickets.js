module.exports = (sequelize, type) => {
    const Ticket = sequelize.define("Ticket",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            name: {
                type: type.STRING,
                allowNull: false,
            },
            description: {
                type: type.TEXT,
                allowNull: true
            },
            type: {
                type: type.ENUM('OTHER', 'FEEDBACK', 'SUPPORT', 'ISSUE', 'CHANGE_REQUEST'),
                allowNull: false,
                defaultValue: 'OTHER'
            },
            status: {
                type: type.ENUM('OPEN', 'CLOSE', 'IN_PROGRESS'),
                allowNull: false,
                defaultValue: 'OPEN'
            },
            priority: {
                type: type.ENUM('LOW', 'MEDIUM', 'HIGH'),
                allowNull: false,
                defaultValue: 'MEDIUM'
            },
            reporter_id: {
                type: type.INTEGER,
                allowNull: true
            },
            approver_id: {
                type: type.INTEGER,
                allowNull: true
            },
            assignee_id: {
                type: type.INTEGER,
                allowNull: true
            },
            department_id: {
                type: type.INTEGER,
                allowNull: true
            },
            customer_id: {
                type: type.INTEGER,
                allowNull: true
            }
        },
        {
            tableName: 'ticket',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexed: [
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['status']
                },
                {
                    fields: ['priority']
                }
            ]
        });
    Ticket.associate = function (models) {
        Ticket.belongsTo(models.Departments, { foreignKey: 'department_id' });
        Ticket.belongsTo(models.User, { as: 'Reporter', foreignKey: 'reporter_id' });
        Ticket.belongsTo(models.User, { as: 'Assignee', foreignKey: 'assignee_id' });
        Ticket.belongsTo(models.User, { as: 'Approver', foreignKey: 'approver_id' });
        Ticket.hasMany(models.TicketDocument, { foreignKey: 'ticket_id' });
        // Ticket.hasMany(models.AuditLog, { foreignKey: 'type_id' });
        Ticket.belongsTo(models.Customer, { foreignKey: 'customer_id' });

    }
    return Ticket;
};
