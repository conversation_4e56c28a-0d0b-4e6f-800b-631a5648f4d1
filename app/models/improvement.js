module.exports =(sequelize,type)=>{
    const Improvement = sequelize.define('Improvement',{
        id:{
            type:type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        tags:{
            type: type.ARRAY(type.INTEGER),
            allowNull: true,
        },
        title: {
            type: type.STRING,
            allowNull: true
        },
        assignee_id: {
            type: type.ARRAY(type.INTEGER),
            allowNull: true
        },
        status: {
            type: type.ENUM('INPROGRESS', 'COMPLETED'),
            defaultValue: "INPROGRESS",
            allowNull: true
        },
        owner_id: {
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        due_date: {
            type: type.DATE,
            allowNull: true,
        },
        document: {
            type: type.JSONB,
            allowNull: true,
        },
        regulation_id:{
            type: type.INTEGER,
            allowNull: true
        },
        origin:{
            type: type.TEXT,
            allowNull: true
        },
        finding:{
            type: type.TEXT,
            allowNull: true
        },
        root_cause:{
            type: type.TEXT,
            allowNull: true
        },
        treatment_plan:{
            type: type.TEXT,
            allowNull: true
        },
        progress:{
            type: type.TEXT,
            allowNull: true
        },
        tech_involvement:{
            type: type.ENUM('NO', 'YES'),
            defaultValue: "NO",
            allowNull: true
        },
        other_area_affected:{
            type: type.TEXT,
            allowNull: true
        },
        comment:{
            type: type.TEXT,
            allowNull: true
        },
        management_review:{
            type: type.TEXT,
            allowNull: true
        },
        management_review_topic:{
            type: type.STRING,
            allowNull: true
        },
        custom_busi_requirement_id: {
            type: type.INTEGER,
            allowNull: true
        },
        entity_id: {
            type: type.INTEGER,
            allowNull: true
        }
    },
    {
        tableName: 'improvement',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['assignee_id']
            },
            {
                fields: ['title']
            },
            {
                fields:['owner_id']
            },
        ]
    });

    
    Improvement.associate = (models => {
        Improvement.belongsTo(models.User, { as :'Assignee',foreignKey: 'assignee_id', constraints: false  });
        Improvement.belongsTo(models.User, { as :'Owner', foreignKey: 'owner_id', constraints: false  });
        Improvement.belongsTo(models.CustomerBusinessRequirements, { foreignKey: 'custom_busi_requirement_id', targetKey: 'id', constraints: false });
        
    })

    return Improvement;

};