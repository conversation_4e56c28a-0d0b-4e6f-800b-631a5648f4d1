const { Sequelize } = require('sequelize'); // Import Sequelize
module.exports = (sequelize, type) => {
    const User = sequelize.define("User",
        {
            id: {
                type: type.INTEGER, primaryKey: true, 
                autoIncrement: true,
                // defaultValue: Sequelize.literal("nextval('users_id_seq')")
            },
            tsp_user_id: {
                type: type.STRING, allowNull: true,
            },
            firstName: {
                type: type.STRING, allowNull: true,
            },
            lastName: {
                type: type.STRING, allowNull: true,
            },
            // role: {
            //     type: type.INTEGER, allowNull: true,
            // },
            role_id: {
                type: type.INTEGER, allowNull: true,
            },
            email: {
                type: type.STRING, allowNull: true, unique: 'email'
            },
            password: {
                type: type.STRING, allowNull: true,
            },
            profile_image: {
                type: type.STRING, allowNull: true, default: null,
            },
            mpin: {
                type: type.STRING, allowNull: true, default: null,
            },
            language: {
                type: type.STRING, allowNull: true, default: null,
            },
            country_code: {
                type: type.STRING, allowNull: true, default: null,
            },
            phone: {
                type: type.STRING, allowNull: true, default: null,
            },
            is_phone_number_verified: {
                type: type.BOOLEAN, allowNull: true, defaultValue: false,
            },
            tnc_accepted: {
                type: type.BOOLEAN, allowNull: true, defaultValue: false,
            },
            is_notifies: {
                type: type.BOOLEAN, allowNull: true, defaultValue: false,
            },
            otp: {
                type: type.INTEGER, allowNull: true, defaultValue: null,
            },
            marketing_email_accepted: {
                type: type.BOOLEAN, allowNull: true, defaultValue: false
            },
            access_token: {
                type: type.STRING, allowNull: true,
            },
            status: {
                type: type.ENUM('active', 'inactive', 'archived'),
                defaultValue: "active",
                allowNull: true
            },
            address: {
                type: type.STRING, allowNull: true, default: null,
            },
            color_code: {
                type: type.STRING,
                allowNull: true,
                defaultValue: JSON.stringify([{ key: "Speeding", value: "FF9950" }, {
                    key: "Idling",
                    value: "00CBA0"
                }, { key: "Harsh Acceleration", value: "4DBFFF" }, {
                    key: "Harsh Cornering",
                    value: "FF7070"
                }, { key: "Harsh Braking", value: "FFEA6C" },]),
            },
            // is_client: {
            //     type: type.INTEGER, allowNull: false, defaultValue: 0, //0=>Normal, 1=>Organization User, 2=>Group User, 3=> Super Admin
            // },
            socket_token: {
                type: type.STRING, allowNull: true, default: null,
            },
            org_point_of_contact: {
                type: type.BOOLEAN, allowNull: true, defaultValue: false
            },
            failed_login_count: {
                type: type.INTEGER, allowNull: true, default: 0,
            },
            failed_login_at: {
                type: type.STRING, allowNull: true,
            },
            customer_id: {
                type: type.INTEGER, allowNull: true
            },
            refresh_token: {
                type: type.STRING,
                allowNull: true
            },
            // deletedAt: {
            //     type: type.DATE,
            // },
            is_email_verified: {
                type: type.BOOLEAN, allowNull: true, defaultValue: false
            },
            user_access: {
                type: type.INTEGER, allowNull: true, defaultValue: 0
            },
            access_level: {
                type: type.INTEGER, allowNull: true, defaultValue: 0
            },
            group_access: {
                type: type.JSON, allowNull: true
            },
            department_id: {
                type: type.INTEGER, allowNull: true
            },
            processes_id: {
                type: type.INTEGER, allowNull: true
            },
            firstLogin:{
                type: type.BOOLEAN, allowNull: true, defaultValue: false
            }
        }, {
        tableName: 'users',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['role_id']
            }
        ]
    });
    User.associate = (models) => {
        User.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        User.belongsTo(models.Role, { foreignKey: 'role_id' });
        User.hasOne(models.OnboardingFlow, { foreignKey: 'user_id' });
        User.hasMany(models.Role, { as: 'Roles', foreignKey: 'created_by' });
        User.hasMany(models.GroupUser, { foreignKey: 'user_id' });
        User.hasMany(models.Departments, { foreignKey: 'spoc_id' });

    User.hasMany(models.Policy, { foreignKey: 'author_id', as: 'Author' });
    User.hasMany(models.Policy, { foreignKey: 'approver_id', as: 'Approver' });
    User.hasMany(models.Policy, { foreignKey: 'collaborator_id', as: 'Collaborator' });
    // User.belongsToMany(models.Policy, { through: 'PolicyReviewers', foreignKey: 'user_id' });
    // User.hasMany(models.Policy, { foreignKey: 'reviewer_id' });
    User.hasMany(models.AuditLog, { foreignKey: 'action_by_id' });

    User.hasMany(models.Ticket, { foreignKey: 'approver_id', as: 'ApprovedTickets' });
    User.hasMany(models.Ticket, { foreignKey: 'reporter_id', as: 'ReportedTickets' });
    User.hasMany(models.Ticket, { foreignKey: 'assignee_id', as: 'AssignedTickets' });
    User.hasMany(models.TicketComments, { foreignKey: 'user_id' });

    User.hasMany(models.VendorDetail, { foreignKey: 'created_by', as: 'Created' });
    User.hasMany(models.VendorDetail, { foreignKey: 'updated_by', as: 'Updated' });
    User.hasMany(models.VendorDetail, { foreignKey: 'vpoc_id', as: 'VendorPOC' });
    // User.hasMany(models.Vendors, { foreignKey: 'poc_id', as: 'VPOC' });
    User.hasMany(models.VendorDetail, { foreignKey: 'reviewer_id', as: 'Review' });
    User.hasMany(models.VendorDetail, { foreignKey: 'assigned_to', as: 'Assign' });

    User.hasMany(models.CustomerAssessment, { as: 'AssignedTo', foreignKey: 'assigned_to' });
    User.hasMany(models.CustomerAssessment, { foreignKey: 'approver' });
    User.hasMany(models.CustomerAssessment, { as: 'Owner', foreignKey: 'owner_id' });

    User.hasMany(models.DsrRequest, { foreignKey: 'user_id' });
    User.belongsTo(models.Departments, { foreignKey: 'department_id', as: 'Department' });
    User.belongsTo(models.Processes, { foreignKey: 'processes_id' });
  };
  return User;
};
