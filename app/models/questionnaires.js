module.exports = (sequelize, type) => {

    const Questionnaires = sequelize.define('Questionnaires', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ques_number: {
            type: type.INTEGER,
            allowNull: true
        },
        question: {
            type: type.STRING,
            allowNull: true
        },
        ques_type: {
            type: type.ENUM(["ONBOARDING", "OBLIGATION"]),
            defaultValue: 'ONBOARDING'
        },
        answer_type: {
            type: type.ENUM(["text", "radio", "dropdown"]),  // text or radio
            defaultValue: 'text'
        },
        industry_vertical_id: {
            type: type.INTEGER,
            allowNull: true
        },
        ans_options: {
            type: type.ARRAY(type.JSON),   // for storing options if the answer type is dropdown/radio
            allowNull: true
            // defaultValue: []
        },
        editable: {
            type: type.BOOLEAN,
            defaultValue: true
        }
    },
        {
            tableName: 'questionnaires',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['industry_vertical_id']
                }
            ]
        });
    Questionnaires.associate = (models) => {
        Questionnaires.belongsTo(models.IndustryVertical, { foreignKey: 'industry_vertical_id', constraints: false  });
        Questionnaires.hasMany(models.QuestionnairesAnswers, { foreignKey: 'question_id', constraints: false  });
    }
    return Questionnaires;
};