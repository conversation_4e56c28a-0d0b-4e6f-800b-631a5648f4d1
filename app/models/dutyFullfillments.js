module.exports = (sequelize, type) => {
    const DutyFullfillments = sequelize.define('DutyFullfillments', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        duty_id: {
            type: type.INTEGER,
            allowNull: true
        },
        fullfilled_by_id: {
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        start_date: {
            type: type.DATE,
            allowNull: true,
        },
        end_date: {
            type: type.DATE,
            allowNull: true,
        },
        fullfillment_verified: {
            type: type.BOOLEAN,
            allowNull: true,
        },
        evidence_url: {
            type: type.STRING,
            allowNull: true,
        },
        docs_url: {
            type: type.STRING,
            allowNull: true,
        },
        status: {
            type: type.ENUM('FINALIZED', 'PENDING', 'ARCHIVED'),
            defaultValue: "PENDING",
            allowNull: true
        },
        comment: {
            type: type.TEXT,
            allowNull: true,
        },
    },
        {
            tableName: 'duty_fullfillments',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['duty_id']
                }
            ]
        });
        DutyFullfillments.associate = (models => {
        DutyFullfillments.belongsTo(models.Duties, { foreignKey: 'duty_id', constraints: false  });
        // Duties.hasMany(Duties, { as: 'children', foreignKey: 'parent_id', constraints: false  });
        // Duties.hasMany(models.DutiesUser, { foreignKey: 'group_id', constraints: false  });
        DutyFullfillments.belongsTo(models.User, { foreignKey: 'fullfilled_by_id', constraints: false  });
        // DutyFullfillments.belongsTo(models.User, { foreignKey: 'owner_id', constraints: false  });
        // Duties.hasMany(models.Policy, { foreignKey: 'entity_id', constraints: false  });
    })


    return DutyFullfillments;

};