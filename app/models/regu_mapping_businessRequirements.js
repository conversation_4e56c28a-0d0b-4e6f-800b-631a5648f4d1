module.exports = (sequelize, DataTypes) => {
  const RegulationBusiRequirementMapping = sequelize.define(
    'RegulationBusiRequirementMapping',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      busi_requirement_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      regulation_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      articles: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'ucf_regulation_busi_requirement_mapping',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['regulation_id']
        }
      ]
    }
  );

  RegulationBusiRequirementMapping.associate = function (models) {
    RegulationBusiRequirementMapping.hasMany(models.UCFCategory, { foreignKey: 'busi_requirement_id', targetKey: 'id' });
    RegulationBusiRequirementMapping.belongsTo(models.RegulationsV2, { foreignKey: 'regulation_id', targetKey: 'id' });
    RegulationBusiRequirementMapping.belongsTo(models.UCFBusinessRequirement, { foreignKey: 'busi_requirement_id' });
    // RegulationBusiRequirementMapping.belongsTo(models.CustomerBusinessRequirements, { foreignKey: 'busi_req_id', targetKey: 'busi_req_id', constraints: false });
  };

  return RegulationBusiRequirementMapping;
};
