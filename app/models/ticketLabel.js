module.exports = (sequelize, type) => {
    const TicketLabel = sequelize.define("TicketLabel",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            title: {
                type: type.STRING,
                allowNull: false,
            },

        },
        {
            tableName: 'ticket_label',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    TicketLabel.associate = function (models) {
        TicketLabel.belongsTo(models.Ticket, { foreignKey: 'department_id' });
        
    }
    return TicketLabel;
};
