module.exports = (sequelize, DataTypes) => {
    const PolicyDocument = sequelize.define('PolicyDocument', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        original_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        policyId: {
            type: DataTypes.INTEGER,
            allowNull: false
            // references: {
            //     model: 'Policy',
            //     key: 'id'
            // }
        }
    }, {
        tableName: 'PolicyDocuments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['policyId']
            }
        ]

    });
    PolicyDocument.associate = function (models) {
        PolicyDocument.belongsTo(models.Policy, { foreignKey: 'policyId' });
    }

    return PolicyDocument;
};