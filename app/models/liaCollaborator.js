module.exports = (sequelize, DataTypes) => {
    const liaCollaborator = sequelize.define('liaCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        lia_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'lia_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['lia_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['lia_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    liaCollaborator.associate = function (models) {
        liaCollaborator.belongsTo(models.CustomerAssessments, { foreignKey: 'lia_id' , targetKey: 'id', constraints: false  });
        liaCollaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        liaCollaborator.belongsTo(models.liaCategory, { foreignKey: 'category_id', constraints: false  });
    };

    return liaCollaborator;
}
