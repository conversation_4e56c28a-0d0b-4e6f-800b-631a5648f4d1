module.exports = (sequelize, type) => {
    const VendorDetail = sequelize.define('VendorDetail', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        vendor_id: { // vendorsMapping id
            type: type.INTEGER,
            allowNull: false
        },
        vpoc_id: {
            type: type.INTEGER,
            allowNull: false
        },
        // name: {
        //     type: type.STRING,
        //     allowNull: true
        // },
        description: {
            type: type.STRING,
            allowNull: true
        },
        contact_mail: {
            type: type.STRING,
            allowNull: true
        },
        contact: {
            type: type.INTEGER,
            allowNull: true
        },
        created_by: {
            type: type.INTEGER,
            allowNull: false
        },
        updated_by: {
            type: type.INTEGER,
            allowNull: true
        },
        group_id: {
            type: type.INTEGER,
            allowNull: false
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false
        },
        department_id: {
            type: type.INTEGER,
            allowNull: false
        },
        reviewer_id: {
            type: type.INTEGER,
            allowNull: false
        },
        assigned_to: {
            type: type.INTEGER,
            allowNull: false
        },
        status: {
            type: type.ENUM('active', 'inactive', 'archived'),
            defaultValue: 'active',
            allowNull: true
        },
        stage: {
            type: type.ENUM('CREATE', 'INTERNAL_ASSESSMENT', 'VENDOR_ASSESSMENT', 'MITIGATION', 'COMPLETED'),
            defaultValue: 'CREATE',
            allowNull: true
        },
        risk_score: {
            type: type.DOUBLE,
            allowNull: true
        },
        risk_tier: {
            type: type.ENUM('HIGH', 'MEDIUM', 'LOW'),
            allowNull: true
        },
        re_assessment_status: {
            type: type.ENUM('DONE', 'PENDING'),
            allowNull: true
        },
        reminder_date: {
            type: type.DATE,
            allowNull: true
        },
        next_review: {
            type: type.DATE,
            allowNull: true
        },
        completion_date: {
            type: type.DATE,
            allowNull: true
        },
        type_id: {
            type: type.INTEGER,
            allowNull: true

        }

    },
        {
            tableName: 'vendor_detail',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                // {
                //     fields: ['owner_id']
                // },
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['department_id']
                },
                {
                    fields: ['group_id']
                },
                {
                    fields: ['status']
                },
                {
                    fields: ['risk_tier']
                },
                {
                    fields: ['re_assessment_status']
                }
            ]
        });
    VendorDetail.associate = function (models) {

        VendorDetail.hasOne(models.VendorsMapping, { foreignKey: 'vendor_id', targetKey: 'id' });
        VendorDetail.belongsTo(models.VendorType, { foreignKey: 'type_id', targetKey: 'id' });

        VendorDetail.belongsTo(models.User, { foreignKey: 'created_by', as: 'Created' });
        VendorDetail.belongsTo(models.User, { foreignKey: 'updated_by', as: 'Updated' });
        VendorDetail.belongsTo(models.User, { foreignKey: 'vpoc_id', as: 'VendorPOC' });
        // VendorDetail.belongsTo(models.User, { foreignKey: 'poc_id', as: 'VPOC' });
        VendorDetail.belongsTo(models.User, { foreignKey: 'reviewer_id', as: 'Review' });
        VendorDetail.belongsTo(models.User, { foreignKey: 'assigned_to', as: 'Assign' });
        VendorDetail.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        VendorDetail.belongsTo(models.Group, { foreignKey: 'group_id' });
        // VendorDetail.belongsTo(models.VendorsMapping, { foreignKey: 'vendor_id' , constraints: false});
        VendorDetail.belongsTo(models.Departments, { foreignKey: 'department_id' });

    }
    return VendorDetail;
}