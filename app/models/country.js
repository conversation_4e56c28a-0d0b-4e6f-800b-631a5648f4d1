module.exports = (sequelize, DataTypes) => {
    const country = sequelize.define('country', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        sort_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        country_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        country_code: {
            type: DataTypes.STRING,
            allowNull: true
        },
    },
        { tableName: 'country', timestamps: true, paranoid: true, freezeTableName: true, deletedAt: 'deletedAt'  });

    return country;

};
