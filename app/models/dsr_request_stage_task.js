module.exports = (sequelize, DataTypes) => {
    const RequestTask = sequelize.define('RequestTask', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        stage_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            // references: {
            //     model: 'type_stages', // references the 'type_stages' table
            //     key: 'id'
            // },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        workflow_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false
        },
        guidance_text: {
            type: DataTypes.STRING,
            allowNull: true
        },
        reminder_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        task_note: {
            type: DataTypes.STRING,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        start_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        due_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        completion_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        requirement: {
            type: DataTypes.JSON,
            allowNull: true
        },
        assignee_id: {
            type: DataTypes.ARRAY(DataTypes.INTEGER),
            allowNull: true
        },
        activepieces_automation_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
        due_days: {
            type: DataTypes.INTEGER,
            allowNull: true
        }
       
    }, {
        tableName: 'dsr_request_task',
        timestamps: true,
        paranoid: true
    });

    RequestTask.associate = function (models) {
        RequestTask.belongsTo(models.RequestTypeStages, { foreignKey: 'stage_id', targetKey: 'id' });
        RequestTask.hasMany(models.TaskDocument, { foreignKey: 'task_id' });

    };

    return RequestTask;
};
