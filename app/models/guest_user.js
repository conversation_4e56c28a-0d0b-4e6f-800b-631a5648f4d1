module.exports = (sequelize, type) => {
  const GuestUser = sequelize.define(
    'GuestUser',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      firstName: {
        type: type.STRING,
        allowNull: true
      },
      lastName: {
        type: type.STRING,
        allowNull: true
      },
      email: {
        type: type.STRING,
        allowNull: false,
        unique: 'email'
      },
      password: {
        type: type.STRING,
        allowNull: true
      },
      profile_image: {
        type: type.STRING,
        allowNull: true,
        default: null
      },
      country_code: {
        type: type.STRING,
        allowNull: true,
        default: null
      },
      phone: {
        type: type.STRING,
        allowNull: true,
        default: null
      },
      otp: {
        type: type.INTEGER,
        allowNull: true,
        defaultValue: null
      },
      access_token: {
        type: type.STRING,
        allowNull: true
      },
      status: {
        type: type.ENUM('active', 'inactive', 'archived'),
        defaultValue: 'active',
        allowNull: true
      },
      address: {
        type: type.STRING,
        allowNull: true,
        default: null
      },
      customer_id: {
        type: type.INTEGER,
        allowNull: true
      },
      refresh_token: {
        type: type.STRING,
        allowNull: true
      }
    },
    {
      tableName: 'guest_users',
      freezeTableName: true,
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['email']
        }
      ]
    }
  );
  // GuestUser.associate = (models) => {

  // }
  return GuestUser;
};
