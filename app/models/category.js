module.exports = (sequelize, DataTypes) => {
    const Category = sequelize.define('Category', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        ropa_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        }
    }, {
        tableName: 'category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['ropa_level']
            }
        ]
    });

    Category.associate = function (models) {
        Category.hasMany(models.Controls, { foreignKey: 'category_id', constraints: false });
        Category.hasMany(models.Collaborator, { foreignKey: 'category_id', constraints: false })
    };

    return Category;
}
