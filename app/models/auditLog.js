module.exports = (sequelize, type) => {
  const AuditLog = sequelize.define(
    'AuditLog',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      type: {
        type: type.ENUM('ROPA', 'POLICY', 'LIA', 'TIA', 'PIA', 'PDA', 'VENDOR', 'SUPPORT', 'VIA', 'VEA', 'DSR', 'ASSESSMENT'),
        allowNull: false
      },
      type_id: {
        //Policy id OR Ropa_id
        type: type.INTEGER,
        allowNull: true
      },
      action: {
        type: type.TEXT,
        allowNull: true
      },
      action_by_id: {
        type: type.INTEGER,
        allowNull: false
      },
      dept_id: {
        type: type.INTEGER,
        allowNull: true
      },
      flag: {
        type: type.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      customer_id: {
        type: type.INTEGER,
        allowNull: true
      },
      comment_id: {
        type: type.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'audit_log',
      freezeTableName: true,
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['type'] // Index type
        },
        {
          fields: ['type_id'] // Index type_id
        },
        {
          fields: ['action_by_id'] // Index action_by_id
        },
        {
          fields: ['dept_id'] // Index dept_id
        },
        {
          fields: ['customer_id'] // Index customer_id
        }
      ]
    }
  );
  AuditLog.associate = function (models) {
    AuditLog.belongsTo(models.User, { foreignKey: 'action_by_id' });
    AuditLog.belongsTo(models.Departments, { foreignKey: 'dept_id' });
    AuditLog.belongsTo(models.PolicyComments, { foreignKey: 'action_by_id', targetKey: 'user_id' });
    AuditLog.belongsTo(models.Customer, { foreignKey: 'customer_id' });
    AuditLog.belongsTo(models.PolicyComments, { foreignKey: 'comment_id' });
  };
  return AuditLog;
};
