module.exports = (sequelize, DataTypes) => {
    const ReviewVIA = sequelize.define('ReviewVIA', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            unique: true
        },
        accurate_information: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        comments: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        reviewer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'review_via',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_question_id']
            },
            {
                fields: ['reviewer_id']
            }
        ]
    });

    ReviewVIA.associate = function (models) {
        ReviewVIA.belongsTo(models.User, { foreignKey: 'reviewer_id' });
        ReviewVIA.belongsTo(models.ViaCustomerControls, { foreignKey: 'customer_question_id' });
    };

    return ReviewVIA;
}
