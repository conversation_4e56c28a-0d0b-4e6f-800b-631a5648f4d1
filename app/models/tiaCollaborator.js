module.exports = (sequelize, DataTypes) => {
    const tiaCollaborator = sequelize.define('tiaCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        tia_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'tia_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['tia_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['tia_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    tiaCollaborator.associate = function (models) {
        tiaCollaborator.belongsTo(models.CustomerAssessments, { foreignKey: 'tia_id' , targetKey: 'id', constraints: false });
        tiaCollaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        tiaCollaborator.belongsTo(models.tiaCategory, { foreignKey: 'category_id', constraints: false  });
    };

    return tiaCollaborator;
}
