module.exports = (sequelize, DataTypes) => {
    const Regulations = sequelize.define('Regulations', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        applicable_regulation: {
            type: DataTypes.STRING,
            allowNull: false
        },
        abbreviation: {
            type: DataTypes.STRING,
            allowNull: false
        },
        relevance_or_reasoning: {
            type: DataTypes.TEXT,
            allowNull: false
        },
        
    }, {
        tableName: 'regulations',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['applicable_regulation']
            }
        ]
    });

    Regulations.associate = function (models) {
        // Regulations.belongsTo(models.Controls, { foreignKey: 'category_id', constraints: false });
        // Regulations.belongsTo(models.RegulationBusiRequirementMapping, { foreignKey: 'regulation_id', constraints: false })
    };

    return Regulations;
}