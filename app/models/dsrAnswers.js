module.exports = (sequelize, type) => {
    const DSRAnswers = sequelize.define('DSRAnswers', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: type.INTEGER,
            allowNull: false,
        },
        answer: {
            type: type.ARRAY(type.STRING),
            allowNull: false
        },
        attachment_link: {
            type: type.STRING,
            allowNull: true
        },
        raw_url: {
            type: type.BOOLEAN,
            allowNull: true
        },
        answered_by: {
            type: type.INTEGER,
            allowNull: true
        },
        extra_answer: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
        form_id:{
            type: type.INTEGER,
            allowNull: false
        },
        dsr_id:{
            type: type.INTEGER,
            allowNull: true
        }
    },
        {
            tableName: 'dsr_answers',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_question_id']
                },
                {
                    fields: ['answered_by']
                }
            ]
        });
    DSRAnswers.associate = function (models) {
        DSRAnswers.belongsTo(models.DSRCustomerControls, { foreignKey: 'customer_question_id', constraints: false  });
        DSRAnswers.belongsTo(models.User, { foreignKey: 'answered_by', constraints: false  });
    }
    return DSRAnswers;
}