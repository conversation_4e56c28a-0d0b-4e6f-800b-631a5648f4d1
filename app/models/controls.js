module.exports = (sequelize, DataTypes) => {
    const Controls = sequelize.define('Controls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
        industry_vertical_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        }

    }, {
        tableName: 'controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            },
            {
                fields: ['parent_id']
            },
            {
                fields: ['industry_vertical_id']
            }
        ]
    });

    Controls.associate = function (models) {
        Controls.belongsTo(models.Category, { foreignKey: 'category_id', constraints: false  });
        Controls.hasMany(models.CustomerControls, { foreignKey: 'question_id', constraints: false  });
        Controls.belongsTo(models.Controls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        Controls.hasMany(models.Controls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
        Controls.belongsTo(models.IndustryVertical, { foreignKey: 'industry_vertical_id', constraints: false  });
    };

    return Controls;
};
