module.exports = (sequelize, type) => {
    const Templates = sequelize.define("Templates",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            name: {
                type: type.STRING,
                allowNull: false,
            },
            content: {
                type: type.JSON,
                allowNull: false
            },
            thumbnail: {
                type: type.STRING,
                allowNull: true,
                default: "https://go-asset-management-profile-image.s3.us-east-2.amazonaws.com/privacy-policy-template.jpeg"
            }
        },
        {
            tableName: 'templates',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
        }
    );
    return Templates;
}
