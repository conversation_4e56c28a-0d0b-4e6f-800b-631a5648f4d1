module.exports = (sequelize, DataTypes) => {
    const VendorList = sequelize.define('VendorList', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        address: {
            type: DataTypes.TEXT,
            allowNull: false
        },
        status: {
            type: DataTypes.ENUM('active', 'inactive', 'archived'),
            defaultValue: "inactive",
            allowNull: true
        },
        poc_type: {
            type: DataTypes.ENUM('PHONE', 'EMAIL'),
            allowNull: false
        },
        email: {
            type: DataTypes.STRING,
            allowNull: true
        },
        phone: {
            type: DataTypes.STRING,
            allowNull: true
        }
    }, {
        tableName: 'vendor_list',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['email']
            }
        ]
    });

    VendorList.associate = function (models) {
        VendorList.belongsTo(models.Customer, { foreignKey: 'customer_id', as: 'customer', constraints: false });
        // VendorList.hasMany(models.Collaborator, { foreignKey: 'category_id', constraints: false })
    };

    return VendorList;
}