module.exports = (sequelize, DataTypes) => {
    const TicketComments = sequelize.define('TicketComments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        comment: {
            type: DataTypes.STRING,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        ticket_id: {
            type: DataTypes.INTEGER,
            allowNull: false
            // references: {
            //     model: 'Policy',
            //     key: 'id'
            // }
        }
    },
        {
            tableName: 'TicketComments',
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['ticket_id']
                },
                {
                    fields: ['user_id']
                }
            ]

        });
    TicketComments.associate = function (models) {
        TicketComments.belongsTo(models.Ticket, { foreignKey: 'ticket_id' });
        TicketComments.belongsTo(models.User, { foreignKey: 'user_id' });
    }

    return TicketComments;
};