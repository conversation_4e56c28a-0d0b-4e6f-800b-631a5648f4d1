module.exports = (sequelize, DataTypes) => {
    const VeaCustomerControls = sequelize.define('VeaCustomerControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // customer_id: {
        //     type: DataTypes.INTEGER,
        //     allowNull: true
        // },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        vea_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
          embedding: {
        type: DataTypes.ARRAY(DataTypes.FLOAT),
        allowNull: true
      },
      is_automated: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      }

    }, {
        tableName: 'vea_customer_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    VeaCustomerControls.associate = function (models) {
        VeaCustomerControls.belongsTo(models.VeaControls, { foreignKey: 'question_id' });
        VeaCustomerControls.belongsTo(models.VeaCategory, { foreignKey: 'category_id' });
        VeaCustomerControls.belongsTo(models.VendorAssessments, { foreignKey: 'vea_id' , targetKey:'id' });
        VeaCustomerControls.hasOne(models.VeaAnswers, { foreignKey: 'customer_question_id' });
        VeaCustomerControls.belongsTo(models.VeaCustomerControls, { as: 'Parent', foreignKey: 'parent_id' });
        VeaCustomerControls.hasMany(models.VeaCustomerControls, { as: 'Children', foreignKey: 'parent_id' });
        VeaCustomerControls.hasOne(models.ReviewVEA, { foreignKey: 'customer_question_id' , as: 'ReviewVEA' });
    };

    return VeaCustomerControls;
}
