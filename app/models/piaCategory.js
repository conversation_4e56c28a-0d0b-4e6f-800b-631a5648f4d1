module.exports = (sequelize, DataTypes) => {
    const piaCategory = sequelize.define('piaCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        pia_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
    }, {
        tableName: 'pia_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['pia_level']
            }
        ]
    });

    piaCategory.associate = function (models) {
        piaCategory.hasMany(models.piaControls, { foreignKey: 'category_id', constraints: false  });
        piaCategory.hasMany(models.piaCollaborator, { foreignKey: 'category_id', constraints: false  });
    };

    return piaCategory;
};