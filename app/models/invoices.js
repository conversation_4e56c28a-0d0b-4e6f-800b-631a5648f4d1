module.exports = (sequelize, type) => {
    const Invoices = sequelize.define('Invoices', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        plan: {
            type: type.ENUM('BASIC', 'STANDARD', 'ADVANCE', 'ENTERPRISE',),
            defaultValue: "BASIC",
            allowNull: false
        },
        invoice_number: {
            type: type.STRING,
            allowNull: false
        },
        discount: {
            type: type.FLOAT,
            allowNull: false
        },
        tax: {
            type: type.FLOAT,
            allowNull: false
        },
        total_amount: {
            type: type.FLOAT,
            allowNull: false
        },
        status: {
            type: type.ENUM('PAID', 'PAY', 'PENDING'),
            defaultValue: "PAY",
            allowNull: false
        },
        url: {
            type: type.STRING,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false
        },
    },
        {
            tableName: 'invoices',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_id']
                }
            ]
        });
    Invoices.associate = function (models) {
        Invoices.belongsTo(models.Customer, { foreignKey: 'customer_id' });
    }
    return Invoices;
}