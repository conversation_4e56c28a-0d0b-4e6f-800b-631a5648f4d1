const { Sequelize } = require('sequelize'); // Import Sequelize
module.exports = (sequelize, type) => {
    const Role = sequelize.define('Role', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            // defaultValue: Sequelize.literal("nextval('roles_id_seq')") // Fetch next ID
        },
        role_name: {
            type: type.STRING,
            allowNull: true
        },
        status: {
            type: type.ENUM('active', 'inactive', 'archived'),
            defaultValue: "active",
            allowNull: true
        },
        created_by: {
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true
        },
        is_module_head: {
            type: type.BOOLEAN,
            defaultValue: false
        },
    },
        {
            tableName: 'roles',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            uniqueKeys: {
                unique_role_customer: {
                    fields: ['role_name', 'customer_id']
                }
            },
            indexes: [
                {
                    fields: ['role_name']
                },
                {
                    fields: ['created_by']
                },
                {
                    fields: ['customer_id']
                }
            ]
        });
    Role.associate = function (models) {
        Role.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        Role.hasMany(models.User, { foreignKey: 'role_id' });
        Role.hasMany(models.UserRolePrivileges, { foreignKey: 'role_id' });
        Role.belongsTo(models.User, { as: 'createdByUser', foreignKey: 'created_by' });
    }
    return Role;
};