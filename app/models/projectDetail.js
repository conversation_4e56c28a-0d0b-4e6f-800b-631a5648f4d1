const currentDate = new Date();
const year = currentDate.getFullYear();
const month = String(currentDate.getMonth() + 1).padStart(2, '0');
const version = `${year}${month}`;

module.exports = (sequelize, type) => {
    const ProjectDetail = sequelize.define("ProjectDetail",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            client_name: {
                type: type.STRING, allowNull: false,
            },
            version: {
                type: type.STRING,
                allowNull: false,
                defaultValue: `V1-1.0.0-${version}` //'V1-1.0.0-202404'
            },
            about: {
                type: type.TEXT,
                allowNull: true
            },
            warning: {
                type: type.TEXT,
                allowNull: true
            },
            why_choose: {
                type: type.TEXT,
                allowNull: true
            },
            key_feature: {
                type: type.TEXT,
                allowNull: true
            },
            mission: {
                type: type.TEXT,
                allowNull: true
            },
            terms_and_conditions: {
                type: type.ARRAY(type.TEXT),
                allowNull: true
            }
        }, {
        tableName: 'projectDetails',
        freezeTableName: true,
        timestamps: true,
        paranoid: true
    });
    return ProjectDetail;
};