module.exports = (sequelize, type) => {
    const Services = sequelize.define('Services', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
        description: {
            type: type.STRING,
            allowNull: true
        },
    },
        {
            tableName: 'services',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });

    return Services;

};