module.exports = (sequelize, type) => {
    const EmailTemplate = sequelize.define('EmailTemplate', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: type.STRING,
            allowNull: true
        },
        subject: {
            type: type.STRING,
            allowNull: false
        },
        content: {
            type: type.TEXT,
            allowNull: false
        },
        logo: {
            type: type.STRING,
            allowNull: true
        },
        bg_color: {
            type: type.STRING,
            allowNull: true
        },
        font_color: {
            type: type.STRING,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false
        },
        author_name: {
            type: type.STRING,
            allowNull: true
        }
    },
        {
            tableName: 'email_templates',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['title']
                }
            ]
        });
        EmailTemplate.associate = (models => {
            EmailTemplate.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        })
    return EmailTemplate;
};