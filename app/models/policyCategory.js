module.exports = (sequelize, DataTypes) => {
    const PolicyCategory = sequelize.define('PolicyCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
    }, {
        tableName: 'policy_category',
        timestamps: true,
        paranoid: true
    });
    PolicyCategory.associate = function (models) {
        PolicyCategory.hasMany(models.Policy, { foreignKey: 'category_id' });
    }

    return PolicyCategory;
};
