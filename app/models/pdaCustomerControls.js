module.exports = (sequelize, DataTypes) => {
    const pdaCustomerControls = sequelize.define('pdaCustomerControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // customer_id: {
        //     type: DataTypes.INTEGER,
        //     allowNull: true
        // },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        pda_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },

    }, {
        tableName: 'pda_customer_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            },
            {
                fields: ['pda_id']
            }
        ]
    });

    pdaCustomerControls.associate = function (models) {
        pdaCustomerControls.belongsTo(models.pdaControls, { foreignKey: 'question_id', constraints: false  });
        pdaCustomerControls.belongsTo(models.pdaCategory, { foreignKey: 'category_id', constraints: false  });
        pdaCustomerControls.belongsTo(models.CustomerAssessments, { foreignKey: 'pda_id' ,targetKey: 'id', constraints: false  });
        pdaCustomerControls.hasOne(models.pdaAnswers, { foreignKey: 'customer_question_id', constraints: false  });
        pdaCustomerControls.belongsTo(models.pdaCustomerControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        pdaCustomerControls.hasMany(models.pdaCustomerControls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
        pdaCustomerControls.hasOne(models.ReviewPDA, { foreignKey: 'customer_question_id' , as: 'ReviewPDA', constraints: false })
    };

    return pdaCustomerControls;
}