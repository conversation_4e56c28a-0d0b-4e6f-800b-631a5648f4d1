module.exports = (sequelize, type) => {
    const UserPackages = sequelize.define('UserPackages', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: type.INTEGER,
            allowNull: true
        },
        package_id: {
            type: type.INTEGER,
            allowNull: true
        },
        // total_price: {
        //     type: type.INTEGER,
        //     defaultValue: 0
        // }
    },
        {
            tableName: 'user_packages',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    UserPackages.associate = function (models) {
        UserPackages.belongsTo(models.Packages, { foreignKey: 'package_id', as: 'package' });
        UserPackages.belongsTo(models.User, { foreignKey: 'user_id', as: 'user' });
    };
    return UserPackages;
};