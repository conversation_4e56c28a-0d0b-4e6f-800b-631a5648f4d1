module.exports = (sequelize, type) => {
    const Departments = sequelize.define('Departments', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: false
        },
        parent_id: {
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false
        },
        group_id: {
            type: type.INTEGER,
            allowNull: false
        },
        spoc_id: {
            type: type.INTEGER,
            allowNull: true
        },
    },
        {
            tableName: 'departments',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['group_id']
                },
                {
                    fields: ['spoc_id']
                }
            ]
        });
    Departments.associate = function (models) {
        Departments.belongsTo(models.Customer, { foreignKey: 'customer_id', constraints: false  });
        Departments.belongsTo(models.Departments, { as: "Parent", foreignKey: 'parent_id', constraints: false  });
        Departments.hasMany(models.Departments, { as: "Children", foreignKey: 'parent_id', constraints: false  });
        Departments.hasMany(models.Policy, { foreignKey: 'department_id', constraints: false  });
        Departments.belongsTo(models.User, { foreignKey: 'spoc_id', constraints: false  });
        Departments.belongsTo(models.Group, { foreignKey: 'group_id', constraints: false  });
        Departments.hasMany(models.Processes, { foreignKey: 'department_id', constraints: false  });
        Departments.hasMany(models.AuditLog, { foreignKey: 'dept_id', constraints: false  });
        Departments.hasMany(models.Ticket, { foreignKey: 'department_id', constraints: false  });
        Departments.hasMany(models.VendorDetail, { foreignKey: 'department_id' , constraints: false});
        Departments.hasMany(models.User, { foreignKey: 'department_id', constraints: false });
    }
    return Departments;
}