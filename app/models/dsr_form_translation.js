module.exports = (sequelize, DataTypes) => {
    const DsrFormTranslation = sequelize.define('DsrFormTranslation', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        form_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        language_code: {
            type: DataTypes.STRING,
            allowNull: false
        },
        language: {
            type: DataTypes.STRING,
            allowNull: true
        },
        content: {
            type: DataTypes.JSONB,
            allowNull: true 
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'dsr_form_translation',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['form_id', 'language_code'],
                unique: true
            }
        ]
    });

    DsrFormTranslation.associate = function (models) {
        DsrFormTranslation.belongsTo(models.DsrForms, { foreignKey: 'form_id' });
    };

    return DsrFormTranslation;
};