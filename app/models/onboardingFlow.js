module.exports = (sequelize, type) => {
    const OnboardingFlow = sequelize.define("OnboardingFlow",
        {
            user_id: {
                type: type.INTEGER, allowNull: false, unique: true
            },
            step: {
                type: type.ENUM('USER_CREATED', 'ORGANISATION_UPDATED', 'EMAIL_VERIFIED', 'PASSWORD_UPDATED', 'ONBOARDING_COMPLETED', 'QUESTIONNAIRES_COMPLETED'), allowNull: false,
            },
        }, {
        tableName: 'onboarding_flows',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['user_id']
            }
        ]

    });
    OnboardingFlow.associate = models => {
        OnboardingFlow.belongsTo(models.User, { foreignKey: 'user_id' });
    };
    return OnboardingFlow;
}