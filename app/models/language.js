module.exports = (sequelize, DataTypes) => {
    const Language = sequelize.define('language', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        language_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
    },
        { tableName: 'language', timestamps: true, paranoid: true, freezeTableName: true, deletedAt: 'deletedAt' });

    return Language;

};