module.exports = (sequelize, DataTypes) => {
  const RegulationsV2 = sequelize.define(
    'RegulationsV2',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      geography: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      mapping_column_header: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      source: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      authoritative_source: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      version: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      url: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      available: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'regulationsV2',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['source']
        }
      ]
    }
  );

  RegulationsV2.associate = function (models) {
    RegulationsV2.belongsTo(models.Controls, { foreignKey: 'category_id' });
    RegulationsV2.hasMany(models.RegulationBusiRequirementMapping, { foreignKey: 'regulation_id' });
    RegulationsV2.hasMany(models.RegulationComplianceStatus, { foreignKey: 'regulation_id' });
    RegulationsV2.belongsToMany(models.DsrForms, { through: models.DsrFormRegulations, foreignKey: 'regulation_id', otherKey: 'form_id' });
  };

  return RegulationsV2;
};
