const { allow } = require("joi");

module.exports =(sequelize,type)=>{
    const Risk = sequelize.define('Risk',{
        id:{
            type:type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: type.STRING,
            allowNull: true
        },
        description: {
            type: type.TEXT,
            allowNull: true
        },
        // risk_name: {
        //     type: type.ENUM('IDENTIFIED', 'EVALUATION', 'MITIGATION', 'CLOSURE', 'MONITORING'),
        //     allowNull: true
        // },
        regulation_id: {
            type: type.INTEGER,
            allowNull: true
        },
        owner_id: {
            type: type.INTEGER,
            allowNull: true
        },
        approver_id: {
            type: type.INTEGER,
            allowNull: true
        },
        stage: {
            type: type.ENUM('IDENTIFIED', 'EVALUATION', 'MITIGATION', 'CLOSURE', 'MONITORING'),
            allowNull: true
        },
        module: {
            type: type.ENUM('DATA_MAPPING', 'ASSESSMENTS', 'VENDOR_RISK_MANAGEMENT', 'OTHER'),
            allowNull: true
        },
        category: {
            type: type.ENUM('LEGAL', 'COMPLIANCE', 'DATA_BREACH', 'OTHER'),
            allowNull: true
        },
        source: {
            type: type.ENUM('INTERNAL_SYSTEM', 'VENDOR', 'CUSTOMER','INTERNAL_AUDIT','EXTERNAL_AUDIT', 'REGULATORY_OBLIGATION', 'OTHER'),
            allowNull: true
        },
        residual_risk_level: {
            type: type.ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW'),
            allowNull: true
        },
        inherent_risk_level: {
            type: type.ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW'),
            allowNull: true
        },
        target_risk_level: {
            type: type.ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW'),
            allowNull: true
        },
        treatment_plan: {
            type: type.TEXT,
            allowNull: true
        },
        treatment_status: {
            type: type.ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'UNDER REVIEW'),
            allowNull: true
        },
        risk_template: {
            type: type.ENUM('DPDPA_COMPLIANCE_RISK_TEMPLATE', 'GDPR_COMPLIANCE_RISK_TEMPLATE', 'CCPA_RISK_ASSESSMENT_TEMPLATE', 'ISO_27001_RISK_TEMPLATE'),
            allowNull: true
        },
        threat: {
            type: type.TEXT,
            allowNull: true
        },
        vulnerability: {
            type: type.TEXT,
            allowNull: true
        },
        identified_date: {
            type: type.DATE,
            allowNull: true,
        },
        reminder_date : {
            type: type.DATE,
            allowNull: true,
        },
        deadline_date: {
            type: type.DATE,
            allowNull: true,
        },
        action_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        entity_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        result: {
            type: type.TEXT,
            allowNull: true
        }
        
    },
    {
        tableName: 'risk',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['approver_id']
            },
            {
                fields: ['title']
            },
            {
                fields:['owner_id']
            },
        ]
    });

    
    Risk.associate = (models => {
        Risk.belongsTo(models.User, { as :'Approver',foreignKey: 'approver_id' });
        Risk.belongsTo(models.User, { as :'Owner', foreignKey: 'owner_id' });
        Risk.belongsTo(models.Group, { foreignKey: 'entity_id', targetKey: 'id'});
        
    })

    return Risk;

};