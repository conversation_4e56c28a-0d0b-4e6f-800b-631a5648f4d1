module.exports = (sequelize, DataTypes) => {
  const ViaCustomerControls = sequelize.define(
    'ViaCustomerControls',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      question_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      // customer_id: {
      //     type: DataTypes.INTEGER,
      //     allowNull: true
      // },
      category_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      via_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      parent_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      is_custom: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        default: false
      },
      title: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      artifact_type: {
        type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
        allowNull: true
      },
      is_attachment: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        default: false
      },
      question: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      fields: {
        type: DataTypes.ARRAY(DataTypes.JSON),
        allowNull: true
      },
      extra_input: {
        type: DataTypes.BOOLEAN,
        allowNull: true
      },
      extra_input_type: {
        type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
        allowNull: true
      },
      extra_input_fields: {
        type: DataTypes.ARRAY(DataTypes.JSON),
        allowNull: true
      },
      endpoint: {
        type: DataTypes.STRING,
        allowNull: true
      }
    },
    {
      tableName: 'via_customer_controls',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['category_id']
        }
      ]
    }
  );

  ViaCustomerControls.associate = function (models) {
    ViaCustomerControls.belongsTo(models.ViaControls, { foreignKey: 'question_id', constraints: false });
    ViaCustomerControls.belongsTo(models.ViaCategory, { foreignKey: 'category_id', constraints: false });
    ViaCustomerControls.belongsTo(models.VendorAssessments, { foreignKey: 'via_id', targetKey: 'id', constraints: false });
    ViaCustomerControls.hasOne(models.ViaAnswers, { foreignKey: 'customer_question_id', constraints: false });
    ViaCustomerControls.belongsTo(models.ViaCustomerControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false });
    ViaCustomerControls.hasMany(models.ViaCustomerControls, { as: 'Children', foreignKey: 'parent_id', constraints: false });
    ViaCustomerControls.hasOne(models.ReviewVIA, { foreignKey: 'customer_question_id', as: 'ReviewVIA', constraints: false });
  };

  return ViaCustomerControls;
};
