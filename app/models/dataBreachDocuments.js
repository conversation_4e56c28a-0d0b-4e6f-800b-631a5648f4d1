module.exports = (sequelize, DataTypes) => {
    const DataBreachDocument = sequelize.define('DataBreachDocument', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        original_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        data_breach_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
        
    }, {
        tableName: 'DataBreachDocuments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['data_breach_id']
            }
        ]

    });
    DataBreachDocument.associate = function (models) {
        DataBreachDocument.belongsTo(models.DataBreachManagement, { foreignKey: 'data_breach_id' });
    }

    return DataBreachDocument;
};