module.exports = (sequelize, type) => {
    const UserQuestionnaires = sequelize.define('UserQuestionnaires', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        question_id: {
            type: type.INTEGER,
            allowNull: true
        },
        user_id: {
            type: type.INTEGER,
            allowNull: false
        },
        status: {
            type: type.BOOLEAN,
            allowNull: false
        },
    },
        {
            tableName: 'user_questionnaires',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
        });
    UserQuestionnaires.associate = function (models) {
        UserQuestionnaires.belongsTo(models.Questionnaires, { foreignKey: 'question_id', as: 'questionnaires' });
        UserQuestionnaires.belongsTo(models.User, { foreignKey: 'user_id', as: 'users' });
    }
    return UserQuestionnaires;
};