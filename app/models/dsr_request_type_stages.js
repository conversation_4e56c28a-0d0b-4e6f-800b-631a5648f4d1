module.exports = (sequelize, DataTypes) => {
    const RequestTypeStages = sequelize.define('RequestTypeStages', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        type_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        step_title: {
            type: DataTypes.STRING,
            allowNull: false
        },
        guidance_text: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        order:{
            type: DataTypes.INTEGER,
            allowNull: false
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        activepieces_automation_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
    }, {
        tableName: 'dsr_request_type_stages',
        timestamps: true,
        paranoid: true
    });

    RequestTypeStages.associate = function (models) {
        RequestTypeStages.belongsTo(models.DsrRequestType, { foreignKey: 'type_id', constraints: false });
        RequestTypeStages.hasMany(models.RequestTask, { foreignKey: 'stage_id', constraints: false });

    };

    return RequestTypeStages;
};
