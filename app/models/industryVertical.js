module.exports = (sequelize, type) => {
  const IndustryVertical = sequelize.define(
    'IndustryVertical',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: type.STRING,
        allowNull: true
      }
    },
    {
      tableName: 'industry_vertical',
      freezeTableName: true,
      timestamps: true,
      paranoid: true
    }
  );
  IndustryVertical.associate = models => {
    IndustryVertical.hasMany(models.Questionnaires, { foreignKey: 'industry_vertical_id' });
    IndustryVertical.hasMany(models.Customer, { foreignKey: 'industry_vertical', targetKey: 'industry_vertical' });
  };
  return IndustryVertical;
};
