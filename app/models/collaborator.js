module.exports = (sequelize, DataTypes) => {
    const Collaborator = sequelize.define('Collaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ropa_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['ropa_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['ropa_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    Collaborator.associate = function (models) {
        Collaborator.belongsTo(models.ROPA, { foreignKey: 'ropa_id', constraints: false });
        Collaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false });
        Collaborator.belongsTo(models.Category, { foreignKey: 'category_id', constraints: false });
    };

    return Collaborator;
}
