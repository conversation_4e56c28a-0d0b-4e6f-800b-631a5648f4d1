const { type } = require(".")

module.exports=(sequelize,DataTypes)=>{
    const AssessmentTemplate = sequelize.define('AssessmentTemplate',{
        id:{
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name:{
            type:DataTypes.STRING,
            allowNull:false
        },
        key:{
            type:DataTypes.STRING,
            allowNull:false
        },
        published:{
            type:DataTypes.ENUM('NO', 'YES'),
            default: 'NO'
        },
        url:{
            type:DataTypes.JSONB,
            allowNull:true
        },
        assessment_id:{
            type: DataTypes.INTEGER,
            allowNull:false
        },
        customer_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        }
    },{
        tableName: 'assessment_template',
        timestamps: true,
        paranoid: true,
    });

    AssessmentTemplate.associate = function(models){
    AssessmentTemplate.belongsTo(models.Assessments,{foreignKey:'assessment_id' });
    }
    return AssessmentTemplate;
}