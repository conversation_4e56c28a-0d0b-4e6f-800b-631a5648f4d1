module.exports = (sequelize, type) => {
  const Duties = sequelize.define(
    'Duties',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      title: {
        type: type.STRING,
        allowNull: true
      },
      assignee_id: {
        type: type.ARRAY(type.INTEGER),
        allowNull: true
      },
      owner_id: {
        type: type.INTEGER,
        allowNull: true
      },
      customer_id: {
        type: type.INTEGER,
        allowNull: true
      },
      start_date: {
        type: type.DATE,
        allowNull: true
      },
      due_date: {
        type: type.DATE,
        allowNull: true
      },
      frequency: {
        type: type.ENUM('ANNUALLY', 'BI_ANNUAL', 'TRI_MONTHLY', 'MONTHLY')
      },
      document: {
        type: type.JSONB,
        allowNull: true
      },
      status: {
        type: type.ENUM('OPEN', 'COMPLETED'),
        defaultValue: 'OPEN',
        allowNull: true
      },
      standard: {
        type: type.TEXT,
        allowNull: true
      },
      criteria: {
        type: type.TEXT,
        allowNull: true
      },
      comment: {
        type: type.TEXT,
        allowNull: true
      },
      evidence: {
        type: type.TEXT,
        allowNull: true
      },
      tags: {
        type: type.ARRAY(type.INTEGER),
        allowNull: true
      },
      custom_busi_requirement_id: {
        type: type.INTEGER,
        allowNull: true
      },
      regulation_id: {
        type: type.INTEGER,
        allowNull: true
      },
      entity_id: {
        type: type.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'duties',
      freezeTableName: true,
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['customer_id']
        },
        {
          fields: ['assignee_id']
        },
        {
          fields: ['title']
        }
      ]
    }
  );
  Duties.associate = models => {
    // Duties.belongsTo(Duties, { as: 'parent', foreignKey: 'parent_id', constraints: false  });
    // Duties.hasMany(Duties, { as: 'children', foreignKey: 'parent_id', constraints: false  });
    // Duties.hasMany(models.DutiesUser, { foreignKey: 'group_id', constraints: false  });
    // Duties.belongsTo(models.User, { as :'Assignee',foreignKey: 'assignee_id' });
    Duties.belongsTo(models.User, { as: 'Owner', foreignKey: 'owner_id' });
    Duties.hasMany(models.DutyFullfillments, { foreignKey: 'duty_id' });
    Duties.belongsTo(models.CustomerBusinessRequirements, { foreignKey: 'custom_busi_requirement_id', targetKey: 'id' });
  };

  return Duties;
};
