module.exports = (sequelize, DataTypes) => {
    const Regions = sequelize.define('Regions', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        region_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
    },
        { tableName: 'regions', timestamps: true, paranoid: true, freezeTableName: true, deletedAt: 'deletedAt' });

    return Regions;

};