module.exports = (sequelize, DataTypes) => {
    const BusinessRequirementPolicy = sequelize.define('BusinessRequirementPolicy', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        regulation_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        busi_req_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        document_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
        
    }, {
        tableName: 'business_requirement_policy',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['busi_req_id']
            }
        ]
    });

    BusinessRequirementPolicy.associate = function (models) {
        BusinessRequirementPolicy.belongsTo(models.CustomerBusinessRequirements, { foreignKey: 'busi_req_id', targetKey: 'id', constraints: false });
    };

    return BusinessRequirementPolicy;
}