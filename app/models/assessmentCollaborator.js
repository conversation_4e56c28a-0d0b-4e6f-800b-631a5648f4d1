module.exports = (sequelize, DataTypes) => {
    const AssessmentCollaborator = sequelize.define('AssessmentCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        assessment_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'assessment_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['assessment_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['assessment_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    AssessmentCollaborator.associate = function (models) {
        AssessmentCollaborator.belongsTo(models.CustomerAssessment, { foreignKey: 'assessment_id' , targetKey: 'id', constraints: false });
        AssessmentCollaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        AssessmentCollaborator.belongsTo(models.AssessmentCategory, { foreignKey: 'category_id', constraints: false  });
    };

    return AssessmentCollaborator;
}