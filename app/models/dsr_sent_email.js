module.exports = (sequelize, type) => {
    const DsrSentEmail = sequelize.define('DsrSentEmail', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        request_id: {
            type: type.INTEGER,
            allowNull: false
        }, 
        email_template_id: {
            type: type.INTEGER,
            allowNull: true
        },   
        subject: {
            type: type.STRING,
            allowNull: true
        },
        content: {
            type: type.TEXT,
            allowNull: false
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: false
        },
        user_id: {
            type: type.INTEGER,
            allowNull: false
        },
        recipients_id: {
            type: type.ARRAY(type.INTEGER),
            allowNull: true
        },
        recipients_email: {
            type: type.ARRAY(type.STRING),
            allowNull: true
        },
        mail_type: {
            type: type.ENUM('EXTERNAL', 'INTERNAL'),
            defaultValue: 'INTERNAL'
        },
        first_name: {
            type: type.STRING,
            allowNull: true
        },
        last_name: {
            type: type.STRING,
            allowNull: true
        },
        read_status: {
            type: type.ENUM('READ', 'UNREAD'),
            defaultValue: 'UNREAD'
        }
    },
        {
            tableName: 'dsr_sent_mail',
            freezeTableName: true,
            timestamps: true,
            paranoid: true
           
        });
        DsrSentEmail.associate = (models => {
            // DsrSentEmail.belongsTo(models.User, { foreignKey: 'user_id' });
            DsrSentEmail.hasMany(models.DsrMailDocuments, { foreignKey: 'dsr_sent_mail_id', targetKey: 'id' })

        })
    return DsrSentEmail;
};