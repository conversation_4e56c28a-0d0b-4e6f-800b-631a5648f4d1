module.exports = (sequelize, DataTypes) => {
    const pdaCollaborator = sequelize.define('pdaCollaborator', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        pda_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
    }, {
        tableName: 'pda_collaborator',
        timestamps: true,
        paranoid: true,
        uniqueKeys: {
            unique_tag: {
                fields: ['pda_id', 'user_id', 'category_id']
            }
        },
        indexes: [
            {
                fields: ['pda_id']
            },
            {
                fields: ['user_id']
            },
            {
                fields: ['category_id']
            }
        ]
    });

    pdaCollaborator.associate = function (models) {
        pdaCollaborator.belongsTo(models.CustomerAssessments, { foreignKey: 'pda_id' , targetKey: 'id', constraints: false });
        pdaCollaborator.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        pdaCollaborator.belongsTo(models.pdaCategory, { foreignKey: 'category_id', constraints: false  });
    };

    return pdaCollaborator;
}
