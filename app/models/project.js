module.exports = (sequelize, type) => {
    const Project = sequelize.define("Project",
        {
            id: {
                type: type.INTEGER, primaryKey: true, autoIncrement: true,
            },
            client_name: {
                type: type.STRING, allowNull: false,
            },
            domain_name: {
                type: type.STRING,
                allowNull: false,
                unique: true // Assuming domain names should be unique
            },
            ip_address: {
                type: type.STRING,
                allowNull: true
            },
            token: {
                type: type.STRING,
                allowNull: false,
                unique: true // Assuming tokens should be unique
            },
            status: {
                type: type.ENUM('active', 'inactive', 'pending'),
                allowNull: false,
                defaultValue: 'pending' // Assuming a default status of 'pending'
            }
        }, {
        tableName: 'projects',
        freezeTableName: true,
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['client_name']
            },
            {
                fields: ['domain_name']
            },
            {
                fields: ['ip_address']
            }
        ]
    });
    return Project;
};
