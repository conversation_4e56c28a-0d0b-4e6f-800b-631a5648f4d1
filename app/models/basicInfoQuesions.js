module.exports = (sequelize, DataTypes) => {
    const BasicInfoQuestions = sequelize.define('BasicInfoQuestions', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        ropa_level: {
            type: DataTypes.ENUM('vendor', 'department', 'process'),
            allowNull: false
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'date'),
            allowNull: false
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: false
        }
    }, {
        tableName: 'basic_info_questions',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['ropa_level']
            }
        ]
    });

    BasicInfoQuestions.associate = function (models) {
        BasicInfoQuestions.hasOne(models.BasicInfoAnswers, { foreignKey: 'question_id' });
    };

    return BasicInfoQuestions;
}