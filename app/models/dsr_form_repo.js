module.exports = (sequelize, DataTypes) => {
    const DsrFormRepository = sequelize.define('DsrFormRepository', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        content: {
            type: DataTypes.JSON,
            allowNull: true 
        },
        url: {
            type: DataTypes.STRING,
            allowNull: true
        },
        busi_unit_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        },
        published:{
            type:DataTypes.ENUM('NO','YES'),
            defaultValue:'NO'
        },
        name:{
            type: DataTypes.STRING,
            allowNull: true
        },
        
    }, {
        tableName: 'dsr_form_repository',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            }
        ]
    });

    DsrFormRepository.associate = function (models) {
        DsrFormRepository.belongsTo(models.Customer, { foreignKey: 'customer_id' });
        DsrFormRepository.belongsTo(models.User, { foreignKey: 'user_id' });
        DsrFormRepository.hasMany(models.DSRCustomerCategory, { foreignKey: 'form_id'});
        DsrFormRepository.hasMany(models.DSRCustomerControls, { foreignKey: 'form_id'});
        
    };

    return DsrFormRepository;
};