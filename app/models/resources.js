const { Sequelize } = require('sequelize'); // Import Sequelize
module.exports = (sequelize, type) => {
    const Resources = sequelize.define('Resources', {
        resource_id: { // 1 hrms , 2 leave
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            // defaultValue: Sequelize.literal("nextval('resources_resource_id_seq')") // Fetches the next available ID
        },
        resource_name: {
            type: type.STRING,
            allowNull: false
        },
        description: {
            type: type.TEXT,
            allowNull: true
        },
        status: {
            type: type.INTEGER,
            allowNull: false,
            defaultValue: '0'
        },
        // resource_key: {
        //     type:type.STRING,
        //     allowNull:true
        // },
        parent_id: {  // null, 1
            type: type.INTEGER,
            allowNull: true
        },
        icon: {
            type: type.STRING,
            allowNull: true
        },
        route: {
            type: type.STRING,
            allowNull: true
        },
        order: {
            type: type.INTEGER,
            allowNull: true
        },
    },
        {
            tableName: 'resources',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['resource_name']
                },
                {
                    fields: ['status']
                },
                {
                    fields: ['parent_id']
                }
            ]
        });
    Resources.associate = function (models) {
        Resources.hasMany(models.CustomerResources, { foreignKey: 'resource_id', constraints: false  });
        Resources.hasMany(models.UserRolePrivileges, { foreignKey: 'resource_id', constraints: false  });
    }
    return Resources;
};