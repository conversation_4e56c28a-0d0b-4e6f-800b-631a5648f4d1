module.exports = (sequelize, DataTypes) => {
    const tiaCategory = sequelize.define('tiaCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        tia_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
    }, {
        tableName: 'tia_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['tia_level']
            }
        ]
    });

    tiaCategory.associate = function (models) {
        tiaCategory.hasMany(models.tiaControls, { foreignKey: 'category_id', constraints: false  });
        tiaCategory.hasMany(models.tiaCollaborator, { foreignKey: 'category_id', constraints: false  });
    };

    return tiaCategory;
};