module.exports = (sequelize, DataTypes) => {
    const Assessments = sequelize.define('Assessments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        type:{
            type: DataTypes.ENUM('impact_assessment' ,'eu_ai_assessment' ,'regulatory_assessment'),
            allowNull: false
        },
        region_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        },
        assessment_name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        key: {
            type: DataTypes.STRING,
            allowNull: true
        },
        status:{
            type: DataTypes.BOOLEAN,
            allowNull: false
        },
        customer_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        }
    }, {
        tableName: 'assessments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['type']
            },
            {
                fields: ['region_id']
            },
            {
                fields: ['assessment_name']
            }
        ]
    });

    Assessments.associate = function (models) {
        Assessments.belongsTo(models.Regions, { foreignKey: 'region_id', constraints: false });
        Assessments.hasMany(models.CustomerAssessments, { foreignKey: 'assessment_id', constraints: false })
        Assessments.hasMany(models.CustomerAssessment,{foreignKey: 'assessment_id', constraints: false});
        Assessments.hasMany(models.AssessmentTemplate,{foreignKey: 'assessment_id', constraints: false});

    };

    return Assessments;
}