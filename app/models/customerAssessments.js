module.exports = (sequelize, DataTypes) => {
    const CustomerAssessment = sequelize.define('CustomerAssessment', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        assessment_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        entity_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        process_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        approver: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        owner_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        assigned_to: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        start_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        end_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        tentative_date: {
            type: DataTypes.DATE,
            allowNull: true,
            default: '1999-01-01' // Set default value to '1999-01-01'
        },
        risks: {
            type: DataTypes.ENUM('Low', 'Medium', 'High'),
            allowNull: true
        },
        risk_score: {
            type: DataTypes.DOUBLE,
            allowNull: true
        },
        progress: {
            type: DataTypes.DOUBLE,
            default: 0
        },
        status: {
            type: DataTypes.ENUM('Yet to Start', 'Started', 'Under Review', 'Changes Requested', 'Completed'),
            allowNull: false,
            default: 'Yet to Start'
        },
        is_already_performed: {
            type:DataTypes.BOOLEAN,
            default: false,
            allowNull: true
        },
        doc_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        document: {
            type: DataTypes.STRING,
            allowNull: true
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        }

    }, {
        tableName: 'customer_assessment',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['assessment_id']
            }
        ]
    });
    CustomerAssessment.associate = function (models) {
        CustomerAssessment.hasMany(models.Regions, { foreignKey: 'region_id' });
        CustomerAssessment.belongsTo(models.Assessments, { foreignKey: 'assessment_id', targetKey: 'id' });
        CustomerAssessment.belongsTo(models.Departments, { foreignKey: 'department_id' });
        CustomerAssessment.belongsTo(models.Processes, { foreignKey: 'process_id' });

        CustomerAssessment.belongsTo(models.Customer, { foreignKey: 'customer_id' });

        CustomerAssessment.belongsTo(models.User, { as: 'AssignedTo', foreignKey: 'assigned_to' });
        CustomerAssessment.belongsTo(models.User, { as: 'Approver', foreignKey: 'approver' });
        CustomerAssessment.belongsTo(models.User, { as: 'Owner', foreignKey: 'owner_id' });

        CustomerAssessment.belongsTo(models.Group, { foreignKey: 'entity_id' });
        CustomerAssessment.belongsTo(models.AssessmentCustomerControls, { foreignKey: 'assessment_id' });
        CustomerAssessment.belongsTo(models.AssessmentCustomerControls, { foreignKey: 'assessment_id' });
        CustomerAssessment.belongsTo(models.AssessmentTemplate, { foreignKey: 'template_id'  });

    };


    return CustomerAssessment;
}