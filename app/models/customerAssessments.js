module.exports = (sequelize, DataTypes) => {
    const CustomerAssessments = sequelize.define('CustomerAssessments', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        assessment_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        entity_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        department_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        process_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        approver: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        owner_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        assigned_to: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        start_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        end_date: {
            type: DataTypes.DATE,
            allowNull: true
        },
        tentative_date: {
            type: DataTypes.DATE,
            allowNull: true,
            default: '1999-01-01' // Set default value to '1999-01-01'
        },
        risks: {
            type: DataTypes.ENUM('Low', 'Medium', 'High'),
            allowNull: true
        },
        risk_score: {
            type: DataTypes.DOUBLE,
            allowNull: true
        },
        progress: {
            type: DataTypes.DOUBLE,
            default: 0
        },
        status: {
            type: DataTypes.ENUM('Yet to Start', 'Started', 'Under Review', 'Changes Requested', 'Completed'),
            allowNull: false,
            default: 'Yet to Start'
        },
        is_already_performed: {
            type:DataTypes.BOOLEAN,
            default: false,
            allowNull: true
        },
        doc_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        document: {
            type: DataTypes.STRING,
            allowNull: true
        },
    }, {
        tableName: 'customer_assessments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['assessment_id']
            }
        ]
    });

    CustomerAssessments.associate = function (models) {
        CustomerAssessments.hasMany(models.Regions, { foreignKey: 'region_id', constraints: false });
        CustomerAssessments.belongsTo(models.Assessments, { foreignKey: 'assessment_id', targetKey: 'id', constraints: false });
        CustomerAssessments.belongsTo(models.Departments, { foreignKey: 'department_id', constraints: false });
        CustomerAssessments.belongsTo(models.Processes, { foreignKey: 'process_id', constraints: false });

        CustomerAssessments.belongsTo(models.Customer, { foreignKey: 'customer_id', constraints: false });

        CustomerAssessments.belongsTo(models.User, { as: 'AssignedTo', foreignKey: 'assigned_to', constraints: false });
        CustomerAssessments.belongsTo(models.User, { as: 'Approver', foreignKey: 'approver', constraints: false });
        CustomerAssessments.belongsTo(models.User, { as: 'Owner', foreignKey: 'owner_id', constraints: false });

        CustomerAssessments.belongsTo(models.Group, { foreignKey: 'entity_id', constraints: false });

        CustomerAssessments.belongsTo(models.liaCollaborator, { foreignKey: 'lia_id', constraints: false });
        CustomerAssessments.belongsTo(models.piaCollaborator, { foreignKey: 'pia_id', constraints: false });
        CustomerAssessments.belongsTo(models.tiaCollaborator, { foreignKey: 'tia_id', constraints: false });
        CustomerAssessments.belongsTo(models.pdaCollaborator, { foreignKey: 'pda_id', constraints: false });

        CustomerAssessments.belongsTo(models.liaCustomerControls, { foreignKey: 'lia_id', constraints: false });
        CustomerAssessments.belongsTo(models.piaCustomerControls, { foreignKey: 'pia_id', constraints: false });
        CustomerAssessments.belongsTo(models.tiaCustomerControls, { foreignKey: 'tia_id', constraints: false });
        CustomerAssessments.belongsTo(models.pdaCustomerControls, { foreignKey: 'pda_id', constraints: false });
    };

    return CustomerAssessments;
}