module.exports = (sequelize, DataTypes) => {
    const DSRCustomerCategory = sequelize.define('DSRCustomerCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        form_id:{
            type:DataTypes.INTEGER,
            allowNull: true
        },
        is_custom: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            default: false
        },
        // ropa_level: {
        //     type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
        //     allowNull: false
        // },
    }, {
        tableName: 'dsr_customer_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['name']
            }
        ]
    });

    DSRCustomerCategory.associate = function (models) {
        DSRCustomerCategory.belongsTo(models.DsrFormRepository, { foreignKey: 'form_id', constraints: false });
        DSRCustomerCategory.hasMany(models.DSRCustomerControls, { foreignKey: 'category_id', constraints: false });
    };

    return DSRCustomerCategory;
}