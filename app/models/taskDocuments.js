module.exports = (sequelize, DataTypes) => {
    const TaskDocument = sequelize.define('TaskDocument', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        original_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        task_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        dsr_request_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },        
    }, {
        tableName: 'dsr_task_documents',
        timestamps: true,
        paranoid: true,        

    });
    TaskDocument.associate = function (models) {
        TaskDocument.belongsTo(models.RequestTask, { foreignKey: 'task_id', targetKey: 'id' });
    }

    return TaskDocument;
};