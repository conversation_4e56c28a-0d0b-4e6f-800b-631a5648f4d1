module.exports = (sequelize, type) => {
    const Group = sequelize.define('Group', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: type.STRING,
            allowNull: true
        },
        parent_id: {
            type: type.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        user_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        spoc_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        status: {
            type: type.ENUM('active', 'inactive', 'archived'),
            defaultValue: "active",
            allowNull: true
        },
        source_country_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        industry_vertical_id: {
            type: type.INTEGER,
            allowNull: true,
        },
        regulation_ids: {
            type: type.ARRAY(type.INTEGER),
            allowNull: true,
        }

    },
        {
            tableName: 'group',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            indexes: [
                {
                    fields: ['customer_id']
                },
                {
                    fields: ['user_id']
                },
                {
                    fields: ['spoc_id']
                }
            ]
        });
    Group.associate = (models => {
        Group.belongsTo(Group, { as: 'parent', foreignKey: 'parent_id', constraints: false  });
        Group.hasMany(Group, { as: 'children', foreignKey: 'parent_id', constraints: false  });
        Group.hasMany(models.GroupUser, { foreignKey: 'group_id', constraints: false  });
        Group.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        Group.belongsTo(models.User, { foreignKey: 'spoc_id', constraints: false  });
        Group.hasMany(models.Policy, { foreignKey: 'entity_id', constraints: false  });
        Group.hasMany(models.CustomerAssessments, { foreignKey: 'entity_id', constraints: false  });
    })


    return Group;

};