module.exports = (sequelize, DataTypes) => {
  const DataBreachManagement = sequelize.define(
    'DataBreachManagement',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      breach_id: {
        type: DataTypes.STRING,
        allowNull: true
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false
      },
      reporter_name: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      designation: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      handled_by: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      //   your_role_in_this_incident: {
      //     type: DataTypes.ARRAY(DataTypes.ENUM('DATA_FIDUCIARY', 'DATA_PROCESSOR')),
      //     allowNull: true
      //   },
      your_role_in_incident: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true
      },

      // category: {
      //     type: DataTypes.ENUM('DATA_LEAK'),
      //     allowNull: true
      // },
      date_of_incident: {
        type: DataTypes.DATE,
        allowNull: true
      },
      date_of_discovery: {
        type: DataTypes.DATE,
        allowNull: true
      },
      incident_details: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      // affected_systems: {
      //     type: DataTypes.ENUM('HR_DATABASE'),
      //     allowNull: true
      // },
      // affected_data_subjects: {
      //     type: DataTypes.ENUM('EMPLOYEE_DATA'),
      //     allowNull: true
      // },
      affected_pii: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true
      },
      no_of_data_records_affected: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      no_of_data_subjects_affected: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      severity_level: {
        type: DataTypes.ENUM('MAJOR', 'MINOR'),
        allowNull: true
      },
      did_you_already_contact_the_data_fiduciary: {
        type: DataTypes.ENUM('YES', 'NO'),
        allowNull: true
      },
      applicable_jurisdiction: {
        type: DataTypes.ARRAY(DataTypes.INTEGER),
        allowNull: true
      },
      evaluate_notes: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      resolution_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      compliance_officer: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      resolution_notes: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM('OPEN', 'CLOSED'),
        defaultValue: 'OPEN'
      },
      /*tentative_date: {
            type: DataTypes.DATE,
            allowNull: true
        }, */
      business_unit: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'data_breach_management',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['status']
        }
      ]
    }
  );

  DataBreachManagement.associate = function (models) {
    DataBreachManagement.belongsTo(models.Customer, { foreignKey: 'customer_id' });
    DataBreachManagement.belongsTo(models.User, { foreignKey: 'handled_by' });
    DataBreachManagement.hasMany(models.DataBreachDocument, { foreignKey: 'data_breach_id' });
    DataBreachManagement.hasMany(models.DataBreachJurisdictionNotify, { foreignKey: 'data_breach_management_id' });
  };

  return DataBreachManagement;
};
