module.exports = (sequelize, type) => {
    const GroupUser = sequelize.define('GroupUser', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        group_id: {
            type: type.INTEGER,
            allowNull: false
        },
        user_id: {
            type: type.INTEGER,
            allowNull: false
        }
    },
        {
            tableName: 'group_user',
            freezeTableName: true,
            timestamps: true,
            paranoid: true,
            uniqueKeys: {
                unique_group_user: {
                    fields: ['group_id', 'user_id']
                }
            },
            indexes: [
                {
                    fields: ['group_id']
                },
                {
                    fields: ['user_id']
                }
            ]
        });

    GroupUser.associate = (models) => {
        GroupUser.belongsTo(models.Group, { foreignKey: 'group_id' });
        GroupUser.belongsTo(models.User, { foreignKey: 'user_id' });
    }

    return GroupUser;


}