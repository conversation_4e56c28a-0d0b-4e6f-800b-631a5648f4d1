module.exports = (sequelize, DataTypes) => {
    const DsrForms = sequelize.define('DsrForms', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        content: {
            type: DataTypes.JSON,
            allowNull: true 
        },
        url: {
            type: DataTypes.STRING,
            allowNull: true
        },
        busi_unit_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        },
        published:{
            type:DataTypes.ENUM('NO','YES'),
            defaultValue:'NO'
        },
        name:{
            type: DataTypes.STRING,
            allowNull: true
        },
        version:{
            type: DataTypes.STRING,
            allowNull: true
        },
        authentication:{
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        authentication_type: {
            type: DataTypes.ENUM('EMAIL', 'WHATSAPP', 'SMS'),
            allowNull: true,  
            defaultValue: null, 
        },
        regulation_id:{
            type: DataTypes.JSONB,
            allowNull: true 
        },
        
    }, {
        tableName: 'dsr_forms',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            }
        ]
    });

    DsrForms.associate = function (models) {
        DsrForms.belongsTo(models.Customer, { foreignKey: 'customer_id', constraints: false  });
        DsrForms.belongsTo(models.User, { foreignKey: 'user_id', constraints: false  });
        DsrForms.hasMany(models.DSRCustomerCategory, { foreignKey: 'form_id', constraints: false });
        DsrForms.hasMany(models.DSRCustomerControls, { foreignKey: 'form_id', constraints: false });
        DsrForms.belongsToMany(models.RegulationsV2, {through: models.DsrFormRegulations,foreignKey: 'form_id',otherKey: 'regulation_id', constraints: false
        });
        DsrForms.belongsTo(models.Group, { foreignKey: 'busi_unit_id', constraints: false  });        
    };

    return DsrForms;
};