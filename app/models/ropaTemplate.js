module.exports = (sequelize, DataTypes) => {
  const RopaTemplate = sequelize.define(
    'RopaTemplate',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      temp_name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      url: {
        type: DataTypes.STRING,
        allowNull: true
      },
      // content: {
      //     type: DataTypes.JSON,
      //     allowNull: true
      // },
      thumbnail: {
        type: DataTypes.STRING,
        allowNull: true,
        default:
          'https://go-asset-management-profile-image.s3.us-east-2.amazonaws.com/privacy-policy-template.jpeg'
      },
      entity_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'ropa_template',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['customer_id']
        }
      ]
    }
  );

  RopaTemplate.associate = function (models) {
    RopaTemplate.belongsTo(models.Customer, {
      foreignKey: 'customer_id'
    });
  };

  return RopaTemplate;
};
