module.exports = (sequelize, DataTypes) => {
    const TicketDocument = sequelize.define('TicketDocument', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        url: {
            type: DataTypes.STRING,
            allowNull: false
        },
        original_name: {
            type: DataTypes.STRING,
            allowNull: true
        },
        ticket_id: {
            type: DataTypes.INTEGER,
            allowNull: false
            // references: {
            //     model: 'Policy',
            //     key: 'id'
            // }
        }
    }, {
        tableName: 'TicketDocuments',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['ticket_id']
            }
        ]

    });
    TicketDocument.associate = function (models) {
        TicketDocument.belongsTo(models.Ticket, { foreignKey: 'ticket_id', constraints: false  });
    }

    return TicketDocument;
};