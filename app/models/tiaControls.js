module.exports = (sequelize, DataTypes) => {
    const tiaControls = sequelize.define('tiaControls', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        title: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        parent_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        artifact_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table'),
            allowNull: true
        },
        is_attachment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
            default: false
        },
        question: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        extra_input: {
            type: DataTypes.BOOLEAN,
            allowNull: true
        },
        extra_input_type: {
            type: DataTypes.ENUM('select', 'radio', 'textarea', 'input', 'checkbox', 'table', 'attachment', 'dynamic'),
            allowNull: true
        },
        extra_input_fields: {
            type: DataTypes.ARRAY(DataTypes.JSON),
            allowNull: true
        },
        endpoint: {
            type: DataTypes.STRING,
            allowNull: true
        },
        industry_vertical_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },

    }, {
        tableName: 'tia_controls',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_id']
            }
        ]
    });

    tiaControls.associate = function (models) {
        tiaControls.belongsTo(models.tiaCategory, { foreignKey: 'category_id', constraints: false  });
        tiaControls.hasMany(models.tiaCustomerControls, { foreignKey: 'question_id', constraints: false  });
        tiaControls.belongsTo(models.tiaControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        tiaControls.hasMany(models.tiaControls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
    };

    return tiaControls;
};