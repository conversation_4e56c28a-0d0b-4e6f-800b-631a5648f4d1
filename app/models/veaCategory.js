module.exports = (sequelize, DataTypes) => {
    const VeaCategory = sequelize.define('VeaCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        template_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        vea_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
    }, {
        tableName: 'vea_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['vea_level']
            }
        ]
    });

    VeaCategory.associate = function (models) {
        VeaCategory.hasMany(models.VeaControls, { foreignKey: 'category_id', constraints: false  });
        VeaCategory.hasMany(models.VeaCollaborator, { foreignKey: 'category_id', constraints: false  });
    };

    return VeaCategory;
};