module.exports = (sequelize, DataTypes) => {
    const UCFCategory = sequelize.define('UCFCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        category_no: {
            type: DataTypes.STRING,
            allowNull: false
        },
        category_name: {
            type: DataTypes.TEXT,
            allowNull: false
        },
        required_docs: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        
    }, {
        tableName: 'ucf_control_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['category_name']
            }
        ]
    });

    UCFCategory.associate = function (models) {
        UCFCategory.hasMany(models.UCFControl, { foreignKey: 'category_id' });
    };

    return UCFCategory;
}