module.exports = (sequelize, DataTypes) => {
    const DsrRequestType = sequelize.define('DsrRequestType', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        flowtype: {
            type: DataTypes.STRING,
            allowNull: false
        },
        dsr_id: {
            type: DataTypes.STRING,
            allowNull: true
        },    
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        workflow_status: {
            type: DataTypes.ENUM('draft', 'published'),
            defaultValue: "draft",
            allowNull: false
        },
        group_id:{
            type: DataTypes.INTEGER,
            allowNull: true
        }
    }, {
        tableName: 'dsr_request_type',
        timestamps: true,
        paranoid: true
    });

    DsrRequestType.associate = function (models) {
        DsrRequestType.belongsTo(models.User, { foreignKey: 'created_by', constraints: false });
        DsrRequestType.belongsTo(models.Customer, { foreignKey: 'customer_id', constraints: false });
        DsrRequestType.hasMany(models.RequestTypeStages, { foreignKey: 'type_id', constraints: false });
        DsrRequestType.belongsTo(models.Group, { foreignKey: 'group_id', constraints: false });
    };

    return DsrRequestType;
};