module.exports = (sequelize, type) => {
    const UserServices = sequelize.define('UserServices', {
        id: {
            type: type.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        service_id: {
            type: type.INTEGER,
            allowNull: false
        },
        user_id: {
            type: type.INTEGER,
            allowNull: false
        },
    },
        {
            freezeTableName: true,
            tableName: 'user_services',
            timestamps: true,
            paranoid: true
        });
    UserServices.associate = function (models) {
        UserServices.belongsTo(models.Services, {
            foreignKey: 'service_id',
            as: 'services', constraints: false 
        });
        UserServices.belongsTo(models.User, {
            foreignKey: 'user_id',
            as: 'users', constraints: false 
        });
    };
    return UserServices;
};