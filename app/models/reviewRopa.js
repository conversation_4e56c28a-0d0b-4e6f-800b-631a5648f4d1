module.exports = (sequelize, DataTypes) => {
    const ReviewROPA = sequelize.define('ReviewROPA', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_question_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            unique: true
        },
        accurate_information: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        comments: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        reviewer_id: {
            type: DataTypes.INTEGER,
            allowNull: false
        }
    }, {
        tableName: 'review_ropa',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_question_id']
            },
            {
                fields: ['reviewer_id']
            }
        ]
    });

    ReviewROPA.associate = function (models) {
        ReviewROPA.belongsTo(models.User, { foreignKey: 'reviewer_id', constraints: false  });
        ReviewROPA.belongsTo(models.CustomerControls, { foreignKey: 'customer_question_id', constraints: false  });
    };

    return ReviewROPA;
}
