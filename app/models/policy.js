module.exports = (sequelize, type) => {
  const Policy = sequelize.define(
    'Policy',
    {
      id: {
        type: type.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: type.STRING,
        allowNull: false
      },
      description: {
        type: type.STRING,
        allowNull: true
      },
      version_no: {
        type: type.INTEGER,
        allowNull: false,
        defaultValue: 1
      },
      policy_id: {
        type: type.STRING,
        allowNull: true
      },
      status: {
        type: type.ENUM('CREATION_OF_POLICY', 'APPROVAL_OF_POLICY', 'REVIEW_OF_POLICY', 'POLICY_IN_USE'),
        allowNull: false,
        defaultValue: 'CREATION_OF_POLICY'
      },
      recurrence: {
        type: type.ENUM('ANNUALLY', 'BI_ANNUALLY', 'QUATERLY', 'MONTHLY', 'NEVER'),
        allowNull: false,
        defaultValue: 'ANNUALLY'
      },
      language: {
        type: type.STRING,
        allowNull: false,
        defaultValue: 'ENGLISH (US)'
      },
      reviewer_id: {
        type: type.ARRAY(type.INTEGER),
        allowNull: true
      },
      approver_id: {
        type: type.INTEGER,
        allowNull: true
      },
      author_id: {
        type: type.INTEGER,
        allowNull: true
      },
      collaborator_id: {
        type: type.INTEGER,
        allowNull: true
      },
      review_date: {
        type: type.DATE,
        allowNull: true
      },
      renewal_date: {
        type: type.DATE,
        allowNull: true
      },
      tentative_date: {
        type: type.DATE,
        allowNull: true
      },
      effective_date: {
        type: type.DATE,
        allowNull: true
      },
      entity_id: {
        type: type.INTEGER,
        allowNull: true
      },
      department_id: {
        type: type.INTEGER,
        allowNull: true
      },
      category_id: {
        type: type.INTEGER,
        allowNull: true
      },
      relevant_law_id: { // associated with regulationsV2
        type: type.ARRAY(type.INTEGER),
        allowNull: true
      },
      result: {
        type: type.ENUM('REJECTED', 'APPROVED', 'PENDING'),
        allowNull: false,
        defaultValue: 'PENDING' // Assuming a default status of 'not uploaded'
      },
      customer_id: {
        type: type.INTEGER,
        allowNull: true
      },
      is_direct_upload: {
        type: type.BOOLEAN,
        allowNull: true
      },
      is_retention_policy: {
        type: type.BOOLEAN,
        allowNull: true,
        defaultValue: false
      },
      retention_period: {
        type: type.INTEGER,
        allowNull: true
      },
      retention_policy_type: {
        type: type.ENUM('LEGAL_OBLIGATION_BASED_RETENTION', 'SPECIFIC_PURPOSE_BASED_RETENTION', 'CONSENT_DRIVEN_RETENTION', 'UNDEFINED'),
        allowNull: true,
        defaultValue: 'UNDEFINED'
      }
    },
    {
      tableName: 'policy',
      freezeTableName: true,
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['customer_id']
        },
        {
          fields: ['department_id']
        },
        {
          fields: ['category_id']
        },
        {
          fields: ['entity_id']
        },
        {
          fields: ['author_id']
        },
        {
          fields: ['approver_id']
        },
        {
          fields: ['reviewer_id']
        },
        {
          fields: ['name']
        },
        {
          fields: ['collaborator_id']
        }
      ]
    }
  );
  Policy.associate = function (models) {
    Policy.belongsTo(models.Customer, { foreignKey: 'customer_id' });
    Policy.belongsTo(models.Departments, { foreignKey: 'department_id' });
    Policy.hasMany(models.RegulationsV2, { foreignKey: 'relevant_law_id' });
    Policy.belongsTo(models.PolicyCategory, { foreignKey: 'category_id' });
    Policy.belongsTo(models.Group, { foreignKey: 'entity_id' });
    Policy.belongsTo(models.User, { foreignKey: 'author_id', as: 'Author' });
    Policy.belongsTo(models.User, { foreignKey: 'collaborator_id', as: 'Collaborator' });
    Policy.belongsTo(models.User, { foreignKey: 'approver_id', as: 'Approver' });
    Policy.belongsToMany(models.User, { through: 'PolicyReviewers', foreignKey: 'policy_id' });
    Policy.hasMany(models.PolicyDocument, { foreignKey: 'policyId' });
    Policy.hasMany(models.CustomerBusinessRequirements, { foreignKey: 'document_id', sourceKey: 'id' });
    // Policy.hasMany(models.AuditLog, { foreignKey: 'type_id' });
  };
  return Policy;
};
