module.exports = (sequelize, DataTypes) => {
    const RegulationComplianceStatus = sequelize.define('RegulationComplianceStatus', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        entity_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        regulation_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        compliant_percentage: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        partial_compliant_percentage: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        non_compliant_percentage: {
            type: DataTypes.FLOAT,
            allowNull: true
        },
        

    }, {
        tableName: 'regualtion_compliance_status',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['customer_id']
            },
            {
                fields: ['entity_id']
            },
            {
                fields: ['regulation_id']
            }
        ]
    });

    RegulationComplianceStatus.associate = function (models) {
        RegulationComplianceStatus.belongsTo(models.RegulationsV2, { foreignKey: 'regulation_id', targetKey: 'id' });
        // CustomerControls.belongsTo(models.Category, { foreignKey: 'category_id', constraints: false  });
        // CustomerControls.belongsTo(models.ROPA, { foreignKey: 'ropa_id', constraints: false  });
        // CustomerControls.hasOne(models.Answers, { foreignKey: 'customer_question_id', onDelete: "CASCADE",constraints: false  });
        // CustomerControls.belongsTo(models.CustomerControls, { as: 'Parent', foreignKey: 'parent_id', constraints: false  });
        // CustomerControls.hasMany(models.CustomerControls, { as: 'Children', foreignKey: 'parent_id', constraints: false  });
        // CustomerControls.hasOne(models.ReviewROPA, { foreignKey: 'customer_question_id', constraints: false  })
    };

    return RegulationComplianceStatus;
}