module.exports = (sequelize, DataTypes) => {
    const pdaCategory = sequelize.define('pdaCategory', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        pda_level: {
            type: DataTypes.ENUM('Department', 'Process', 'Vendor'),
            allowNull: false
        },
    }, {
        tableName: 'pda_category',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                fields: ['pda_level']
            }
        ]
    });

    pdaCategory.associate = function (models) {
        pdaCategory.hasMany(models.pdaControls, { foreignKey: 'category_id', constraints: false  });
    };

    return pdaCategory;
};