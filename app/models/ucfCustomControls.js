module.exports = (sequelize, DataTypes) => {
  const UCFCustomControl = sequelize.define(
    'UCFCustomControl',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      control_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      category_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      control_no: {
        type: DataTypes.STRING,
        allowNull: true
      },
      control_description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      in_scope_regulations: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      }
    },
    {
      tableName: 'ucf_custom_control',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['category_id']
        }
      ]
    }
  );

  UCFCustomControl.associate = function (models) {
    UCFCustomControl.belongsTo(models.UCFCustomCategory, { foreignKey: 'category_id', targetKey: 'id' });
    UCFCustomControl.hasMany(models.UCFBusinessRequirement, { foreignKey: 'control_id' });
    UCFCustomControl.belongsTo(models.UCFControl, { foreignKey: 'control_id', targetKey: 'id' });
  };

  return UCFCustomControl;
};
