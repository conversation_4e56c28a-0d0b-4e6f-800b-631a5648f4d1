const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const authService = require('../services/auth');
const roleService = require('../services/role');
const { USER_ROLE } = require('../constant/common');
const { Op } = require('sequelize');
const sequelize = require('sequelize');
const constants = require('../constant/common');

exports.getOrgRoles = async (req, res) => {
  try {
    const { Role, User } = db.models;
    let { page, size, customer_id, search, sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date } = req.query;
    const { limit, offset } = getPagination(page, size);

    let orgCondition = { customer_id: customer_id };
    if (search) {
      orgCondition[Op.or] = [{ role_name: { [Op.iLike]: `%${search}%` } }];
    }
    // Add date range condition
    if (start_date && end_date) {
      start_date = new Date(start_date);
      start_date.setHours(0, 0, 0, 0);
      start_date = start_date.toISOString();

      end_date = new Date(end_date);
      end_date.setHours(23, 59, 32, 312);
      end_date = end_date.toISOString();

      orgCondition.createdAt = {
        [Op.between]: [start_date, end_date]
      };
    }
    if (!start_date && end_date) {
      end_date = new Date(end_date);
      end_date.setHours(23, 59, 32, 312);
      end_date = end_date.toISOString();

      orgCondition.createdAt = {
        [Op.lte]: end_date // Less than end date
      };
    }
    if (start_date && !end_date) {
      start_date = new Date(start_date);
      start_date.setHours(0, 0, 0, 0);
      start_date = start_date.toISOString();

      orgCondition.createdAt = {
        [Op.gte]: start_date // Greater than start date
      };
    }

    const order = [[sort_by, sort_order]];
    const roleCondition = {
      ...orgCondition,
      role_name: {
        [Op.notIn]: [constants.USER_ROLE[8], constants.USER_ROLE[2]]
      }
    };

    // get Role Listing
    // const roleList = await commonService.getList(Role, orgCondition, {}, limit, offset, order);
    const roleList = await roleService.getRolesWithCreators(Role, User, roleCondition, limit, offset, order);
    // get role id
    const roleId = roleList?.rows?.map(items => {
      return items.id;
    });

    // get user count
    const userCount = await commonService.getListWithGroup(User, { role_id: { [Op.in]: roleId }, customer_id }, ['role_id', [sequelize.fn('COUNT', sequelize.col('id')), 'user_count']]);

    let userCountMap;
    if (userCount) {
      // add userCount to map
      userCountMap = userCount?.reduce((map, count) => {
        map[count.role_id] = count.user_count;
        return map;
      }, {});
    }

    // Add user count to each role in roleList
    roleList?.rows?.forEach(role => {
      role.total_user = Number(userCountMap[role.id]) || 0;
    });

    // if sort by total count then sort by total user count
    // if (sort_by == 'total_user') {
    //     roleList.rows.sort((a, b) => {
    //         if (sort_order == 'ASC') {
    //             return a.total_user - b.total_user;
    //         }
    //         else {
    //             return b.total_user - a.total_user;
    //         }
    //     });
    // }

    return response.success(req, res, { msgCode: 'ROLE_FETCHED', data: roleList }, httpStatus.OK);
  } catch (error) {
    console.log('getOrgRoles', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getRoleUsers = async (req, res) => {
  try {
    const { Role, User } = db.models;
    let { page, size, role_id, search, sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date } = req.query;
    const { limit, offset } = getPagination(page, size);

    let roleCondition = { role_id: role_id };
    if (search) {
      roleCondition[Op.or] = [{ firstName: { [Op.iLike]: `%${search}%` } }, { lastName: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }];
    }
    // Add date range condition
    if (start_date && end_date) {
      start_date = new Date(start_date);
      start_date.setHours(0, 0, 0, 0);
      start_date = start_date.toISOString();

      end_date = new Date(end_date);
      end_date.setHours(23, 59, 32, 312);
      end_date = end_date.toISOString();

      roleCondition.createdAt = {
        [Op.between]: [start_date, end_date]
      };
    }
    if (!start_date && end_date) {
      end_date = new Date(end_date);
      end_date.setHours(23, 59, 32, 312);
      end_date = end_date.toISOString();

      roleCondition.createdAt = {
        [Op.lte]: end_date // Less than end date
      };
    }
    if (start_date && !end_date) {
      start_date = new Date(start_date);
      start_date.setHours(0, 0, 0, 0);
      start_date = start_date.toISOString();

      roleCondition.createdAt = {
        [Op.gte]: start_date // Greater than start date
      };
    }

    const order = [[sort_by, sort_order]];

    const selectedAttributes = ['id', 'firstName', 'lastName', 'email', 'role_id', 'createdAt', 'status'];
    // // get Users for Particular Role Listing
    // const userList = await commonService.getList(User, roleCondition , {}, limit, offset, order);
    const userList = await commonService.getListAssociateWithCount(User, Role, roleCondition, {}, selectedAttributes, ['role_name'], limit, offset, order);

    return response.success(req, res, { msgCode: 'USER_FETCHED', data: userList }, httpStatus.OK);
  } catch (error) {
    console.log('getRoleUsers', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.assignRole = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Role, User } = db.models;
    const { check, uncheck } = req.body;
    const roleIdToAssign = req.body.role_id; // Role ID to assign (check)
    // getting role id of Basic
    const customerId = await commonService.findByCondition(User, { id: req.data.userId }, ['customer_id']);
    const basicId = await commonService.findByCondition(Role, { customer_id: customerId.customer_id, role_name: USER_ROLE[8] }); // Role ID to assign (uncheck)
    const basicRoleIdToAssign = basicId.id;

    // Perform role assignment (check) using batch update
    if (check.length > 0) {
      // await User.update({ roleId: roleIdToAssign }, { where: { id: { [Op.in]: check } } });
      const list = await commonService.updateData(User, { role_id: roleIdToAssign }, { id: { [Op.in]: check } }, dbTrans);
    }

    // Perform role unassignment (uncheck) using batch update
    if (uncheck.length > 0) {
      // await User.update({ roleId: basicRoleIdToAssign }, { where: { id: { [Op.in]: uncheck } } }); // assign basic role
      await commonService.updateData(User, { role_id: basicRoleIdToAssign }, { id: { [Op.in]: uncheck } }, dbTrans);
    }
    // Send success response
    //   res.status(200).json({ message: 'Role assignment and unassignment completed successfully' });
    return response.success(req, res, { msgCode: 'ASSIGNED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('getRoleUsers', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getRoleDetails = async (req, res) => {
  try {
    const { Role, UserRolePrivileges, Resources, User } = db.models;

    // get role details

    // const userName = await commonService.getListAssociateWithoutCount(Role , User , {id: req.params.id} , {User.id : Role})
    let roleDetails = await roleService.getRoleDetails(Role, UserRolePrivileges, Resources, { id: req.params.id });
    const userName = await commonService.findByCondition(User, { id: roleDetails.created_by }, ['firstName', 'lastName']);

    const resources = {};
    const result = roleDetails.UserRolePrivileges;

    result?.forEach(({ Resource }) => {
      resources[Resource.resource_id] = { ...Resource, children: [] };
    });

    Object.values(resources)?.forEach(resource => {
      if (resource.parent_id) {
        resources[resource.parent_id]?.children.push(resource);
      }
    });

    Object.values(resources)?.forEach(resource => {
      resource.children.sort((a, b) => a.order - b.order);
    });

    // Filter out the child nodes from the top level
    const tree = Object.values(resources)?.filter(resource => !resource.parent_id);

    tree.sort((a, b) => a.order - b.order);

    roleDetails.created_by = userName;
    roleDetails.resources = tree;
    delete roleDetails.UserRolePrivileges;

    if (!roleDetails) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    return response.success(req, res, { msgCode: 'ROLE_FETCHED', data: roleDetails }, httpStatus.OK);
  } catch (error) {
    console.log('getRoleDetails', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.updateRole = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Role, UserRolePrivileges } = db.models;
    req.body.created_by = req.data.userId;

    //checking role  already exists or not for a particular customer
    const checkRole = await commonService.findByCondition(Role, { role_name: req.body.role_name, customer_id: req.data.customer_id }, ['id']);
    if (checkRole && checkRole.id != req.params.id) {
      return response.error(req, res, { msgCode: 'ROLE_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (req.body.role_name === constants.USER_ROLE[2] || req.body.role_name === constants.USER_ROLE[8]) {
      return response.error(req, res, { msgCode: 'ROLE_UPDATE_NOT_ALLOWED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // update Role
    let updateRole = await commonService.updateData(Role, req.body, { id: req.params.id }, dbTrans);
    if (!updateRole[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (req.body?.resource_list) {
      let deleteRoleId = [],
        userRolePrivilegesData = [];

      req.body?.resource_list?.forEach(element => {
        if (element.type == 'delete') {
          deleteRoleId.push(element.resource_id);
        } else if (element.type == 'add') {
          userRolePrivilegesData.push({
            resource_id: element.resource_id,
            role_id: req.params.id
          });
        }
      });

      if (deleteRoleId.length > 0) {
        // delete User Role
        const deleteUserRoleData = {
          resource_id: {
            [Op.in]: deleteRoleId
          },
          role_id: req.params.id
        };
        const deleteUserRole = await commonService.deleteQuery(UserRolePrivileges, deleteUserRoleData, dbTrans);
        if (!deleteUserRole) {
          return response.error(req, res, { msgCode: 'ERROR_RESOURCE_DELETE' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }

      if (userRolePrivilegesData.length > 0) {
        // insert user role privileges
        const userRolePrivileges = await authService.BulkData(UserRolePrivileges, userRolePrivilegesData, dbTrans);
        if (!userRolePrivileges) {
          return response.error(req, res, { msgCode: 'ERROR_RESOURCE_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        updateRole[1].user_role_priviledges = userRolePrivileges;
      }
    }

    return response.success(req, res, { msgCode: 'ROLE_UPDATED', data: updateRole[1] }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('updateRole', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createRole = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Role, UserRolePrivileges } = db.models;
    req.body.created_by = req.data.userId;

    if (req.body.role_name === USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'DPO_ROLE_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //checking role  already exists or not for a particular customer
    const checkRole = await commonService.findByCondition(Role, { role_name: req.body.role_name, customer_id: req.body.customer_id }, ['id']);
    if (checkRole) {
      return response.error(req, res, { msgCode: 'ROLE_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // create Role
    let createRole = await commonService.addDetail(Role, req.body, dbTrans);
    if (!createRole) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let resourcesData = [];
    req.body?.resource_list?.forEach(items => {
      resourcesData.push({
        role_id: createRole.id,
        resource_id: items
      });
    });

    // create user role priviledges
    const createUserResouce = await authService.BulkData(UserRolePrivileges, resourcesData, dbTrans);
    if (!createUserResouce) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    createRole.user_role_priviledges = createUserResouce;

    return response.success(req, res, { msgCode: 'USER_FETCHED', data: createRole }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('createRole', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
