const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const sequelize = require('sequelize');
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const { Op } = require('sequelize');
const authService = require('../services/auth');
const { USER_ROLE } = require('../constant/common');
const { createTree, buildHierarchy, createGroupTree } = require('../utils/helper');
const groupService = require('../services/group');
const { custom } = require('joi');

exports.getOrgGroup = async (req, res) => {
  try {
    const { Group, GroupUser } = db.models;
    let { customer_id, search, sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date } = req.query;

    //implementing searching and sorting  in the query itself to reduce complexity of code
    let groupCondition = { customer_id: customer_id };
    if (search) {
      groupCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
    }

    // Add date range condition
    if (start_date && end_date) {
      groupCondition.createdAt = {
        [Op.between]: [start_date, end_date]
      };
    }
    if (!start_date && end_date) {
      groupCondition.createdAt = {
        [Op.lte]: end_date // Less than end date
      };
    }
    if (start_date && !end_date) {
      // end_date= new Date();   // Default is current time
      groupCondition.createdAt = {
        [Op.gte]: start_date // Greater than start date
      };
    }

    const order = [[sort_by, sort_order]];

    // get Group Listing
    // const groupList = await groupService.getListAssociateWithCount(Group, Group, {customer_id:1}, {}, {}, {}, limit, offset, order);
    const getGroup = await commonService.getListWithoutCount1(Group, groupCondition, {}, undefined, undefined, order);

    //         const getGroupQuery = `SELECT g.id AS group_id,
    //         g.name,
    //         g.user_id,
    //         (u."firstName" || ' ' || u."lastName") AS created_by,
    //         g.parent_id,
    //         g.customer_id
    //  FROM "group" g
    //  LEFT JOIN "users" u ON g.user_id = u.id
    //  WHERE g.name != ?
    //    AND g.customer_id = ?

    //  UNION

    //  SELECT g2.id AS group_id,
    //         g2.name,
    //         g2.user_id,
    //         (u2."firstName" || ' ' || u2."lastName") AS created_by,
    //         g2.parent_id,
    //         g2.customer_id
    //  FROM "group" g1
    //  JOIN "group" g2 ON g1.id = g2.parent_id
    //  LEFT JOIN "users" u2 ON g2.user_id = u2.id
    //  WHERE g1.name != ?
    //    AND g1.customer_id = ?

    //  UNION

    //  SELECT g3.id AS group_id,
    //         g3.name,
    //         g3.user_id,
    //         (u3."firstName" || ' ' || u3."lastName") AS created_by,
    //         g3.parent_id,
    //         g3.customer_id
    //  FROM "group" g1
    //  JOIN "group" g2 ON g1.id = g2.parent_id
    //  JOIN "group" g3 ON g2.id = g3.parent_id
    //  LEFT JOIN "users" u3 ON g3.user_id = u3.id
    //  WHERE g1.name != ?
    //    AND g1.customer_id = ?`;
    //         const getGroupData = ['Unassigned Assets', customer_id, 'Unassigned Assets', customer_id, 'Unassigned Assets', customer_id]
    //         const getGroup = await authService.GetData(getGroupQuery, getGroupData);

    //         console.log('nono');
    //         if (getGroup.length <= 0) {
    //             console.log('good');
    //             return response.success(req, res, { msgCode: "GROUP_FETCHED", data: getGroup }, httpStatus.OK);
    //         }

    const groupId = getGroup?.map(items => {
      return items.id;
    });

    const groupUser = await commonService.getListWithoutCount1(GroupUser, { group_id: { [Op.in]: groupId } });

    // get total user count under each group
    getGroup?.forEach(group => {
      group.user_count = groupUser?.filter(item => item.group_id === group.id).length;
    });

    // create tree
    const groupTreeData = createGroupTree(getGroup);
    if (groupTreeData.length > 0) {
      groupTreeData[0].parent_id = null;
    }
    return response.success(req, res, { msgCode: 'GROUP_FETCHED', data: groupTreeData }, httpStatus.OK);
  } catch (error) {
    console.log('getOrgGroup', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getCompanyStructure = async (req, res) => {
  try {
    const { Group, GroupUser, User, Departments, Processes } = db.models;
    const { customer_id, search, sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date, page, size } = req.query;

    const { limit, offset } = getPagination(page, size);

    // implementing searching and sorting  in the query itself to reduce complexity of code
    let groupCondition = { customer_id: customer_id },
      departmentCondition = { customer_id: customer_id },
      processCondition = { customer_id: customer_id };
    if (search) {
      groupCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
      departmentCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
      processCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
    }

    // Add date range condition
    if (start_date && end_date) {
      groupCondition.updatedAt = {
        [Op.between]: [start_date, end_date]
      };
      departmentCondition.updatedAt = {
        [Op.between]: [start_date, end_date]
      };
      processCondition.updatedAt = {
        [Op.between]: [start_date, end_date]
      };
    }
    if (!start_date && end_date) {
      groupCondition.updatedAt = {
        [Op.lte]: end_date // Less than end date
      };
      departmentCondition.updatedAt = {
        [Op.lte]: end_date // Less than end date
      };
      processCondition.updatedAt = {
        [Op.lte]: end_date // Less than end date
      };
    }
    if (start_date && !end_date) {
      // end_date= new Date();   // Default is current time
      groupCondition.updatedAt = {
        [Op.gte]: start_date // Greater than start date
      };
      departmentCondition.updatedAt = {
        [Op.gte]: start_date // Greater than start date
      };
      processCondition.updatedAt = {
        [Op.gte]: start_date // Greater than start date
      };
    }

    const groupAttributes = {
      include: [[sequelize.fn('COUNT', sequelize.col('GroupUsers.id')), 'total_users']]
    };
    const getGroup = await groupService.getBusinessUnitData(Group, GroupUser, groupCondition, {}, groupAttributes, [], limit, offset);

    let groupUserId = [];
    const groupId = [];
    getGroup?.forEach(items => {
      groupId.push(items.id);
      items.type = 'Group';
      groupUserId.push(items.spoc_id);
      items.children = [];
    });

    // get user data
    const getGroupUserData = await commonService.getListWithoutCount1(User, { id: { [Op.in]: groupUserId } }, ['id', 'firstName', 'lastName', 'profile_image']);

    getGroup?.forEach(items => {
      const userData = getGroupUserData?.find(u => u.id == items.spoc_id);
      items.user_data = userData ? userData : {};
    });

    departmentCondition.group_id = { [Op.in]: groupId };
    const getDepartment = await commonService.getListWithoutCount1(Departments, departmentCondition);
    const departmentId = [];
    let departmentUserId = [];
    getDepartment?.forEach(items => {
      departmentId.push(items.id);
      items.type = 'Department';
      departmentUserId.push(items.spoc_id);
      items.children = [];
    });

    // get user data
    const getDepartmentUserData = await commonService.getListWithoutCount1(User, { id: { [Op.in]: departmentUserId } }, ['id', 'firstName', 'lastName', 'profile_image']);

    getDepartment?.forEach(items => {
      const userData = getDepartmentUserData.find(u => u.id == items.spoc_id);
      items.user_data = userData ? userData : {};
    });

    processCondition.department_id = { [Op.in]: departmentId };
    if (departmentId.length > 0) {
      const getProcesses = await commonService.getListWithoutCount1(Processes, processCondition);

      let processUserid = [];
      getProcesses?.forEach(items => {
        items.type = 'Processes';
        processUserid.push(items.spoc_id);
        items.children = [];
      });

      // get user data
      const getProcessUserData = await commonService.getListWithoutCount1(User, { id: { [Op.in]: processUserid } }, ['id', 'firstName', 'lastName', 'profile_image']);
      getProcesses?.forEach(items => {
        const userData = getProcessUserData?.find(u => u.id == items.spoc_id);
        items.user_data = userData ? userData : {};
      });

      // create Processes tree
      const processesTree = createTree(getProcesses);

      if (processesTree.length > 0) {
        processesTree[0].parent_id = null;
      }

      // Create a hashmap to store departmentTree data based on group_id
      const processTreeMap = {};
      processesTree?.forEach(item => {
        const departmentId = item.department_id;
        if (!processTreeMap[departmentId]) {
          processTreeMap[departmentId] = [];
        }
        processTreeMap[departmentId].push(item);
      });
      // Update getGroup items with departmentTree data
      getDepartment?.forEach(items => {
        const assigneeData = processTreeMap[items.id] || [];
        items.children = items.children.concat(assigneeData);
      });
    }

    // create department tree
    const departmentTree = createTree(getDepartment);
    if (departmentTree.length > 0) {
      departmentTree[0].parent_id = null;
    }

    // Create a hashmap to store departmentTree data based on group_id
    const departmentTreeMap = {};
    departmentTree?.forEach(item => {
      const groupId = item.group_id;
      if (!departmentTreeMap[groupId]) {
        departmentTreeMap[groupId] = [];
      }
      departmentTreeMap[groupId].push(item);
    });

    // Update getGroup items with departmentTree data
    getGroup?.forEach(items => {
      const assigneeData = departmentTreeMap[items.id] || [];
      items.children = items.children.concat(assigneeData);
    });

    // create department tree
    const groupTree = createTree(getGroup);
    if (groupTree.length > 0) {
      groupTree[0].parent_id = null;
    }

    return response.success(req, res, { msgCode: 'GROUP_FETCHED', data: groupTree }, httpStatus.OK);
  } catch (error) {
    console.log('getOrgGroup', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.createGroup = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Group, GroupUser } = db.models;

    // create Group
    const createGroup = await commonService.addDetail(Group, req.body, dbTrans);
    if (!createGroup) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (req.body.spoc_id) {
      const groupUserData = await commonService.findByCondition(GroupUser, { group_id: id, user_id: req.body.spoc_id }, ['id', 'user_id']);
      if (!groupUserData) {
        const groupUser = await commonService.addDetail(GroupUser, { group_id: id, user_id: req.body.spoc_id }, dbTrans);
        if (!groupUser) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_GROUPUSER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    return response.success(req, res, { msgCode: 'USER_FETCHED', data: createGroup }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('createUser', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.editGroup = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Group, GroupUser } = db.models;
    const { id } = req.params;

    // update Group
    const updateGroup = await commonService.updateData(Group, req.body, { id: id }, dbTrans);
    if (!updateGroup) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (req.body.spoc_id) {
      const groupUserData = await commonService.findByCondition(GroupUser, { group_id: id, user_id: req.body.spoc_id }, ['id', 'user_id']);
      if (!groupUserData) {
        const groupUser = await commonService.addDetail(GroupUser, { group_id: id, user_id: req.body.spoc_id }, dbTrans);
        if (!groupUser) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_GROUPUSER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    return response.success(req, res, { msgCode: 'GROUP_UPDATED', data: {} }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('updateGroup', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createBusinessUnit = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Group, GroupUser, UCFControl, UCFBusinessRequirement, CustomerBusinessRequirements, CustomerRegulations, UCFCustomControl } = db.models;
    // Create business Unit
    const check = await commonService.findByCondition(Group, { name: req.body.name, customer_id: req.body.customer_id });
    if (check) {
      return response.error(req, res, { msgCode: 'GROUP_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const newGroup = await commonService.addDetail(Group, req.body, dbTrans);
    if (!newGroup) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_GROUP' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // adding entry to the Group user Table
    if (newGroup.spoc_id) {
      req.body.group_id = newGroup.id;
      req.body.user_id = newGroup.spoc_id;
      const updateGroupUser = await commonService.addDetail(GroupUser, req.body, dbTrans);
      if (!updateGroupUser) {
        return response.error(req, res, { msgCode: 'ERROR_UPDATING_GROUPUSER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    if (req?.body?.regulation_ids) {
      const regulationData = {
        customer_id: req.data.customer_id,
        entity_id: newGroup.id,
        regulation_ids: req.body.regulation_ids
      };
      const addRegulation = await commonService.addDetail(CustomerRegulations, regulationData, dbTrans);
      if (!addRegulation) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      const data = await commonService.getListWith3Models(UCFCustomControl, UCFControl, UCFBusinessRequirement, { customer_id: req.body.customer_id }, {}, {}, {}, ['id'], ['id']);
      if (!data) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      const customData = req.body.regulation_ids.flatMap(regulation_id =>
        data.flatMap(control => {
          if (control.control_id) {
            return control.UCFControl.UCFBusinessRequirements.map(busiReq => ({
              customer_id: req.data.customer_id,
              control_id: control.id,
              busi_req_id: busiReq.id,
              group_id: newGroup.id,
              regulation_id: regulation_id
            }));
          } else {
            return [];
          }
        })
      );

      const customRegulations = await commonService.getList(CustomerRegulations, { customer_id: req.data.customer_id }, {}, null, null, [['id', 'ASC']]);

      // console.log("-------",customRegulations)

      const getcustomData = await commonService.getList(
        CustomerBusinessRequirements,
        { customer_id: req.body.customer_id, group_id: customRegulations.rows[0].entity_id, regulation_id: customRegulations.rows[0].regulation_ids[0], is_custom: true },
        {}
      );

      if (getcustomData.rows[0]) {
        req.body.regulation_ids.forEach(regulation_id => {
          getcustomData.rows.forEach(data => {
            customData.push({
              customer_id: req.data.customer_id,
              control_id: data.control_id,
              busi_req_id: null,
              group_id: newGroup.id,
              regulation_id: regulation_id,
              reference_no: data.reference_no,
              business_requirement: data.business_requirement,
              articles: null,
              is_custom: true
            });
          });
        });
      }

      // console.log(customData[0]);
      const addData = await commonService.bulkAdd(CustomerBusinessRequirements, customData, dbTrans);
      if (!addData) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    return response.success(req, res, { msgCode: 'GROUP_CREATED', data: newGroup }, httpStatus.CREATED, dbTrans);
  } catch (error) {
    console.error('Error creating GROUP', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.deleteBusinessUnit = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Departments, Group, GroupUser, CustomerRegulations, CustomerBusinessRequirements } = db.models;
    const group_id = req.params.id;

    // check if group exists
    const checkGroup = await commonService.findByCondition(Group, { id: group_id });
    if(!checkGroup){
      return response.error(req, res, { msgCode: 'GROUP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Check for dependencies - don't delete if department(s) exist
    const checkDepartments = await commonService.findByCondition(Departments, { group_id: group_id });
    if(checkDepartments){
      return response.error(req, res, { msgCode: 'CANNOT_DELETE_GROUP' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Check for dependencies - don't delete if sub-group exist
    const checkParent = await commonService.findByCondition(Group, { parent_id: group_id });
    if(checkParent){
      return response.error(req, res, { msgCode: 'CANNOT_DELETE_GROUP' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Deleting associated data in order.
    // 1. deleting data from GroupUser table
    if(checkGroup.spoc_id){
      const deleteGroupUser = await commonService.deleteQuery(GroupUser, { group_id: checkGroup.id, user_id: checkGroup.spoc_id }, dbTrans);
      if(!deleteGroupUser){
        return response.error(req, res, { msgCode: 'ERROR_DELETING_GROUP_USER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    // 2. deleting data from CustomerRegulations table
    if(checkGroup.regulation_ids && checkGroup.regulation_ids.length){

      const regulationCondition = {
        customer_id: req.data.customer_id,
        entity_id: checkGroup.id,
        regulation_ids: checkGroup.regulation_ids
      };
      const deleteCustomerRegulations = await commonService.deleteQuery(CustomerRegulations, regulationCondition, dbTrans);
      if(!deleteCustomerRegulations){
        return response.error(req, res, { msgCode: 'ERROR_DELETING_CUSTOMER_REGULATIONS' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      // 3. deleting data from CustomerBusinessRequirements table
      const deleteCustomerBusinessRequirements = await commonService.deleteQuery(CustomerBusinessRequirements, { group_id: checkGroup.id, customer_id: req.data.customer_id }, dbTrans);
      if(!deleteCustomerBusinessRequirements){
        return response.error(req, res, { msgCode: 'ERROR_DELETING_CUSTOMER_BUSINESS_REQUIREMENTS' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }  

    // Finally delete the group itself
    const deleteBusinessUnit = await commonService.deleteQuery(Group, { id: group_id }, dbTrans);
    if(!deleteBusinessUnit){
      return response.error(req, res, { msgCode: 'GROUP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'GROUP_DELETED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log("group.js (app/controllers): deleteBusinessUnit(): error:  ", error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

// exports.getEntity = async (req, res) => {
//     try {
//         const { Group , Departments} = db.models;
//         const { id } = req.params;
//         const { search } = req.query;

//         // const groupDetail = await commonService.findByCondition(Group ,{customer_id: id} ,['id' , 'name'])
//

//         let entityCondition = { customer_id: id, parent_id: { [Op.not]: null } };

//         console.log(req.data);
//         if (search) {
//             entityCondition[Op.or] = [
//                 { name: { [Op.iLike]: `%${search}%` } }
//             ];
//         }
//      // Checking group exist or not
//         const check = await commonService.findByCondition(Group, { customer_id: id });
//         if (!check) {
//             return response.error(req, res, { msgCode: 'GROUP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
//         }

//          const entityData = await commonService.getList(Group, entityCondition);

//         return response.success(req, res, { msgCode: "ENTITY_FETCHED", data: entityData }, httpStatus.OK);

//     } catch (error) {
//         console.error('Error getting Entity list ', error);
//         response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
//     }
// }
exports.getEntity = async (req, res) => {
  try {
    const { Group, GroupUser, Role } = db.models;
    const { id } = req.params;
    const { search } = req.query;

    // const groupDetail = await commonService.findByCondition(Group ,{customer_id: id} ,['id' , 'name'])
    // Checking group exist or not
    const check = await commonService.findByCondition(Group, { customer_id: id });
    if (!check) {
      return response.error(req, res, { msgCode: 'GROUP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
    }

    let entityData;
    const role = await commonService.findByCondition(Role, { id: req.data.roleId }, ['role_name']);
    // checking the role is Data Protection Officer or not to show the all entities
    if (role.role_name == USER_ROLE[2]) {
      // let entityCondition = { customer_id: id, parent_id: { [Op.not]: null } };
      let entityCondition = { customer_id: id, name: { [Op.not]: 'Unassigned Assets' } };
      if (search) {
        entityCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
      }
      entityData = await commonService.getList(Group, entityCondition);
    } else {
      // for all other roles we fetch the entity according to their enty in GROUP user table entry
      let entityCondition = { user_id: req.data.userId };
      if (search) {
        entityCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
      }
      entityData = await commonService.getListAssociateWithCount(GroupUser, Group, entityCondition, {}, []);
      // Extract rows from the result object
      const { rows } = entityData;
      // Map over the rows and extract Group objects
      const flattenedRows = rows?.map(({ Group }) => Group);
      // Replace the original rows with the flattenedRows
      entityData.rows = flattenedRows;
    }

    return response.success(req, res, { msgCode: 'ENTITY_FETCHED', data: entityData }, httpStatus.OK);
  } catch (error) {
    console.error('Error getting Entity list ', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
