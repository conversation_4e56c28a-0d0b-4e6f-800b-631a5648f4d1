const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const policyService = require('../services/policy');

const { Op } = require('sequelize');
const authConstant = require('../constant/auth');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');

const { sendMailWithMultipleAttachments } = require('../config/email');
const { sendMail } = require('../config/email');
const { downloadFile } = require('../utils/s3-bucket');
const path = require('path');
const { AuditLog } = db.models;

exports.createPolicy = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy } = db.models;
    // check policy
    const checkPolicy = await commonService.findByCondition(Policy, { customer_id: req.data.customer_id, name: req.body.name }, ['id']);
    if (checkPolicy) {
      return response.error(req, res, { msgCode: 'POLICY_ALREADY_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // create Policy
    req.body.customer_id = req.data.customer_id;

    const createPolicy = await commonService.addDetail(Policy, req.body, dbTrans);
    if (!createPolicy) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_POLICY' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //Entry in Audit Log
    const auditData = {
      type: 'POLICY',
      type_id: createPolicy.id,
      action: `Policy "${req.body.name}" created`,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'POLICY_ADDED', data: createPolicy }, httpStatus.OK, dbTrans);
  } catch (error) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.addCollaborator = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, User } = db.models;
    const { collaborator_id } = req.body;
    const checkPolicy = await commonService.findByCondition(Policy, { id: req.params.policy_id }, ['id', 'name']);
    if (!checkPolicy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const addCollab = await commonService.updateData(Policy, { collaborator_id: collaborator_id }, { id: req.params.policy_id }, dbTrans);
    // console.log(addCollab);
    if (!addCollab[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //audit log
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Collaborator Added to ${checkPolicy.name} by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: req.params.policy_id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'COLLABORATOR_ADDED', data: addCollab[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('add collab', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getPolicyList = async (req, res) => {
  try {
    const { Policy, Departments, PolicyCategory, Group, Role, PolicyDocument } = db.models;
    const { page, size, status, start_date, end_date, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
    let policyCondition = { customer_id: req.data.customer_id };

    // implementation of searching and the filteration of data
    if (search) {
      policyCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
    }
    if (status) {
      policyCondition.status = status;
    }

    if (start_date) {
      policyCondition = {
        review_date: {
          [Op.gte]: req.query.start_date
        }
      };
    }
    if (end_date) {
      policyCondition = {
        review_date: {
          [Op.lte]: req.query.end_date
        }
      };
    }

    if (start_date && end_date) {
      policyCondition = { review_date: { [Op.between]: [start_date, end_date] } };
    }

    const { limit, offset } = getPagination(page, size);

    let order = [[sort_by, sort_order]];
    // Add sorting based on department name and policy category name and also for Group name
    if (sort_by === 'departmentName') {
      order = [[{ model: Departments, as: 'Department' }, 'name', sort_order]];
    } else if (sort_by === 'policyCategoryName') {
      order = [[{ model: PolicyCategory, as: 'policyCategory' }, 'name', sort_order]];
    } else if (sort_by === 'groupName') {
      order = [[{ model: Group, as: 'group' }, 'name', sort_order]];
    }
    // fetching the role name
    const { role_name } = await commonService.findByCondition(Role, { id: req.data.roleId });
    req.data.roleName = role_name;
    // DPO can see all data, so no changes to policyCondition
    //conditon for the Role otheer than DPO i.e. Department head or Employee
    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      // Fetch department ids where user is Department Head
      const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
      if (departments && departments.rows.length > 0) {
        // Department head can only see their own department associated policy and also in which he is Approver, author or reviewer
        const departmentIds = departments?.rows?.map(department => department.id);
        policyCondition.department_id = { [Op.in]: departmentIds };
        policyCondition = {
          [Op.or]: [
            {
              [Op.and]: [{ reviewer_id: { [Op.contains]: [req.data.userId] } }, { status: policyConstant.POLICY_STATUS[1] }]
            },
            {
              [Op.and]: [{ approver_id: req.data.userId }, { status: policyConstant.POLICY_STATUS[2] }]
            },
            {
              [Op.and]: [{ collaborator_id: req.data.userId }, { status: policyConstant.POLICY_STATUS[0] }]
            },
            { author_id: req.data.userId },
            { status: policyConstant.POLICY_STATUS[3], customer_id: req.data.customer_id }
          ]
        };
      } else {
        // Employee can only see their own POLICY in which the are approver ,  reviewer or author or collaborator
        policyCondition = {
          [Op.or]: [
            {
              [Op.and]: [{ reviewer_id: { [Op.contains]: [req.data.userId] } }, { status: policyConstant.POLICY_STATUS[1] }]
            },
            {
              [Op.and]: [{ approver_id: req.data.userId }, { status: policyConstant.POLICY_STATUS[2] }]
            },
            {
              [Op.and]: [{ collaborator_id: req.data.userId }, { status: policyConstant.POLICY_STATUS[0] }]
            },
            { author_id: req.data.userId },
            { status: policyConstant.POLICY_STATUS[3], customer_id: req.data.customer_id }
          ]
        };
      }
    }
    //getting the role based data according to the policyCondition for DPO , HOD , EMP
    // const policyList = await policyService.getListWithMultipleAssociate(Policy, Departments, PolicyCategory, Group, policyCondition, {}, {}, {}, ['id', 'name', 'status', 'recurrence', 'entity_id', 'renewal_date'], ['name'], ['name'], ['name'], limit, offset, order);
    const policyList = await policyService.getListWithMultipleAssociates(
      Policy,
      Departments,
      PolicyCategory,
      Group,
      PolicyDocument,
      policyCondition,
      {},
      {},
      {},
      {},
      ['id', 'name', 'status', 'recurrence', 'entity_id', 'renewal_date'],
      ['name'],
      ['name'],
      ['name'],
      ['id', 'original_name'],
      limit,
      offset,
      order
    );
    if (!policyList) {
      return response.error(req, res, { msgCode: 'ERROR_IN_POLICYLIST' }, httpStatus.NOT_FOUND);
    }

    return response.success(req, res, { msgCode: 'POLICY_FETCHED', data: policyList }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.updatePolicy = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy } = db.models;
    // Check if the policy exists
    const checkPolicy = await commonService.findByCondition(Policy, { id: req.params.id });

    if (!checkPolicy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let checkCondition = {};
    //checking Authorization of User to manippulate the Policy
    if (checkPolicy.status == policyConstant.POLICY_STATUS[3]) {
      return response.error(req, res, { msgCode: 'CANT_UPDATE' }, httpStatus.NOT_FOUND, dbTrans);
    } else if (checkPolicy.status == policyConstant.POLICY_STATUS[0]) {
      checkCondition = {
        [Op.and]: [{ id: req.params.id }, { author_id: req.data.userId }]
      };
    } else {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.NOT_FOUND, dbTrans);
    }

    //checking the policy name is passed to body or not so that UniqueNess of the policy name remains
    if (req.body.name) {
      // check the policy name already  exist or not
      let isPolicyExist = await commonService.findByCondition(Policy, { name: req.body.name, id: { [Op.ne]: req.params.id } });
      if (isPolicyExist) {
        return response.error(req, res, { msgCode: 'POLICY_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    // Update the policy
    const updatedPolicy = await commonService.updateData(Policy, req.body, checkCondition, dbTrans);
    if (!updatedPolicy[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_POLICY' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // maintaining Audit data
    const auditData = [];
    if (checkPolicy.recurrence !== updatedPolicy[1].recurrence) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${checkPolicy.name}: Recurrence Updated`,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      });
    }
    if (checkPolicy.department_id !== updatedPolicy[1].department_id) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${checkPolicy.name}: Department Changed`,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      });
    }
    if (checkPolicy.entity_id !== updatedPolicy[1].entity_id) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${checkPolicy.name}: Entity Changed`,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      });
    }
    const reviewerIdSet1 = new Set(checkPolicy.reviewer_id);
    const reviewerIdSet2 = new Set(updatedPolicy[1].reviewer_id);
    if (!(reviewerIdSet1.size === reviewerIdSet2.size && [...reviewerIdSet1].every(id => reviewerIdSet2.has(id)))) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${checkPolicy.name}: Reviewers Updated`,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      });
    }
    // Adding audit log
    const audit = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'POLICY_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('updatePolicy', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.policyDetail = async (req, res) => {
  try {
    const { Policy, Departments, PolicyCategory, Group, User, PolicyDocument, CustomPolicy } = db.models;
    // const policyList = await policyService.getListWithMultipleAssociate(Policy, Departments, PolicyCategory, Group, { id: req.params.id }, {}, {}, {}, ['id', 'status', 'author_id', 'approver_id', 'reviewer_id', 'recurrence', 'review_date', 'tentative_date', 'createdAt'], ['name'], ['name'], ['name']);
    const policyList = await policyService.getListWithMultipleAssociateV2(
      Policy,
      Departments,
      PolicyCategory,
      Group,
      User,
      'Collaborator',
      User,
      'Author',
      User,
      'Approver',
      { id: req.params.id },
      {},
      {},
      {},
      {},
      {},
      {},
      ['id', 'status', 'author_id', 'approver_id', 'reviewer_id', 'recurrence', 'review_date', 'tentative_date', 'createdAt'],
      ['name'],
      ['name'],
      ['name'],
      ['firstName', 'lastName'],
      ['firstName', 'lastName'],
      ['firstName', 'lastName']
    );
    // console.log(policyList);
    if (!policyList) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    //calculating initials
    policyList.rows[0].collaboratorName = policyList?.rows[0]?.Collaborator;
    const collaboratorInitials = policyList?.rows[0]?.Collaborator?.firstName.charAt(0).toUpperCase() + policyList?.rows[0]?.Collaborator?.lastName.charAt(0).toUpperCase();
    policyList.rows[0].collaboratorInitials = collaboratorInitials;
    delete policyList?.rows[0]?.Collaborator;

    policyList.rows[0].authorName = policyList?.rows[0]?.Author;
    const authorInitials = policyList?.rows[0]?.Author?.firstName.charAt(0).toUpperCase() + policyList?.rows[0]?.Author?.lastName.charAt(0).toUpperCase();
    policyList.rows[0].authorInitials = authorInitials;
    delete policyList?.rows[0]?.Author;

    policyList.rows[0].approverName = policyList?.rows[0]?.Approver;
    const approverInitials = policyList?.rows[0]?.Approver?.firstName.charAt(0).toUpperCase() + policyList?.rows[0]?.Approver?.lastName.charAt(0).toUpperCase();
    policyList.rows[0].approverInitials = approverInitials;
    delete policyList?.rows[0]?.Approver;

    //fetching reviewer data and adding to the policy data
    const reviewerData = await commonService.getList(User, { id: { [Op.in]: policyList.rows[0].reviewer_id } }, ['id', 'firstName', 'lastName']);
    if (reviewerData) {
      const reviewers = reviewerData.rows?.map(reviewer => {
        const initials = reviewer.firstName.charAt(0).toUpperCase() + reviewer.lastName.charAt(0).toUpperCase();
        return {
          id: reviewer.id,
          name: reviewer.firstName + ' ' + reviewer.lastName,
          initials: initials
        };
      });
      policyList.rows[0].reviewerName = reviewers;
    }
    //fetching policy document url and adding to the policy data
    const policyDocument = await commonService.getListWithoutCount(PolicyDocument, { policyId: policyList.rows[0].id }, ['id', 'original_name', 'url', 'createdAt']);
    if (policyDocument) {
      policyList.rows[0].policyDocument = policyDocument;
    } else {
      policyList.rows[0].policyDocument = null;
    }
    //checking the policy is already created with AI or not
    const customPolicy = await commonService.findByCondition(CustomPolicy, { policy_id: policyList.rows[0].id }, ['id', 'content']);
    if (!customPolicy) {
      policyList.rows[0].isCustomUpdate = false;
    } else {
      policyList.rows[0].isCustomUpdate = true;
    }

    return response.success(req, res, { msgCode: 'POLICY_FETCHED', data: policyList }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getReview = async (req, res) => {
  try {
    const { Policy, User } = db.models;
    // const policy = await commonService.findByCondition(Policy , { id: req.params.id } ,['result' , 'comment' ] );
    const policy = await policyService.getListAssociateWithoutCount(Policy, User, 'Approver', { id: req.params.id }, {}, ['name', 'result', 'reviewer_id'], ['firstName', 'lastName']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    //adding policy Approver name to the policy data
    policy[0].approverName = policy[0].Approver.firstName + ' ' + policy[0].Approver.lastName;
    // adding the reviewer names to the policy data
    const reviewerData = await commonService.getList(User, { id: { [Op.in]: policy[0].reviewer_id } }, ['firstName', 'lastName']);

    const reviewerName = reviewerData.rows?.map(reviewer => reviewer.firstName + ' ' + reviewer.lastName);
    policy[0].reviewerName = reviewerName;

    return response.success(req, res, { msgCode: 'POLICY_FETCHED', data: policy }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.updateReview = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, User, PolicyComments } = db.models;
    //checking policy exist or not and also fetching the Apporver NAME
    const policy = await policyService.getListAssociateWithoutCount(
      Policy,
      User,
      'Approver',
      { id: req.params.id },
      {},
      ['name', 'result', 'status', 'review_date', 'reviewer_id', 'approver_id', 'author_id', 'recurrence'],
      ['firstName', 'lastName', 'email']
    );
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const policyName = policy[0].name;

    let checkCondition = {};
    //checking Authorization of User to manippulate the Policy
    if (policy[0].status == policyConstant.POLICY_STATUS[3]) {
      return response.error(req, res, { msgCode: 'CANT_UPDATE' }, httpStatus.NOT_FOUND, dbTrans);
    } else if (policy[0].status == policyConstant.POLICY_STATUS[1]) {
      checkCondition = {
        [Op.and]: [{ id: req.params.id }, { reviewer_id: { [Op.contains]: [req.data.userId] } }]
      };
    } else if (policy[0].status == policyConstant.POLICY_STATUS[2]) {
      checkCondition = {
        [Op.and]: [{ id: req.params.id }, { approver_id: req.data.userId }]
      };
    }
    const check = await commonService.findByCondition(Policy, checkCondition, ['name']);
    if (!check) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const approverEmail = policy[0]?.Approver?.email;
    //adding the id of the author to the reviewer array to get the email of author without extra query an also
    //checking whether there is same reviewer and author
    let found = null;
    let i = -1;
    policy[0]?.reviewer_id?.forEach(element => {
      if (element == policy[0].author_id) {
        found = true;
        ++i;
      }
    });
    //if there is no same reviewer and author then push the author id to the reviewer array
    if (!found) {
      policy[0].reviewer_id.push(policy[0].author_id);
    }

    //getting the Reviewers and author Emails and data
    const reviewerData = await commonService.getList(User, { id: { [Op.in]: policy[0].reviewer_id } }, ['firstName', 'lastName', 'email', 'id']);
    const reviewerEmail = reviewerData.rows?.map(reviewer => reviewer.email);

    //fetching the author email and Data
    if (!found) {
      var authorEmail = reviewerEmail.pop();
      var authorData = reviewerData.rows.pop();
    } else {
      var authorEmail = reviewerEmail[i];
      var authorData = reviewerData.rows[i];
    }

    //updating Policies Comment
    let comment = null;
    if (req.body.comment) {
      const commentData = {
        user_id: req.data.userId,
        comment: req.body.comment,
        policy_id: req.params.id
      };
      // delete req.body.comment;
      comment = await commonService.addDetail(PolicyComments, commentData, dbTrans);
      if (!comment) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.NOT_FOUND, dbTrans);
      }
    }
    //seting the renewal date
    if (req.body.review_date) {
      let reviewDate = new Date(req.body.review_date);
      let renewalDate = new Date(reviewDate);
      if (policy[0].recurrence == 'BI_ANNUALLY') {
        req.body.renewal_date = renewalDate.setDate(renewalDate.getDate() + 730);
      } else if (policy[0].recurrence == 'ANNUALLY') {
        req.body.renewal_date = renewalDate.setDate(renewalDate.getDate() + 365);
      } else if (policy[0].recurrence == 'QUATERLY') {
        req.body.renewal_date = renewalDate.setDate(renewalDate.getDate() + 91);
      } else if (policy[0].recurrence == 'MONTHLY') {
        req.body.renewal_date = renewalDate.setDate(renewalDate.getDate() + 30);
      }
    }

    //updating the data in the POLICY
    const updatedReview = await commonService.updateData(Policy, req.body, { id: req.params.id }, dbTrans);
    if (!updatedReview[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Calculate the date 15 days from now
    const today = new Date();
    const futureDate = new Date(today);
    futureDate.setDate(futureDate.getDate() + 15);

    // Getting the {Positiopn of User i.e. APProver Author or Reviewer}
    let position;
    if (req.data.userId == policy[0]?.author_id) {
      position = 'Author';
    } else if (req.data.userId == policy[0]?.approver_id && policy[0].status === 'APPROVAL_OF_POLICY') {
      position = 'Approver';
    } else {
      position = 'Reviewer';
    }

    //triggering the mail according to the status
    if (updatedReview[1].status === 'REVIEW_OF_POLICY') {
      const subject = `${updatedReview[1].name} is Ready for Review: We Need Your Input`;
      const textTemplate = 'policy_review_mail_template.ejs';

      reviewerData?.rows.forEach(async row => {
        const sendData = {
          name: `${row?.firstName} ${row?.lastName}`,
          policyName: `${updatedReview[1].name}`,
          policyAuthor: `${authorData.firstName} ${authorData.lastName}`,
          deadlineDate: `${futureDate}`,
          url: `${process.env.SERVER_IP}/login/${req.params.id}`
        };
        await sendMail(row?.email, sendData, subject, textTemplate);
      });
    }
    if (updatedReview[1].status === 'APPROVAL_OF_POLICY') {
      const subject = `${updatedReview[1].name} is Ready for Approval: Your Attention Required!`;
      const textTemplate = 'policy_approve_mail_template.ejs';
      const sendData = {
        name: `${policy[0].Approver?.firstName} ${policy[0].Approver?.lastName}`,
        policyName: `${updatedReview[1].name}`,
        policyAuthor: `${authorData.firstName} ${authorData.lastName}`,
        deadlineDate: `${futureDate}`,
        url: `${process.env.SERVER_IP}/login/${req.params.id}`
      };

      sendMail(approverEmail, sendData, subject, textTemplate);
    }
    if (req.body.result === 'REJECTED') {
      const subject = `Oops! ${updatedReview[1].name} has been Rejected, Let's Pick It Up from Here!`;
      const textTemplate = 'policy_reject_mail_template.ejs';
      var sendDataAuthor = {};
      var sendDataReviewer = {};

      if (req.data.userId === policy[0].approver_id) {
        sendDataAuthor = {
          name: `${authorData.firstName} ${authorData.lastName}`,
          policyApprover: `${policy[0].Approver?.firstName} ${policy[0].Approver?.lastName}`,
          approverEmail: `${approverEmail}`,
          policyReviewer: false,
          reviewerEmail: false,
          relevant: position,
          url: `${process.env.SERVER_IP}/login/${req.params.id}`
        };

        reviewerData?.rows.forEach(async row => {
          sendDataReviewer = {
            name: `${row?.firstName} ${row?.lastName}`,
            policyApprover: `${policy[0].Approver?.firstName} ${policy[0].Approver?.lastName}`,
            approverEmail: `${approverEmail}`,
            policyReviewer: false,
            reviewerEmail: false,
            relevant: position,
            url: `${process.env.SERVER_IP}/login/${req.params.id}`
          };
          await sendMail(row?.email, sendDataReviewer, subject, textTemplate);
        });
      } else {
        const reviewer = reviewerData.rows
          ?.filter(reviewer => reviewer?.email === req.data.email)
          ?.map(reviewer => ({
            firstName: reviewer.firstName,
            lastName: reviewer.lastName,
            email: reviewer.email
          }));

        sendDataAuthor = {
          name: `${authorData.firstName} ${authorData.lastName}`,
          policyApprover: false,
          approverEmail: false,
          policyReviewer: `${reviewer[0]?.firstName} ${reviewer[0]?.lastName}`,
          reviewerEmail: `${reviewer[0]?.email}`,
          relevant: position,
          url: `${process.env.SERVER_IP}/login/${req.params.id}`
        };

        reviewerData?.rows.forEach(async row => {
          sendDataReviewer = {
            name: `${row?.firstName} ${row?.lastName}`,
            policyApprover: false,
            approverEmail: false,
            policyReviewer: `${reviewer[0]?.firstName} ${reviewer[0]?.lastName}`,
            reviewerEmail: `${reviewer[0]?.email}`,
            relevant: position,
            url: `${process.env.SERVER_IP}/login/${req.params.id}`
          };
          await sendMail(row?.email, sendDataReviewer, subject, textTemplate);
        });
      }
      sendMail(authorEmail, sendDataAuthor, subject, textTemplate);
    }

    // maintaining Audit log data
    const auditData = [];

    // if (policy[0].status !== updatedReview[1].status) {
    //     auditData.push({
    //         type: 'POLICY',
    //         type_id: req.params.id,
    //         action: `Status Updated by ${position}`,
    //         action_by_id: req.data.userId
    //     });
    // }

    if (policy[0].result !== updatedReview[1].result) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${policyName}: Result Updated by ${position}`,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      });
    }
    if (req.body.comment) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${policyName}: Comment Added by ${position}`,
        action_by_id: req.data.userId,
        flag: true,
        customer_id: req.data.customer_id,
        comment_id: comment.id
      });
    }
    if (policy[0].review_date !== updatedReview[1].review_date) {
      auditData.push({
        type: 'POLICY',
        type_id: req.params.id,
        action: `${policyName}: Review Date Updated by ${position}`,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      });
    }
    // Adding audit log
    const audit = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'POLICY_UPDATED', data: updatedReview[1] }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.uploadPolicyDocuments = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { PolicyDocument, Policy } = db.models;

    const policy = await commonService.findByCondition(Policy, { id: req.params.id, customer_id: req.data.customer_id }, ['id', 'status', 'name']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //adjusting the name of the file to store in database

    const bulkData = [];
    req.body?.uploadData.forEach(data => {
      bulkData.push({
        original_name: data.originalName,
        url: data.url,
        policyId: req.params.id
      });
    });

    //adding to the database
    const documentUpload = await commonService.bulkAdd(PolicyDocument, bulkData, dbTrans);
    if (!documentUpload) {
      return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //Entry in Audit Log
    const auditData = {
      type: 'POLICY',
      type_id: req.params.id,
      action: `${policy.name}: One Document Uploaded`,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };

    // Adding audit log
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'DOCUMENT_UPLOADED', data: documentUpload }, httpStatus.OK, dbTrans);
  } catch (error) {
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.deletePolicyDocuments = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { PolicyDocument, User } = db.models;
    const data = await commonService.deleteQuery(PolicyDocument, { url: req.body.url }, dbTrans);
    console.log(data);
    if (!data) {
      return response.error(req, res, { msgCode: 'DOCUMENT_DELETED_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //audit log
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Policy Document is Deleted by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      //   type_id: req.params.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'DOCUMENT_DELETED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
  }
};

exports.downloadPolicyDocuments = async (req, res) => {
  try {
    const { Policy, PolicyDocument, User } = db.models;
    const policy = await commonService.findByCondition(Policy, { id: req.params.policy_id }, ['id', 'status']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'DOCUMENT_DELETED_ERROR' }, httpStatus.BAD_REQUEST);
    }
    if (policy.status !== 'POLICY_IN_USE') {
      return response.error(req, res, { msgCode: 'POLICY_NOT_IN_USE' }, httpStatus.BAD_REQUEST);
    }

    const data = await commonService.getListWithoutCount(PolicyDocument, { policyId: req.params.policy_id }, ['url', 'original_name']);
    if (!data) {
      return response.error(req, res, { msgCode: 'DOCUMENT_DELETED_ERROR' }, httpStatus.BAD_REQUEST);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const filePaths = [];
    const fileUrls = [];
    console.log('-->>>', data);

    data?.forEach(async document => {
      if (document.original_name === 'Web Document') {
        fileUrls.push(document.url);
      } else {
        const filePath = path.join(__dirname, '../utils/excel', document.original_name); // Example directory name
        filePaths.push(filePath);
        await downloadFile(document.url, filePath);
      }
    });

    if (fileUrls.length > 0) {
      await sendMail(req.data.email, { name: `${user?.firstName} ${user?.lastName}`, urls: fileUrls }, 'Your Copy of Policy Document made on GoTrust', 'policy_download_link.ejs');
    }
    if (filePaths.length > 0) {
      await sendMailWithMultipleAttachments(req.data.email, { name: `${user?.firstName} ${user?.lastName}` }, 'Your Copy of Policy Document made on GoTrust', 'policy_download.ejs', filePaths);
    }

    return response.success(req, res, { msgCode: 'POLICY_DOWNLOADED', data: 'Data Downloaded In Mail' }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST);
  }
};

// exports.uploadDocuments = async (req, res) => {
//     const dbTrans = await db.transaction();
//     try {
//         if (!req.files.length) {
//             return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST, dbTrans);
//         }
//         const imagesArrayURL = req.files.map((file) => file.path)
//         // console.log("------------->>>>>",req.files[0].originalname);

//         // upload documents to s3
//         const uploadDocuemntToS3 = await uploadDocument.uploadToS3(`${imagesArrayURL[0]}`);
//         if (uploadDocuemntToS3.status != true) {
//             // delete file from local
//             if (req.files[0]) {
//                 // delete file from local
//                 await deleteFile(req.files[0].path)
//             }
//             console.log('S3 Error', uploadDocuemntToS3)
//             return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
//         }
//         if (req.files[0]) {
//             // delete file from local
//             await deleteFile(req.files[0].path)
//         }
//         //adding document to db
//         const { PolicyDocument } = db.models;
//         // req.body.originalName = req.files[0].originalname;
//         req.body.policyId = req.params.id;
//         req.body.url = uploadDocuemntToS3.data.Location;

//         const documentUpload = await commonService.addDetail(PolicyDocument,req.body ,dbTrans);
//         documentUpload.originalName = req.files[0].originalname;
//         documentUpload.location = uploadDocuemntToS3.data.Location;

//         return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: documentUpload }, httpStatus.OK , dbTrans);

//     } catch (err) {
//         console.log('uploadDocuments', err);
//         if (req.files[0]) {
//             // delete file from local
//             await deleteFile(req.files[0].path)
//         }
//         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
//     }
// };
