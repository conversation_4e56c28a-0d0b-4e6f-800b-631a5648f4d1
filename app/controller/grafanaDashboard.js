const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');


exports.createCustomDashboard= async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const {GrafanaDashboardList}=db.models;
        if(req.data.roleName !== 'Data Protection Officer'){
            return response.error(req,res,{msgCode:'UNAUTHORIZED'},httpStatus.UNAUTHORIZED,dbTrans);
        }
        const check = await commonService.findByCondition(GrafanaDashboardList,{name:req.body.name});
        if(check){
            return response.error(req, res, { msgCode: 'DASHBOARD_ALREADY_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const createDashboard = await commonService.addDetail(GrafanaDashboardList, req.body, dbTrans);
        
        if(!createDashboard){
            return response.error(req, res, { msgCode: 'ERROR_CREATING_DASHBOARD' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req,res, {msgCode:'DASHBOARD_CREATED',data:createDashboard}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error in creating",error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.updateDashboard = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{

        const {GrafanaDashboardList,User}=db.models;
        if(req.data.roleName !== 'Data Protection Officer'){
            return response.error(req,res,{msgCode:'UNAUTHORIZED'},httpStatus.UNAUTHORIZED,dbTrans);
        }
        const check = await commonService.findByCondition(GrafanaDashboardList,{id:req.params.id});

        if(!check){
            return response.error(req,res,{msgCode:'DASHBOARD_NOT_FOUND'},httpStatus.BAD_REQUEST,dbTrans);
        }
        const updateData = await commonService.updateData(GrafanaDashboardList,req.body,{id:req.params.id},dbTrans);

        if(!updateData[1]){
            return response.error(req,res,{msgCode:'UPDATE_ERROR'},httpStatus.BAD_REQUEST,dbTrans);
        }

        return response.success(req,res, {msgCode:'DASHBOARD_UPDATED',data:updateData[1]}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }

}

exports.listDashboard = async(req,res)=>{
    try{
        const {GrafanaDashboardList,User}=db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        
        const { limit, offset } = getPagination(page, size);

        let order = [[sort_by, sort_order]];
        const list = await commonService.getList(GrafanaDashboardList,null,{},limit, offset, order);
        if(!list){
            return response.error(req,res,{msgCode:'DASHBOARD_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }

        return response.success(req,res, {msgCode:'LIST_FETCHED',data:list}, httpStatus.OK); 

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.getdetails = async(req,res)=>{
    try{ 
        const {GrafanaDashboardList,User}=db.models;
        const condition = {
            name:req.query.name
        }
        const data = await commonService.findByCondition(GrafanaDashboardList,condition,{});
        if(!data){
            return response.error(req,res,{msgCode:'DASHBOARD_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }
        return response.success(req,res, {msgCode:'DATA_FETCHED',data:data}, httpStatus.OK);
    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.deleteDashboard = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{

        const {GrafanaDashboardList}=db.models;
        if(req.data.roleName !== 'Data Protection Officer'){
            return response.error(req,res,{msgCode:'UNAUTHORIZED'},httpStatus.UNAUTHORIZED,dbTrans);
        }
        const check = await commonService.findByCondition(GrafanaDashboardList,{id:req.params.id});

        if(!check){
            return response.error(req,res,{msgCode:'DASHBOARD_NOT_FOUND'},httpStatus.BAD_REQUEST,dbTrans);
        }

        const deleteData = await commonService.deleteQuery(GrafanaDashboardList,{id:req.params.id},dbTrans);

        if(!deleteData){
            return response.error(req,res,{msgCode:'DELETE_ERROR'},httpStatus.BAD_REQUEST,dbTrans);
        }

        return response.success(req,res, {msgCode:'DASHBOARD_DELETED',data:deleteData[1]}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }

}
