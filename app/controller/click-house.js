const response = require('../response');
const httpStatus = require('http-status');
const { createClient } = require('@clickhouse/client');
const { getPagination2 } = require('../config/helper');

exports.getDatabaseServiceEntity = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = 'SELECT * FROM  dbservice_entity';
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        console.log('www', rows);

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "SERVICE_ENTITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getDatabaseServiceEntity', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.getStructuredData = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });
        let { page, size, search } = req.query;

        const { limit, offset } = getPagination2(page, size);

        let searchCondition = "";
        if (search) {
            search = search.replace(/'/g, "''"); // Escape single quotes for SQL safety
            searchCondition = `WHERE column_name ILIKE '%${search}%' OR json ILIKE '%${search}%'`;
        }

        const countQuery = `SELECT COUNT(*) as total FROM structured_view_data_final ${searchCondition};`;
        const countResult = await client.query({
            query: countQuery,
            format: 'JSONEachRow',
        });
        const totalRows = (await countResult.json())[0]?.total || 0;

        // Query to fetch paginated data
        const query = `SELECT * FROM structured_view_data_final ${searchCondition}ORDER BY column_name ASC LIMIT ${limit} OFFSET ${offset}`;
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });
        // Fetch and print the result
        const rows = await resultSet.json();

        const meta_query = `
            SELECT * FROM structured_view_data_meta_data;
        `;

        const metaResultSet = await client.query({
            query: meta_query,
            format: 'JSONEachRow',
        });

        const metaRows = await metaResultSet.json();

        console.log('www', rows);

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "SERVICE_ENTITY_FETCHED", data: { total: totalRows, results: rows, metaData: metaRows } }, httpStatus.OK);

    } catch (err) {
        console.log('getDatabaseServiceEntity', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};





exports.getDatabaseSchemaEntity = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM  database_schema_entity where fqnHash like '${req.query.fqnHash}%'`;
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "ENTITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getDatabaseSchemaEntity', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getTableEntity = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        //f4ee0dcd98304deebf3995a88622e3f2.bcbfa28c7412fe379540665b05393f76.a2bc7a01a1da09980c5474040cb48f4b.cda66af50198f874ffe7859a89cc4a8f
        const query = `SELECT * FROM  table_entity where fqnHash like '${req.query.fqnHash}%'`;
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "ENTITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getTableEntity', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getProfileTimeSeries = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM  profiler_data_time_series where entityFQNHash like '${req.query.entityFQNHash}%'`;
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "ENTITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getProfileTimeSeries', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getColumnNer = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM  structured_view_data_ner where table_id='${req.query.table_id}'`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "ENTITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getProfileTimeSeries', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.unstructuredCatalogue = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        let { page, size, search } = req.query;
        const { limit, offset } = getPagination2(page, size);

        let searchCondition = "";
        if (search) {
            search = search.replace(/'/g, "''"); // Escape single quotes for SQL safety
            searchCondition = `WHERE file_name ILIKE '%${search}%' OR json ILIKE '%${search}%'`;
        }

        let returnArr = {};
        const countQuery = `
        SELECT COUNT(*) AS total_count FROM unstructured_view_data_ner 
        ${searchCondition}`;
        const countResultSet = await client.query({
            query: countQuery,
            format: 'JSONEachRow',
        });

        const countRows = await countResultSet.json();
        returnArr['total_count'] = countRows.length > 0 ? countRows[0].total_count : 0;

        // Fetch paginated data
        const query = `
            SELECT * FROM unstructured_view_data_ner 
            ${searchCondition} 
            ORDER BY file_name ASC NULLS LAST
            LIMIT ${limit} OFFSET ${offset};
        `;

        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        const rows = await resultSet.json();
        returnArr['all_records'] = rows;


        const dataElementquery = `SELECT * FROM "default"."data_element" where parameter_applicability = 'False'`;
        // const query = `SELECT * FROM "default"."data_element" ${searchCondition}`;
        const dataElementresultSet = await client.query({
            query: dataElementquery,
            format: 'JSONEachRow',
        });

        const dataElementrows = await dataElementresultSet.json();
        const PIICategory = dataElementrows?.map(items => { return items.parameter_value });
        

        returnArr['all_records']?.forEach(items =>{
            const entityObj = JSON.parse(items.json);

            // Remove keys from removeKeys
            for (const key of PIICategory) {
                delete entityObj['detected_entities'][key];
            }
            items.json = JSON.stringify(entityObj);
        })

        // Fetch meta information
        const meta_query = `
            SELECT 
                COUNT(DISTINCT source) AS data_system_count,
                COUNT( file_name) AS scanned_doc_count,
                SUM(file_size) AS total_volume,
                COUNT(DISTINCT file_type) AS file_formats_count
            FROM ner_unstructured_data;
        `;

        const metaResultSet = await client.query({
            query: meta_query,
            format: 'JSONEachRow',
        });

        const metaRows = await metaResultSet.json();
        returnArr['meta_info'] = metaRows;

        return response.success(req, res, { msgCode: "ENTITY_FETCHED", data: returnArr }, httpStatus.OK);
    } catch (err) {
        console.log('getProfileTimeSeries', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.getDataManagement = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."unstructured_view_data_management"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "UNSTRUCTURED_DATA_MANAGEMENT_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getDataManagement', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getDocumentList = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        let { page, size, search } = req.query;
        const { limit, offset } = getPagination2(page, size); // Get pagination details


        const dataElementquery = `SELECT * FROM "default"."data_element" where parameter_applicability = 'False'`;
        // const query = `SELECT * FROM "default"."data_element" ${searchCondition}`;
        const dataElementresultSet = await client.query({
            query: dataElementquery,
            format: 'JSONEachRow',
        });

        const dataElementrows = await dataElementresultSet.json();
        const PIICategory = dataElementrows?.map(items => { return items.parameter_value });

        let searchCondition = "";
        if (search) {
            search = search.replace(/'/g, "''"); // Escape single quotes for SQL safety
            searchCondition = `WHERE document_name ILIKE '%${search}%' OR entity ILIKE '%${search}%'`;
        }

        // Query for paginated records
        const query = `
            SELECT * FROM "default"."unstructured_view_document_listing"
            ${searchCondition}
            ORDER BY document_name ASC
            LIMIT ${limit} OFFSET ${offset};
        `;

        console.log('Executing Query:', query);
        const resultSet = await client.query({ query, format: 'JSONEachRow' });
        let rows = await resultSet.json();

        rows?.forEach(items => {
            const entityObj = JSON.parse(items.entity);

            // Remove keys from removeKeys
            for (const key of PIICategory) {

                delete entityObj[key];
            }
            
            items.entity = JSON.stringify(entityObj);
        });

        // Query for total count
        const countQuery = `
            SELECT COUNT(*) AS total_count 
            FROM "default"."unstructured_view_document_listing"
            ${searchCondition};
        `;

        console.log('Executing Count Query:', countQuery);
        const countResultSet = await client.query({ query: countQuery, format: 'JSONEachRow' });
        const countRows = await countResultSet.json();
        const totalCount = countRows.length > 0 ? countRows[0].total_count : 0;

        return response.success(req, res, { msgCode: "DOCUMENT_LIST", data: { totalCount, rows } }, httpStatus.OK);

    } catch (err) {
        console.log('getDocumentList', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.getFileFormat = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."unstructured_view_file_format_statistics"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "FILE_FORMAT", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getFileFormat', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getLocation = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."unstructured_view_region_data_statistics"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "FILE_FORMAT", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getFileFormat', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getSankyGraph = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const dataElementquery = `SELECT * FROM "default"."data_element" where parameter_applicability = 'False'`;
        // const query = `SELECT * FROM "default"."data_element" ${searchCondition}`;
        const dataElementresultSet = await client.query({
            query: dataElementquery,
            format: 'JSONEachRow',
        });

        const dataElementrows = await dataElementresultSet.json();
        // const PIICategory = dataElementrows?.map(items => { return items.parameter_value });
        const PIICategory = dataElementrows?.map(item => `'${item.parameter_value}'`);

        const query = `SELECT * FROM "default"."unstructured_view_sanky_graph" where detected_pii NOT IN(${PIICategory})`;
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // PIICategory.push('DEMOGRAPHIC_INFORMATION');
        // rows?.forEach(items => {

        //     // Remove keys from removeKeys
        //     for (const key of PIICategory) {
        //         if(key==items.detected_pii){
        //             delete items;
        //         }
        //     }
        // });

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "LOCATION_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getFileFormat', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getViewSummary = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."unstructured_view_summary"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "SUMMARY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getViewSummary', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredViewDetails = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_data_details"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "VIEW_DETAILS_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredViewDetails', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredElementCategories = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_data_element_categories"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "ELEMENT_CATEGORIES_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredElementCategories', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredViewDetailsManagement = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_data_management"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "DATA_MANAGEMENT_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredViewDetailsManagement', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredElementTypes = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        let { page, size, search } = req.query;
        const { limit, offset } = getPagination2(page, size); // Get limit & offset

        let searchCondition = "";
        if (search) {
            search = search.replace(/'/g, "''"); // Escape single quotes for SQL safety
            searchCondition = `WHERE data_element_types ILIKE '%${search}%'`;
        }

        // Query for paginated records
        const query = `
            SELECT * FROM "default"."structured_view_data_element_types"
            ${searchCondition}
            ORDER BY data_element_types ASC
            LIMIT ${limit} OFFSET ${offset};
        `;

        console.log('Executing Query:', query);
        const resultSet = await client.query({ query, format: 'JSONEachRow' });
        const rows = await resultSet.json();

        // Query for total count
        const countQuery = `
            SELECT COUNT(*) AS total_count 
            FROM "default"."structured_view_data_element_types"
            ${searchCondition};
        `;

        const countResultSet = await client.query({ query: countQuery, format: 'JSONEachRow' });
        const countRows = await countResultSet.json();
        const totalCount = countRows.length > 0 ? countRows[0].total_count : 0;

        return response.success(req, res, {
            msgCode: "ELEMENT_TYPES_FETCHED",
            data: { totalCount, rows }
        }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredElementTypes', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredShankyGraph = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_data_shankey"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "VIEW_DETAILS_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredShankyGraph', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredLocation = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_data_locations"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "LOCATION_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredLocation', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getUnstructuredViewDetails = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."unstructured_view_data_details"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "VIEW_DETAILS_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredLocation', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getUnstructuredElements = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        let { page, size, search } = req.query;
        const { limit, offset } = getPagination2(page, size); // Get pagination details
        console.log(limit, offset)

        const dataElementquery = `SELECT * FROM "default"."data_element" where parameter_applicability = 'False'`;
        // const query = `SELECT * FROM "default"."data_element" ${searchCondition}`;
        const dataElementresultSet = await client.query({
            query: dataElementquery,
            format: 'JSONEachRow',
        });

        const dataElementrows = await dataElementresultSet.json();
        // const PIICategory = dataElementrows?.map(items => { return items.parameter_value });
        const PIICategory = dataElementrows?.map(item => `'${item.parameter_value}'`);

        let searchCondition = `WHERE data_element_type NOT IN(${PIICategory})`;
        // let searchCondition = ""
        if (search) {
            // search = search.replace(/'/g, "''"); // Escape single quotes for SQL safety
            searchCondition += ` AND data_element_type ILIKE '%${search}%'`;
        }

        // Query for paginated records
        const query = `
            SELECT * FROM "default"."unstructured_view_data_element_listing"
            ${searchCondition}
            ORDER BY data_element_type ASC
            LIMIT ${limit} OFFSET ${offset};
        `;

        console.log('Executing Query:', query);
        const resultSet = await client.query({ query, format: 'JSONEachRow' });
        const rows = await resultSet.json();

        // Query for total count
        const countQuery = `
            SELECT COUNT(*) AS total_count 
            FROM "default"."unstructured_view_data_element_listing"
            ${searchCondition};
        `;

        console.log('Executing Count Query:', countQuery);
        const countResultSet = await client.query({ query: countQuery, format: 'JSONEachRow' });
        const countRows = await countResultSet.json();
        const totalCount = countRows.length > 0 ? countRows[0].total_count : 0;

        return response.success(req, res, {
            msgCode: "ELEMENT_CATEGORIES_FETCHED",
            data: { totalCount, rows }
        }, httpStatus.OK);

    } catch (err) {
        console.log('getUnstructuredElements', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.getStructuredViewLocation = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_location"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "STRUCTURED_VIEW_LOCATION", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredViewLocation', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getProfilerMeta = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."profiler_metadata"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "PROFILER_META_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getProfilerMeta', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getStructuredSensitivity = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."structured_view_data_sensitivity"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "STRUCTURED_SENSITIVITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getStructuredSensitivity', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getUnStructuredSensitivity = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const query = `SELECT * FROM "default"."unstructured_view_data_sensitivity"`;
        console.log('www', query);
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        // Fetch and print the result
        const rows = await resultSet.json();

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "UNSTRUCTURED_SENSITIVITY_FETCHED", data: rows }, httpStatus.OK);

    } catch (err) {
        console.log('getUnStructuredSensitivity', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateColumnNer = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const updateData = req.body; // Assuming update data is in request body

        // Constructing the SET clause dynamically
        const setClauses = Object?.keys(updateData)
            ?.map(column => `${column} = '${updateData[column]}'`)
            .join(', ');

        const updateQuery = `ALTER TABLE "default"."column_ner_results" UPDATE ${setClauses} WHERE id = '${req.query.id}'`;

        console.log('Executing Update Query:', updateQuery);

        await client.command({
            query: updateQuery,
        });

        // rows?.forEach(items=>{
        //     items.json = JSON.parse(items.json);
        // })

        return response.success(req, res, { msgCode: "DATA_UPDATED" }, httpStatus.OK);

    } catch (err) {
        console.log('updateColumnNer', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateNerUnstructuredData = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const updateData = req.body; // Assuming update data is in request body

        // Constructing the SET clause dynamically
        const setClauses = Object?.keys(updateData)
            ?.map(column => `${column} = '${updateData[column]}'`)
            .join(', ');

        const updateQuery = `ALTER TABLE "default"."ner_unstructured_data" UPDATE ${setClauses} WHERE id = '${req.query.id}'`;

        console.log('Executing Update Query:', updateQuery);

        await client.command({
            query: updateQuery,
        });

        return response.success(req, res, { msgCode: "DATA_UPDATED" }, httpStatus.OK);
    } catch (err) {
        console.log('updateNerUnstructuredData', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getDataElement = async (req, res) => {
    try {
        let { page, size, search } = req.query;

        const { limit, offset } = getPagination2(page, size);

        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });
        let searchCondition = "";
        if (search) {
            search = search.replace(/'/g, "''"); // Escape single quotes for SQL safety
            searchCondition = `WHERE parameter_name ILIKE '%${search}%' or parameter_value ILIKE '${search}'`;
        }

        const countQuery = `SELECT COUNT(*) as count FROM "default"."data_element" ${searchCondition}`;

        const query = `SELECT * FROM "default"."data_element" ${searchCondition} LIMIT ${limit} OFFSET ${offset}`;
        // const query = `SELECT * FROM "default"."data_element" ${searchCondition}`;
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow',
        });

        const countSet = await client.query({
            query: countQuery, // Pass it as "query"
            format: 'JSONEachRow',
        });


        const count = await countSet.json();
        // Fetch and print the result
        const rows = await resultSet.json();

        const data = {
            count: Number(count[0]?.count),
            rows
        }

        return response.success(req, res, { msgCode: "DATA_FETCHED", data }, httpStatus.OK);
    } catch (err) {
        console.log('getDataElement', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.createDataElement = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const data = [req.body];

        const dataElement = await client.insert({
            table: 'data_element',
            values: data, // Expecting an array of objects [{ column1: value1, column2: value2, column3: value3 }]
            format: 'JSONEachRow',
        });

        return response.success(req, res, { msgCode: "DATA_CREATED", data: dataElement }, httpStatus.OK);
    } catch (err) {
        console.log('createDataElement', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateDataElement = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const updateData = req.body; // Assuming update data is in request body

        // Constructing the SET clause dynamically
        const setClauses = Object?.keys(updateData)
            ?.map(column => `${column} = '${updateData[column]}'`)
            .join(', ');

        const updateQuery = `ALTER TABLE "default"."data_element" UPDATE ${setClauses} WHERE id = '${req.query.id}'`;

        console.log('Executing Update Query:', updateQuery);

        await client.command({
            query: updateQuery,
        });

        return response.success(req, res, { msgCode: "DATA_UPDATED" }, httpStatus.OK);
    } catch (err) {
        console.log('updateNerUnstructuredData', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.deleteDataElement = async (req, res) => {
    try {
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        const deleteQuery = `ALTER TABLE "default"."data_element" DELETE WHERE id = '${req.query.id}'`;

        console.log('Executing Delete Query:', deleteQuery);

        await client.command({
            query: deleteQuery,
        });

        return response.success(req, res, { msgCode: "DATA_DELETED" }, httpStatus.OK);
    } catch (err) {
        console.log('updateNerUnstructuredData', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getUniqueDataType = async (req, res) => {
    try {
        // Initialize ClickHouse client
        const client = new createClient({
            url: process.env.CLICK_HOUSE_URL,
            port: process.env.CLICK_HOUSE_PORT,
            username: process.env.CLICK_HOUSE_USERNAME,
            password: process.env.CLICK_HOUSE_PASSWORD,
            database: process.env.CLICK_HOUSE_DATABASE,
        });

        // SQL query to get unique data types from both views
        const query = `SELECT CAST(data_element_types AS String) AS data_element FROM structured_view_data_element_types 
        UNION DISTINCT 
        SELECT CAST(data_element_type AS String) AS data_element FROM unstructured_view_data_element_listing`;

        // Execute the query
        const resultSet = await client.query({
            query,
            format: 'JSONEachRow' // Each row comes in raw text per line
        });

        const rawResponse = await resultSet.text(); // Get raw response first

        // Parse raw response manually (split by new lines)
        const rows = rawResponse.trim().split('\n').map(row => row.trim());

        // Return the data in a clean response
        return response.success(req, res, { msgCode: "DATA_FETCHED", data: rows }, httpStatus.OK);
    } catch (error) {
        console.error('Error executing query:', error);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
