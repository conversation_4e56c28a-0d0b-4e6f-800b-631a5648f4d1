const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const assessmentService = require('../services/assessment');
const constant = require('../constant/ROPA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');
const { question } = require('../constant/ROPA');
const csv = require('csv-parser');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
// const { sendMail, sendMailWithAttach } = require('../config/email');
// const { transformData, createExcelFile, transformBasicInfo, exportRopa } = require('../utils/helper');
// const { IAM } = require('aws-sdk');
// const { createClient } = require('@clickhouse/client');


exports.countAssessment = async (req, res) => {
    try {
        const { CustomerAssessment } = db.models;
        const assessmentData = await commonService.getListGroupBy(
            CustomerAssessment, 
            { customer_id: req.data.customer_id }, 
            ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
            ['status']
        );

        // const assessmentData = await commonService.getListGroupBy(CustomerAssessments, { customer_id: req.data.customer_id ,status: 'Completed' }, [`is_already_performed`, [sequelize.fn('COUNT', 'id'), 'count']], [`is_already_performed`]);
        if (!assessmentData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        let totalCount = 0;
        let yetToStartCount = 0;
        let inProgressCount = 0;
        let completedCount = 0;
        assessmentData.forEach(row => {
            const count = parseInt(row.count || 0);
            totalCount += count;

            switch(row.status) {
                case 'Yet to Start':
                    yetToStartCount = count;
                    break;
                case 'Started':
                case 'Under Review':
                case 'Changes Requested':
                    inProgressCount += count;
                    break;
                case 'Completed':
                    completedCount = count;
                    break;
            }
        });
        const data = [
            { 
                assessment: 'TOTAL', 
                count: String(totalCount)
            },
            { 
                assessment: 'YET_TO_START', 
                count: String(yetToStartCount)
            },
            { 
                assessment: 'IN_PROGRESS', 
                count: String(inProgressCount)
            },
            { 
                assessment: 'COMPLETED', 
                count: String(completedCount)
            }
        ];
        // let count = 0;
        // assessmentData?.forEach(row => {
        //     count = count + parseInt(row.count);
        // });
        // let data = [];
        // if (assessmentData[0]?.is_already_performed === true) {
        //     data.push({ assessment: 'UPLOADED', count: String(assessmentData[0]?.count ?? 0 ) });
        //     data.push({ assessment: 'CREATED', count: String(assessmentData[1]?.count ?? 0 ) });
        // } else {
        //     data.push({ assessment: 'UPLOADED', count: String(assessmentData[1]?.count ?? 0 ) });
        //     data.push({ assessment: 'CREATED', count: String(assessmentData[0]?.count ?? 0 ) });
        // }

        // data.push({ assessment: 'TOTAL', count: String(count) ?? 0 });


        return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);
    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.assessmentsList = async (req, res) => {
    try {
        const { User, Group, Departments, Processes, Assessments, CustomerAssessment,AssessmentTemplate } = db.models;
        // const { liaCollaborator, piaCollaborator, tiaCollaborator, pdaCollaborator } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let userType = null;
        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};
        let searchCondition = {};
        // const groupIds = [req.params.entity_id];


        if (search && !search_key) {
            searchCondition = {
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Assessment.assessment_name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
                    // sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                ]
            };
        };

        if (search && search_key) {
            if (search_key === 'Department') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Process') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Assessment') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Assessment.assessment_name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'AssignedTo') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Approver') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            }
            else if (search_key === 'Owner') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Status') {
                searchCondition = {
                    status: sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'),
                    { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'Risks') {
                searchCondition = {
                    risks: sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'),
                    { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'is_assigned') {
                searchCondition = {
                    is_Assigned: true
                };
            }
            // } else if (search_key === 'Status') {
            //     searchCondition = {
            //         status: sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
            //     };
            // } else if (search_key === 'Risks') {
            //     searchCondition = {
            //         risks: sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
            //     };
            // }
        }

        const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
        const departmentIds = departments?.rows?.map(department => department.id);
        const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
        const processIds = processes?.rows?.map(process => process.id);

        if (req.data.roleName === authConstant.USER_ROLE[2]) {
            userType = 'DPO';
        } else if (departmentIds.length > 0) {
            userType = 'Department Head';
        } else {
            userType = 'Employee';
        }


        let assessmentFilter = { customer_id: req.data.customer_id };

        const list = await assessmentService.getListWithMultipleAssociates(CustomerAssessment, Assessments, AssessmentTemplate, Departments, Processes, User, 'AssignedTo', User, 'Approver', User, 'Owner', Group,
            {
                [Op.and]: [
                    assessmentFilter,
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('CustomerAssessment.entity_id'), { [Op.ne]: null })
                        ]
                    },
                    is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
                    searchCondition
                ]
            },
            {},
            {},
            {},
            customerFilter,
            userFilter,
            {},
            {},
            {},
            ['id', 'assessment_id', 'risks', 'progress', 'start_date', 'end_date', 'tentative_date', 'status'], ['id', 'type', 'assessment_name', 'key'], ['id','name','key'],['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], ['id', 'firstName', 'lastName'], ['id', 'firstName', 'lastName'], ['id', 'name'], limit, offset, order);
        if (!list) {
            return response.error(req, res, { msgCode: "LIST_NOT_FOUND" }, httpStatus.BAD_REQUEST);
        }


        const taskOverview = {
            user_type: userType,
            count: list.count,
            rows: list?.rows.map(listItem => {
                // if (listItem.Process) {
                //     listItem.Department = listItem.Process.Department;
                //     delete listItem.Process.Department;
                //     delete listItem.Process.User;
                // }
                if(listItem.Assessment){
                    listItem.Assessment.AssessmentTemplates =listItem.AssessmentTemplate?[{...listItem.AssessmentTemplate}]:null
                    delete listItem.AssessmentTemplate;
                }

                if (listItem.AssignedTo) {
                    if (listItem?.AssignedTo?.id === req.data.userId) {
                        listItem.isAssigned = true;
                    } else {
                        listItem.isAssigned = false;
                    }
                    listItem.AssignedTo.name = `${listItem?.AssignedTo?.firstName} ${listItem?.AssignedTo?.lastName}`;
                    delete listItem.AssignedTo.firstName;
                    delete listItem.AssignedTo.lastName;
                }

                if (listItem?.Approver) {
                    listItem.Approver.name = `${listItem?.Approver?.firstName} ${listItem?.Approver?.lastName}`;
                    delete listItem.Approver.firstName;
                    delete listItem.Approver.lastName;
                }

                if (listItem.Owner) {
                    listItem.Owner.name = `${listItem?.Owner?.firstName} ${listItem?.Owner?.lastName}`;
                    listItem.Owner.intials = listItem?.Owner?.firstName.charAt(0).toUpperCase() +listItem?.Owner?.lastName.charAt(0).toUpperCase()
                    delete listItem.Owner.firstName;
                    delete listItem.Owner.lastName;
                }

                // listItem.SPOC = { id: listItem.Department.User.id, name: `${tiaItem.Department.User.firstName} ${tiaItem.Department.User.lastName}` },
                // listItem.isCollaborator = collaboratorIds.includes(listItem.id);
                // delete tiaItem.Department.User;

                return listItem;
            }),
            
        };

        return response.success(req, res, { msgCode: "TASK_OVERVIEW_FETCHED", data: taskOverview }, httpStatus.OK);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST);
    }
};
