const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const { Op } = require("sequelize");

exports.departmentList = async (req, res) => {
    try {
        const { Departments } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let departmentCondition = { customer_id: req.params.id };
        if (search) {
            departmentCondition = { ...departmentCondition, ...{ name: { [Op.iLike]: `%${search}%` } } };
        }
        // get departments
        const getDepartment = await commonService.getList(Departments, departmentCondition, ['id' ,'name' ,'parent_id' , 'customer_id' ,'spoc_id'], limit, offset , order);
        if (!getDepartment) {
            return response.error(req, res, { msgCode: 'DEPARTMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "DEPARTMENT_FETCHED", data: getDepartment }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.createDepartments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { Departments, ROPA, GroupUser } = db.models;
        // Create department
        const check = await commonService.findByCondition(Departments, { name: req.body.name, customer_id: req.body.customer_id, group_id: req.body.group_id });
        if (check) {
            return response.error(req, res, { msgCode: 'DEPARTMENT_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const newDepartment = await commonService.addDetail(Departments, req.body, dbTrans);
        if (!newDepartment) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_DEPARTMENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        //creating Group User entry in the group_user table
        if (newDepartment.spoc_id) {
            req.body.group_id = newDepartment.group_id;
            req.body.user_id = newDepartment.spoc_id;
            const check = await commonService.findByCondition(GroupUser, { group_id: req.body.group_id, user_id: req.body.user_id });
            // console.log(check);
            if (!check) {
                const updateGroupUser = await commonService.addDetail(GroupUser, req.body, dbTrans);
            }
        }

        const ropa = {
            department_id: newDepartment.id,
            customer_id: req.body.customer_id,
            group_id: req.body.group_id,
            status: 'Yet to Start',
            // is_already_performed: true
        }
        const newRopa = await commonService.addDetail(ROPA, ropa, dbTrans);
        if (!newRopa) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_ROPA' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "DEPARTMENT_CREATED", data: newDepartment }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating department:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


exports.updateDepartment = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { Departments, GroupUser } = db.models;
        // Check department
        const check = await commonService.findByCondition(Departments, { id: req.params.department_id, customer_id: req.data.customer_id });
        if (!check) {
            return response.error(req, res, { msgCode: 'DEPARTMENT_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const update = await commonService.updateData(Departments, req.body, { id: req.params.department_id }, dbTrans);
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (req.body.spoc_id) {
            const check = await commonService.findByCondition(GroupUser, { group_id: update[1].group_id, user_id: req.body.spoc_id });
            if (!check) {
                const groupUserData = {
                    group_id: update[1].group_id,
                    user_id: req.body.spoc_id
                }
                const updateGroupUser = await commonService.addDetail(GroupUser, groupUserData, dbTrans);
                if (!updateGroupUser) {
                    return response.error(req, res, { msgCode: 'ERROR_CREATING_GROUP_USER' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        return response.success(req, res, { msgCode: "DEPARTMENT_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getDepartmentList = async (req, res) => {
    try {
        const { Departments } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let departmentCondition = { group_id: req.params.group_id };
        if (search) {
            departmentCondition = { ...departmentCondition, ...{ name: { [Op.iLike]: `%${search}%` } } };
        }
        // get departments
        const getDepartment = await commonService.getList(Departments, departmentCondition, ['id' ,'name' ,'parent_id' , 'customer_id' ,'spoc_id'], limit, offset , order);
        if (!getDepartment) {
            return response.error(req, res, { msgCode: 'DEPARTMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "DEPARTMENT_FETCHED", data: getDepartment }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.deleteDepartment = async(req, res) => {
    const dbTrans = await db.transaction();

    try {
        const { ROPA, Processes, Departments } = db.models;
        const department_id = req.params.department_id;

        const checkROPA = await commonService.findByCondition(ROPA, {department_id: department_id, status: {
            [Op.ne]: 'Yet to Start'
        }});
        // console.log(checkROPA);
        if(checkROPA){
            return response.error(req, res, { msgCode: 'CANNOT_DELETE_DEPARTMENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        const checkProcess = await commonService.findByCondition(Processes, { department_id: department_id });
        // console.log(checkProcess);
        if(checkProcess){
            return response.error(req, res, { msgCode: 'CANNOT_DELETE_DEPARTMENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        const checkSubDepartment = await commonService.findByCondition(Departments, { parent_id:  department_id });
        // console.log(checkSubDepartment);
        if(checkSubDepartment){
            return response.error(req, res, { msgCode: 'CANNOT_DELETE_DEPARTMENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        const deleteROPAs = await commonService.deleteQuery(ROPA, { department_id: department_id }, dbTrans);
        if(!deleteROPAs){
            return response.error(req, res, { msgCode: 'DEPARTMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const deleteDepartment = await commonService.deleteQuery(Departments, { id: department_id }, dbTrans);
        if(!deleteDepartment){
            return response.error(req, res, { msgCode: 'DEPARTMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        // console.log("ROPA EXISTS: ", isRopaExists);
        return response.success(req, res, { msgCode: "DEPARTMENT_DELETED" }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.log("deleteDepartment", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};