const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const dayjs = require('dayjs');
const commonService = require('../services/common');
const privacyOpsService = require('../services/privacyOps');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');


exports.createRisk= async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const { Risk }=db.models;
        req.body.customer_id = req.data.customer_id;
        req.body.owner_id = req.data.userId;
        const createRisk = await commonService.addDetail(Risk, req.body, dbTrans);
        
        if(!createRisk){
            return response.error(req, res, { msgCode: 'ERROR_CREATING_RISK' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req,res, {msgCode:'RISK_CREATED',data:createRisk}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error in creating duty",error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.updateRisk = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{

        const { Risk }=db.models;
    
        const check = await commonService.findByCondition(Risk, { customer_id:req.data.customer_id, id:req.params.risk_id });

        if(!check){
            return response.error(req,res,{msgCode:'RISK_NOT_FOUND'},httpStatus.BAD_REQUEST,dbTrans);
        }

        const updateData = await commonService.updateData(Risk, req.body, { id:req.params.risk_id }, dbTrans);

        if(!updateData[1]){
            return response.error(req,res,{msgCode:'UPDATE_ERROR'},httpStatus.BAD_REQUEST,dbTrans);
        }

        return response.success(req,res, {msgCode:'RISK_UPDATED',data:updateData[1]}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }

}
exports.detailRisk = async(req,res)=>{
    try{
        const { Risk, User, Group}=db.models;
        const riskCondition = {
            id:req.params.risk_id,
            customer_id:req.data.customer_id
        }
        const riskDetail = await privacyOpsService.getDetailWithMultiAssociate(Risk, User, Group, riskCondition, {}, {}, {}, ['id', 'firstName', 'lastName'], ['id', 'name']);
        if(!riskDetail.rows[0]){
            return response.error(req,res,{msgCode:'DUTY_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }

        const transformedRiskDetail = {
            result: {
                ...riskDetail.rows[0], // Spread all properties from the first row
                Approver: riskDetail.rows[0].Approver
                    ? { id: riskDetail.rows[0].Approver.id, name: `${riskDetail.rows[0].Approver.firstName} ${riskDetail.rows[0].Approver.lastName}`.trim() }
                    : null,
                Owner: riskDetail.rows[0].Owner
                    ? { id: riskDetail.rows[0].Owner.id, name: `${riskDetail.rows[0].Owner.firstName} ${riskDetail.rows[0].Owner.lastName}`.trim() }
                    : null,
                Group: riskDetail.rows[0].Group ? { id: riskDetail.rows[0].Group.id, name: riskDetail.rows[0].Group.name } : null
            }
        };

        return response.success(req,res, {msgCode:'API_SUCCESS',data: transformedRiskDetail}, httpStatus.OK);

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.riskList = async(req,res)=>{
    try{
        const { Risk, User, Group}=db.models;
        const { page, size, start_date, end_date, search, sort_by = 'createdAt', sort_order = 'DESC' , module, category , source } = req.query;

        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];

        const riskCondition = {
            customer_id:req.data.customer_id,
            entity_id:req.query.entity_id
        }

        //search condition
        
        if (search) {
            riskCondition[Op.or] = [
                { title: { [Op.iLike]: `%${search}%` } },
                { description: { [Op.iLike]: `%${search}%` } },
                // { module: { [Op.eq]: search } },
                // { stage: { [Op.eq]: search } },
                // { category: { [Op.eq]: search } },
                // { source: { [Op.eq]: search } },
                // sequelize.where(sequelize.cast(sequelize.col('module'), 'TEXT'), { [Op.eq]: search }) ,
            ];
        }

        //Filters

        if(module){
            riskCondition[Op.or] = [
                { module: { [Op.eq]: module } },
            ];
        }
        if(category){
            riskCondition[Op.or] = [
                { category: { [Op.eq]: category } }
            ];
        }
        if(source){
            riskCondition[Op.or] = [
                { source: { [Op.eq]: source } },
            ];
        }

        //date filter

        if (start_date) {
            riskCondition = {
                createdAt: {
                    [Op.gte]: req.query.start_date
                }
            }
        }
        if (end_date) {
            riskCondition = {
                createdAt: {
                    [Op.lte]: req.query.end_date
                }
            }
        }

        if (start_date && end_date) {
            dutyCondition = { createdAt: { [Op.between]: [start_date, end_date] } }
        }
        
        const list = await privacyOpsService.getDetailWithMultiAssociate(Risk, User, Group, riskCondition, {}, {}, {}, ['id', 'firstName', 'lastName'], ['id', 'name'], limit, offset, order);
        if(!list.rows){
            return response.error(req,res,{msgCode:'DUTY_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }
        

        return response.success(req,res, {msgCode:'API_SUCCESS',data:list}, httpStatus.OK); 

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
// exports.countDuties = async(req,res)=>{
//     try{
//         const {Duties}=db.models;
//         const Data = await commonService.getListGroupBy(
//             Duties, 
//             { customer_id: req.data.customer_id }, 
//             ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
//             ['status']
//         );

//         let totalCount = 0;
//         let activeCount = 0;
//         let archiveCount = 0;
        

//         Data.forEach(row => {
//             const count = parseInt(row.count || 0);
//             totalCount += count;

//             switch(row.status) {
//                 case 'OPEN':
//                     activeCount = count;
//                     break;
//                 case 'COMPLETED':
//                     archiveCount = count;
//                     break;
//             }
//         });

//         const data = [
//             { 
//                 duty: 'TOTAL', 
//                 count: String(totalCount)
//             },
//             { 
//                 duty: 'OPEN', 
//                 count: String(activeCount)
//             },
//             { 
//                 duty: 'ARCHIVE', 
//                 count: String(archiveCount)
//             }
//         ];
//         return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);

//     }
//     catch(error){
//         console.log("error",error);
//         return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);

//     }
// }