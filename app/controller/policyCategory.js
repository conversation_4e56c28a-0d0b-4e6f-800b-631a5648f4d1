const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const policyService = require('../services/policy');
// const roleService = require('../services/role');
// const { USER_ROLE } = require('../constant/common');
const { Op } = require('sequelize');
// const sequelize = require('sequelize');


exports.getCategory = async (req, res) => {
    try {
        const { PolicyCategory } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order='DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);

        //implementing searching and sorting  in the query itself to reduce complexity of code
        let categoryCondition = { };
        if (search) {
            categoryCondition[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
              ];
        }

        const order = [[sort_by, sort_order]];

        const getCategoryData = await commonService.getList(PolicyCategory, categoryCondition, { exclude: ['createdAt', 'updatedAt', 'deletedAt'] }, limit, offset, order);
        return response.success(req, res, { msgCode: "CATEGORY_FETCHED", data: getCategoryData }, httpStatus.OK);
    } catch (error) {
        console.log("getCategory", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};