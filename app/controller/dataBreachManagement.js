const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { Op, Sequelize, where } = require('sequelize');
const { getPagination, getPagination2 } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { sendMailWithMultipleAttachments,sendMailsWithMultipleAttachments, sendMail, sendMailWithAttach} = require('../config/email');
const ExcelJS = require('exceljs');
const path = require("path");
const uploadDocument = require('../utils/s3-bucket');
const { deleteFile } = require('../utils/delete-files');
const axios = require('axios');
const { signToken } = require('../config/helper');
const CryptoJS = require('crypto-js');
const { transformAuditData, createExcelForAuditData } = require('../utils/helper');
const dsrConfig = require('../config/dsr-config');
const moment = require('moment');
const https = require('https');
const { USER_ROLE, CLIENT_ONBOARD, MESSAGE, ONBOARDING_STATUS } = require('../constant/common');
const { automationOnCompleteTask } = require('../utils/dsr-helper');

function displayDate(dateString){
    const date = new Date(dateString);

    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getUTCFullYear();

    // Format as DD-MM-YYYY
    const formattedDate = `${day}-${month}-${year}`;
    return formattedDate;
}

exports.createIncident = async (req, res) => {
    const dbTrans = await db.transaction();
    try {

        const { DataBreachManagement, DataBreachDocument } = db.models;
           
        req.body.user_id = req.data.userId;
        req.body.customer_id = req.data.customer_id; 
       
        //get lastinserted id 
        const lastInsertedReq = await dsrService.getOneRecord(DataBreachManagement, {}, null, null, null, [['id', 'DESC']]);
        let lastId = 1;
        if(lastInsertedReq.id){
            lastId = lastInsertedReq.id+1;
        }        
        const currentDate = new Date().toISOString().split('T')[0]; // Get YYYY-MM-DD format
        req.body.breach_id = `DB-${currentDate}-${lastId}`;

        const createIncident = await commonService.addDetail( DataBreachManagement , req.body, dbTrans);
        if(!createIncident){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_INCIDENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //insert documents
        if(req.body.documents){
            if(req.body.documents.length > 0){
                const bulkData = [];
                req.body.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        data_breach_id: createIncident.id
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(DataBreachDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
       
        return response.success(req, res, { msgCode: "INCIDENT_CREATED", data: createIncident }, httpStatus.CREATED, dbTrans);
      

    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getAllDBMList = async (req, res) => {
    try {
        const { DataBreachManagement, User} = db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC', business_unit } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};
        
        let dsrReqCondition = {customer_id : req.data.customer_id};
        let dsrConditionForTask = {};
        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            let checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.


        if (search) {
            dsrReqCondition = { ...dsrReqCondition, ...{ title: { [Op.iLike]: `%${search}%` } } };
        }

        //filter
        business_unit ? dsrReqCondition['business_unit'] = business_unit : ''
  
        let DBMList = null
        if(role == "guest"){
            
            DBMList = await commonService.getList(DataBreachManagement, dsrReqCondition, {}, limit, offset, order);
        } else {
            
            DBMList = await commonService.getList2(DataBreachManagement, User, dsrReqCondition, {}, {}, ['firstName', 'lastName'], limit, offset, order);
        }

        if (!DBMList) {
            return response.error(req, res, { msgCode: 'INCIDENT_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
                 
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: DBMList }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getBreachById = async (req, res) => {
    try {
        const { DataBreachManagement, DataBreachDocument, DataBreachJurisdictionNotify } = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dsrReqCondition =  {id: req.params.id, customer_id : req.data.customer_id,}   
               
        let getRequest = null;

      
        getRequest = await dsrService.getSingleLeftJoinDataThree(DataBreachManagement, DataBreachDocument, DataBreachJurisdictionNotify, dsrReqCondition, '', '', {}, ['id', 'url', 'original_name'], ['id', 'data_breach_management_id', 'breach_identification_no', 'have_you_filled_up_the_branch_form'], limit, offset, order);
      

        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getBreachCount = async (req, res) => {
    try {
        const { DataBreachManagement } = db.models;
        let condition = { customer_id: req.data.customer_id };

        const totalBreaches = await DataBreachManagement.count({ where: condition });

        const openBreaches = await DataBreachManagement.count({
            where: {...condition, status: "OPEN" }
        });

        const closedBreaches = totalBreaches - openBreaches;
        
        return response.success(req, res, { msgCode: "DASHBOARD_BREACH_COUNT", data: { totalBreaches, openBreaches, closedBreaches } }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getBreachStatusDistribution = async (req, res) => {
    try {
        const { DataBreachManagement } = db.models;
        let condition = { customer_id: req.data.customer_id,
            date_of_incident: {
                [Op.gte]: Sequelize.fn(
                    'DATE_TRUNC',
                    'year',
                    Sequelize.fn('NOW')
                )
            }
          };

        const breaches = await DataBreachManagement.findAll({
            where: condition,
            attributes: ['date_of_incident', 'status']
        });

        const monthLabels = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        let monthlyData = monthLabels.map(month => ({ month, openCount: 0, closedCount: 0 }));
        
        breaches.forEach(breach => {
            let monthIndex = new Date(breach.date_of_incident).getMonth();
            if (breach.status === "OPEN") {
                monthlyData[monthIndex].openCount++;
            } else {
                monthlyData[monthIndex].closedCount++;
            }
        });

        return response.success(req, res, { msgCode: "DASHBOARD_BREACH_STATUS_DISTRIBUTION", data: monthlyData }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getSeverityDistribution = async (req, res) => {
    try {
        const { DataBreachManagement } = db.models;
        let condition = { customer_id: req.data.customer_id };

        const breaches = await DataBreachManagement.findAll({
            where: condition,
            attributes: ['severity_level']
        });

        let majorCount = 0;
        let minorCount = 0;

        breaches.forEach(breach => {
            if ( breach.severity_level === "MAJOR" ){
                majorCount++;
            } else if ( breach.severity_level === "MINOR" ){
                minorCount++;
            }
        });

        const severityData = { majorCount, minorCount };
        
        return response.success(req, res, { msgCode: "DASHBOARD_SEVERITY_DISTRIBUTION", data: severityData }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
exports.uploadDocuments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
       
        //adjusting the name of the file to store in database
        const bulkData = [];
        req.body?.uploadData.forEach(data => {
            bulkData.push({
                original_name: data.originalName,
                url: data.url
            })
        })

        return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: bulkData }, httpStatus.CREATED, dbTrans);

    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


exports.addAction = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DataBreachAction } = db.models;
        const { data_breach_id , title, description, assignee_id, department_id, assigned_date, deadline_date } = req.body;

        const newAction = await DataBreachAction.create({ data_breach_id, title, description, assignee_id, department_id, assigned_date, deadline_date, customer_id: req.data.customer_id, user_id: req.data.userId }, { transaction: dbTrans });
        await dbTrans.commit();

        return response.success(req, res, { msgCode: "CREATE_ADD_ACTION", data: newAction }, httpStatus.CREATED);
      
    } catch (err) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateIncident = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DataBreachManagement } = db.models;

        let incId = req.params.id 

        const check = await commonService.findByCondition(DataBreachManagement, { id: incId});
        if(!check) {
            return response.error(req, res, { msgCode: 'INCIDENT_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const update = await commonService.updateData(DataBreachManagement, req.body, {id: incId }, dbTrans);

        if(!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: 'INCIDENT_UPDATED', data: update[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.addDataBreachNotification = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DataBreachJurisdictionNotify } = db.models;

        req.body.user_id = req.data.userId;
        req.body.customer_id = req.data.customer_id; 

        let findCond = {
            data_breach_management_id : req.body.data_breach_management_id,
            applicable_jurisdiction_id : req.body.applicable_jurisdiction_id
        }

        let addDataBreachNotification;
        const checkRecordExist = await dsrService.getOneRecord(DataBreachJurisdictionNotify, findCond, null, null, null, [['id', 'DESC']]);
        if(checkRecordExist){
            addDataBreachNotification = await commonService.updateData(DataBreachJurisdictionNotify, req.body, {id: checkRecordExist.id }, dbTrans);
        } else {
            addDataBreachNotification = await commonService.addDetail( DataBreachJurisdictionNotify , req.body, dbTrans);
            if(!addDataBreachNotification){           
                return response.error( req, res, { msgCode: 'ERROR_CREATING_BREACH_NOTIFICATION' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }        
       
        return response.success(req, res, { msgCode: "ADD_DATA_BREACH_NOTIFICATION", data: addDataBreachNotification }, httpStatus.CREATED, dbTrans);
        
    } catch (err) {        
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getActionListing = async (req, res) => {
    try {
        const { addAction } = db.models;
        let condition = { customer_id: req.data.customer_id, data_breach_id : req.params.data_breach_id};

        const checkRecordExist = await dsrService.getAllRecord(addAction, condition, null, null, null, [['id', 'DESC']]);
        
        return response.success(req, res, { msgCode: "GET_ACTION_LISTING", data: checkRecordExist }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);   
    }
};