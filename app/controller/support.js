const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const supportService= require('../services/support');
const { getPagination } = require('../config/helper');
const { Op } = require('sequelize');
const authConstant = require('../constant/auth');
const uploadDocument = require('../utils/s3-bucket');
const { deleteFile } = require('../utils/delete-files');


exports.createTicket = async(req, res) =>{
    const dbTrans = await db.transaction();
    try{
        const { Ticket , TicketDocument } = db.models;
        req.body.reporter_id = req.data.userId;
        const ticket = await commonService.addDetail(Ticket,req.body , dbTrans);
        if(!ticket){
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        // console.log(req.body);
        if (!req.files.length) {
            return response.success(req, res, { msgCode: "TICKET_RAISED", data: ticket }, httpStatus.OK , dbTrans);
        }
        const imagesArrayURL = req.files?.map((file) => file.path)
        // upload documents to s3
        const uploadDocuemntToS3 = await uploadDocument.uploadToS3(`${imagesArrayURL[0]}`);
        if (uploadDocuemntToS3.status != true) {
            // delete file from local
            if (req.files[0]) {
                // delete file from local
                await deleteFile(req.files[0].path)
            }
            console.log('S3 Error', uploadDocuemntToS3)
            return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        if (req.files[0]) {
            // delete file from local
            await deleteFile(req.files[0].path)
        }
        req.body.original_name = req.files[0].originalname;
        req.body.url = uploadDocuemntToS3.data.Location;
        req.body.ticket_id = ticket.id;
        //adding to the database
        const documentUpload = await commonService.addDetail(TicketDocument, req.body, dbTrans);
        if (!documentUpload) {
            return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        ticket.data = documentUpload;
        return response.success(req, res, { msgCode: "TICKET_RAISED", data: ticket }, httpStatus.OK , dbTrans);
    }catch(error){
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


exports.updateTicket = async(req, res) =>{
    const dbTrans = await db.transaction();
    try{
        const { Ticket , TicketDocument , AuditLog ,User} = db.models;
        const ticket = await commonService.findByCondition(Ticket,{id: req.params.id} , ['id','name']);
        if(!ticket){
            return response.error(req, res, { msgCode: 'TICKET_NOT_FETCHED' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        const updatedTicket = await commonService.updateData(Ticket, req.body ,{id: req.params.id} ,dbTrans);
        if(!updatedTicket[1]){
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST,dbTrans);
        }

        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!user) {
            return response.error(req, res, { msgCode: 'AUDIT_ERROR' }, httpStatus.NOT_FOUND, dbTrans);
        }

        // maintaining Audit data
        const auditData = [];
        if (ticket.type !== updatedTicket[1].type) {
            auditData.push({
                type: 'SUPPORT',
                type_id: ticket.id,
                action: `Ticket type updated by ${user.firstName +" "+ user.lastName}`,
                action_by_id: req.data.userId
            });
        }
        if (ticket.department_id !== updatedTicket[1].department_id) {
            auditData.push({
                type: 'SUPPORT',
                type_id: ticket.id,
                action: `Ticket depertment updated by ${user.firstName +" "+ user.lastName}`,
                action_by_id: req.data.userId
            });
        }
        if (ticket.assignee_id !== updatedTicket[1].assignee_id) {
            auditData.push({
                type: 'SUPPORT',
                type_id: ticket.id,
                action: `Ticket assignee updated by ${user.firstName +" "+ user.lastName}`,
                action_by_id: req.data.userId
            });
        }
        if (ticket.priority !== updatedTicket[1].priority) {
            auditData.push({
                type: 'SUPPORT',
                type_id: ticket.id,
                action: `Ticket priority updated by ${user.firstName +" "+ user.lastName}`,
                action_by_id: req.data.userId
            });
        }
        if (ticket.status !== updatedTicket[1].status) {
            auditData.push({
                type: 'SUPPORT',
                type_id: ticket.id,
                action: `Ticket status updated by ${user.firstName +" "+ user.lastName}`,
                action_by_id: req.data.userId
            });
        }


        if (!req.files.length) {
            return response.success(req, res, { msgCode: "TICKET_RAISED", data: updatedTicket[1] }, httpStatus.OK , dbTrans);
        }

        const imagesArrayURL = req.files?.map((file) => file.path)
        // upload documents to s3
        const uploadDocuemntToS3 = await uploadDocument.uploadToS3(`${imagesArrayURL[0]}`);
        if (uploadDocuemntToS3.status != true) {
            // delete file from local
            if (req.files[0]) {
                // delete file from local
                await deleteFile(req.files[0].path)
            }
            console.log('S3 Error', uploadDocuemntToS3)
            return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        if (req.files[0]) {
            // delete file from local
            await deleteFile(req.files[0].path)
        }
        req.body.original_name = req.files[0].originalname;
        req.body.url = uploadDocuemntToS3.data.Location;
        req.body.ticket_id = ticket.id;
        //adding to the database
        const documentUpload = await commonService.addDetail(TicketDocument, req.body, dbTrans);
        if (!documentUpload) {
            return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        updatedTicket[1].data = documentUpload;

        auditData.push({
            type: 'SUPPORT',
            type_id: ticket.id,
            action: `Document added by ${user.firstName +" "+ user.lastName}`,
            action_by_id: req.data.userId
        });
        // Adding audit log
        const audit = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
        if (!audit) {
            return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TICKET_UPDATED", data: updatedTicket[1] }, httpStatus.OK , dbTrans);
    }catch(error){
        console.log(error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.ticketDetails = async(req, res) =>{
    try{
        const { Ticket,TicketComments,TicketDocument,  Departments , User} = db.models;
        const ticket = await supportService.getListWithMultipleAssociate(Ticket, Departments, User ,'Reporter', User ,'Assignee' ,TicketDocument, { id: req.params.id }, {},{}, {}, {},['id', 'name', 'description', 'status', 'type','priority', 'reporter_id', 'assignee_id'] ,['name'] , ['firstName', 'lastName'], ['firstName', 'lastName'] ,['original_name', 'url']);
        if(!ticket){
            return response.error(req, res, { msgCode: 'TICKET_NOT_FETCHED' }, httpStatus.BAD_REQUEST);
        }
        
        ticket.rows[0].assignee = ticket?.rows[0]?.Assignee?.firstName + ' ' + ticket?.rows[0]?.Assignee?.lastName;
        ticket.rows[0].assigneeInitials = ticket?.rows[0]?.Assignee?.firstName.charAt(0).toUpperCase() + ticket?.rows[0]?.Assignee?.lastName.charAt(0).toUpperCase();
        ticket.rows[0].reporter = ticket?.rows[0]?.Reporter?.firstName + ' ' + ticket?.rows[0]?.Reporter?.lastName;
        ticket.rows[0].reporterInitials = ticket?.rows[0]?.Reporter?.firstName.charAt(0).toUpperCase() + ticket?.rows[0]?.Reporter?.lastName.charAt(0).toUpperCase();
        //beautifying the response
        delete ticket.count;
        delete ticket.rows[0].Reporter;
        delete ticket.rows[0].Assignee;
        // const commentListing = await commonService.getList(TicketComments , {ticket_id: ticket?.rows[0].id} ,['comment', 'user_id', 'ticket_id']);
        // ticket.rows[0].comment = commentListing;

        const commentListing = await commonService.getListAssociateWithCount(TicketComments ,User , {ticket_id: ticket?.rows[0].id} ,{} ,['comment'] , ['firstName', 'lastName']);
        
        commentListing.rows.forEach(comment => {
            comment.User.name = comment?.User?.firstName+ ' ' +comment?.User?.lastName;
            comment.User.initials = comment?.User?.firstName.charAt(0).toUpperCase() + comment?.User?.lastName.charAt(0).toUpperCase();
            delete comment.User.firstName;
            delete comment.User.lastName;
          });

        ticket.rows[0].comments = commentListing;
        return response.success(req, res, { msgCode: "API_SUCCESS", data: ticket }, httpStatus.OK );
    }catch(error){
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};



exports.ticketListing = async(req , res) => {
    try{
        const { Departments, Ticket , User , Role } = db.models;
        let { page, size, search ,status , sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date } = req.query;
        let ticketCondition = {  customer_id : req.params.id };

        // fetching the role name
        const { role_name } = await commonService.findByCondition(Role , {id: req.data.roleId} );
        req.data.roleName = role_name;
        if(req.data.roleName !== authConstant.USER_ROLE[2]){
            ticketCondition.reporter_id = req.data.userId ;
        }

        // implementation of searching and the filteration of data
        if (search) {
            ticketCondition[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
            ];
        }
        if (status) {
            ticketCondition.status = status
        }

        if (start_date) {
            ticketCondition = {
                createdAt: {
                    [Op.gte]: req.query.start_date
                }
            }
        }
        if (end_date) {
            ticketCondition = {
                createdAt: {
                    [Op.lte]: req.query.end_date
                }
            }
        }

        if (start_date && end_date) {
            ticketCondition = { createdAt: { [Op.between]: [start_date, end_date] } }
        }

        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];

        const tickets = await supportService.getTicketData(Ticket , User,'Reporter' , Departments , ticketCondition ,{} , {}, ['id','name','description', 'status', 'priority', 'createdAt'], ['firstName' , 'lastName'], ['name'] , limit , offset , order );
        // const tickets = await commonService.getListWithMultiAssociate(Ticket , Departments ,User ,{ customer_id : req.params.id } ,{} , {}, ['id','name','description', 'status', 'priority', 'createdAt'], ['name'] , ['firstName' , 'lastName'] );
        if(!tickets){
            return response.error(req, res, { msgCode: 'ERROR_IN_TICKET_DATA' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "TICKETS_FETCHED", data: tickets }, httpStatus.OK);
    }catch (error) {
        console.log("support", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.dashboard = async(req, res) => {
    try{
        const { Ticket , Departments , Role} = db.models;
        let dashboardCondition = {customer_id : req.data.customer_id};
        // fetching the role name
        const { role_name } = await commonService.findByCondition(Role , {id: req.data.roleId} );
        req.data.roleName = role_name;
        if(req.data.roleName == authConstant.USER_ROLE[2]){
            // dashboardCondition.customer_id = req.data.customer_id ;
        }else{
            const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
            if (departments && departments.rows.length > 0) {
                // Department head can only see their own department associated ticket and also in which he is , author or reviewer
                const departmentIds = departments?.rows?.map(department => department.id);
                dashboardCondition.department_id = { [Op.in]: departmentIds };
            }else{
                return response.error(req, res, { msgCode: 'ERROR_IN_TICKET_DATA' }, httpStatus.BAD_REQUEST);
            }
        }
        const ticketData = await supportService.getListGroupBy(Ticket, dashboardCondition , ['status'] ,['status']);
        if (!ticketData) {
            return response.error(req, res, { msgCode: 'ERROR_IN_TICKET_DATA' }, httpStatus.BAD_REQUEST);
        }
        const ticketPriorityData = await supportService.getListGroupBy(Ticket, dashboardCondition , ['priority'] ,['priority']);
        if (!ticketPriorityData) {
            return response.error(req, res, { msgCode: 'ERROR_IN_TICKET_DATA' }, httpStatus.BAD_REQUEST);
        }
        const ticketStats = {
            ticketData,
            ticketPriorityData
        }
        
        return response.success(req, res, { msgCode: "DASHBOARD_FETCHED", data: ticketStats }, httpStatus.OK);

    }catch (error) {
        console.log("dashboard", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.priorityWiseTickets = async(req, res) => {
    try{
        const { Ticket , Departments , Role } = db.models;
        let priorityCondition ={ priority: req.params.priority };
        const { role_name } = await commonService.findByCondition(Role , {id: req.data.roleId} );
        req.data.roleName = role_name;
        if(req.data.roleName == authConstant.USER_ROLE[2]){
            priorityCondition.customer_id = req.data.customer_id;
        }else{
            const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
            if (departments && departments.rows.length > 0) {
                // Department head can only see their own department associated ticket and also in which he is  author or reviewer
                const departmentIds = departments?.rows?.map(department => department.id);
                priorityCondition.department_id = { [Op.in]: departmentIds };
            }else{
                priorityCondition.reporter_id = req.data.userId ;
            }
        }
        const tickets = await commonService.getList(Ticket, priorityCondition, ['id', 'name', 'priority', 'description']);
        if(!tickets){
            return response.error(req, res, { msgCode: 'ERROR_IN_TICKET_DATA' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "TICKETS_FETCHED", data: tickets }, httpStatus.OK);       

    }catch(error){
        console.log("error" , error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.comment = async(req ,res) => {
    const dbTrans = await db.transaction();
    try{
        const { Ticket , TicketComments , User ,AuditLog} = db.models;
        const ticket = await commonService.findByCondition(Ticket,{id: req.body.ticket_id} , ['id','name']);
        if(!ticket){
            return response.error(req, res, { msgCode: 'TICKET_NOT_FETCHED' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        req.body.user_id = req.data.userId;
        const ticketComment = await commonService.addDetail(TicketComments ,req.body, dbTrans);
        if(!ticketComment){
            return response.error(req, res, { msgCode: 'COMMENT_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!user) {
            return response.error(req, res, { msgCode: 'AUDIT_ERROR' }, httpStatus.NOT_FOUND, dbTrans);
        }
        
        //Entry in Audit Log
        const auditData = {
            type: 'SUPPORT',
            type_id: ticket.id,
            action: `Comment Added by ${user.firstName +" "+ user.lastName}`,
            action_by_id: req.data.userId
        };
        
        const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
        if (!audit) {
            return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "COMMENT_SUCCESS", data: ticketComment }, httpStatus.OK,dbTrans);

    }catch(error){
        console.log("---->>>>",error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
        
    }
};

exports.toDoTickets = async(req,res) => {
    try{
        const { Ticket , User, Departments } = db.models;
        let { page, size , sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        
        const tickets = await supportService.getTicketData(Ticket , User,'Assignee' , Departments , { assignee_id: req.data.userId } ,{} , {}, ['id','name','description', 'status', 'priority', 'createdAt'], ['firstName' , 'lastName'], ['name'] , limit , offset , order );
        
        if(!tickets){
            return response.error(req, res, { msgCode: 'ERROR_IN_TICKET_DATA' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "TICKETS_FETCHED", data: tickets }, httpStatus.OK);
    }catch(error){
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR );
    }
};


exports.ticketHistory = async (req, res) => {
    try {
        const { AuditLog  } = db.models;

        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        
        const auditData = await commonService.getList(AuditLog , { type:'SUPPORT' , type_id: req.params.ticket_id }, ['id', 'action' ,'action_by_id', 'createdAt'] , limit, offset, order);
        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // Getting hte Initials
        auditData?.rows.forEach(row => {
            const words = row.action.split(" ");
            const lastTwoWords = words.slice(-2);
            if (lastTwoWords.length === 2) {
                const initials = lastTwoWords.map(word => word.charAt(0).toUpperCase()).join("");
                row.initial = initials;
            }
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


// exports.getCreateTicket = async(req, res) =>{
//     try{
//         const { User } = db.models;
//         const ticket = await commonService.findByCondition(User, {id: req.data.userId},['firstName', 'lastName' ] );
//         if(!ticket){
//             return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST);
//         }
//         ticket.reporter = ticket?.firstName+' '+ticket?.lastName;
//         delete ticket.firstName;
//         delete ticket.lastName;
//         return response.success(req, res, { msgCode: "TICKET_OPENED", data: ticket }, httpStatus.OK );
//     }catch(error){
//        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR );
//     }
// };


// exports.createTicket = async(req, res) =>{
//     const dbTrans = await db.transaction();
//     try{
//         const { Ticket } = db.models;
//         req.body.reporter_id = req.data.userId;
//         const ticket = await commonService.addDetail(Ticket,req.body , dbTrans);
//         if(!ticket){
//             return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
//         }
//         return response.success(req, res, { msgCode: "TICKET_RAISED", data: ticket }, httpStatus.OK , dbTrans);
//     }catch(error){
//         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
//     }
// };

// exports.ticketDocument = async (req, res) => {
//     const dbTrans = await db.transaction();
//     try {
//         const { TicketDocument } = db.models;
//         //adjusting the name of the file to store in database
//         req.body.original_name = req.body.originalName;
//         delete req.body.originalName;
//         req.body.ticket_id = req.params.id;
//         //adding to the database
//         const documentUpload = await commonService.addDetail(TicketDocument, req.body, dbTrans);
//         if (!documentUpload) {
//             return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
//         }
        
//         return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: documentUpload }, httpStatus.OK, dbTrans);
//     } catch (error) {
//         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
//     }
// };
