const ejs = require('ejs');
const { env } = require('../constant/environment');

exports.getTable = async (req, res) => {
    const { data, columns, page, size, search_key, sort_by } = req.body;
    const offset = (Number(page) - 1) * Number(size);
    let preparedData = data;

    if (search_key) {
        // Filter the data based on the search_key
        const filteredData = data.filter(row => {
            return columns.some(column => {
                return row[column] && row[column].toString().toLowerCase().includes(search_key.toLowerCase());
            });
        });

        preparedData = filteredData;
    }

    if (sort_by) {
        const sortedData = preparedData.sort((a, b) => {
            for (let key of sort_by) {
                if (a[key] < b[key]) return -1;
                if (a[key] > b[key]) return 1;
            }
            return 0;
        });

        preparedData = sortedData;
    }

    paginatedData = preparedData.slice(offset, offset + Number(size));

    const template = `
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
            }
            th {
                background-color: #f2f2f2;
                text-align: left;
            }
            #table-container {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
            }
        
            #pagination {
                margin-top: 20px;
                text-align: right;s
            }
        </style>
        <div id="table-container">
            <table id="table">
                <thead>
                <tr>
                    <% columns.forEach(column => { %>
                    <th><%= column %></th>
                    <% }); %>
                </tr>
                </thead>
                <tbody>
                <% paginatedData.forEach(row => { %>
                    <tr>
                        <% columns.forEach(column => { %>
                        <td><%= row[column] %></td>
                        <% }); %>
                    </tr>
                <% }); %>
                </tbody>
            </table>
        </div>
        <div id="pagination">
            <button id="first-page" <%= page === 1 ? 'disabled' : '' %> class="page-btn" data-page="1">First</button>
            <button id="prev-page" <%= page === 1 ? 'disabled' : '' %> class="page-btn" data-page="<%= page - 1 %>">Prev</button>
            <span>Page <%= page %> of <%= Math.ceil(data.length / size) %></span>
            <button id="next-page" <%= page === Math.ceil(data.length / size) ? 'disabled' : '' %> class="page-btn" data-page="<%= page + 1 %>">Next</button>
            <button id="last-page" <%= page === Math.ceil(data.length / size) ? 'disabled' : '' %> class="page-btn" data-page="<%= Math.ceil(data.length / size) %>">Last</button>
        </div>

        <script>
            document.getElementById('imported-screen').addEventListener('click', function(event) {
                if (event.target.classList.contains('page-btn')) {
                    const page = event.target.getAttribute('data-page');
            
                    fetch('${env.BASE_URL}/api/v1/table', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'x-api-key': '${req.headers['x-api-key']}',
                        },
                        body: JSON.stringify({
                            page: page,
                            size: <%= size %>,
                            ${search_key ? "search_key : '<%- search_key %>'," : ""}
                            ${sort_by ? `sort_by: ${JSON.stringify(sort_by)},` : ""}
                            data: <%- JSON.stringify(data) %>,
                            columns: <%- JSON.stringify(columns) %>
                        }),
                    })
                    .then(response => response.text())
                    .then(data => {
                        // Replace the current table with the new table
                        document.getElementById('imported-screen').innerHTML = data;
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                    });
                }
            });
        </script >
    `;
    const html = await ejs.render(template, { columns, paginatedData, search_key, page: Number(page), size: Number(size), sort_by, search_key, data: preparedData });

    return res.send(html);
};