const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const constant = require('../constant/PDA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
const { sendMail } = require('../config/email');
const CryptoJS = require('crypto-js');
exports.getForm = async (req, res) => {
    try{
        const { DsrFormRepository } = db.models;

        const formData = await commonService.findByCondition(DsrFormRepository, {customer_id : req.data.customer_id}, {});
        if(!formData){
            return response.error(req, res, { msgCode: 'FORM_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        
        return response.success( req, res, { msgCode: 'FORM_FETCHED', data: formData }, httpStatus.OK);
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.customForm =async (req, res) => {
    const dbTrans = await db.transaction(); 
    try{
        const { DsrFormRepository } = db.models;
        const data ={
            content : req.body.formData,
            url : req.body?.formURL,
            customer_id: req.data.customer_id,
            user_id: req.data.userId
        }
        
        const formData = await commonService.findByCondition(DsrFormRepository, {customer_id : req.data.customer_id}, {});
        if(formData){
            const updateForm = await commonService.updateData(DsrFormRepository, data, {customer_id : req.data.customer_id}, dbTrans);
            if(!updateForm){
                return response.error(req, res, { msgCode: 'FORM_UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }
            return response.success(req, res, { msgCode: 'FORM_UPDATED' , data:updateForm[1] }, httpStatus.OK, dbTrans);
        }

        const createForm = await commonService.addDetail(DsrFormRepository, data, dbTrans);
        if(!createForm){
            return response.error(req, res, { msgCode: 'FORM_CREATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: 'FORM_CREATED' , data:createForm }, httpStatus.OK, dbTrans);
    }catch(err){
        console.log(" error", err);
        return response.error( req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.createForm = async(req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DsrForms, DSRCategory, DSRCustomerCategory, DSRControls, DSRCustomerControls,RegulationsV2 } = db.models;
        const order = [['createdAt','ASC']]
        req.body.customer_id = req.data.customer_id;
        req.body.user_id = req.data.userId;
        if(req.body.authentication_type){
            req.body.authentication = true;
        }
       
        const createForm = await commonService.addDetailv2(DsrForms, req.body, dbTrans);
        if (!createForm) {
            return response.error(req, res, { msgCode: 'FORM_CREATE_ERROR' }, httpStatus.BAD_REQUEST,dbTrans);
        }
        if (req.body.regulation_id && req.body.regulation_id.length > 0) {
            // Find regulations based on provided IDs
            const regulations = await RegulationsV2.findAll({
                where: { id: req.body.regulation_id }
            });
            if (regulations.length > 0) {
                // Associate regulations with the form
                const associatedResult = await createForm.addRegulationsV2(regulations, { transaction: dbTrans });
               
            }
        }
        // const categories = await commonService.getList(DSRCategory, { is_custom: false }, {},null,null,order);
        // if (!categories || categories.rows.length === 0) {
        //     return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.BAD_REQUEST,dbTrans);
        // }

        // let categoryMap ={} ;
        // for (const category of categories.rows) {
        //     const categoryData = {
        //         name: category.name,
        //         category_id: category.id,
        //         customer_id: createForm.customer_id,
        //         is_custom: false,
        //         form_id: createForm.id,
        //     };
        //     const addCategory = await commonService.addDetail(DSRCustomerCategory, categoryData, dbTrans);

        //     if (!addCategory) {
        //         return response.error(req, res, { msgCode: 'CATEGORY_CREATE_ERROR' }, httpStatus.BAD_REQUEST,dbTrans);
        //     }
        //     categoryMap[category.id] = addCategory.id;;
        // }
        // let phoneControl = null;
        // if (req.body.authentication_type === "SMS"||req.body.authentication_type === "WHATSAPP") {
        //     phoneControl = await commonService.findByCondition(DSRControls,{ title: "Phone Number" } ,{});
            
        // }
        
        const controls = await commonService.getList(DSRControls, {}, {},null,null,order);
        if (!controls || controls.rows.length === 0) {
            return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST,dbTrans);
        }
        
        
        for (const control of controls.rows) {
            let isOptional = control.is_optional; // Default value

    // Check if authentication type is SMS or WHATSAPP and the question relates to a phone number
                if ((req.body.authentication_type === "SMS" || req.body.authentication_type === "WHATSAPP") 
                    && control.title.toLowerCase().includes("phone number")) { 
                    isOptional = false; 
                }
            const controlData = { 
                question_id: control.id,
                // category_id: categoryMap[control.category_id],
                form_id: createForm.id,
                customer_id: createForm.customer_id,
                is_custom: false,
                title: control.title,
                description: control.description,
                artifact_type: control.artifact_type,
                is_attachment: control.is_attachment,
                question: control.question,
                fields: control.fields,
                extra_input: control.extra_input,
                extra_input_type: control.extra_input_type,
                extra_input_fields: control.extra_input_fields,
                endpoint: control.endpoint,
                is_optional:isOptional,
                order:control.order,
                rules:null,
                rule_applied:null,
            };

            const addControl = await commonService.addDetail(DSRCustomerControls, controlData, dbTrans);
            if (!addControl) {
                return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST,dbTrans);
            }
        }

        // if (phoneControl) {
        //     const phoneControlData = {
        //         question_id: phoneControl.id,
        //         form_id: createForm.id,
        //         customer_id: createForm.customer_id,
        //         is_custom: false,
        //         title: phoneControl.title,
        //         description: phoneControl.description,
        //         artifact_type: phoneControl.artifact_type,
        //         is_attachment: phoneControl.is_attachment,
        //         question: phoneControl.question,
        //         fields: phoneControl.fields,
        //         extra_input: phoneControl.extra_input,
        //         extra_input_type: phoneControl.extra_input_type,
        //         extra_input_fields: phoneControl.extra_input_fields,
        //         endpoint: phoneControl.endpoint,
        //         is_optional:phoneControl.is_optional,
        //         order:phoneControl.order
        //     };
        //     const addPhoneQuestion = await commonService.addDetail(DSRCustomerControls, phoneControlData, dbTrans);
        //     if(!addPhoneQuestion){
        //         return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: createForm }, httpStatus.BAD_REQUEST,dbTrans);
        //     }
        // }

        await dbTrans.commit();

        // const formWithCategories = await DSRCustomerCategory.findAll({
        //     where: { form_id: createForm.id },
        //     include: [{ model: DSRCustomerControls, required: false }]
        // });
        

        return response.success(req, res, { msgCode: 'FORM_CREATED', data: createForm }, httpStatus.OK);
    } catch (err) {
        console.log("Error:", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }
};
// exports.getCategories = async (req,res)=>{
//     try{
//         const { DSRCustomerCategory } = db.models;
//         const order = [['createdAt','ASC']]
//         const formData = await commonService.getList(DSRCustomerCategory, {form_id : req.params.id}, ['id','name'],null,null,order);

//         if(!formData){
//             return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.BAD_REQUEST);
//         }

        
//         return response.success( req, res, { msgCode: 'CATEGORY_FETCHED', data: formData }, httpStatus.OK);
//     }catch(err){
//         console.log(" error", err);
//         return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
//     }
// }
exports.getControls = async(req,res)=>{
    try{
        const { DSRCustomerControls} = db.models;
        const order = [['order','ASC']]
        const controlCondition = {
            form_id : req.params.id,
            customer_id:req.data.customer_id,
        }
        const formData = await commonService.getList(DSRCustomerControls, controlCondition, {},null,null,order);

        if(!formData){
            return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        
        return response.success( req, res, { msgCode: 'CONTROLS_FETCHED', data: formData }, httpStatus.OK);
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.getFormv2 = async (req, res) => {
    try{
        const { DsrForms, Group } = db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);

        const order = [[sort_by,sort_order]]

        const formData = await dsrService.getMultiLeftJoinData(DsrForms, Group, {customer_id : req.data.customer_id}, {}, {}, ['id', 'name'], limit,offset,order);
        if(!formData){
            return response.error(req, res, { msgCode: 'FORM_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        
        return response.success( req, res, { msgCode: 'FORM_FETCHED', data: formData }, httpStatus.OK);
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
// exports.addCategory = async (req,res)=>{
//     const dbTrans = await db.transaction();
//     try{
//         const {DSRCustomerCategory} = db.models;
//         req.body.form_id = req.params.id;
//         req.body.customer_id=req.data.customer_id
//         req.body.is_custom = true;
//         const addCategory = await commonService.addDetail(DSRCustomerCategory,req.body,dbTrans);
//         if(!addCategory){
//             return response.error(req, res, { msgCode: 'CATEGORY_CREATE_ERROR' }, httpStatus.BAD_REQUEST,dbTrans);
//         }

//         return response.success( req, res, { msgCode: 'CATEGORY_ADDED', data: addCategory }, httpStatus.OK,dbTrans);
//     }
//     catch(error){
//         console.log(" error", err);
//         return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
//     }
// }
exports.addCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DSRCustomerControls } = db.models;
        const data = req.body.questions;
        data.forEach(item => {
            item.is_custom = true;
        });
        const addedControls = await commonService.bulkAdd(DSRCustomerControls, data, dbTrans);
        if (!addedControls) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_CREATED", data: addedControls }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}
exports.deleteCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DSRCustomerControls } = db.models;
        const condition = {
            id: req.params.question_id,
            is_optional: true
          };
        const deleteControls = await commonService.deleteQuery(DSRCustomerControls,condition,dbTrans);
        if (!deleteControls) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_DELETED", data: deleteControls }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}
exports.updateCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DSRCustomerControls,DSRControls} = db.models;
        const { title, description, artifact_type, question, fields, is_attachment,rules,rule_applied} = req.body;
        let raw_question = null;
        const originalQuestion = await commonService.getDataAssociate(DSRControls, DSRCustomerControls, {}, { id: req.params.question_id }, {}, {});
        if (!originalQuestion) {
            raw_question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(DSRCustomerControls, { id: req.params.question_id  }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            raw_question = customQuestion;
        }

        const updatedValues = {
            title: title || raw_question.title,
            description: description || raw_question.description,
            artifact_type: artifact_type || raw_question.artifact_type,
            is_attachment: is_attachment || raw_question.is_attachment,
            question: question || raw_question.question,
            fields: fields || raw_question.fields,
            extra_input: raw_question.extra_input,
            extra_input_type: raw_question.extra_input_type,
            extra_input_fields: raw_question.extra_input_fields,
            is_custom: true,
            rules:rules||raw_question?.rules,
            rule_applied:rule_applied||raw_question?.rule_applied

        }

        const updatedControls = await commonService.updateData(DSRCustomerControls, updatedValues, { id: req.params.question_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}
exports.deleteForm = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const { DSRCustomerControls,DSRCustomerCategory,DsrForms} = db.models;
        const formId = req.params.form_id;
        
        const deleteForm = await commonService.deleteQuery(DsrForms,{id:formId,published:'NO'},dbTrans);
        if (!deleteForm) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_FORM" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const deleteControls = await commonService.deleteQuery(DSRCustomerControls,{form_id:formId},dbTrans);
        if (!deleteControls) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        return response.success(req, res, { msgCode: "FORM_DELETED" }, httpStatus.OK, dbTrans);

    }
    catch(error){
        console.log(error);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.publishCustomForm = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const { DsrForms,DSRCustomerControls,DSRCustomerCategory } = db.models;
        const checkForm = await commonService.findByCondition(DsrForms,{id:req.params.form_id},{});
        if(!checkForm){
            return response.error(req, res, { msgCode: "FORM_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
        if(checkForm.version&&checkForm.published=='YES'){
            return  response.error(req, res, { msgCode: "FORM_ALREADY_PUBLISHED" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let newVersionNumber;
        if(checkForm.parent_id === null){
            const formNameInitials = checkForm.name
                .split(' ')
                .map(word => word.charAt(0).toUpperCase())
                .join('');
            newVersionNumber=`${formNameInitials}-V1`;
        }
        else{
            const formCondition = {
                [Op.and]: [
                  {
                    [Op.or]: [
                      { parent_id: checkForm.parent_id },
                      { id: checkForm.parent_id }
                    ]
                  },
                  {
                    version: {
                      [Op.not]: null
                    }
                  }
                ]
              };

        const getFormList = await commonService.getListWithoutCount(DsrForms, formCondition , ['version'], null, null, [['id', 'DESC']]);
        if(!getFormList.length){
            return response.error(req, res, { msgCode: "FORM_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
        let highestVersion = getFormList.reduce((max, item) => {
            const parts = item.version.split('-V');
            const num = parseInt(parts[1]);
            if (!isNaN(num) && num > max.number) {
              return { prefix: parts[0], number: num };
            }
            return max;
          }, { prefix: '', number: -1 });
        
          newVersionNumber = `${highestVersion.prefix}-V${highestVersion.number + 1}`;
        }

        const data ={
            version:newVersionNumber,
            published:'YES',
            url:req.body.url
        }
        const addVersion  = await commonService.updateData(DsrForms,data,{id:req.params.form_id},dbTrans);
        if(!addVersion){
            return response.error(req, res, { msgCode: "FORM_NOT_PUBLISHED" }, httpStatus.NOT_FOUND, dbTrans);
        }
        return response.success(req, res, { msgCode: "FORM_PUBLISHED", data: addVersion[1] }, httpStatus.OK, dbTrans);
        // const newData = {
        //     customer_id:req.data.customer_id,
        //     user_id : req.data.userId,
        //     name: checkForm.name,
        //     busi_unit_id:checkForm.busi_unit_id,
        //     version:romanVersion
        // }
        // const createNewForm = await commonService.addDetail(DsrForms,newData,dbTrans);
        // if(!createNewForm){
        //     return response.error(req, res, { msgCode: 'FORM_CREATE_ERROR' }, httpStatus.BAD_REQUEST,dbTrans);
        // }


        // const categories = await commonService.getList(DSRCustomerCategory, { form_id:checkForm.id }, {});
        // if (!categories || categories.rows.length === 0) {
        //     return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.BAD_REQUEST,dbTrans);
        // }

        // let categoryMap ={} ;
        // for (const category of categories.rows) {
        //     const categoryData = {
        //         name: category.name,
        //         category_id: category.id,
        //         customer_id: createNewForm.customer_id,
        //         is_custom: category.is_custom,
        //         form_id: createNewForm.id,
        //     };
        //     const addCategory = await commonService.addDetail(DSRCustomerCategory, categoryData, dbTrans);

        //     if (!addCategory) {
        //         return response.error(req, res, { msgCode: 'CATEGORY_CREATE_ERROR' }, httpStatus.BAD_REQUEST,dbTrans);
        //     }
        //     categoryMap[category.id] = addCategory.id;;
        // }

        // const controls = await commonService.getList(DSRCustomerControls, { form_id:checkForm.id  }, {});
        // if (!controls || controls.rows.length === 0) {
        //     return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST,dbTrans);
        // }

        // for (const control of controls.rows) {
            
        //     const controlData = { 
        //         question_id: control.question_id,
        //         category_id: categoryMap[control.category_id],
        //         form_id: createNewForm.id,
        //         customer_id: createNewForm.customer_id,
        //         is_custom: control.is_custom,
        //         title: control.title,
        //         description: control.description,
        //         artifact_type: control.artifact_type,
        //         is_attachment: control.is_attachment,
        //         question: control.question,
        //         fields: control.fields,
        //         extra_input: control.extra_input,
        //         extra_input_type: control.extra_input_type,
        //         extra_input_fields: control.extra_input_fields,
        //         endpoint: control.endpoint
        //     };

        //     const addControl = await commonService.addDetail(DSRCustomerControls, controlData, dbTrans);
        //     if (!addControl) {
        //         return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST,dbTrans);
        //     }
        // }
        // await db.commit();

        // return response.success(req, res, { msgCode: "NEW_FORM_VERSION_PUBLISHED"}, httpStatus.OK, dbTrans);


    }
    catch(error){
        console.log("error",error);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.getPublishedForm = async(req,res)=>{
    try{
        const {DsrForms,Customer,DSRCustomerControls}=db.models;
        const form_id = req.params.form_id;
        const customer_id = req.params.customer_id;
        
        const secretKey = '321@123';
        const encryptedCustomerId = customer_id.replaceAll('Por21Ld', '/');
        const encryptedFormId = form_id.replaceAll('Por21Ld', '/');
        const custId = CryptoJS.AES.decrypt(encryptedCustomerId, secretKey);  
        const formId = CryptoJS.AES.decrypt(encryptedFormId, secretKey);
        const customerId = parseInt(custId.toString(CryptoJS.enc.Utf8));
        const decryptFormId = parseInt(formId.toString(CryptoJS.enc.Utf8));
        const checkCustomer = await commonService.findByCondition(Customer, { id: customerId });
        if (!checkCustomer) {
            return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        const controlsData = await commonService.getDataAssociate(DsrForms,DSRCustomerControls,{id:decryptFormId,customer_id:customerId},{},{},{});
        if(!controlsData){
            return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        if (controlsData.DSRCustomerControls) {
            controlsData.DSRCustomerControls = controlsData.DSRCustomerControls.sort((a, b) => a.order - b.order);
        }
        return response.success(req,res,{msgCode:'FORM_FETCHED', data:controlsData},httpStatus.OK); 
    }
    catch(error){
        console.log("error",error);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
    
}


// exports.getPredefinedControls = async(req,res)=>{
//     try{
//         const{DsrForms,DSRControls} = db.models;

//         const fetchForm = await commonService.findByCondition(DsrForms,{id:req.params.form_id},{});
//         if (!fetchForm) {
//             return response.error(req, res, { msgCode: 'FORM_NOT_FOUND' }, httpStatus.BAD_REQUEST);
//         }
//         console.log(fetchForm)
//         let controls;
//         console.log(fetchForm.authentication)
//         if(fetchForm.authentication===null){
//             controls = await commonService.getList(DSRControls,{is_custom:false},{}); 
           
//         }
//         else{
//             controls = await commonService.getList(DSRControls,{},{}); 
//         }
        
//         controls?.rows.map(item=>{
//             item.question_id = item.id;
//         })
    

//         return response.success(req, res, { msgCode: "CONTROLS_FETCHED",data:controls}, httpStatus.OK);
//     }
//     catch(error){
//         console.log("error",error);
//         return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
//     }
// }
exports.updateFormContent = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const{DsrForms , DSRCustomerControls} = db.models;
        const formData = await commonService.findByCondition(DsrForms,{id:req.params.form_id},{});
        if(!formData){
            return response.error(req, res, { msgCode: "FORM_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);

        }
        if(formData.published === 'YES'){
            let parentId;
            if(formData.parent_id === null){
                parentId = formData.id;
            }
            else{
            parentId = formData.parent_id;
            }    
            const newVersion = await commonService.addDetail(DsrForms,{
                customer_id:formData.customer_id,
                user_id:formData.user_id,
                name:formData.name,
                busi_unit_id:formData.busi_unit_id,
                version:null,
                parent_id:parentId,
                authentication:formData.authentication,
                authentication_type:formData.authentication_type,
                regulation_id:formData.regulation_id,
                content:req.body.content,
                published:'NO',
            },dbTrans);

            if(!newVersion){
                return response.error(req, res, { msgCode: 'FORM_CREATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            const controls = await commonService.getListWithoutCount(DSRCustomerControls, { form_id: req.params.form_id }, {});
            if (!controls || controls.length === 0) {
                return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
            }
            let data =[];
            controls.map(item=>{
                data.push({
                    question_id:item.question_id,
                    category_id:item.category_id,
                    form_id:newVersion.id,
                    customer_id:item.customer_id,
                    is_custom:item.is_custom,
                    title:item.title,
                    description:item.description,
                    artifact_type:item.artifact_type,
                    is_attachment:item.is_attachment,
                    question:item.question,
                    fields:item.fields,
                    extra_input:item.extra_input,
                    extra_input_type:item.extra_input_type,
                    extra_input_fields:item.extra_input_fields,
                    endpoint:item.endpoint,
                    is_optional:item.is_optional,
                    order:item.order,
                    rules:item.rules,
                    rule_applied:item.rule_applied,
                });
            });
            const addControls = await commonService.bulkAdd(DSRCustomerControls, data, dbTrans);
            if (!addControls) {
                return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            return response.success(req, res, { msgCode: "FORM_VERSION_CREATED", data:newVersion }, httpStatus.OK,dbTrans);

        }
        const updateContent = await commonService.updateData(DsrForms,{content:req.body.content},{id:req.params.form_id},dbTrans);
        if(!updateContent[1]){
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "FORM_UPDATED"}, httpStatus.OK,dbTrans);

    }
    catch(error){
        console.log("error",error);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }
}

exports.updateFieldOrder = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const {DsrForms,Customer,DSRCustomerControls}=db.models;
        const form_id = req.params.form_id;

        const checkForm = await commonService.findByCondition(DSRCustomerControls,{form_id:req.params.form_id},{});
        if(!checkForm){
            return response.error(req, res, { msgCode: "FORM_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if(!req?.body?.orders){
            return response.error(req, res, { msgCode: "FORM_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        for(let fieldObj of req?.body?.orders){
            let data = {
                'order' : fieldObj.order
            }
            const updateForm = await commonService.updateData(DSRCustomerControls, data, {id : fieldObj.id}, dbTrans);
            if(!updateForm){
                return response.error(req, res, { msgCode: 'FORM_UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }               
        
        return response.success(req, res, { msgCode: "FORM_UPDATED"}, httpStatus.OK,dbTrans);
    }
    catch(error){
        console.log("error",error);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    
}

