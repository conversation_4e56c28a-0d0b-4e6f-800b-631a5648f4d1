const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const { Op } = require("sequelize");


exports.createProcesses = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { Processes, ROPA, Departments, GroupUser } = db.models;
        // Create department
        const check = await commonService.findByCondition(Processes, { name: req.body.name, department_id: req.body.department_id });
        if (check) {
            return response.error(req, res, { msgCode: 'PROCESS_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const newProcess = await commonService.addDetail(Processes, req.body, dbTrans);
        if (!newProcess) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_PROCESS' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (newProcess.spoc_id) {
            const department = await commonService.findByCondition(Departments, { id: newProcess.department_id }, ['group_id']);
            const checkGroupUser = await commonService.findByCondition(GroupUser, { group_id: department?.group_id, user_id: req.body.spoc_id });
            if (!checkGroupUser) {
                const groupUserData = {
                    group_id: department.group_id,
                    user_id: req.body.spoc_id
                }
                const updateGroupUser = await commonService.addDetail(GroupUser, groupUserData, dbTrans);
                if (!updateGroupUser) {
                    return response.error(req, res, { msgCode: 'ERROR_CREATING_GROUP_USER' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
        const ropa = {
            process_id: newProcess.id,
            status: 'Yet to Start',
            customer_id: req.body.customer_id,
            group_id: req.body.group_id,
            // is_already_performed: true
        }
        const newRopa = await commonService.addDetail(ROPA, ropa, dbTrans);
        if (!newRopa) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_ROPA' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "PROCESS_CREATED", data: newProcess }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating PROCESS', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.updateProcess = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { Processes, Departments, GroupUser } = db.models;
        // Check department
        const check = await commonService.findByCondition(Processes, { id: req.params.process_id, customer_id: req.data.customer_id });
        if (!check) {
            return response.error(req, res, { msgCode: 'PROCESS_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const update = await commonService.updateData(Processes, req.body, { id: req.params.process_id }, dbTrans);
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (req.body.spoc_id) {
            const department = await commonService.findByCondition(Departments, { id: update[1].department_id }, ['group_id']);
            const check = await commonService.findByCondition(GroupUser, { group_id: department?.group_id, user_id: req.body.spoc_id });
            if (!check) {
                const groupUserData = {
                    group_id: department.group_id,
                    user_id: req.body.spoc_id
                }
                const updateGroupUser = await commonService.addDetail(GroupUser, groupUserData, dbTrans);
                if (!updateGroupUser) {
                    return response.error(req, res, { msgCode: 'ERROR_CREATING_GROUP_USER' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        return response.success(req, res, { msgCode: "PROCESS_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

 exports.processList = async(req, res) => {
    try{
        const { Processes } = db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let processCondition = {department_id : req.params.department_id};
        if(search){
            processCondition = {...processCondition, ...{name:  { [Op.iLike]: `%${search}%` } }};
        }
        const list = await commonService.getList(Processes , processCondition , ['id','name' ,'parent_id' ,'spoc_id'] , limit, offset , order);
        if(!list){
            return response.error(req, res, { msgCode: 'PROCESS_NOT_FOUND' }, httpStatus.BAD_REQUEST );
        }
        return response.success(req, res, { msgCode: "PROCESS_LIST", data: list }, httpStatus.OK );
    }catch(err){
        console.log(err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR );

    }
};

exports.deleteProcess = async (req, res) => {
    const dbTrans = await db.transaction();

    try {
        const { ROPA, Processes } = db.models;
        const process_id = req.params.process_id;
        
        const checkROPA = await commonService.findByCondition(ROPA, {process_id: process_id, status: {
            [Op.ne]: 'Yet to Start'
        }});
        if(checkROPA){
            return response.error(req, res, { msgCode: 'CANNOT_DELETE_PROCESS' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const checkSubProcess = await commonService.findByCondition(Processes, { parent_id: process_id });
        if(checkSubProcess){
            return response.error(req, res, { msgCode: 'CANNOT_DELETE_PROCESS' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const deleteROPAs = await commonService.deleteQuery(ROPA, { process_id: process_id }, dbTrans);
        if(!deleteROPAs){
            return response.error(req, res, { msgCode: 'PROCESS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const deleteProcess = await commonService.deleteQuery(Processes, {id: process_id}, dbTrans);
        if(!deleteProcess){
            return response.error(req, res, { msgCode: 'PROCESS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        // console.log("ROPA EXISTS: ", isRopaExists);
        return response.success(req, res, { msgCode: "PROCESS_DELETED" }, httpStatus.OK, dbTrans);
    } catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
};