const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { sendMailWithMultipleAttachments,sendMailsWithMultipleAttachments, sendMail, sendMailWithAttach} = require('../config/email');
const ExcelJS = require('exceljs');
const path = require("path");
const uploadDocument = require('../utils/s3-bucket');
const { deleteFile } = require('../utils/delete-files');
const axios = require('axios');
const { signToken } = require('../config/helper');
const CryptoJS = require('crypto-js');
const { transformAuditData, createExcelForAuditData } = require('../utils/helper');
const dsrConfig = require('../config/dsr-config');
const moment = require('moment');
const https = require('https');
const { USER_ROLE, CLIENT_ONBOARD, MESSAGE, ONBOARDING_STATUS } = require('../constant/common');
const { automationOnCompleteTask } = require('../utils/dsr-helper');
const auditLogger = require('../utils/audit-logger');
function displayDate(dateString){
    const date = new Date(dateString);

    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getUTCFullYear();

    // Format as DD-MM-YYYY
    const formattedDate = `${day}-${month}-${year}`;
    return formattedDate;
}

exports.getAllRequestList = async (req, res) => {
    try {
        const { User, DsrRequest, DataSubject, DsrRequestType, country, RequestTypeStages, CustomRequestTask,DsrSentEmail} = db.models;
        const { status, page, size, search, sort_by = 'createdAt', sort_order = 'DESC', data_subject_id, request_type, assignee_id, start_date, end_date, filter_status } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};
       
        let dsrReqCondition = {};
        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            let checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.

        //get data for assignee
       
        if(role == "dpo"){
            dsrReqCondition =  {customer_id : req.data.customer_id, status : status ? status : 'PENDING'}  
        } else if((role == "task_assignee") || (role == "assignee")){
            
            
            //this condition for assigned request listing
            dsrReqCondition =  {
                customer_id : req.data.customer_id,
                [Op.or]: [
                    { assignee_id: req.data.userId },                        
                ],
                //status: { [Op.notIn]: ['REJECTED_IN_PROGRESS', 'REJECTED'] }
                status: { [Op.notIn]: ['REJECTED', 'ARCHIVED'] }
            }
                       
        } else {
            //this for guest
            dsrReqCondition =  {customer_id : req.data.customer_id, 'user_id' : req.data.userId, status: { [Op.notIn]: ['ARCHIVED'] }}             
        }
        
        // if(status){
        //     dsrReqCondition = { ...dsrReqCondition, ...{ status: status } };
        // }

        if (search) {
            dsrReqCondition = { ...dsrReqCondition, ...{ dsr_id: { [Op.iLike]: `%${search}%` } } };
        }

        //filter
        data_subject_id ? dsrReqCondition['data_subject_id'] = data_subject_id : ''
        request_type ? dsrReqCondition['request_type'] = request_type : ''
        assignee_id ? dsrReqCondition['assignee_id'] = assignee_id : ''
        if(start_date && end_date){
            dsrReqCondition['assigned_date'] = {
                [Op.gte]: new Date(start_date),
                [Op.lte]: new Date(end_date + 'T23:59:59Z')
            }
        }
        
        if(filter_status){
            if(filter_status == "NOT_STARTED"){
                dsrReqCondition['workflow_step_id'] = {
                    [Op.is]: null
                }                
            } else if(filter_status == "IN_PROGRESS"){
                dsrReqCondition['workflow_step_id'] = {
                    [Op.not]: null
                } 
            }            
        }

        let getRequest = null;

        dsrReqCondition['first_verification'] = true;

        if(role == "guest"){
            getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'createdAt', 'assigned_date'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], [], limit, offset, order);
        } else {
            getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason', 'business_unit', 'status', 'request_type','first_verification','second_verification'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], ['firstName', 'lastName', 'email'], limit, offset, order);
        }

        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        //get indial workflow step. this is required for audit log
        //const initialWorkflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.request_type});
        //getRequest.first_workflow_step_id = initialWorkflowStep.id;

        if(getRequest.rows.length > 0){
            for(let key in getRequest.rows){
                if (getRequest.rows[key].workflow_step_id === null) {
                    const order =[['createdAt','ASC']]
                    const firstWorkFlowId = await commonService.getList(RequestTypeStages,{type_id:getRequest?.rows[key]?.request_type},{},null,null,order)
                    getRequest.rows[key].workflow_step_id= firstWorkFlowId?.rows[0]?.id;

                }
                if(getRequest.rows[key].workflow_step_id){
                    const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.rows[key].workflow_step_id});
                    if (workflowStep) {
                        getRequest.rows[key].step_status = workflowStep.step_title
                    }
                } else {
                    getRequest.rows[key].step_status = null
                }
                // Check for unread messages
                const guestCond = {
                    customer_id: req.data.customer_id,
                    request_id: getRequest.rows[key].id,
                    read_status: 'UNREAD'
                };
                
                // If user is not a guest, only check for external messages
                if (role !== 'guest') {
                    guestCond.mail_type = 'EXTERNAL';
                }
                
                const unreadCount = await commonService.count(DsrSentEmail, guestCond);
                getRequest.rows[key].has_unread_messages = unreadCount > 0;
                getRequest.rows[key].unread_message_count = unreadCount;
            }
        }
        getRequest['role'] = role
                 
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.archiveAndUnarchiveRequest = async (req, res) => {

    const dbTrans = await db.transaction();

    try {
        const { User, DsrRequest, DataSubject, DsrRequestType, country, RequestTypeStages, DsrSentEmail} = db.models;
        
        // `req_id` -  DSR request Id.
        const { req_id } = req.params;

        // only DPO and department head is allowed to archive a DSR request
        if (req.data.roleName !== USER_ROLE[2]) {
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        // handling archive request, archiving a DSR request having id equal to 'req_id' query param.
        const checkDsrRequest = await commonService.findByCondition(DsrRequest, { id: req_id, customer_id: req.data.customer_id });
        if (!checkDsrRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // check if request is already archived
        let reqNewStatus;
        let previous_status = checkDsrRequest.previous_status;
        if(checkDsrRequest.status === 'ARCHIVED'){
            // UNARCHIVE: Rollback to last non-archived status
            reqNewStatus = previous_status;
            previous_status = 'ARCHIVED';
        }
        else{
            // ARCHIVE: Save current status and set status to ARCHIVED
            previous_status = checkDsrRequest.status;
            reqNewStatus = 'ARCHIVED';
        }
        
        // all types of DSR requests (e.g., PENDING, APPROVED, REJECT IN PROGRESS, REJECTED, and COMPLETED) can be archived.
        const updateRequest = await commonService.updateData(DsrRequest, { status: reqNewStatus, previous_status: previous_status }, { id: req_id }, dbTrans);
        if (!updateRequest[1]) {
            return response.error(req, res, { msgCode: 'DSR_REQUEST_ARCHIVING_FAILED' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let dataSubjCond = {};
        let dsrReqCondition = {};
        
        //get data for assignee
        let role = 'dpo';        
        dsrReqCondition =  {customer_id : req.data.customer_id, status : reqNewStatus}  

        let getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason', 'business_unit', 'status', 'request_type','first_verification','second_verification'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], ['firstName', 'lastName', 'email']);
        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if(getRequest?.rows?.length > 0){
            for(let key in getRequest.rows){
                if (getRequest.rows[key]?.workflow_step_id === null) {
                    const order =[['createdAt','ASC']]
                    const firstWorkFlowId = await commonService.getList(RequestTypeStages,{type_id:getRequest?.rows[key]?.request_type},{},null,null,order)
                    getRequest.rows[key].workflow_step_id= firstWorkFlowId?.rows[0]?.id;

                }
                if(getRequest.rows[key]?.workflow_step_id){
                    const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.rows[key].workflow_step_id});
                    if (workflowStep) {
                        getRequest.rows[key].step_status = workflowStep.step_title
                    }
                } else {
                    getRequest.rows[key].step_status = null
                }
                // Check for unread messages
                const guestCond = {
                    customer_id: req.data.customer_id,
                    request_id: getRequest.rows[key].id,
                    read_status: 'UNREAD'
                };
                
                // If user is not a guest, only check for external messages
                if (role !== 'guest') {
                    guestCond.mail_type = 'EXTERNAL';
                }
                
                const unreadCount = await commonService.count(DsrSentEmail, guestCond);
                getRequest.rows[key].has_unread_messages = unreadCount > 0;
                getRequest.rows[key].unread_message_count = unreadCount;
            }
        }
        getRequest['role'] = role
        return response.success(req, res, { msgCode: reqNewStatus === 'ARCHIVED' ? "REQUEST_ARCHIVED" : "REQUEST_UNARCHIVED", data: getRequest }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getTaskRequestList = async (req, res) => {
    try {
        const { User, DsrRequest, DataSubject, DsrRequestType, country, RequestTypeStages, CustomRequestTask} = db.models;
        const { status, page, size, search, sort_by = 'createdAt', sort_order = 'DESC', data_subject_id, request_type, assignee_id, start_date, end_date, filter_status } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};
       
        let dsrReqCondition = {};

        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            let checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.
          
        //this condition for assigned request listing
        const userIdsToFind = [req.data.userId];
        let taskCondition = {
            assignee_id: {
                [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
            },
            customer_id :  req.data.customer_id
        }

        const allTasks = await dsrService.getAllRecord(CustomRequestTask, taskCondition, ['id', 'request_id', 'assignee_id'], limit, offset, order);

        let requestIds = []

        for(task of allTasks){

            requestIds.push(task.request_id)

        }

        dsrReqCondition =  {customer_id : req.data.customer_id, id : requestIds,
            status: {
                [Op.ne]: 'REJECTED'  // Op.ne means "not equal"
            }            
        }  
       
        if(status){
            dsrReqCondition = { ...dsrReqCondition, ...{ status: status } };
        }

        if(role === 'assignee' || role === 'task_assignee'){
            dsrReqCondition = { ...dsrReqCondition, ...{ status: { [Op.notIn]: ['ARCHIVED'] } } };
        }
        

        if (search) {
            dsrReqCondition = { ...dsrReqCondition, ...{ dsr_id: { [Op.iLike]: `%${search}%` } } };
        }

        //filter
        data_subject_id ? dsrReqCondition['data_subject_id'] = data_subject_id : ''
        request_type ? dsrReqCondition['request_type'] = request_type : ''
        assignee_id ? dsrReqCondition['assignee_id'] = assignee_id : ''
        if(start_date && end_date){
            dsrReqCondition['assigned_date'] = {
                [Op.gte]: new Date(start_date),
                [Op.lte]: new Date(end_date + 'T23:59:59Z')
            }
        }
        
        if(filter_status){
            if(filter_status == "NOT_STARTED"){
                dsrReqCondition['workflow_step_id'] = {
                    [Op.is]: null
                }                
            } else if(filter_status == "IN_PROGRESS"){
                dsrReqCondition['workflow_step_id'] = {
                    [Op.not]: null
                } 
            }            
        }
        dsrReqCondition['assignee_id'] = {[Op.not]: null}
        let getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason', 'business_unit', 'status'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], ['firstName', 'lastName', 'email'], limit, offset, order);
        

        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        if(getRequest.rows.length > 0){
            for(let key in getRequest.rows){
                if(getRequest.rows[key].workflow_step_id){
                    const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.rows[key].workflow_step_id});
                    if (workflowStep) {
                        getRequest.rows[key].step_status = workflowStep.step_title
                    }
                } else {
                    getRequest.rows[key].step_status = null
                }
            }
        }
        
        getRequest['role'] = role
                 
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.exportAllRequestList = async (req, res) => {
    try {
        const { User, DsrRequest, DataSubject, DsrRequestType, country, RequestTypeStages, CustomRequestTask} = db.models;
        const { status, page, size, search, sort_by = 'createdAt', sort_order = 'DESC', data_subject_id, request_type, assignee_id, start_date, end_date, filter_status } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};
        
        let dsrReqCondition = {};


        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            let checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.

        //get data for assignee
        if(role == "dpo"){
            dsrReqCondition =  {customer_id : req.data.customer_id, status : 'PENDING'}  
        } else if(role == "task_assignee"){
            const userIdsToFind = [req.data.userId];

            let taskCondition = {
                assignee_id: {
                    [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                },
                customer_id :  req.data.customer_id
            }

            const allTasks = await dsrService.getAllRecord(CustomRequestTask, taskCondition, ['id', 'request_id', 'assignee_id'], limit, offset, order);
            let requestIds = []
            for(task of allTasks){
                requestIds.push(task.request_id)
            }

            dsrReqCondition =  {customer_id : req.data.customer_id, id : requestIds}   

        } else if (role == 'assignee') {
            const userIdsToFind = [req.data.userId];

            let taskCondition = {
                assignee_id: {
                    [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                },
                customer_id :  req.data.customer_id
            }

            const allTasks = await dsrService.getAllRecord(CustomRequestTask, taskCondition, ['id', 'request_id', 'assignee_id'], limit, offset, order);
            let requestIds = []
            for(task of allTasks){
                requestIds.push(task.request_id)
            }

            //dsrReqCondition =  {customer_id : req.data.customer_id, status : 'APPROVED', 'assignee_id' : req.data.userId} 
            dsrReqCondition =  {
                customer_id : req.data.customer_id,
                [Op.or]: [
                    { assignee_id: req.data.userId },
                    { id: requestIds }
                ]
            }
        } else {
            //this for guest
            dsrReqCondition =  {customer_id : req.data.customer_id, 'user_id' : req.data.userId}             
        }
              
       
        if(status){
            dsrReqCondition = { ...dsrReqCondition, ...{ status: status } };
        }

        if (search) {
            dsrReqCondition = { ...dsrReqCondition, ...{ dsr_id: { [Op.iLike]: `%${search}%` } } };
        }

        //filter
        data_subject_id ? dsrReqCondition['data_subject_id'] = data_subject_id : ''
        request_type ? dsrReqCondition['request_type'] = request_type : ''
        assignee_id ? dsrReqCondition['assignee_id'] = assignee_id : ''            
        if(start_date && end_date){
            dsrReqCondition['assigned_date'] = {
                [Op.gte]: new Date(start_date),
                [Op.lte]: new Date(end_date + 'T23:59:59Z')
            }
        }   
        
        if(filter_status){
            if(filter_status == "NOT_STARTED"){
                dsrReqCondition['workflow_step_id'] = {
                    [Op.is]: null
                }                
            } else if(filter_status == "IN_PROGRESS"){
                dsrReqCondition['workflow_step_id'] = {
                    [Op.not]: null
                } 
            }            
        }

        let getRequest = null
        if(role == "guest"){
            getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'createdAt', 'assigned_date'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], [], limit, offset, order);
        } else {
            getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], ['firstName', 'lastName', 'email'], limit, offset, order);
        }

        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        if(getRequest.rows.length > 0){
            for(let key in getRequest.rows){
                if(getRequest.rows[key].workflow_step_id){
                    const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.rows[key].workflow_step_id});
                    if (workflowStep) {
                        getRequest.rows[key].step_status = workflowStep.step_title
                    }
                } else {
                    getRequest.rows[key].step_status = null
                }
            }
        }
        
        getRequest['role'] = role

        //export start here 
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('My Sheet');    
        
        if(status == 'PENDING'){
            // Add columns
            worksheet.columns = [
                { header: 'Request ID', key: 'request_id', width: 10 },
                { header: 'Request Type', key: 'request_type', width: 28 },
                { header: 'Requested By', key: 'requested_by', width: 16 },
                { header: 'Request Date', key: 'request_date', width: 16 },
                { header: 'Region', key: 'region', width: 14 },
            ];

            // Add rows
            getRequest.rows.forEach(row => {
                worksheet.addRow({
                    request_id: row?.dsr_id,
                    request_type: row?.DsrRequestType?.flowtype,
                    requested_by: row?.DataSubject?.first_name + " " + row?.DataSubject?.last_name,
                    request_date : row?.assigned_date ? displayDate(row?.assigned_date) : '',
                    region : row?.DataSubject?.country?.country_name
                });
            })

            
        } else if(status == 'APPROVED'){
            worksheet.columns = [
                { header: 'DSR ID', key: 'request_id', width: 10 },
                { header: 'Subject Type', key: 'subject_type', width: 14 },
                { header: 'Full Name', key: 'full_name', width: 14 },
                { header: 'Request Type', key: 'request_type', width: 14 },                
                { header: 'Data Discovery', key: 'data_discovery', width: 14 },                
                { header: 'Start Date', key: 'start_date', width: 14 },
                { header: 'Deadline', key: 'deadline', width: 14 },
                { header: 'Extended', key: 'extended', width: 14 },
                { header: 'Status', key: 'status', width: 14 },
                { header: 'Steps Status', key: 'steps_status', width: 14 },
                { header: 'Assigned', key: 'assigned', width: 14 },
            ];
            
            getRequest.rows.forEach(row => {
                worksheet.addRow({
                    request_id: row?.dsr_id,
                    subject_type: row?.DataSubject?.relationship,                    
                    full_name: row?.DataSubject?.first_name + " " + row?.DataSubject?.last_name,
                    request_type: row?.DsrRequestType?.flowtype,
                    data_discovery: row?.data_discovery,
                    start_date : row?.assigned_date ? displayDate(row?.assigned_date) : '',
                    deadline : row?.deadline_date ? displayDate(row?.deadline_date) : '',
                    extended : row?.extended, 
                    status : row?.DataSubject?.country?.country_name, 
                    steps_status : row?.step_status,  
                    assigned : row?.User?.firstName + " " + row?.User?.lastName,  
                });
            })
        } else if(status == 'REJECTED' || status == 'REJECTED_IN_PROGRESS'){
            // Add columns
            worksheet.columns = [
                { header: 'Request ID', key: 'request_id', width: 10 },
                { header: 'Request Type', key: 'request_type', width: 28 },
                { header: 'Requested By', key: 'requested_by', width: 16 },
                { header: 'Request Date', key: 'request_date', width: 16 },
                { header: 'Region', key: 'region', width: 14 },
                { header: 'Reason', key: 'reason', width: 14 }
            ];
        
            

            // Add rows
            getRequest.rows.forEach(row => {
                worksheet.addRow({
                    request_id: row?.dsr_id,
                    request_type: row?.DsrRequestType?.flowtype,
                    requested_by: row?.DataSubject?.first_name + " " + row?.DataSubject?.last_name,
                    request_date : row?.assigned_date ? displayDate(row?.assigned_date) : '',
                    region : row?.DataSubject?.country?.country_name,
                    reason : row?.reject_reason
                });
            })
        }

        
        // Save the workbook
      //  await workbook.xlsx.writeFile('output.xlsx');
      
        const fileName = `dsr${Date.now()}.xlsx`;
        const excelDir = path.join(__dirname, '../utils/excel'); // Example directory name
        
        const filePath = path.join(excelDir, fileName);
        await workbook.xlsx.writeFile(filePath);
      
        const uploadedFiles = [];
        const uploadResult = await uploadDocument.uploadToS3(filePath);
        if (uploadResult.status !== true) {
            // Delete all successfully uploaded files from local 
            if (filePath) {
                // delete file from local
                await deleteFile(filePath)
            }
            console.log('S3 Error', uploadResult);
            return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
        }
        if (filePath) {
            // delete file from local
            await deleteFile(filePath)
        }
        uploadedFiles.push({
          //  originalName: file.originalname,
            url: uploadResult.data.Location
        })
     
        //export end here

                 
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: uploadedFiles }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getAllAssigneeList = async (req, res) => {
    try {
        const { User ,GroupUser} = db.models;
        const { user_type , group_id} = req.query;
        
        let order = [['firstName', 'ASC']];
        let userCondition = { customer_id: req.data.customer_id };
        
        if(user_type){
            userCondition.status = user_type;
        }
        if(group_id){
            const userList = await dsrService.getAllRecord(GroupUser, {group_id: group_id}, ['user_id']);
            const userIds = userList.map(u => u.user_id);
            userCondition.id = { [Op.in]: userIds } 
        }
        let limit, offset = null;

        const users = await dsrService.getAllRecord(User, userCondition, ['id', 'firstName', 'lastName'], limit, offset, order);
        
        if (!users) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
                 
        return response.success(req, res, { msgCode: "USER_FETCHED", data: users }, httpStatus.OK);

    } catch (err) {
        console.log(err)
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getRequestById = async (req, res) => {
    try {
        const { DsrMailDocuments, DsrSentEmail, DsrRequest, DataSubject, DsrRequestType, country, RequestDocument, User, RequestTypeStages, CustomRequestTask} = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};
        let dsrReqCondition =  {id: req.params.id, customer_id : req.data.customer_id,}   
               
        let getRequest = null;


        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.

       
        if(role == "guest"){
            getRequest = await dsrService.getMultiAssocDataById(DsrRequest, DsrRequestType, DataSubject, country, RequestDocument, User, dsrReqCondition, {}, dataSubjCond, {}, {}, {}, ['id' ,'dsr_id', 'createdAt', 'assigned_date', 'deadline_date'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'email'], ['country_name'], [], [], limit, offset, order);
        } else {
            getRequest = await dsrService.getMultiAssocDataById(DsrRequest, DsrRequestType, DataSubject, country, RequestDocument, User, dsrReqCondition, {}, dataSubjCond, {}, {}, {}, ['id' ,'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'description', 'reject_reason', 'status', 'business_unit'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email', 'phone_no', 'postal_code'], ['country_name'], ['original_name', 'url', 'document_type'], ['firstName', 'lastName'], limit, offset, order);

        }


        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }


        if(getRequest.workflow_step_id){
            const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.workflow_step_id});
            if (workflowStep) {
                getRequest.step_status = workflowStep.step_title
            }
        } else {
            getRequest.step_status = null
        }


        let guestCond = {
            customer_id : req.data.customer_id,
            request_id : req.params.id,
        }
        
        if(role == "guest"){
            guestCond['mail_type'] =  "EXTERNAL"
        } 

        //get mail data.            
        let getEmails = await dsrService.getSentEmails(DsrSentEmail, User, DsrMailDocuments, guestCond, {}, {}, ['subject', 'content', 'createdAt', 'first_name', 'last_name'], ['firstName', 'lastName'], ['url', 'original_name'], limit, offset, order);            
        getRequest.email = getEmails;
                       
        getRequest.role = role

        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getRequestMailActivityById = async (req, res) => {
    try {
        const { DsrMailDocuments, DsrSentEmail, DsrRequest, User} = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
               
    
        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.

       
       

        //if(role == "guest"){
            //get mail data.
            let guestCond = {
                customer_id : req.data.customer_id,
                request_id : req.params.id,
            }
            let getEmails = await dsrService.getSentEmails(DsrSentEmail, User, DsrMailDocuments, guestCond, {}, {}, ['subject', 'content', 'createdAt', 'first_name', 'last_name','read_status'], ['firstName', 'lastName'], ['url', 'original_name'], limit, offset, order);

            const unreadCount = getEmails.reduce((count, email) => {
                return count + (email.read_status === 'UNREAD' ? 1 : 0);
            }, 0);        
        //}
            
    
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: {result: getEmails, unreadCount: unreadCount}}, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.createRequest = async (req, res) => {
    const dbTrans = await db.transaction();
    try {

        const { RequestDocument, DsrRequestType, DsrRequest, DataSubject, DsJointParty, RequestTask, RequestTypeStages, CustomRequestTask, User } = db.models;
        
        const workflow = await commonService.findByCondition(DsrRequestType, { id: req.body.dsr_request_type_id});
        if (!workflow) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        let dataSubjectData = {
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            email: req.body.email,
            phone_no: req.body.phone_no,
            is_data_subject: req.body.is_data_subject,
            unique_identification_type: req.body.unique_identification_type,
            unique_identification_number: req.body.unique_identification_number,
            address_1: req.body.address_1,
            address_2: req.body.address_2,
            country_id: req.body.country_id,
            state_id: req.body.state_id,
            city: req.body.city,
            postal_code: req.body.postal_code,
            relationship: req.body.relationship,
            dob : req.body.dob,
            third_party_name : req.body.third_party_name,
            third_party_practice_name : req.body.third_party_practice_name,
            third_party_email : req.body.third_party_email,
            third_party_contact_number : req.body.third_party_contact_number,
            second_address_1: req.body.second_address_1,
            second_address_2: req.body.second_address_2,
            second_country_id: req.body.second_country_id,
            second_state_id: req.body.second_state_id,
            second_city: req.body.second_city,
            second_postal_code: req.body.second_postal_code,
            customer_id: req.data.customer_id,
            user_id: req.data.userId,
        }
        
        const insertDataSubject = await commonService.addDetail( DataSubject , dataSubjectData, dbTrans);
        if(!insertDataSubject){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_DATA_SUBJECT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //get lastinserted id 
        const lastInsertedReq = await dsrService.getOneRecord(DsrRequest, {}, null, null, null, [['id', 'DESC']]);
        let lastId = 1;
        if(lastInsertedReq.id){
            lastId = lastInsertedReq.id+1;
        }        
        let dsr_id = workflow.flowtype[0]+'-'+lastId;

        const today = new Date();
        const deadline_date = new Date(today);

        deadline_date.setDate(today.getDate() + dsrConfig.dsr_request_deadline_days);
       
        const requestData = {
            dsr_id: dsr_id,
            request_type: req.body.dsr_request_type_id,
            description: req.body.description,
            data_subject_id: insertDataSubject.id,
            customer_id: req.data.customer_id,
            user_id: req.data.userId,
            request_date: Date.now(),
            is_internal_request: req.body.is_internal_request,
            dsr_return_preference: req.body.dsr_return_preference,
            deadline_date : deadline_date
        }

        if(req.body.business_unit){
            requestData['business_unit'] = req.body.business_unit
        }

        const newRequest = await commonService.addDetail( DsrRequest , requestData, dbTrans);

        if(!newRequest){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //insert documents
        if(req.body.letter_of_authority_doc){
            if(req.body.letter_of_authority_doc.length > 0){
                const bulkData = [];
                req.body.letter_of_authority_doc.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'letter_of_authority'
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(RequestDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        if(req.body.identification_doc){
            if(req.body.identification_doc.length > 0){
                const IdentBulkData = [];
                req.body.identification_doc.forEach(data => {
                    IdentBulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'identification_documents'
                    })
                })

                
                //adding to the database
                const identDocumentUpload = await commonService.bulkAdd(RequestDocument, IdentBulkData, dbTrans);
                if (!identDocumentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }


        if(req.body.joint_party_details){
            let jointPartyDetails = {
                data_subject_id : insertDataSubject.id,
                first_name: req.body.joint_party_details.first_name,
                last_name: req.body.joint_party_details.last_name,
                email: req.body.joint_party_details.email,
                phone_no: req.body.joint_party_details.phone_no,
                unique_identification_type: req.body.joint_party_details.unique_identification_type,
                unique_identification_number: req.body.joint_party_details.unique_identification_number,
                address_1: req.body.joint_party_details.address_1,
                address_2: req.body.joint_party_details.address_2,
                country_id: req.body.joint_party_details.country_id,
                state_id: req.body.joint_party_details.state_id,
                city: req.body.joint_party_details.city,
                postal_code: req.body.joint_party_details.postal_code,
                relationship: req.body.relationship,
                dob : req.body.joint_party_details.dob,
                customer_id: req.data.customer_id,
                user_id: req.data.userId,
            }

            const addJointPartyDetails = await commonService.addDetail( DsJointParty , jointPartyDetails, dbTrans);
            if(!addJointPartyDetails){
                return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }

        //insert all step task to custom task
        let reqStagesCond = {
            type_id : req.body.dsr_request_type_id
         }
 
         let reqTypeOrder = [['id', 'ASC']];
         const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);   
         const stageIds = stages.map(step => step.id);
         let taskCond = {
             stage_id: {
                 [Op.in]: stageIds, // This creates the WHERE IN clause
             },
         };
         
         let activepieces_automation_id_array = [];  
        const taskOrder = [
            ['stage_id', 'ASC'],  // First, order by stage_id ascending
            ['id', 'ASC']         // Then, order by id ascending within each stage
        ];
        
         const getAllTask = await dsrService.getAllRecord(RequestTask, taskCond, ['title', 'guidance_text', 'reminder_date', 'task_note', 'start_date', 'due_date', 'completion_date', 'department_id', 'requirement', 'assignee_id', 'stage_id', 'activepieces_automation_id', 'due_days'], null,null,taskOrder);   
         let runAutomation = false;
         let count = 1;
         if(getAllTask.length > 0){
             for(task of getAllTask){
                 task['request_id'] = newRequest.id
                 task['customer_id'] = req.data.customer_id
                 task['created_by'] = req.data.userId
                 task['is_custom'] = false
                 task['workflow_id'] = workflow.id  
                 
                 const taskkk = await commonService.addDetail(CustomRequestTask, task, dbTrans);
                 if(taskkk.activepieces_automation_id){
                    if(count == 1){
                        runAutomation = true;
                    }
                    if(runAutomation){
                        activepieces_automation_id_array.push({
                            activepieces_automation_id : taskkk.activepieces_automation_id,
                            custom_task_id : taskkk.id
                        });
                    }
                 } else {
                    runAutomation = false;
                 }
                 
                 count++
                 if(!taskkk){
                    return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
                 }

                 if(task.assignee_id){
                    //send email to task assignee.
                    
                    const allUser = await dsrService.getAllRecord(User, {id : task.assignee_id}, ['firstName', 'lastName', 'email']);
        
                    const textTemplate = "dsr_mail.ejs";                    
                    const subject = `Notification of DSR Task Assignment`;
                    let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                    for(let emailUser of allUser){
                        let contentText = `
                            <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                            <p>I hope you are doing well.</p>
                            <p>This is to inform you that a Task has been assigned to you in the Data Subject Request (DSR) with Request ID ${newRequest.dsr_id} for processing. Please review the details below and take the necessary actions to ensure compliance within the required time frame.</p>
                            <p>To begin processing the request, please log into the Gotust platform and follow the steps outlined there.</p>
                            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                            <p>We appreciate your prompt attention to this matter.</p>
                            <p>Thank you for your cooperation.</p>
                        `;
        
                        const sendData = {
                            content: contentText,
                            
                        };
        
                        if(emailUser.email){
                            await sendMail(
                                emailUser.email,
                                sendData,
                                subject,
                                textTemplate
                            );
                        }                
                    }
                }
             }
         }

        if (dbTrans !== undefined) {
            await dbTrans.commit();
        }

         //call webhook     
         //const customeReqTasks = await dsrService.getAllRecord(CustomRequestTask, {workflow_id : req.body.dsr_request_type_id}, ['id' , 'stage_id', 'workflow_id', 'activepieces_automation_id'], null, null, reqTypeOrder);   
          
        console.log('=== activepieces log')
        console.log(activepieces_automation_id_array)
         if(activepieces_automation_id_array){
            for(let activepiecesObj of activepieces_automation_id_array){
                console.log('=== inside for loop')
                if(activepiecesObj){
                    console.log('=== inside if condition')
                    const activepiecesWebhookUlr = process.env.ACTIVEPIECES_WEBHOOK_URL ? process.env.ACTIVEPIECES_WEBHOOK_URL : "https://gt-workflow.gotrust.tech";
                    let webhookurl = activepiecesWebhookUlr+"/api/v1/webhooks/"+activepiecesObj.activepieces_automation_id;
    
            
                
                    webhookurl = `${webhookurl}?phone_number=${activepiecesObj.phone_number}&email=${dataSubjectData.DataSubject.email}&task_id=${activepiecesObj.task_id}`;
                    if (webhookurl) {
                        let config = {
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: webhookurl,
                            headers: { },
                                        
                        };
                    
                        let x = await axios.request(config);
                        
                        console.log(x);
                        console.log('=== webhook is called')
                    }
                }
            }
         }
        
        const token = await signToken({
            email: req.body.email,
            data_subject_id: insertDataSubject.id
        },36000);

        const generateVerificationToken = (token) => {
            // Implement token generation logic (e.g., JWT or random string)
            
            const baseUrl = process.env.FRONTEND_BASE_URL;

            return `${baseUrl}/data-subject-rights/my-request`;  // Example URL with token
        };

        const sendData = {
            content: 'You are just one click away from getting started with your DSR request. Simply verify your email address to activate your GoTrust account and track your DSR request.',
            dsr_link: generateVerificationToken(token),  // Add verification link here
            dsr_link_test : "Click here to email confirmation"
        };
        
        const textTemplate = "dsr_verify_mail.ejs";                    
        const subject = `Email Verification for ${req.body.email}`;

        
        await sendMail(
            req.body.email,
            sendData,
            subject,
            textTemplate
        );
    //end of verify email here
      
  

       
        return response.success(req, res, { msgCode: "REQUEST_CREATED", data: insertDataSubject }, httpStatus.CREATED);
      

    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.uploadDsrDocuments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {

        
  
        //adjusting the name of the file to store in database
        const bulkData = [];
        req.body?.uploadData.forEach(data => {
            bulkData.push({
                original_name: data.originalName,
                url: data.url
            })
        })

        return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: bulkData }, httpStatus.CREATED, dbTrans);


    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.assignRequest = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DsrRequest, DSRAuditLog, User ,CustomRequestTask } = db.models;
        
        let dsr_request_id = req.params.dsr_request_id
        
        const check = await commonService.findByCondition(DsrRequest, { id: dsr_request_id, customer_id: req.data.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let obj = {
            assignee_id : req.body.assignee_id,            
        }
        if(check.assignee_id===null||!check.assignee_id){
            console.log('===inside upate task date====')
            const start_date = moment().toISOString();
            const date = new Date();
            const isoString = date.toISOString();
            console.log('=====start date with moment is is==='+start_date);
            console.log('=====start date with date method is ==='+isoString);
            console.log("======Request id is =="+dsr_request_id);
            if(start_date){
                const updateStart = await commonService.updateData(CustomRequestTask,{start_date:start_date},{request_id:dsr_request_id},dbTrans);
                console.log(updateStart);
                if(!updateStart[1]){
                    return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        const update = await commonService.updateData(DsrRequest, obj, { id: dsr_request_id }, dbTrans);
        
        if (!update[1]) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const dsrRequestAfter = await commonService.findByCondition(DsrRequest, 
            { id: dsr_request_id },
            ['id', 'dsr_id', 'assignee_id', 'status', 'workflow_step_id']
        );
        if(!dsrRequestAfter){
            return response.error(req, res, { msgCode: 'DSR_REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const user = await commonService.findByCondition(User, {
            id: req.body.assignee_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
        
        if(req.body.workflow_step_id){
            const auditLog = await auditLogger.createDetailedAuditLog({
                dsrId: dsr_request_id,
                stepId: req.body.workflow_step_id,
                actorId: req.data.userId,
                actionType: 'ASSIGN',
                before: {
                    assignee_id: check.assignee_id
                },
                after: {
                    assignee_id: dsrRequestAfter.assignee_id
                },
                metadata: {
                    dsr_id: check.dsr_id
                }
            }, req, dbTrans);
            
            if (!auditLog) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }

        //sent email to request assignee        
        const textTemplate = "dsr_mail.ejs";                    
        const subject = `New Data Subject Request Assigned – Action Required`;
        let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
        
        let contentText = `
            <p>Dear ${user?.firstName} ${user?.lastName},</p>
            <p>Hope you're doing well.</p>
            <p>A new Data Subject Request (ID: ${check.dsr_id}) has been assigned to you for processing. Please log in to the GoTrust platform to review and take the necessary steps:</p>
            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
            <p>Kindly ensure timely action to maintain compliance.</p>
            <p>Thanks for your prompt attention.</p>
        `;

        const sendData = {
            content: contentText,
            
        };

        if(user.email){
            await sendMail(
                user.email,
                sendData,
                subject,
                textTemplate
            );
        }   

        return response.success(req, res, { msgCode: "REQUEST_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.approvedRejectRequest = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DataSubject, RequestTypeStages, DsrRequest, DSRAuditLog, User, Role, CustomRequestTask } = db.models;
        
        let dsr_request_id = req.params.dsr_request_id
        
        const check = await commonService.findByCondition(DsrRequest, { id: dsr_request_id, customer_id: req.data.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        } 
        let stepOrder = [['id', 'ASC']]; 
        let limit, offset = null;
        const step = await dsrService.getOneRecord(RequestTypeStages, { type_id: check.request_type}, ['id','type_id', 'step_title'], limit, offset, stepOrder);
        if(!step){
            return response.error(req, res, { msgCode: 'STEP_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        let obj = {
            status : req.body.status            
        }

        if(req.body.reject_reason){
            obj['reject_reason'] = req.body.reject_reason;
        }

        if(req.body.status == "APPROVED"){
           
            let dataSubCond = {id : check.data_subject_id}
            const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email','first_name', 'last_name']);
            if(!dataSubject){
                return response.error(req, res, { msgCode: 'DATA_SUBJECT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            //add DPO as assignee of assignee not added
            if(check.assignee_id == null){
                obj['assignee_id'] = req.data.userId
            }

             const today = new Date();

            obj['assigned_date'] = today; 
                    
            if(check.workflow_step_id ==  null){
                obj['workflow_step_id'] = step.id;
            }
            if (check?.assignee_id === null || !check?.assignee_id) {
                const start_date = moment().toISOString();
                const updateStart = await commonService.updateData(CustomRequestTask, { start_date: start_date }, { request_id: dsr_request_id }, dbTrans);
                if (!updateStart[1]) {
                  return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

            //send approved request email
            const sendData = {
                content: `<p>Dear ${dataSubject?.first_name} ${dataSubject?.last_name},</p>
                <p> We’ve reviewed your Data Subject Request ${check.dsr_id}, and it has been successfully verified and approved.<p>`,                       
            };

            const textTemplate = "dsr_mail.ejs";                    
            const subject = `Confirmation of DSR Request ${check.dsr_id}`;

            await sendMail(
                dataSubject?.email,
                sendData,
                subject,
                textTemplate
            );

        }

        if(req.body.status == "COMPLETED"){
            let dataSubCond = {id : check.data_subject_id}
            const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);
            if(!dataSubject){
                return response.error(req, res, { msgCode: 'DATA_SUBJECT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            //add DPO as assignee of assignee not added
            if(check.assignee_id === null){
                obj['assignee_id'] = req.data.userId
            }      
                    
            if(check.workflow_step_id ===  null){
                obj['workflow_step_id'] = step.id;
            }
            

            //send approved request email
            const sendData = {
                content: `We are pleased to inform you that your Data Subject Request (DSR) for  Request ID ${check.dsr_id} has been successfully processed and completed.
`,                       
            };

            const textTemplate = "dsr_mail.ejs";                    
            const subject = `DSR Request ${check.dsr_id} Completed`;

            await sendMail(
                dataSubject?.email,
                sendData,
                subject,
                textTemplate
            );

        }



        const update = await commonService.updateData(DsrRequest, obj, { id: dsr_request_id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        const user = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditLog = await auditLogger.createDetailedAuditLog({
            dsrId: dsr_request_id,
            stepId: step.id,
            actorId: req.data.userId,
            actionType: 'STATUS_CHANGE',
            before: {
                status: check.status,
                reject_reason: req.body.reject_reason
            },
            after: {
                status: req.body.status,
                reject_reason: req.body.reject_reason
            },
            metadata: {
                dsr_id: check.dsr_id,
                step_title: step.step_title
            }
        }, req, dbTrans);
        
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if(req.body.status == "REJECTED_IN_PROGRESS"){
            const roleData = await commonService.findByCondition(Role, {
                role_name: "Data Protection Officer",
                customer_id: check.customer_id
                
            }, ['id']);

            if(roleData){                
                const dpo = await commonService.findByCondition(User, {
                    role_id: roleData.id
                }, ['firstName', 'lastName', 'email']);
                if (dpo) {                   
                    //sent email to dpo     
                    const textTemplate = "dsr_mail.ejs";                    
                    const subject = `DSR Request ${check.dsr_id} - Rejected`;
                    let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                    
                    let contentText = `
                        <p>Dear ${dpo?.firstName} ${dpo?.lastName},</p>
                        <p>This is to to inform you that the Data Subject Request (DSR) with Request ID ${check.dsr_id} has been reviewed and, unfortunately, has been rejected. </p>
                        <p>If you require further clarification or information on rejection, login to the GoTrust Portal using the link below:</p>
                        <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                        <p>Thank you for your understanding and continued cooperation.</p>
                    `;

                    const sendData = {
                        content: contentText,
                        
                    };

                    if(dpo.email){
                        await sendMail(
                            dpo.email,
                            sendData,
                            subject,
                            textTemplate
                        );
                    } 
                }
            }

        }
        if(req.body.status == "REJECTED"){
            let dataSubCond = {id : check.data_subject_id}
            const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email','first_name', 'last_name']);
            if(!dataSubject){
                return response.error(req, res, { msgCode: 'DATA_SUBJECT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            //send approved request email
            const sendData = {
                content: `<p>Dear ${dataSubject?.first_name} ${dataSubject?.last_name},</p>
                <p> We’ve reviewed your Data Subject Request ${check.dsr_id}, and it has been rejected.<p>`,                       
            };

            const textTemplate = "dsr_mail.ejs";                    
            const subject = `Rejection of DSR Request ${check.dsr_id}`;

            await sendMail(
                dataSubject?.email,
                sendData,
                subject,
                textTemplate
            );
        }
        

        return response.success(req, res, { msgCode: "REQUEST_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.createTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DsrRequest, TaskDocument, CustomRequestTask, RequestTypeStages, DSRAuditLog, User } = db.models;
                 
        const checkStep = await commonService.findByCondition(RequestTypeStages, { id: req.body.stage_id});
        if (!checkStep) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const check = await commonService.findByCondition(CustomRequestTask, { title: req.body.title, stage_id : req.body.stage_id });
       
        if (check) {
            return response.error(req, res, { msgCode: 'TASK_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        req.body.created_by = req.data.userId;
        req.body.customer_id = req.data.customer_id;

        //if assignee is added and there no start date then add today data as start date
        if(req.body.assignee_id && req.body.start_date == null){
            req.body.start_date = new Date();
        }       
        
        const task = await commonService.addDetail(CustomRequestTask, req.body, dbTrans);
         
         if (!task) {
             return response.error(req, res, { msgCode: 'ERROR_CREATING_TASK' }, httpStatus.BAD_REQUEST, dbTrans);
         }

         //insert documents
         //adjusting the name of the file to store in database
        const bulkData = [];
        if(req.body?.documents){
            if(req.body?.documents.length > 0){
                req.body?.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        task_id: task.id,
                        dsr_request_id: req.body.request_id
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(TaskDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
  

         const user = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

         // Create detailed audit log
         const auditLog = await auditLogger.createDetailedAuditLog({
            dsrId: req.body.request_id,
            stepId: req.body.stage_id,
            actorId: req.data.userId,
            actionType: 'TASK_CREATE',
            metadata: {
                taskName: task.title,
                taskId: task.id,
                assigneeIds: req.body.assignee_id,
                dueDate: req.body.due_date,
                documentCount: bulkData.length,
                documentNames: bulkData.map(doc => doc.original_name),
                stepTitle: checkStep.step_title
            }
        }, req, dbTrans);
        
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        if(req.body.assignee_id){
            //send email to task assignee.
            const dsrReq = await commonService.findByCondition(DsrRequest, { id:  task.request_id });
            
            const allUser = await dsrService.getAllRecord(User, {id : req.body.assignee_id}, ['firstName', 'lastName', 'email']);

            const textTemplate = "dsr_mail.ejs";                    
            const subject = `Notification of DSR Task Assignment`;
            let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
            for(let emailUser of allUser){
                let contentText = `
                    <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                    <p>I hope you are doing well.</p>
                    <p>This is to inform you that a Task has been assigned to you in the Data Subject Request (DSR) with Request ID ${dsrReq.dsr_id} for processing. Please review the details below and take the necessary actions to ensure compliance within the required time frame.</p>
                    <p>To begin processing the request, please log into the Gotust platform and follow the steps outlined there.</p>
                    <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                    <p>We appreciate your prompt attention to this matter.</p>
                    <p>Thank you for your cooperation.</p>
                `;

                const sendData = {
                    content: contentText,
                    
                };

                if(emailUser.email){
                    await sendMail(
                        emailUser.email,
                        sendData,
                        subject,
                        textTemplate
                    );
                }                
            }
        }

         return response.success(req, res, { msgCode: "TASK_CREATED", data: task }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.uploadTaskDocuments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {

        const {  RequestTask, TaskDocument } = db.models;

        const check = await commonService.findByCondition(RequestTask, { id: req.body.task_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //adjusting the name of the file to store in database
        const bulkData = [];
        req.body?.uploadData.forEach(data => {
            bulkData.push({
                original_name: data.originalName,
                url: data.url,
                task_id: req.body.task_id,
                dsr_request_id: req.body.dsr_request_id
            })
        })

        //adding to the database
        const documentUpload = await commonService.bulkAdd(TaskDocument, bulkData, dbTrans);
        if (!documentUpload) {
            return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: documentUpload }, httpStatus.CREATED, dbTrans);


    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.deleteTaskDocuments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { TaskDocument } = db.models;
        const data = await commonService.deleteQuery(TaskDocument, { url: req.body.url }, dbTrans);
       
        if (!data) {
            return response.error(req, res, { msgCode: "DOCUMENT_DELETED_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "DOCUMENT_DELETED" }, httpStatus.OK, dbTrans);
    }
    catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
    }
};

exports.updateTaskBackup = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomRequestTask, DSRAuditLog, User, TaskDocument,DsrRequest } = db.models;
     
        let id = req.params.task_id

        if(req.body.id){
            delete req.body['id']
        }
        

        const check = await commonService.findByCondition(CustomRequestTask, { id: id, customer_id: req.data.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //if assignee is added and there no start date then add today data as start date
        if(req.body.assignee_id && req.body.start_date == null){
            req.body.start_date = new Date();
        }

        if(req.body.progress && req.body.progress == 'COMPLETED'){
            req.body.completion_date = new Date();
        }
        // req.body.due_date = moment(req.body.start_date).add(parseInt(req.body.due_days, 10), 'days').toISOString();
        const update = await commonService.updateData(CustomRequestTask, req.body, { id: id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const user = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const bulkData = [];
        if(req.body?.documents){
            if(req.body?.documents.length > 0){
                req.body?.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        task_id: id,
                        dsr_request_id: req.body.request_id
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(TaskDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        let actionType = 'TASK_UPDATE';
        if (taskBefore.progress !== 'COMPLETED' && taskAfter.progress === 'COMPLETED') {
            actionType = 'TASK_COMPLETE';
        }

        // Create detailed audit log
        const auditLog = await auditLogger.createDetailedAuditLog({
            dsrId: check.request_id,
            stepId: check.stage_id,
            actorId: req.data.userId,
            actionType: actionType,
            before: {
                title: check.title,
                assignee_id: check.assignee_id,
                start_date: check.start_date,
                due_date: check.due_date,
                completion_date: check.completion_date,
                progress: check.progress
            },
            after: {
                title: req.body.title,
                assignee_id: req.body.assignee_id,
                start_date: req.body.start_date,
                due_date: req.body.due_date,
                completion_date: req.body.completion_date,
                progress: req.body.progress
            },
            metadata: {
                taskName: check.title,
                taskId: id,
                documentCount: bulkData.length,
                documentNames: bulkData.map(doc => doc.original_name),
                completionDate: req.body.completion_date
            }
        }, req, dbTrans);
        
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        //send email to assignee if it is assigned at first time.
        
            const currentAssigneeIds = check.assignee_id;
            const newAssignees = Array.isArray(req.body.assignee_id) ? req.body.assignee_id : [req.body.assignee_id];
            const assigneesToNotify = newAssignees.filter(assignee => !currentAssigneeIds?.includes(assignee));

            if(assigneesToNotify.length>0) {

                const dsrReq = await commonService.findByCondition(DsrRequest, { id:  update[1].request_id });
                const allUser = await dsrService.getAllRecord(User, {id :assigneesToNotify }, ['firstName', 'lastName', 'email']);
            
                const textTemplate = "dsr_mail.ejs";                    
                const subject = `Notification of DSR Task Assignment`;
                let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                
                for(let emailUser of allUser){
                        let contentText = `
                            <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                            <p>I hope you are doing well.</p>
                            <p>This is to inform you that a Task has been assigned to you in the Data Subject Request (DSR) with Request ID ${dsrReq.dsr_id} for processing. Please review the details below and take the necessary actions to ensure compliance within the required time frame.</p>
                            <p>To begin processing the request, please log into the Gotust platform and follow the steps outlined there.</p>
                            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                            <p>We appreciate your prompt attention to this matter.</p>
                            <p>Thank you for your cooperation.</p>
                            `;
                
                            const sendData = {
                                content: contentText,
                        
                            };

                            if(emailUser.email){
                                await sendMail(
                                    emailUser.email,
                                    sendData,
                                    subject,
                                    textTemplate
                                );
                               
                            }                
                }
            }
           
            
            return response.success(req, res, { msgCode: "TASK_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

        } catch (err) {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
        }
};

exports.updateTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomRequestTask, DSRAuditLog, User, TaskDocument,DsrRequest } = db.models;
     
        let id = req.params.task_id
 
        if(req.body.id){
            delete req.body['id']
        }
        
 
        const check = await commonService.findByCondition(CustomRequestTask, { id: id, customer_id: req.data.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
 
        //if assignee is added and there no start date then add today data as start date
        if(req.body.assignee_id && req.body.start_date == null){
            req.body.start_date = new Date();
        }
        
        
        if(req.body.progress && req.body.progress == 'COMPLETED'){
            req.body.completion_date = new Date();
        }
        
        const update = await commonService.updateData(CustomRequestTask, req.body, { id: id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
 
        const user = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
 
        const bulkData = [];
        if(req.body?.documents){
            if(req.body?.documents.length > 0){
                req.body?.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        task_id: id,
                        dsr_request_id: req.body.request_id
                    })
                })
 
                //adding to the database
                const documentUpload = await commonService.bulkAdd(TaskDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
 
        let actionType = 'TASK_UPDATE';
        if (check.progress !== 'COMPLETED' && check.progress === 'COMPLETED') {
            actionType = 'TASK_COMPLETE';
        }

        // Create detailed audit log
        const auditLog = await auditLogger.createDetailedAuditLog({
            dsrId: check.request_id,
            stepId: check.stage_id,
            actorId: req.data.userId,
            actionType: actionType,
            before: {
                title: check.title,
                assignee_id: check.assignee_id,
                start_date: check.start_date,
                due_date: check.due_date,
                completion_date: check.completion_date,
                progress: check.progress
            },
            after: {
                title: req.body.title,
                assignee_id: req.body.assignee_id,
                start_date: req.body.start_date,
                due_date: req.body.due_date,
                completion_date: req.body.completion_date,
                progress: req.body.progress
            },
            metadata: {
                taskName: check.title,
                taskId: req.body.id,
                documentCount: bulkData.length,
                documentNames: bulkData.map(doc => doc.original_name),
                completionDate: req.body.completion_date
            }
        }, req, dbTrans);
        
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        //send email to assignee if it is assigned at first time.
        
            const currentAssigneeIds = check.assignee_id;
            const newAssignees = Array.isArray(req.body.assignee_id) ? req.body.assignee_id : [req.body.assignee_id];
            const assigneesToNotify = newAssignees.filter(assignee => !currentAssigneeIds?.includes(assignee));
 
            if(assigneesToNotify.length>0) {
 
                const dsrReq = await commonService.findByCondition(DsrRequest, { id:  update[1].request_id });
                const allUser = await dsrService.getAllRecord(User, {id :assigneesToNotify }, ['firstName', 'lastName', 'email']);
            
                const textTemplate = "dsr_mail.ejs";                    
                const subject = `Notification of DSR Task Assignment`;
                let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                
                for(let emailUser of allUser){
                        let contentText = `
                            <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                            <p>I hope you are doing well.</p>
                            <p>This is to inform you that a Task has been assigned to you in the Data Subject Request (DSR) with Request ID ${dsrReq.dsr_id} for processing. Please review the details below and take the necessary actions to ensure compliance within the required time frame.</p>
                            <p>To begin processing the request, please log into the Gotust platform and follow the steps outlined there.</p>
                            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                            <p>We appreciate your prompt attention to this matter.</p>
                            <p>Thank you for your cooperation.</p>
                            `;
                
                            const sendData = {
                                content: contentText,
                        
                            };
 
                            if(emailUser.email){
                                await sendMail(
                                    emailUser.email,
                                    sendData,
                                    subject,
                                    textTemplate
                                );
                               
                             }                
                }
            }
           
            if (dbTrans !== undefined) {
                await dbTrans.commit();
            }

            //Automation code start here
                let automationObj = {
                    request_id : check.request_id,
                    completed_task_id : id,
                    stage_id : check.stage_id
                }
                if(req.body.progress && req.body.progress == 'COMPLETED'){
                    await automationOnCompleteTask(automationObj)
                }
            //Automation code end here

                        
            //End complete request
            return response.success(req, res, { msgCode: "TASK_UPDATED", data: update[1] }, httpStatus.OK);
 
        } catch (err) {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
        }
};
 


exports.getApprovedReqestById = async (req, res) => {
    try {
        //Note : we are not using this api for guest user.

        const { Departments, DsrRequest, CustomRequestTask, RequestDocument, User, RequestTypeStages, TaskDocument, DSRAuditLog} = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);

        let dsrReqCondition =  {id: req.params.id}   
            
        let getRequest = {};
        let allWoorkflowStep = [];

        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            checkDsrReqCond = {                                              
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.

        let stepOrder = [['id', 'ASC']];        
        
        if(role == "dpo" || role == 'assignee' || role == 'task_assignee'){
            getRequest = await dsrService.getSingleLeftJoinData(DsrRequest, RequestDocument, dsrReqCondition, {}, ['id' , 'workflow_step_id', 'request_type']);
            

            if (!getRequest) {
                return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
            }

            
            if(req.query.request_for && req.query.request_for == 'request'){
                //get request step
                allWoorkflowStep = await dsrService.getAllRecord(RequestTypeStages, { type_id: getRequest.request_type}, ['id', 'type_id', 'step_title', 'guidance_text', 'order'], limit, offset, stepOrder);
                getRequest.steps = allWoorkflowStep
            }
        }

        //get task by step.       
        let taskCond = {
            request_id: req.params.id
        }

        if(req.query.request_for && req.query.request_for == 'request'){
            let stageId = null
            if(req.query.workflow_step_id){
                stageId = req.query.workflow_step_id
            } else {
                
                stageId = allWoorkflowStep[0]?.id 
            }

            taskCond[Op.or] = [
                //{ progress: 'COMPLETED' },
                { stage_id : stageId }
            ];
            
        } else {
            const userIdsToFind = [req.data.userId];

            taskCond['assignee_id'] = {
                [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
            }
        }

        const allTask = await dsrService.dsrTaskListing(CustomRequestTask, TaskDocument, Departments, RequestTypeStages, taskCond, '', {}, {}, '', '', ['name'], ['step_title'], limit, offset, stepOrder);
        if(allTask.length > 0){
            for(key in allTask) {                
                if(allTask[key].assignee_id){
                    let taskUserCond = {}
                    //task can be created by dpo or assignee. 
                    if(role == "dpo"){                                            
                        taskUserCond = allTask[key].assignee_id
                    } else {
                        if(req.query.request_for && req.query.request_for == 'request'){
                            taskUserCond = allTask[key].assignee_id
                        } else {
                            taskUserCond = allTask[key].created_by
                        }                        
                    }
                    const allUser = await dsrService.getAllRecord(User, {id : taskUserCond}, ['firstName', 'lastName']); 
                    allTask[key].users = allUser;
                } else {
                    allTask[key].users = [];             
                }                                              
            }
        }   
        
        getRequest.tasks = allTask

        // if(req.query.request_for && req.query.request_for == 'request'){
        //     if(role == "dpo" || role == 'assignee' || role == 'task_assignee'){
        //         //get logs
        //         let auditCond = { type_id: req.params.id, type : "DSR", step: req.query.step }
        //         const allLogs = await dsrService.getMultiLeftJoinWithoutCount(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'createdAt'], ['firstName', 'lastName']);
        //         getRequest.logs = allLogs
        //     }
        // }

        getRequest.role = role
        
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateRequest = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DsrRequest, User, DSRAuditLog, RequestTypeStages, DataSubject } = db.models;
        
        let dsr_request_id = req.params.dsr_request_id
                
        const check = await commonService.findByCondition(DsrRequest, { id: dsr_request_id, customer_id: req.data.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if(req.body.extended && req.body.extended == "YES"){
            let deadlineDate = check.deadline_date
            let date = new Date(deadlineDate);
            date.setDate(date.getDate() + dsrConfig.dsr_request_extended_days);

            // Format the new date back to a string if needed
            let newDeadlineDate = date.toISOString(); // This will give you the date in ISO format
            req.body.deadline_date = newDeadlineDate

            const user = await commonService.findByCondition(User, {
                id: req.data.userId
            }, ['firstName', 'lastName', 'email']);
            if (!user) {
                return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
    
            assignedToName = `${user.firstName} ${user.lastName}`;
            const auditAction = `${assignedToName} extended the request`;
            if(req.body.workflow_step_id){
                
                const auditLog = await auditLogger.createDetailedAuditLog({
                    dsrId: dsr_request_id,
                    stepId: req.body.workflow_step_id,
                    actorId: req.data.userId,
                    actionType: 'DEADLINE_EXTENSION',
                    before: {
                        deadline_date: dsrRequestBefore.deadline_date,
                        extended: dsrRequestBefore.extended
                    },
                    after: {
                        deadline_date: newDeadlineDate,
                        extended: "YES"
                    },
                    metadata: {
                        dsr_id: dsrRequestBefore.dsr_id,
                        extension_days: dsrConfig.dsr_request_extended_days,
                        reason: req.body.extension_reason || "Not specified"
                    }
                }, req, dbTrans);
                
                if (!auditLog) {
                    return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
            
            
        }

        //Send email after verify step.
        if(req.body.workflow_step_id){
            //check if mail already sent.
            if(check.is_acknowledge_mail_sent == "NO"){
                //check if current step is acknowldege.
                let allWoorkflowStep = await dsrService.getAllRecord(RequestTypeStages, { type_id: check.request_type}, ['id', 'type_id', 'step_title', 'guidance_text', 'order']);
                if(allWoorkflowStep[1]?.id == req.body.workflow_step_id){
                    //send email
                   
                    const textTemplate = "dsr_mail.ejs";                    
                    const dataSubject = await commonService.findByCondition(DataSubject, { id: check.id});
                    const sendData = {
                        content: `<p>Dear ${dataSubject?.first_name} ${dataSubject?.last_name},</p>
                        <p>We’re pleased to inform you that your request has been successfully verified.</p>
                        <p>If you have any questions or need further assistance, feel free to reach out.</p>`,                       
                    };
                    const subject = `Request Verification Complete`;

                    await sendMail(
                        dataSubject?.email,
                        sendData,
                        subject,
                        textTemplate
                    );
                    req.body.is_acknowledge_mail_sent = "YES"
                }
            }
        }
        
        const update = await commonService.updateData(DsrRequest, req.body, { id: dsr_request_id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        return response.success(req, res, { msgCode: "REQUEST_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getAllDepartments = async (req, res) => {
    try {
        const { Departments } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        
        let departmentCondition = { customer_id:  req.data.customer_id};
        
        if (search) {
            departmentCondition = { ...departmentCondition, ...{ name: { [Op.iLike]: `%${search}%` } } };
        }
        // get departments
        const getDepartment = await commonService.getList(Departments, departmentCondition, ['id' ,'name' ,'parent_id' , 'customer_id' ,'spoc_id'], limit, offset , order);
        if (!getDepartment) {
            return response.error(req, res, { msgCode: 'DEPARTMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "DEPARTMENT_FETCHED", data: getDepartment }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.send_guestside_mail = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DataSubject, DsrMailDocuments, DsrSentEmail, DsrRequest, User, Role } = db.models;
            
        const check = await commonService.findByCondition(DsrRequest, { id: req.body.dsr_request_id, customer_id: req.body.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //get assignee email id
        let assignee = await commonService.findByCondition(User, { id: check.assignee_id}, ['id', 'email']);
        if (!assignee) {
            const userWithROle = await commonService.getDataAssociate(User, Role, { customer_id: req.body.customer_id }, {role_name: "Data Protection Officer" }, ['id', 'email'],{});
            assignee = userWithROle
        }
        //get subject info
        const dataSubject = await commonService.findByCondition(DataSubject, { id: check.data_subject_id});

        //insert email content in db.
        const sendData = {
            content: req.body.content,
            request_id: req.body.dsr_request_id,
            user_id : req.data.userId,
            customer_id : req.body.customer_id,
            mail_type : 'EXTERNAL',
            first_name : dataSubject.first_name,
            last_name : dataSubject.last_name,
        };
        const saveMail = await commonService.addDetail(DsrSentEmail, sendData, dbTrans);
       
        if(!saveMail){
            return response.error(req, res, { msgCode: 'EMAIL_FAILURE' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const bulkData = [];
        const attachmentUrls = []

        if(req.body?.documents){
            if(req.body?.documents.length > 0){
                req.body?.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_sent_mail_id: saveMail.id,
                        dsr_request_id: req.body.dsr_request_id
                    })

                    attachmentUrls.push(data.url)
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(DsrMailDocuments, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
        const assigneeEmail = assignee.email;
        const emailList = Array.isArray(assigneeEmail) ? assigneeEmail : [assigneeEmail];
        const textTemplate = "dsr_mail.ejs";
        const subject = `Mail from ${req.data.email}`;
        await sendMailsWithMultipleAttachments(
            emailList,
            sendData,
            subject,
            textTemplate,
            attachmentUrls
        );
       
         return response.success(req, res, { msgCode: "EMAIL_SUCCESS", data: [] }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.send_assigneeside_mail = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DataSubject, DsrMailDocuments, DsrSentEmail, DsrRequest, User, DSRAuditLog } = db.models;
            
        const check = await commonService.findByCondition(DsrRequest, { customer_id : req.data.customer_id, id: req.body.dsr_request_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const dataSubject = await commonService.findByCondition(DataSubject, { id: check.data_subject_id});
        if (!dataSubject) {
            return response.error(req, res, { msgCode: 'DATA_SUBJECT_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        //get assignee email id
        let assigneeValidation = [];
        let emailIds = []
        if(req.body.mail_type == 'EXTERNAL'){
            assigneeValidation.push(dataSubject.email)
            emailIds.push(dataSubject.email)
        } else {
            if(req.body.recipients_email){
                assigneeValidation = req.body.recipients_email
                emailIds = req.body.recipients_email
            }
           
            if(assigneeValidation <= 0){
                return response.error(req, res, { msgCode: 'EMAIL_REQUIRED' }, httpStatus.BAD_REQUEST, dbTrans);
            }
            
        }

        let assignee = []
        if(req.body.mail_type == 'INTERNAL'){
            assignee = await dsrService.getAllRecord(User, { email: assigneeValidation, customer_id : req.data.customer_id,}, ['id', 'email']);
            
            if (assignee.length <= 0) {
                return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }

        let recipients_id = []
        if(assignee.length > 0){
            assignee.forEach(data => {
                recipients_id.push(
                    data.id
                )                
            })
        }
        const baseUrl = process.env.FRONTEND_BASE_URL;
        const mailUrl =`${baseUrl}/data-subject-rights/my-request`;
        let sendData;
        if(req.body.mail_type == 'EXTERNAL'){
        //insert email content in db.
         sendData = {
            content: `<p>Dear ${dataSubject.first_name} ${dataSubject.last_name},</p>
            <p>There has been an update on your data subject request (Reference ID: ${check.dsr_id}). Our team has made progress, and you can now view the latest status and details in your portal.</p>
            <p><a href=${mailUrl}>Click here to access your portal</a></p>
            <p>Please log in using the email address you provided when submitting your request.</p>`,
            dsr_link: null, // Add verification link here
            dsr_link_test: null

        };
    }else{
        sendData={
            content: req.body.content,
            dsr_link: null,
            dsr_link_test : "Click Here",
        }
    }
        const subjectData =`Update on Your Data Request (Ref: ${check.dsr_id})`
        let loginUser = await commonService.findByCondition(User, { id : req.data.userId,}, ['id', 'email', 'firstName', 'lastName']);

        const insertData = {
            content: req.body.content,
            request_id: req.body.dsr_request_id,
            user_id : req.data.userId,
            customer_id : req.data.customer_id,
            mail_type : req.body.mail_type,
            subject : req.body.subject,
            recipients_email : emailIds,
            first_name : loginUser.firstName,
            last_name : loginUser.lastName,
        };
        
        if(recipients_id.length > 0){
            insertData['recipients_id'] = recipients_id
        }

        const saveMail = await commonService.addDetail(DsrSentEmail, insertData, dbTrans);
       
        if(!saveMail){
            return response.error(req, res, { msgCode: 'EMAIL_FAILURE' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const bulkData = [];
        const attachmentUrls = []
        if(req.body?.documents){
            if(req.body?.documents.length > 0){
                req.body?.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_sent_mail_id: saveMail.id,
                        dsr_request_id: req.body.dsr_request_id
                    })

                    attachmentUrls.push(data.url)
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(DsrMailDocuments, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
     
        const textTemplate = "dsr_request_notification_mail.ejs";
        
    

        await sendMailsWithMultipleAttachments(
            //assignee.email,
            emailIds,
            sendData,
            subjectData,
            textTemplate,
            attachmentUrls
        );
        let auditAction;
        if(req.body.mail_type == 'EXTERNAL'){
            auditAction = `External chat from ${loginUser.firstName} ${loginUser.lastName} with subject ${req.body.subject}`;
        } else{
            auditAction = `Internal chat from ${loginUser.firstName} ${loginUser.lastName} with subject ${req.body.subject}`;
        }

        
        if(req.body.workflow_step_id){
            const auditLog = await auditLogger.createDetailedAuditLog({
                dsrId: req.body.dsr_request_id,
                stepId: req.body.workflow_step_id,
                actorId: req.data.userId,
                actionType: 'MESSAGE_SEND',
                metadata: {
                    messageType: req.body.mail_type,
                    subject: req.body.subject,
                    recipients: emailIds,
                    documentCount: bulkData.length,
                    documentNames: bulkData.map(doc => doc.original_name),
                    isChat: true,
                    messagePreview: req.body.content.substring(0, 100) + (req.body.content.length > 100 ? '...' : '')
                }
            }, req, dbTrans);
            
            if (!auditLog) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        
         return response.success(req, res, { msgCode: "EMAIL_SUCCESS", data: [] }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getAllGuestrequest = async (req, res) => {
    try {
        const { User, DsrRequest, DataSubject, DsrRequestType, country, RequestTypeStages, CustomRequestTask,Group} = db.models;
        const { status, page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};       
        let dsrReqCondition = {};

        let role = "guest";      
        
        let dataSubCond =  { 'email' : req.data.email}        
              
        const dataSubject = await dsrService.getAllRecord(DataSubject, dataSubCond, ['id', 'email']);
        if(! dataSubject){
            return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: [] }, httpStatus.OK);
        }

        const datSubIds = dataSubject?.map(row => row.id);

        dsrReqCondition =  {data_subject_id : datSubIds}             
                    
        if(status && status !== 'ARCHIVED'){
            dsrReqCondition = { ...dsrReqCondition, ...{ status: status } };
        }
        else {
            dsrReqCondition = { ...dsrReqCondition, ...{ status: { [Op.notIn]: ['ARCHIVED'] } } };
        }

        let getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id' ,'dsr_id', 'createdAt', 'assigned_date','status','business_unit'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], [], limit, offset, order);
       
        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        
        if(getRequest.rows.length > 0){
            for(key in getRequest.rows){
                if(getRequest.rows[key].workflow_step_id){
                    const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.rows[key].workflow_step_id});
                    if (workflowStep) {
                        getRequest.rows[key].step_status = workflowStep.step_title
                    }
                } else {
                    getRequest.rows[key].step_status = null
                }
            }
        }
          await Promise.all(
            (getRequest?.rows || []).map(async (item) => {
              const busiUnit = await commonService.findByCondition(Group, { id: item.business_unit }, ['name']);
              if (busiUnit) {
                item.business_unit = busiUnit.name;
              }
            })
          );
        getRequest['role'] = role
                 
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getGuestRequestById = async (req, res) => {
    try {
        const { DsrMailDocuments, DsrSentEmail, DsrRequest, DataSubject, DsrRequestType, country, RequestDocument, User } = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond =  {email: req.data.email}
    
        let dsrReqCondition =  {id: req.params.id}   
               
        let role = "guest";    
        
        //is user allowed for request
        let isAllowedRequest = await dsrService.getSingleCrossJoinData(DsrRequest, DataSubject, dsrReqCondition, dataSubjCond); 
        if (!isAllowedRequest) {
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.BAD_REQUEST);
        }
    
        let getRequest = await dsrService.getMultiAssocDataById(DsrRequest, DsrRequestType, DataSubject, country, RequestDocument, User, dsrReqCondition, {}, {}, {}, {}, {}, ['id' ,'dsr_id', 'createdAt', 'assigned_date', 'deadline_date', 'customer_id', 'assignee_id'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'email'], ['country_name'], [], [], limit, offset, order);
        
        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        //check if guest is authrise for this request.
        let dataSubCond =  {'email' : req.data.email, 'id' : getRequest.DataSubject.id}             

        const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);
        if(! dataSubject){
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.BAD_REQUEST);    
        }

        let guestCond = {
            request_id : req.params.id,
        }
        
        if(role == "guest"){
            guestCond['mail_type'] =  "EXTERNAL"
        } 

        //get mail data.            
        let getEmails = await dsrService.getSentEmails(DsrSentEmail, User, DsrMailDocuments, guestCond, {}, {}, ['subject', 'content', 'createdAt', 'first_name', 'last_name'], ['firstName', 'lastName'], ['url', 'original_name'], limit, offset, order);            
        getRequest.email = getEmails;
                       
        getRequest.role = role

        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.createGuestRequest = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        

        const { RequestDocument, DsrRequestType, DsrRequest, DataSubject, DsJointParty, RequestTask, RequestTypeStages, CustomRequestTask } = db.models;
        
        const workflow = await commonService.findByCondition(DsrRequestType, { id: req.body.dsr_request_type_id});
        if (!workflow) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }


        let dataSubjectData = {
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            email: req.body.email,
            phone_no: req.body.phone_no,
            is_data_subject: req.body.is_data_subject,
            unique_identification_type: req.body.unique_identification_type,
            unique_identification_number: req.body.unique_identification_number,
            address_1: req.body.address_1,
            address_2: req.body.address_2,
            country_id: req.body.country_id,
            state_id: req.body.state_id,
            city: req.body.city,
            postal_code: req.body.postal_code,
            relationship: req.body.relationship,
            dob : req.body.dob,
            third_party_name : req.body.third_party_name,
            third_party_practice_name : req.body.third_party_practice_name,
            third_party_email : req.body.third_party_email,
            third_party_contact_number : req.body.third_party_contact_number,
            second_address_1: req.body.second_address_1,
            second_address_2: req.body.second_address_2,
            second_country_id: req.body.second_country_id,
            second_state_id: req.body.second_state_id,
            second_city: req.body.second_city,
            second_postal_code: req.body.second_postal_code,
            customer_id: req.body.customer_id,
          //  user_id: req.data.userId,
        }
        
        const insertDataSubject = await commonService.addDetail( DataSubject , dataSubjectData, dbTrans);
        if(!insertDataSubject){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_DATA_SUBJECT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //get lastinserted id 
        const lastInsertedReq = await dsrService.getOneRecord(DsrRequest, {}, null, null, null, [['id', 'DESC']]);
        let lastId = 1;
        if(lastInsertedReq.id){
            lastId = lastInsertedReq.id+1;
        }        
        let dsr_id = workflow.flowtype[0]+'-'+lastId;

        const today = new Date();
        const deadline_date = new Date(today);

        deadline_date.setDate(today.getDate() + dsrConfig.dsr_request_deadline_days);

        const requestData = {
            dsr_id: dsr_id,
            request_type: req.body.dsr_request_type_id,
            description: req.body.description,
            data_subject_id: insertDataSubject.id,
            customer_id: req.body.customer_id,
         //   user_id: req.data.userId,
            request_date: Date.now(),
            deadline_date : deadline_date,
            is_internal_request: req.body.is_internal_request,
            dsr_return_preference: req.body.dsr_return_preference
        }

        if(req.body.business_unit){
            requestData['business_unit'] = req.body.business_unit
        }

        const newRequest = await commonService.addDetail( DsrRequest , requestData, dbTrans);

        if(!newRequest){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //insert documents
        if(req.body.letter_of_authority_doc){
            if(req.body.letter_of_authority_doc.length > 0){
                const bulkData = [];
                req.body.letter_of_authority_doc.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'letter_of_authority'
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(RequestDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        if(req.body.identification_doc){
            if(req.body.identification_doc.length > 0){
                const IdentBulkData = [];
                req.body.identification_doc.forEach(data => {
                    IdentBulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'identification_documents'
                    })
                })

                
                //adding to the database
                const identDocumentUpload = await commonService.bulkAdd(RequestDocument, IdentBulkData, dbTrans);
                if (!identDocumentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }


        if(req.body.joint_party_details){
            let jointPartyDetails = {
                data_subject_id : insertDataSubject.id,
                first_name: req.body.joint_party_details.first_name,
                last_name: req.body.joint_party_details.last_name,
                email: req.body.joint_party_details.email,
                phone_no: req.body.joint_party_details.phone_no,
                unique_identification_type: req.body.joint_party_details.unique_identification_type,
                unique_identification_number: req.body.joint_party_details.unique_identification_number,
                address_1: req.body.joint_party_details.address_1,
                address_2: req.body.joint_party_details.address_2,
                country_id: req.body.joint_party_details.country_id,
                state_id: req.body.joint_party_details.state_id,
                city: req.body.joint_party_details.city,
                postal_code: req.body.joint_party_details.postal_code,
                relationship: req.body.relationship,
                dob : req.body.joint_party_details.dob,
                customer_id: req.body.customer_id,
                //user_id: req.data.userId,
            }

            const addJointPartyDetails = await commonService.addDetail( DsJointParty , jointPartyDetails, dbTrans);
            if(!addJointPartyDetails){
                return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }

        //insert all step task to custom task
        let reqStagesCond = {
            type_id : req.body.dsr_request_type_id
         }
 
         const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title']);   
         const stageIds = stages.map(step => step.id);
         let taskCond = {
             stage_id: {
                 [Op.in]: stageIds, // This creates the WHERE IN clause
             },
         };
         
 
         const getAllTask = await dsrService.getAllRecord(RequestTask, taskCond, ['title', 'guidance_text', 'stage_id', 'created_by']);   
         if(getAllTask.length > 0){
             for(let task of getAllTask){
                 task['request_id'] = newRequest.id
                 task['customer_id'] = req.body.customer_id
                // task['created_by'] = req.data.userId
                 task['is_custom'] = false
                 task['workflow_id'] = workflow.id  
                 
                 const taskkk = await commonService.addDetail(CustomRequestTask, task, dbTrans);
                 if(!taskkk){
                    return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
                 }
             }
         }
       
        return response.success(req, res, { msgCode: "REQUEST_CREATED", data: insertDataSubject }, httpStatus.CREATED, dbTrans);
      

    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.createGuestFormRequestTemp = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        
        const { ActivepiecesWebhooks, DsrRequestType } = db.models;
        
        const workflow = await commonService.findByCondition(DsrRequestType, { id: req.body.dsr_request_type_id});
        if (!workflow) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //temp code start here
        
        const getWebhook = await commonService.findByCondition(ActivepiecesWebhooks, { activepieces_workflow_id: 'bzzqKnuJvow34C90Q7Cck'});
        if (getWebhook) {
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: getWebhook.url,
                headers: { },
                data: {                     
                    dpo_email: "<EMAIL>",
                    user_email: req.body.email,
                    body_data : req.body,
                    request_id : 2,
                    data_subject_id : 2,
                    dpo_id : 1
                }                    
            };
        
            let x = await axios.request(config);
            console.log(x);
            
        }
        return response.success(req, res, { msgCode: "REQUEST_CREATED" }, httpStatus.CREATED, dbTrans);
        
        //temp code end here
    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


exports.createGuestFormRequest = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        

        const { Customer, RequestDocument, DsrRequestType, DsrRequest, DataSubject, DsJointParty, RequestTask, RequestTypeStages, CustomRequestTask, User } = db.models;
        
        const workflow = await commonService.findByCondition(DsrRequestType, { id: req.body.dsr_request_type_id});
        if (!workflow) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const secretKey = '321@123';
        const encryptedCustomerId = req.body.customer_id.replaceAll('Por21Ld', '/');; 
        const custId = CryptoJS.AES.decrypt(encryptedCustomerId, secretKey);  
        const decodedCustomerId = custId.toString(CryptoJS.enc.Utf8)
        
        // check customer
        const checkCustomer = await commonService.findByCondition(Customer, { id: decodedCustomerId }, ['id']);
        if (!checkCustomer) {
            return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let dataSubjectData = {
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            email: req.body.email,
            phone_no: req.body.phone_no,
            is_data_subject: req.body.is_data_subject,
            unique_identification_type: req.body.unique_identification_type,
            unique_identification_number: req.body.unique_identification_number,
            address_1: req.body.address_1,
            address_2: req.body.address_2,
            country_id: req.body.country_id,
            state_id: req.body.state_id,
            city: req.body.city,
            postal_code: req.body.postal_code,
            relationship: req.body.relationship,
            dob : req.body.dob,
            third_party_name : req.body.third_party_name,
            third_party_practice_name : req.body.third_party_practice_name,
            third_party_email : req.body.third_party_email,
            third_party_contact_number : req.body.third_party_contact_number,
            second_address_1: req.body.second_address_1,
            second_address_2: req.body.second_address_2,
            second_country_id: req.body.second_country_id,
            second_state_id: req.body.second_state_id,
            second_city: req.body.second_city,
            second_postal_code: req.body.second_postal_code,
            customer_id: decodedCustomerId,
          //  user_id: req.data.userId,
        }
        
        const insertDataSubject = await commonService.addDetail( DataSubject , dataSubjectData, dbTrans);
        if(!insertDataSubject){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_DATA_SUBJECT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //get lastinserted id 
        const lastInsertedReq = await dsrService.getOneRecord(DsrRequest, {}, null, null, null, [['id', 'DESC']]);
        let lastId = 1;
        if(lastInsertedReq.id){
            lastId = lastInsertedReq.id+1;
        }        
        let dsr_id = workflow.flowtype[0]+'-'+lastId;

        const today = new Date();
        const deadline_date = new Date(today);

        deadline_date.setDate(today.getDate() + dsrConfig.dsr_request_deadline_days);

        const requestData = {
            dsr_id: dsr_id,
            request_type: req.body.dsr_request_type_id,
            description: req.body.description,
            data_subject_id: insertDataSubject.id,
            customer_id: decodedCustomerId,
         //   user_id: req.data.userId,
            request_date: Date.now(),
            deadline_date : deadline_date,
            is_internal_request: req.body.is_internal_request,
            dsr_return_preference: req.body.dsr_return_preference
        }

        if(req.body.business_unit){
            requestData['business_unit'] = req.body.business_unit
        }

        const newRequest = await commonService.addDetail( DsrRequest , requestData, dbTrans);

        if(!newRequest){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //insert documents
        if(req.body.letter_of_authority_doc){
            if(req.body.letter_of_authority_doc.length > 0){
                const bulkData = [];
                req.body.letter_of_authority_doc.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'letter_of_authority'
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(RequestDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        if(req.body.identification_doc){
            if(req.body.identification_doc.length > 0){
                const IdentBulkData = [];
                req.body.identification_doc.forEach(data => {
                    IdentBulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'identification_documents'
                    })
                })

                
                //adding to the database
                const identDocumentUpload = await commonService.bulkAdd(RequestDocument, IdentBulkData, dbTrans);
                if (!identDocumentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        if(req.body.joint_party_details_first_name &&  req.body.joint_party_details_first_name.length > 0){
            for(key in req.body.joint_party_details_first_name){
                let jointPartyDetails = {
                    data_subject_id : insertDataSubject.id,
                    first_name: req.body.joint_party_details_first_name,
                    last_name: req.body.joint_party_details_last_name,
                    email: req.body.joint_party_details_email,
                    phone_no: req.body.joint_party_details_phone_no,
                    unique_identification_type: req.body.joint_party_details_unique_identification_type,
                    unique_identification_number: req.body.joint_party_details_unique_identification_number,
                    address_1: req.body.joint_party_details_address_1,
                    address_2: req.body.joint_party_details_address_2,
                    country_id: req.body.joint_party_details_country_id,
                    state_id: req.body.joint_party_details_state_id,
                    city: req.body.joint_party_details_city,
                    postal_code: req.body.joint_party_details_postal_code,
                    relationship: req.body.relationship,
                    dob : req.body.joint_party_details_dob,
                    customer_id: decodedCustomerId,
                    
                }
               
                const addJointPartyDetails = await commonService.addDetail( DsJointParty , jointPartyDetails, dbTrans);
                if(!addJointPartyDetails){
                    return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }

            
        }

        //insert all step task to custom task
        let reqStagesCond = {
            type_id : req.body.dsr_request_type_id
         }
 
         let reqTypeOrder = [['id', 'ASC']];
         const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);   
         const stageIds = stages.map(step => step.id);
         let taskCond = {
             stage_id: {
                 [Op.in]: stageIds, // This creates the WHERE IN clause
             },
         };
         
 
            let activepieces_automation_id_array = [];   
            const taskOrder = [
                ['stage_id', 'ASC'],  // First, order by stage_id ascending
                ['id', 'ASC']         // Then, order by id ascending within each stage
            ];
            
            const getAllTask = await dsrService.getAllRecord(RequestTask, taskCond, ['title', 'guidance_text', 'reminder_date', 'task_note', 'start_date', 'due_date', 'completion_date', 'department_id', 'requirement', 'assignee_id', 'stage_id', 'activepieces_automation_id', 'due_days'], null,null,taskOrder);   
            let runAutomation = false;
            let count = 1;
            if(getAllTask.length > 0){
                for(let task of getAllTask){
                    task['request_id'] = newRequest.id
                    task['customer_id'] = decodedCustomerId
                   // task['created_by'] = req.data.userId
                    task['is_custom'] = false
                    task['workflow_id'] = workflow.id  
                    
                    const taskkk = await commonService.addDetail(CustomRequestTask, task, dbTrans);
                    if(taskkk.activepieces_automation_id){
                        if(count == 1){
                            runAutomation = true;
                        }
                        if(runAutomation){
                            activepieces_automation_id_array.push({
                                activepieces_automation_id : taskkk.activepieces_automation_id,
                                custom_task_id : taskkk.id
                            });
                        }
                    } else {
                        runAutomation = false;
                    }
                    
                    count++
                    if(!taskkk){
                        return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
                    }

                    if(task.assignee_id){
                        //send email to task assignee.
                        
                        const allUser = await dsrService.getAllRecord(User, {id : task.assignee_id}, ['firstName', 'lastName', 'email']);
            
                        const textTemplate = "dsr_mail.ejs";                    
                        const subject = `Notification of DSR Task Assignment`;
                        let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                        for(let emailUser of allUser){
                            let contentText = `
                                <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                                <p>I hope you are doing well.</p>
                                <p>This is to inform you that a Task has been assigned to you in the Data Subject Request (DSR) with Request ID ${newRequest.dsr_id} for processing. Please review the details below and take the necessary actions to ensure compliance within the required time frame.</p>
                                <p>To begin processing the request, please log into the Gotust platform and follow the steps outlined there.</p>
                                <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                                <p>We appreciate your prompt attention to this matter.</p>
                                <p>Thank you for your cooperation.</p>
                            `;
            
                            const sendData = {
                                content: contentText,
                                
                            };
            
                            if(emailUser.email){
                                await sendMail(
                                    emailUser.email,
                                    sendData,
                                    subject,
                                    textTemplate
                                );
                            }                
                        }
                    }
                }
            }

            if (dbTrans !== undefined) {
                await dbTrans.commit();
            }

            //call webhook     
            //const customeReqTasks = await dsrService.getAllRecord(CustomRequestTask, {workflow_id : req.body.dsr_request_type_id}, ['id' , 'stage_id', 'workflow_id', 'activepieces_automation_id'], null, null, reqTypeOrder);   
            
            console.log('=== activepieces log')
            console.log(activepieces_automation_id_array)
            if(activepieces_automation_id_array){
                for(let activepiecesObj of activepieces_automation_id_array){
                    console.log('=== inside for loop')
                    if(activepiecesObj){
                        console.log('=== inside if condition')
                        
                        const activepiecesWebhookUlr = process.env.ACTIVEPIECES_WEBHOOK_URL ? process.env.ACTIVEPIECES_WEBHOOK_URL : "https://gt-workflow.gotrust.tech";
                        let webhookurl = activepiecesWebhookUlr+"/api/v1/webhooks/"+activepiecesObj.activepieces_automation_id;
                        let paramData = {                     
                            task_id: activepiecesObj.custom_task_id,
                            activepieces_automation_id: activepiecesObj.activepieces_automation_id,
                            phone_number : req.body.phone_no,
                            dsr_request_id: newRequest.id,
                            data_subject_id: insertDataSubject.id  
                        } 
                
                        let jsonData = JSON.stringify(paramData)

                        webhookurl = `${webhookurl}?custom_data=${jsonData}`
                        if (webhookurl) {
                            let config = {
                                method: 'post',
                                maxBodyLength: Infinity,
                                //url: getWebhook.url,
                                url: webhookurl,
                                //url: `https://workflow-dev.gotrust.tech/api/v1/webhooks/${activepiecesObj.activepieces_automation_id}?custom_data=${jsonData}`,
                                headers: { },
                                // httpsAgent: new https.Agent({
                                //     rejectUnauthorized: false  // Disable SSL certificate validation
                                // }),
                                
                                // data: {
                                //     custom_task_id :  
                                //     data_subject_id : insertDataSubject.id,   
                                //     request_id : newRequest.id,                 
                                    
                                //     dpo_email: req.data.email,
                                //     user_email: req.body.email,
                                //     body_data : req.body,                                
                                //     dpo_id : 1
                                
                                // }                   
                            };
                        
                            let x = await axios.request(config);
                            
                            console.log(x);
                            console.log('=== webhook is called')
                        }
                    }
                }
            }
            
            const token = await signToken({
                email: req.body.email,
                data_subject_id: insertDataSubject.id
            },36000);

            const generateVerificationToken = (token) => {
                // Implement token generation logic (e.g., JWT or random string)
                
                const baseUrl = process.env.FRONTEND_BASE_URL;

                return `${baseUrl}/data-subject-rights/my-request`;  // Example URL with token
            };

            const sendData = {
                content: 'You are just one click away from getting started with your DSR request. Simply verify your email address to activate your GoTrust account and track your DSR request.',
                dsr_link: generateVerificationToken(token),  // Add verification link here
                dsr_link_test : "Click here to email confirmation"
            };
            
            const textTemplate = "dsr_verify_mail.ejs";                    
            const subject = `Email Verification for ${req.body.email}`;

            
            await sendMail(
                req.body.email,
                sendData,
                subject,
                textTemplate
            );
        //end of verify email here
      
  

       
        return response.success(req, res, { msgCode: "REQUEST_CREATED", data: insertDataSubject }, httpStatus.CREATED);
      

    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getAllInfoById = async (req, res) => {
    try {
        const { DsJointParty, DsrMailDocuments, DsrSentEmail, DsrRequest, DataSubject, DsrRequestType, country, RequestDocument, User, RequestTypeStages, CustomRequestTask,Group,state} = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let dataSubjCond = {};
       
        let dsrReqCondition =  {id: req.params.id, customer_id : req.data.customer_id,}   
               
        let getRequest = null;


        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.

        if(role == "guest"){
            getRequest = await dsrService.getMultiAssocDataById(DsrRequest, DsrRequestType, DataSubject, country, RequestDocument, User, dsrReqCondition, {}, dataSubjCond, {}, {}, {}, ['id' ,'dsr_id', 'createdAt', 'assigned_date', 'deadline_date'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'email'], ['country_name'], [], [], limit, offset, order);
        } else {
            getRequest = await dsrService.getMultiAssocDataById(DsrRequest, DsrRequestType, DataSubject, country, RequestDocument, User, dsrReqCondition, {}, dataSubjCond, {}, {}, {} );
        }
        if(getRequest?.business_unit){
            const businessName = await commonService.findByCondition(Group,{id:getRequest.business_unit},['name']);
            getRequest.business_unit=businessName.name
            
        }
        if(getRequest?.DataSubject?.state_id){
            const stateName = await commonService.findByCondition(state,{country_id:getRequest.DataSubject.country_id,id:getRequest.DataSubject.state_id},['state_name']);
            getRequest.DataSubject.state_id = stateName.state_name
        }

        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }


        if(getRequest.workflow_step_id){
            const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.workflow_step_id});
            if (workflowStep) {
                getRequest.step_status = workflowStep.step_title
            }
        } else {
            getRequest.step_status = null
        }


        let guestCond = {
            customer_id : req.data.customer_id,
            request_id : req.params.id,
        }
        
        if(role == "guest"){
            guestCond['mail_type'] =  "EXTERNAL"
        } 

        //get mail data.            
        let jointParty = await dsrService.getOneRecord(DsJointParty, {'data_subject_id' : getRequest.data_subject_id});            
        getRequest.joint_party = jointParty;
                       
        getRequest.role = role

        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getAllInfoByIdForGuest = async (req, res) => {
    try {
        const { DsJointParty, DsrRequest, DataSubject, DsrRequestType, country, RequestDocument, User, RequestTypeStages,Group,state} = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        
        let dsrReqCondition =  {id: req.params.id}   
        let dataSubjCond =  {email: req.data.email}
        let getRequest = null;
        let role = "guest"

        //is user allowed for request
        let isAllowedRequest = await dsrService.getSingleCrossJoinData(DsrRequest, DataSubject, dsrReqCondition, dataSubjCond); 
        if (!isAllowedRequest) {
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.BAD_REQUEST);
        }

        getRequest = await dsrService.getMultiAssocDataById(DsrRequest, DsrRequestType, DataSubject, country, RequestDocument, User, dsrReqCondition, {}, {}, {}, {}, {}, 
            ['id' ,'dsr_id', 'createdAt', 'assigned_date', 'deadline_date', 'description', 'business_unit', 'dsr_return_preference', 'is_internal_request'], ['id', 'flowtype'],
            ['id', 'is_data_subject', 'first_name', 'last_name', 'unique_identification_type', 'unique_identification_number', 'address_1', 'address_2', 'country_id', 'state_id', 'city', 'postal_code', 'relationship', 'email', 'is_email_verified', 'phone_no', 'dob', 'third_party_name', 'third_party_practice_name', 'third_party_email', 'third_party_contact_number', 'second_address_1', 'second_address_2', 'second_country_id', 'second_state_id', 'second_city', 'second_postal_code', 'customer_id', 'user_id'],
            ['country_name'], ['url', 'original_name', 'document_type'], [], limit, offset, order);
        
        if (!getRequest) {
            return response.error(req, res, { msgCode: 'REQUEST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }


        if(getRequest.workflow_step_id){
            const workflowStep = await commonService.findByCondition(RequestTypeStages, { id: getRequest.workflow_step_id});
            if (workflowStep) {
                getRequest.step_status = workflowStep.step_title
            }
        } else {
            getRequest.step_status = null
        }
        if(getRequest?.business_unit){
            const businessName = await commonService.findByCondition(Group,{id:getRequest.business_unit},['name']);
            getRequest.business_unit=businessName.name
            
        }
        if(getRequest?.DataSubject?.state_id){
            const stateName = await commonService.findByCondition(state,{country_id:getRequest.DataSubject.country_id,id:getRequest.DataSubject.state_id},['state_name']);
            getRequest.DataSubject.state_id = stateName.state_name
        }

        let guestCond = {
            customer_id : req.data.customer_id,
            request_id : req.params.id,
        }
        
        if(role == "guest"){
            guestCond['mail_type'] =  "EXTERNAL"
        } 

        //get mail data.            
        let jointParty = await dsrService.getOneRecord(DsJointParty, {'data_subject_id' : getRequest.data_subject_id});            
        getRequest.joint_party = jointParty;
                       
        getRequest.role = role

        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};



exports.getStepWiseAuditLog = async (req, res) => {
    try{
        const { DSRAuditLog, User, DsrRequest, CustomRequestTask } = db.models;
        let auditCond = { type_id: req.params.req_id, type : "DSR" , step: req.query.step }
        // const allLogs = await dsrService.getMultiLeftJoinWithoutCount(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName']);
        // if(!allLogs){
        //     return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        // }
        //get role start here
        let role = null;        
        if(req.data.roleName == "Data Protection Officer"){
            role = 'dpo'
        } else {
            //if request is assigned then user is manager else user is assignee.
            checkDsrReqCond = {
                'customer_id' : req.data.customer_id,
                'assignee_id' : req.data.userId,
            }

            let checkDsrRequest = await dsrService.getOneRecord(DsrRequest, checkDsrReqCond, ['id']);
            if(checkDsrRequest){
                role = 'assignee'
            } else {
                //check if task is assigned 
                const userIdsToFind = [req.data.userId];

                let taskCondition = {
                    assignee_id: {
                        [Op.overlap]: userIdsToFind // This checks for any overlap between the array in the column and the provided array
                    },
                    customer_id :  req.data.customer_id
                }

                const isTaskAssign = await dsrService.getOneRecord(CustomRequestTask, taskCondition, ['id']);
                if(isTaskAssign){
                    role = 'task_assignee'
                } else {
                    role = 'guest'
                }                
            }
        }        
        //get role end here.


        if(req.query.request_for && req.query.request_for == 'request'){
            if(role == "dpo" || role == 'assignee' || role == 'task_assignee'){
                const allLogs = await dsrService.getMultiLeftJoinWithoutCount(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'createdAt'], ['firstName', 'lastName']);
                if(!allLogs){
                    return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
                }
                return response.success( req, res, { msgCode: "AUDIT_LOG_FETCHED", data: allLogs }, httpStatus.OK);
            }
        }
        return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        
    }
    catch (err) {
        console.log("audit errorr",err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};





exports.getAuditLog = async (req, res) => {
    try{
        const { DSRAuditLog, User, RequestTypeStages, DsrRequest, DsrRequestType } = db.models;
        let auditCond = { type_id: req.params.req_id, type : "DSR" }
        const allLogs = await dsrService.getMultiLeftJoinWithoutCount(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName']);
        // const allLogs = await dsrService.getMultiLeftJoinWithGroupByAndAggregation(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName'],['DSRAuditLog.step']);
        // const allLogs = await commonService.listGroupBy(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName'], ['step']);
        if(!allLogs){
            return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        const reqData = await commonService.getListWith3Models(DsrRequest, DsrRequestType, RequestTypeStages, {id: req.params.req_id}, {}, {}, {}, {}, {});
        if(!reqData){
            return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        const groupedByStep = allLogs.reduce((acc, log) => {
            const step = log.step !== null ? log.step : 'null';
            if (!acc[step]) {
                acc[step] = [];
            }
            acc[step].push(log);
            return acc;
        }, {});

        const stagesByOrder = reqData[0]?.DsrRequestType?.RequestTypeStages.reduce((map, stage) => {
            map[stage.id] = stage.step_title;
            return map;
          }, {});
        
        
        // Transform groupedByStep to be keyed by step_title
        const updatedGroupedByStep = Object.keys(groupedByStep).reduce((result, key) => {
            const stepOrder = parseInt(key.trim(), 10); // Convert key to number and remove extra spaces
            const stepTitle = stagesByOrder[stepOrder] || key; // Use key if no step_title matches
            groupedByStep[key].forEach((entry) => {
                const updatedEntry = { ...entry, step: stepOrder || null }; // Retain numeric step value in the "step" field
                if (!result[stepTitle]) result[stepTitle] = [];
                result[stepTitle].push(updatedEntry);
              }); 
            return result;
        }, {});
    
        return response.success( req, res, { msgCode: "AUDIT_LOG_FETCHED", data: updatedGroupedByStep }, httpStatus.OK);
    }
    catch (err) {
        console.log("audit errorr",err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.downloadAuditLog = async (req, res) => {
    try{
        const { DSRAuditLog, DsrRequest, DsrRequestType, DataSubject, RequestDocument, RequestTypeStages, User, Customer } = db.models;
        let auditCond = { type_id: req.params.req_id, type : "DSR" }
        // let userCon = { user_id: req.data.userId };
        let dsrReqCondition =  {id: req.params.req_id} 

        const getRequest = await dsrService.getMultiAssocDataByIdV2(DsrRequest, DsrRequestType, DataSubject, Customer, RequestDocument, User, dsrReqCondition, {}, {}, {}, {}, {}, ['id' ,'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'description', 'reject_reason', 'status', 'business_unit'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email', 'phone_no', 'postal_code'], ['name'], ['original_name', 'url', 'document_type'], ['firstName', 'lastName']);
        if(!getRequest){
            return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        
        const allLogs = await dsrService.getMultiLeftJoinWithoutCount(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName']);
        // const allLogs = await dsrService.getMultiLeftJoinWithGroupByAndAggregation(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName'],['DSRAuditLog.step']);
        // const allLogs = await commonService.listGroupBy(DSRAuditLog, User, auditCond, {}, ['id', 'action', 'step', 'createdAt'], ['firstName', 'lastName'], ['step']);
        if(!allLogs){
            return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        console.log("----------", allLogs)
        const reqData = await commonService.getListWith3Models(DsrRequest, DsrRequestType, RequestTypeStages, dsrReqCondition, {}, {}, {}, {}, {});
        if(!reqData){
            return response.error( req, res, { msgCode: 'AUDIT_LOG_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        console.log("=============", reqData[0]?.DsrRequestType?.RequestTypeStages)
        // Create a mapping of step order to step title
        const stepMapping = reqData[0]?.DsrRequestType?.RequestTypeStages.reduce((map, stage) => {
            map[stage.id] = stage.step_title;
            return map;
          }, {});
          // Update allLogs to replace step with step_title
          const updatedLogs = allLogs.map((log) => {
            const updatedStep = log.step !== null ? stepMapping[log.step] || log.step : null;
            return { ...log, step: updatedStep };
          });
        

        const excelData = await transformAuditData(updatedLogs);
        
        const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
        console.log(excelData);
        const excelFile = await createExcelForAuditData(excelData, date, getRequest?.Customer?.name, getRequest?.dsr_id, getRequest?.DsrRequestType?.flowtype, getRequest?.description);
        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        const mailData = {
            name: `${user?.firstName} ${user?.lastName}`,
            reqType: getRequest?.DsrRequestType?.flowtype,
            reqDetail: getRequest?.description
        }

        await sendMailWithAttach(
            req.data.email,
            mailData,
            'Your Copy of DSR Audit Log file made on GoTrust',
            'dsr_audit_download.ejs',
            excelFile
        );

        return response.success(req, res, { msgCode: 'AUDIT_DOWNLOADED' }, httpStatus.OK );
    }catch (err) {
        console.log("audit errorr",err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.sendReminderForRequestAssigneeInDSR = async (req, res) => {
    try {
        const { User, DsrRequest, DataSubject, DsrRequestType, country} = db.models;
        let dataSubjCond = {};
        let dsrReqCondition = {};
        let limit = null;       
        dsrReqCondition =  {
           // id: { [Op.in]: [350,320] },
            status: { [Op.in]: ['APPROVED', 'PENDING'] },
            assignee_id: {
                [Op.ne]: null  // Sequelize condition to check for NOT NULL
            },
            createdAt: {
                [Op.lt]: Sequelize.literal("CURRENT_TIMESTAMP - INTERVAL '5 days'") // Subtract 5 days from the current timestamp
            }
            // deadline_date: {
            //     [Op.lt]: Sequelize.literal("CURRENT_TIMESTAMP - INTERVAL '1 week'") // Get records where deadline_date is 1 week before now
            //   //  [Op.lt]: Sequelize.literal("deadline_date - INTERVAL 7 DAY")
            // }
        }
                            
        let getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id', 'assignee_id', 'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason', 'business_unit', 'status', 'request_type'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], ['firstName', 'lastName', 'email'], limit);
     
        
        if(getRequest.rows.length > 0){
            for(key in getRequest.rows){
                if(getRequest.rows[key].User){
                    let emailUser = getRequest.rows[key].User;
                    if (emailUser.email) {
                                           
                        
                        let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                        let contentText = `
                            <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                            <p>This is a gentle reminder that the Data Subject Request (DSR) with Request ID ${getRequest.rows[key].dsr_id} is still pending and requires your attention. As per the compliance requirements, it is essential to complete this task within the stipulated timeframe to ensure adherence to privacy regulations. To review and process the request, please log into the GoTrust platform using the link below:</p>
                            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                            <p>Kindly prioritize this task and take the necessary actions at the earliest.</p>
                            <p>If you encounter any issues or need assistance, please do not hesitate to reach out.</p>
                            <p>Your prompt action is greatly appreciated.</p>
                            <p>Thank you for your cooperation.</p>
                        `;
        
                        
                    }
                    
                }
            }
        }
        
            
        return response.success(req, res, { msgCode: "REQUEST_FETCHED", data: getRequest }, httpStatus.OK);

    } catch (err) {
        console.log(err);
    }
};
exports.createRequestV2 = async(req,res)=>{
    const dbTrans = await db.transaction()
    try{
        const {Customer,DSRCustomerControls,DSRControls,DSRAnswers,DataSubject,DsrRequest,DsrRequestType,RequestTypeStages,RequestTask,CustomRequestTask,User,DsrForms,PendingVerification,RequestDocument}=db.models
        const { answers, form_id, answered_by ,customer_id} = req.body;
        const order = [['order','ASC']]
        const sanitizeKey = (str) => {
            return str
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '_') // Replace non-alphanumeric characters with a single underscore
              .replace(/^_+|_+$/g, '');    // Remove leading and trailing underscores
          };
             
        const decodedCustomerId =CryptoJS.AES.decrypt(customer_id.replaceAll('Por21Ld', '/'), '321@123').toString(CryptoJS.enc.Utf8)
        const decodedFormrId = CryptoJS.AES.decrypt(form_id.replaceAll('Por21Ld', '/'), '321@123').toString(CryptoJS.enc.Utf8)

          const [authData, masterQuestions, customerControls,] = await Promise.all([
            commonService.findByCondition(DsrForms, {id: decodedFormrId,customer_id: decodedCustomerId}, ['id', 'authentication', 'authentication_type', 'busi_unit_id']),
            commonService.getListWithoutCount(DSRControls, {}, {}),
            commonService.getList(DSRCustomerControls, { form_id: decodedFormrId}, ['id', 'question_id', 'order'], null, null, [['order', 'ASC']])
        ]);

        // Optimization 3: Use Map instead of object for faster lookups
        const masterQuestionMap = new Map(masterQuestions.map(question => [sanitizeKey(question.title), question.id]));
        const questionMap = new Map(customerControls.rows.map(control => [control.id, control.question_id]));
        // console.log(questionMap)
        // Step 3: Separate master and custom answers
        const processAnswers = (answers, questionMap) => {
            const masterAnswers = {};
            const customAnswers = {};

            for (const [customerControlId, answer] of Object.entries(answers)) {
                const masterQuestionId = questionMap.get(parseInt(customerControlId));
                if (masterQuestionId) {
                    masterAnswers[masterQuestionId] = answer;
                } else {
                    customAnswers[customerControlId] = answer;
                }
            }

            return { masterAnswers, customAnswers };
        };
        // console.log(masterAnswers)
        
        const { masterAnswers, customAnswers } = processAnswers(answers, questionMap);
        console.log(masterAnswers)
        const getAnswerValue = (key, defaultValue = '') => masterAnswers[masterQuestionMap.get(key)] || defaultValue;
        // Step 4: Dynamically map dataSubjectFields using masterQuestionMap
        const dataSubjectFields = {
            is_data_subject: getAnswerValue("are_you_data_subject") === "Yes",
            first_name: getAnswerValue("first_name"),
            last_name: getAnswerValue("last_name"),
            country_id: parseInt(getAnswerValue("country"), 10),
            relationship: getAnswerValue("what_is_your_relationship_with_us").toUpperCase(),
            email: getAnswerValue("email").toLowerCase(),
            phone_no: getAnswerValue("phone_number") || null,
            request_type: parseInt(getAnswerValue("request_type"), 10),
            customer_id: parseInt(decodedCustomerId)
        };
        const otherQuestion = {
            is_internal_request:getAnswerValue("office_use_only_is_this_an_internal_request")==="Yes",
            dsr_return_preference:getAnswerValue("dsr_request_materials_return_preference").toUpperCase()||"",
            identification_doc:getAnswerValue("documents_upload")
        }
        const workflow = await commonService.findByCondition(DsrRequestType, { id: dataSubjectFields.request_type});
        if (!workflow) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        console.log(dataSubjectFields)
        const insertDataSubject = await commonService.addDetail( DataSubject , dataSubjectFields, dbTrans);
        if(!insertDataSubject){
            return response.error( req, res, { msgCode: 'ERROR_CREATING_DATA_SUBJECT' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //get lastinserted id 
        const lastInsertedReq = await dsrService.getOneRecord(DsrRequest, {}, null, null, null, [['id', 'DESC']]);
        let lastId = 1;
        if(lastInsertedReq.id){
            lastId = lastInsertedReq.id+1;
        }        
        const dsr_id = workflow.flowtype[0]+'-'+lastId;
        const today = new Date();
        const deadline_date = new Date(today);

        deadline_date.setDate(today.getDate() + dsrConfig.dsr_request_deadline_days);

        const requestData = {
            dsr_id: dsr_id,
            request_type: dataSubjectFields.request_type,
            // description: req.body.description,
            data_subject_id: insertDataSubject.id,
            customer_id: decodedCustomerId,
         //   user_id: req.data.userId,
            request_date: Date.now(),
            deadline_date : deadline_date,
            is_internal_request: otherQuestion.is_internal_request?otherQuestion.is_internal_request:null,
            dsr_return_preference: otherQuestion.dsr_return_preference?otherQuestion.dsr_return_preference:null,
        }
        const busi = await commonService.findByCondition(DsrForms,{id:decodedFormrId},['busi_unit_id']);
        if(busi){
            requestData['business_unit']=busi.busi_unit_id;
        }
        requestData['business_unit']=workflow.group_id;
        const newRequest = await commonService.addDetail( DsrRequest , requestData, dbTrans);
        let reqStagesCond = {
            type_id : dataSubjectFields.request_type
         }
         if(otherQuestion.identification_doc){
            if(otherQuestion.identification_doc.length > 0){
                const IdentBulkData = [];
                console.log(otherQuestion.identification_doc)
                otherQuestion.identification_doc.forEach(data => {
                    IdentBulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        dsr_data_subject_id: insertDataSubject.id,
                        dsr_request_id: newRequest.id,
                        document_type : 'identification_documents'
                    })
                })
                //adding to the database
                const identDocumentUpload = await commonService.bulkAdd(RequestDocument, IdentBulkData, dbTrans);
                if (!identDocumentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
 
         let reqTypeOrder = [['id', 'ASC']];
         const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);  
         const stageIds = stages.map(step => step.id);
         let taskCond = {
             stage_id: {
                 [Op.in]: stageIds, // This creates the WHERE IN clause
             },
         };
            const taskOrder = [
                ['stage_id', 'ASC'],  // First, order by stage_id ascending
                ['id', 'ASC']         // Then, order by id ascending within each stage
            ];
            const getAllTask = await dsrService.getAllRecord(RequestTask, taskCond, ['title', 'guidance_text', 'reminder_date', 'task_note', 'start_date', 'due_date', 'completion_date', 'department_id', 'requirement', 'assignee_id', 'stage_id', 'activepieces_automation_id', 'due_days'], null,null,taskOrder);   
            if (getAllTask.length > 0) {
                // Prepare tasks in bulk to insert
                const tasksToInsert = getAllTask.map(task => ({
                    ...task,
                    request_id: newRequest.id,
                    customer_id: decodedCustomerId,
                    is_custom: false,
                    workflow_id: workflow.id
                }));
                // Bulk insert all tasks at once
                const insertedTasks = await commonService.bulkAdd(CustomRequestTask, tasksToInsert, dbTrans);
            
                if (!insertedTasks || insertedTasks.length !== getAllTask.length) {
                    return response.error(req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
                }
                // Fetch all assignees in parallel (to avoid multiple DB calls)
                const assigneeIds = [
                    ...new Set(getAllTask.flatMap(task => 
                        Array.isArray(task.assignee_id) ? task.assignee_id : (task.assignee_id ? [task.assignee_id] : [])
                    ))
                ];
                const allUsers = assigneeIds.length > 0 
                    ? await dsrService.getAllRecord(User, { id: assigneeIds }, ['firstName', 'lastName', 'email']) 
                    : [];
            
                    if (allUsers.length > 0) {
                        const subject = `Notification of DSR Task Assignment`;
                        let loginUrl = process.env.FRONTEND_BASE_URL || "";
                        const textTemplate = "dsr_mail.ejs";    
                    
                        // Prepare all email sending promises
                         allUsers.map(emailUser => {
                            if (emailUser.email) {
                                let contentText = `
                                    <p>Dear ${emailUser.firstName} ${emailUser.lastName},</p>
                                    <p>I hope you are doing well.</p>
                                    <p>This is to inform you that a Task has been assigned to you in the Data Subject Request (DSR) with Request ID ${newRequest.dsr_id} for processing.</p>
                                    <p>Please review the details below and take the necessary actions.</p>
                                    <p>To begin processing the request, please log into the Gotust platform and follow the steps outlined there.</p>
                                    <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                                    <p>We appreciate your prompt attention to this matter.</p>
                                    <p>Thank you for your cooperation.</p>
                                `;
                    
                                const sendData = { content: contentText };
                    
                                // Trigger email without waiting
                                sendMail(emailUser.email, sendData, subject, textTemplate)
                                    .catch(error => console.error(`Failed to send email to ${emailUser.email}:`, error));
                            }
                        });
                    }
                    
            }
            
            
        //end of verify email here
      const answersToInsert = [];
      for (let customerControlId in answers) {
        let answerValue = answers[customerControlId];
    
        // Convert object inside array to JSON string
        if (Array.isArray(answerValue)) {
            answerValue = answerValue.map(item => 
                typeof item === 'object' ? JSON.stringify(item) : item
            );
        }
    
        answersToInsert.push({
            customer_question_id: parseInt(customerControlId),
            answer: Array.isArray(answerValue) ? answerValue : [answerValue],  // Keep the original format except for objects inside arrays
            form_id: parseInt(decodedFormrId),
            dsr_id: parseInt(newRequest.id)
        });
    }
        // Bulk insert instead of individual inserts
        const insertedAnswers = await commonService.bulkAdd(DSRAnswers, answersToInsert, dbTrans);
        
        if (!insertedAnswers || insertedAnswers.length !== answersToInsert.length) {
            return response.error(req, res, { msgCode: 'ANSWER_NOT_SAVED' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
          const encryptedRequestId = newRequest.id.toString();
          const requestId = CryptoJS.AES.encrypt(encryptedRequestId, '321@123').toString().replaceAll('/', 'Por21Ld');  
          console.log(requestId)
          console.log(form_id)
        if(authData.authentication){
            const otp =  Math.floor(100000 + Math.random() * 900000);
            if(authData.authentication_type === 'EMAIL'){
                const email = getAnswerValue("email").toLowerCase()
                const addOtp = await commonService.addDetail(PendingVerification,{email:email,request_id: newRequest.id,otp:otp,firstName:dataSubjectFields?.first_name,lastName:dataSubjectFields?.last_name},dbTrans);
                if(!addOtp){
                    return response.error(req,res,{msgCode:"OTP_NOT_SEND"},httpStatus.BAD_REQUEST,dbTrans)
                }
                const subject = `DSR [${newRequest.dsr_id}] Verification Code: ${otp}`;
                const textTemplate = "dsr_verification_mail.ejs";
                const sendData = {
                  name: `${addOtp?.firstName} ${addOtp?.lastName}`,
                  otp: otp,
                  
                };
          
                await sendMail(
                email,
                  sendData,
                  subject,
                  textTemplate,
                );

            }
            else if(authData.authentication_type === 'WHATSAPP'){
                const accessToken = process.env.WHATSAPP_TOKEN;

                let phone_number = dataSubjectFields.phone_no;
                const email = getAnswerValue("email").toLowerCase()
                const addOtp = await commonService.addDetail(PendingVerification,{email:email,request_id: newRequest.id,otp:otp,firstName:dataSubjectFields?.first_name,lastName:dataSubjectFields?.last_name,phone:phone_number},dbTrans);
                if(!addOtp){
                    return response.error(req,res,{msgCode:"OTP_NOT_SEND"},httpStatus.BAD_REQUEST,dbTrans)
                }
                const data = {
                            messaging_product: "whatsapp",
                            to: phone_number,
                            recipient_type: "individual",
                            type: "template",
                            template: {
                                name: "send_otp",
                                language: {
                                    code: "en_us"
                                },
                               components: [
                    {
                        "type": "body",
                        "parameters": [{"type": "text", "text": otp}]
                    },
                    {
                        "type": "button",
                        "sub_type": "url",
                        "index": "0",
                        "parameters": [{"type": "text", "text": otp}]
                    }
                ]
                            }
                        };
                
                          await axios.post('https://graph.facebook.com/v22.0/513164111889091/messages', data, {
                            headers: {
                                'Authorization': `Bearer ${accessToken}`,
                                'Content-Type': 'application/json'
                            },
                            
                        })
            }
        }
        const data = {
            request_id:newRequest.id,
            form_id:authData.id,
            customer_id:decodedCustomerId,
            authentication_type:authData.authentication_type 
            
        }
        await dbTrans.commit()
        return response.success(req, res, { msgCode: "OTP_SENT", data: data }, httpStatus.OK);

    }
    catch(error){
        console.log(error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }
}
exports.dsrFormResendOtp = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const {Customer,DSRCustomerControls,DSRControls,DSRAnswers,DataSubject,DsrRequest,DsrRequestType,RequestTypeStages,RequestTask,CustomRequestTask,User,DsrForms,PendingVerification,RequestDocument}=db.models
        const { answers, form_id, answered_by ,customer_id, request_id } = req.body;
        const order = [['order','ASC']]
        const sanitizeKey = (str) => {
            return str
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '_') // Replace non-alphanumeric characters with a single underscore
              .replace(/^_+|_+$/g, '');    // Remove leading and trailing underscores
        };
        const secretKey = '321@123';
        const encryptedCustomerId = customer_id.replaceAll('Por21Ld', '/');
        const custId = CryptoJS.AES.decrypt(encryptedCustomerId, secretKey);  
        const decodedCustomerId =parseInt(custId.toString(CryptoJS.enc.Utf8));
        const encryptedFormId=form_id.replaceAll('Por21Ld', '/');
        const formId = CryptoJS.AES.decrypt(encryptedFormId, secretKey);
        const decodedFormrId = parseInt(formId.toString(CryptoJS.enc.Utf8));          
        
        const encryptedReqId=request_id.replaceAll('Por21Ld', '/');
        const reqId = CryptoJS.AES.decrypt(encryptedReqId, secretKey);
        const decodedReqId = parseInt(reqId.toString(CryptoJS.enc.Utf8));   
        const dsrForm = await commonService.findByCondition(DsrForms, { id: decodedFormrId },{});
        if(!dsrForm){
            return response.error(req,res,{msgCode: "FORM_DOES_NOT_EXIST"},httpStatus.BAD_REQUEST,dbTrans);
        }
        const otp =  Math.floor(100000 + Math.random() * 900000);
        const user = await commonService.findByCondition(PendingVerification, { request_id: decodedReqId });
        if(!user){
            return response.error(req,res,{msgCode: "REQUEST_DOES_NOT_EXIST"},httpStatus.BAD_REQUEST, dbTrans)
        }
        const addOtp = await commonService.updateData(PendingVerification,{otp:otp}, {id:user.id}, dbTrans);
        if(!addOtp){
            return response.error(req,res,{msgCode:"OTP_NOT_SEND"},httpStatus.BAD_REQUEST, dbTrans);
        }
        if (dsrForm.authentication && dsrForm.authentication_type === 'EMAIL') {
          const subject = `${MESSAGE.VERIFICATION_CODE} ${otp}`;
          const textTemplate = 'guest_otp_template.ejs';
          const sendData = {
            name: `${user.firstName} ${user.lastName}`,
            otp: otp
          };

          await sendMail(user?.email, sendData, subject, textTemplate);
        } else if (dsrForm.authentication && dsrForm.authentication_type === 'WHATSAPP') {
          const accessToken = process.env.WHATSAPP_TOKEN;

          let phone_number = user.phone;
          const data = {
            messaging_product: 'whatsapp',
            to: phone_number,
            recipient_type: 'individual',
            type: 'template',
            template: {
              name: 'send_otp',
              language: {
                code: 'en_us'
              },
              components: [
                {
                  type: 'body',
                  parameters: [{ type: 'text', text: otp }]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: '0',
                  parameters: [{ type: 'text', text: otp }]
                }
              ]
            }
          };

          await axios.post('https://graph.facebook.com/v22.0/513164111889091/messages', data, {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          });
        }
       
        return response.success(req, res, { msgCode: "OTP_SENT", data: {} }, httpStatus.OK, dbTrans);

        
    } catch(error){
        console.log(error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.viewSubmitForm = async (req, res) => {
    try {
        const { DSRAnswers, DsrForms, DSRCustomerControls, DSRControls, country ,DsrRequestType} = db.models;
        const dsrReqId = req.params.request_id;
        const order = [['customer_question_id', 'ASC']];

        // Function to sanitize question titles (stable keys)
        const sanitizeKey = (str) => {
            return str
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '_') // Replace non-alphanumeric characters with underscores
                .replace(/^_+|_+$/g, ''); // Trim leading/trailing underscores
        };

        // Fetch all answers related to the given dsr_id
        const getSubmitFormData = await commonService.getListWithoutCount(
            DSRAnswers,
            { dsr_id: dsrReqId },
            ['id', 'answer', 'attachment_link', 'raw_url', 'customer_question_id']
        );

        if (!getSubmitFormData || getSubmitFormData.length === 0) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        // Fetch customer controls to get form_id and question details
        const customerControls = await commonService.getList(
            DSRCustomerControls,
            { id: getSubmitFormData.map(row => row.customer_question_id) }, // Fetch only related controls
            ['id', 'form_id', 'question_id', 'title', 'artifact_type', 'is_attachment', 'order','fields','rules','rule_applied'],
            null,
            null
        );

        if (!customerControls.rows.length) {
            return response.error(req, res, { msgCode: 'FORM_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        // Extract form_id (since it's present in DSRCustomerControls)
        const formId = customerControls.rows[0].form_id;

        // Fetch master questions
        const masterQuestions = await commonService.getListWithoutCount(DSRControls, {}, ['id', 'title']);
        const masterQuestionMap = {};
        masterQuestions.forEach((question) => {
            masterQuestionMap[question.id] = question.title;
        });
        // Map customer control IDs to question details
        const questionMap = {};
        customerControls.rows.forEach(control => {
            const masterTitle = masterQuestionMap[control.question_id]; // Check if it exists in master
            questionMap[control.id] = {
                question_id: control.question_id,
                title: masterTitle || control.title, // Use master title if available, otherwise custom
                sanitized_title: sanitizeKey(masterTitle || control.title), // Ensure stable key
                artifact_type: control.artifact_type,
                fields:control.fields,
                is_attachment: control.is_attachment,
                order: control.order,
                rules:control?.rules,
                rule_applied:control?.rule_applied
            };
        });
        // Process answers with structured output
        const questions = [];
        for (const row of getSubmitFormData) {
            const questionInfo = questionMap[row.customer_question_id] || {};

            let answerValue = row.answer || null;
            
            // Handle country question - replace ID with country name
            if (questionInfo.sanitized_title === 'country' && answerValue) {
                const countryData = await commonService.findByCondition(country,{id:parseInt(answerValue)},['country_name'])
                if (countryData) {
                    answerValue = [countryData.country_name];
                }
            }
            if(questionInfo.sanitized_title === 'request_type' && answerValue){
                const requestTypeData = await commonService.findByCondition(DsrRequestType,{id:parseInt(answerValue)},['flowtype'])
                if (requestTypeData) {
                    answerValue = [requestTypeData.flowtype];
                }
            }
            if (questionInfo.sanitized_title === 'documents_upload' && answerValue) {
                try {
                    // Parse each string in the answer array into a JSON object
                    answerValue = answerValue.map(item => {
                        try {
                            return JSON.parse(item.replace(/\\/g, '')); // Remove escape slashes and parse JSON
                        } catch (err) {
                            console.error("Error parsing document JSON:", err, item);
                            return null; // Handle invalid JSON gracefully
                        }
                    }).filter(Boolean); // Remove null entries if parsing fails
                } catch (error) {
                    console.error("Error processing upload_documents:", error);
                    answerValue = [];
                }

            }
            questions.push({
                id: row.customer_question_id,
                title: questionInfo.title,
                artifact_type: questionInfo.artifact_type,
                fields:questionInfo.fields,
                is_attachment: questionInfo.is_attachment,
                order: questionInfo.order,
                rules:questionInfo.rules,
                rule_applied:questionInfo.rule_applied,
                answer: {
                    id: row.id,
                    value: answerValue.length>1?answerValue:answerValue[0], // Ensure consistency
                    attachment_link: row.attachment_link,
                    raw_url: row.raw_url
                }
            });
        }

        // Fetch form details
        const getFormDetails = await commonService.findByCondition(
            DsrForms,
            { id: formId },
            ['id', 'content', 'url', 'regulation_id', 'authentication_type']
        );

        if (!getFormDetails) {
            return response.error(req, res, { msgCode: 'FORM_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        // Format the final response
        const formattedResponse = {
            formDetails: {
                id: getFormDetails.id,
                content: getFormDetails.content,
                url: getFormDetails.url,
                regulation_id: getFormDetails.regulation_id,
                authentication_type: getFormDetails.authentication_type,
                questions
            }
        };

        return response.success(req, res, { msgCode: 'DATA_FETCHED', data: formattedResponse }, httpStatus.OK);
    } catch (error) {
        console.error(error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.markMessageRead = async (req, res) => {
    const dbTrans = await db.transaction();
    try {    
        const { DsrSentEmail } = db.models;
        const guestCond = {
            customer_id : req.data.customer_id,
            request_id : req.params.id
        }
        const check = await commonService.findByCondition(DsrSentEmail, guestCond);
        if (check) {
            const update = await commonService.updateData(DsrSentEmail, {read_status : 'READ'}, guestCond, dbTrans);
            if (!update) {
                return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
       
        return response.success(req, res, { msgCode: "MARKED_READ", data: [] }, httpStatus.OK, dbTrans);
    }
    catch (error) {
        console.error(error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};
