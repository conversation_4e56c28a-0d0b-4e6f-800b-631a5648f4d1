const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const resourceService = require('../services/resource');
const { getPagination } = require('../config/helper');
const { Op } = require('sequelize');

exports.resourcesList = async (req, res) => {
    try {
        const { Resources } = db.models;
        let { page, size, search, sort_by = 'order', sort_order = 'ASC', start_date, end_date } = req.query;
        const { limit, offset } = getPagination(page, size);

        //implementing searching and sorting  in the query itself to reduce complexity of code
        let resourceCondition = { 'resource_name': { [Op.not]: "Customer Management" }, [Op.or]: [{ status: 1 }, { status: 2 }] };
        if (search) {
            resourceCondition[Op.or] = [
                { resource_name: { [Op.iLike]: `%${search}%` } }
            ];
        }
        // Add date range condition
        if (start_date && end_date) {
            start_date = new Date(start_date);
            start_date.setHours(0, 0, 0, 0);
            start_date = start_date.toISOString();

            end_date = new Date(end_date);
            end_date.setHours(23, 59, 32, 312);
            end_date = end_date.toISOString();

            resourceCondition.createdAt = {
                [Op.between]: [start_date, end_date]
            };
        }
        if (!start_date && end_date) {
            end_date = new Date(end_date);
            end_date.setHours(23, 59, 32, 312);
            end_date = end_date.toISOString();

            resourceCondition.createdAt = {
                [Op.lte]: end_date // Less than end date
            };
        }
        if (start_date && !end_date) {
            start_date = new Date(start_date);
            start_date.setHours(0, 0, 0, 0);
            start_date = start_date.toISOString();

            // end_date= new Date();   // Default is current time 
            resourceCondition.createdAt = {
                [Op.gte]: start_date // Greater than start date
            };
        }

        const order = [[sort_by, sort_order]];

        // get resources list
        const getResourcesList = await commonService.getList(Resources, resourceCondition, { exclude: ["createdAt", "updatedAt", "deletedAt"] }, limit, offset, order);

        const resources = {};
        const result = getResourcesList.rows;

        result?.forEach((Resource) => {
            resources[Resource.resource_id] = { ...Resource, children: [] };
        });

        Object.values(resources).forEach(resource => {
            if (resource.parent_id) {
                resources[resource.parent_id]?.children.push(resource);
            }
        });

        Object.values(resources).forEach(resource => {
            resource.children.sort((a, b) => a.order - b.order);
        });

        // Filter out the child nodes from the top level
        const tree = Object.values(resources)?.filter(resource => !resource.parent_id);

        tree.sort((a, b) => a.order - b.order);

        return response.success(req, res, { msgCode: "RESOURCES_FETCHED", data: tree }, httpStatus.OK);
    } catch (error) {
        console.log("resourcesList", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.userResources = async (req, res) => {
    try {
        const { Resources, CustomerResources } = db.models;

        //implementing searching and sorting  in the query itself to reduce complexity of code
        let resourceCondition = { customer_id: req.data.customer_id };

        // get resources list
        const getResourcesList = await resourceService.getCustomerResources(CustomerResources, Resources, resourceCondition, { [Op.or]: [{ status: 1 }, { status: 2 }] });

        const resources = {};
        const result = getResourcesList?.map(resource => resource.Resource);

        result?.forEach((Resource) => {
            resources[Resource.resource_id] = { ...Resource, children: [] };
        });

        Object.values(resources)?.forEach(resource => {
            if (resource.parent_id) {
                resources[resource.parent_id]?.children.push(resource);
            }
        });

        Object.values(resources).forEach(resource => {
            resource.children.sort((a, b) => a.order - b.order);
        });

        // Filter out the child nodes from the top level
        const tree = Object.values(resources)?.filter(resource => !resource.parent_id);

        tree.sort((a, b) => a.order - b.order);

        return response.success(req, res, { msgCode: "RESOURCES_FETCHED", data: tree }, httpStatus.OK);
    } catch (error) {
        console.log("resourcesList", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
exports.addResource = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { Resources, Role } = db.models;
        const order = [['order', 'DESC']];
        let newOrder = 1;

        const roleDetails = await commonService.findByCondition(Role, {id: req.data.roleId}, {});
        if(roleDetails.role_name !== 'God Admin'){
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans );
        }

        if (req.body.parent_id === null) {
            // Fetch the latest non-parent resource to determine order
            const parentResource = await commonService.getList(Resources, { parent_id: null, status: 1 }, ['order'], null, null, order);
            if (parentResource.rows?.length> 0) {
                newOrder = parentResource?.rows[0]?.order + 1;
            } else{
                return response.error(req,res,{msgCode:"RESOURCE_LIST_NOT_FOUND"},httpStatus.BAD_REQUEST,dbTrans);
            }
        } else {
            // Fetch the latest child resource under the parent to determine order
            const childResource = await commonService.getList(Resources, { parent_id: req.body.parent_id, status: 1 }, ['order'], null, null, order);
            if (childResource.rows.length>0) {
                newOrder = childResource?.rows[0]?.order + 1;
            }else if(childResource.rows.length === 0){
                newOrder = 1;
            } else{
                return response.error(req,res,{msgCode:"RESOURCE_LIST_NOT_FOUND"},httpStatus.BAD_REQUEST,dbTrans);
            }
        }

        req.body.order = newOrder;

        // Add resource
        const addResource = await commonService.addDetail(Resources, req.body, dbTrans);
        return response.success(req, res, { msgCode: "RESOURCES_ADDED", data: addResource }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.error("error", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }
};

