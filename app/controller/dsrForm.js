const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { sendMailWithMultipleAttachments, sendMail} = require('../config/email');
const ExcelJS = require('exceljs');
const path = require("path");
const uploadDocument = require('../utils/s3-bucket');
const { deleteFile } = require('../utils/delete-files');
const CryptoJS = require('crypto-js');
const jwt = require('jsonwebtoken');


exports.renderDsrForm = async (req, res) => {
    try {
        const { country, DsrRequestType, User, Group, Customer,DsrFormRepository } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        //const customerId = "727";
        const secretKey = '321@123';
        //U2FsdGVkX1/gGlPz3ARDQ/sPwxPZMbxBzlIHCOk8GII=  
        //'U2FsdGVkX18lySBEo3gru9GVlC0AYMkpruuTJR82FS8='

        //const encryptedCustomerId = CryptoJS.AES.encrypt('751', secretKey).toString();
        //const encodedData = encryptedCustomerId.replaceAll('/', 'Por21Ld')

        //const customerId = atob(encryptedCustomerId);   
        
        const encryptedCustomerId = req.params.customer_id.replaceAll('Por21Ld', '/');; 
        const custId = CryptoJS.AES.decrypt(encryptedCustomerId, secretKey);  
        const customerId = custId.toString(CryptoJS.enc.Utf8);

        // console.log("test36===customerId===>",encryptedCustomerId,custId, customerId )
        
        let countryCondition = []

        // check customer
        const checkCustomer = await commonService.findByCondition(Customer, { id: customerId });
        if (!checkCustomer) {
            return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        // console.log("checkCustomer------------------>",checkCustomer)
        // get form repo data
        const formRepoData = await commonService.findByCondition(DsrFormRepository, {customer_id : customerId}, {});
        console.log("test36===form==repo===>",customerId, formRepoData.content)
        const getCountryData = await dsrService.getAllRecord(country, countryCondition);
        
        let WorkflowCondition = { customer_id: customerId, workflow_status : 'published' };
        let userCondition =  {}
        const getWorkflow = await dsrService.getAllRecord(DsrRequestType, WorkflowCondition);

        const units = await dsrService.getAllRecord(Group, { customer_id: customerId });
        const site_key= process?.env?.DSR_CAPTCHA_KEY?process?.env?.DSR_CAPTCHA_KEY:"6Lcfea8qAAAAADwmCW19v5Jrz1jPZy6PjNnNS4dJ";
        
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "";

        res.render('index', { 'country' : getCountryData, 'request_type' : getWorkflow, 'units' : units, 'customer_id': encryptedCustomerId, 'form_repo_data': formRepoData, site_key:site_key, 'base_url' :  backEndUrl});

    } catch (error) {
        console.log("getCountry", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);

    }
};

exports.verifyGuestEmail = async (req, res) => {
    try {
        const { DataSubject,DsrFormRepository } = await db.models;
              
        let token = req.params.token; 
        if (!token) return resolve({ status: 0, message: "Unauthorized" });
        token = token.replace(/^Bearer\s+/, '');
     
        const content = await jwt.verify(token, process.env.SECRET_KEY);

        if (!content || !content.email) { // Adjust checks as per your token structure
          return response.error(req, res, { msgCode: 'INVALID_TOKEN' }, httpStatus.UNAUTHORIZED);
        }

        const dataSubject = await commonService.findByCondition(DataSubject, { email: content.email, id : content.data_subject_id}, ['id', 'email']);
        if (!dataSubject) {
          return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
        }

        const update = await commonService.updateData(DataSubject, {is_email_verified : true}, { id: content.data_subject_id });
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST);
        }
        const formRepoData = await commonService.findByCondition(DsrFormRepository, {customer_id : dataSubject.customer_id}, {});
       
        let msg = "Email id verified successfully"
        let link = "https://dev.gotrust.tech/data-subject-rights/my-request"

        //send link for guest login.
        const textTemplate = "dsr_verify_mail.ejs";                    
        const subject = `Access your DSR request id`;
        
        // Add the verification link to the email content
        const sendData = {
            content: `\n\nPlease click link below to check DSR request.`,
            dsr_link : link,
            dsr_link_test : "Click here to view request"
        };
        
        await sendMail(
            content.email,
            sendData,
            subject,
            textTemplate
        );
             
        res.render('guest_email_verfication', { 'dsrmsg' : msg, 'link' : link, 'form_repo_data': formRepoData});

    } catch (error) {
        console.log("getCountry", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);

    }
};

