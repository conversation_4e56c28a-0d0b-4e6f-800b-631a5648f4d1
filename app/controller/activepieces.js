const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { sendMailWithMultipleAttachments, sendMail} = require('../config/email');
const ExcelJS = require('exceljs');
const path = require("path");
const uploadDocument = require('../utils/s3-bucket');
const { deleteFile } = require('../utils/delete-files');
const { Pool } = require('pg');
const axios = require('axios');
const FormData = require('form-data');
const https = require('https');
const { signToken } = require('../config/helper');
const { automationOnCompleteTask } = require('../utils/dsr-helper');

function displayDate(dateString){
    const date = new Date(dateString);

    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getUTCFullYear();

    // Format as DD-MM-YYYY
    const formattedDate = `${day}-${month}-${year}`;
    return formattedDate;
}

exports.ocrVerification = async (req, res) => {
   
    try {
        console.log('ocr verification')
        console.log(req.body)
      //   console.log(req.body.req_data.request_id)
        
         const { DataSubject, RequestDocument, DsrRequest } = db.models;

            const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : req?.body?.req_data?.request_id}, ['id', 'request_type', 'data_subject_id']);
            
            const dsrReqDoc = await dsrService.getOneRecord(RequestDocument, {dsr_request_id : dsrReq.id, document_type : 'identification_documents'});
                    
            if(! dsrReq){
                return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
            }

            if(! dsrReqDoc){
                return response.error(req, res, { msgCode: 'OCR_NOT_MATTACHED' }, httpStatus.BAD_REQUEST);
            }
            
            const dataSubject = await commonService.findByCondition(DataSubject, { id: dsrReq.data_subject_id});
            
            if(! dataSubject){
                return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
            }

            let data = new FormData();
         
            data.append('url', dsrReqDoc.url);

            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'http://148.113.6.140:8000/analyze-image',
                headers: { 
                    ...data.getHeaders()
                },
                data : data
            };

       
            const documentInfo = await axios.request(config);

            if(! documentInfo){
                return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
            }
            
           
                let name = dataSubject.first_name+" "+dataSubject.last_name
                let dataSubName = name.toLowerCase();
                let documentName = documentInfo?.data?.detected_name.toLowerCase();
                if(dataSubName != documentName){                    
                    return response.error(req, res, { msgCode: 'OCR_NOT_MATTACHED' }, httpStatus.BAD_REQUEST);
                }

                let detected_id_number = documentInfo?.data?.detected_id_number.replace(/\s+/g, '');
                let datasub_id_number = dataSubject?.unique_identification_number.replace(/\s+/g, '');
                
                if(datasub_id_number != detected_id_number){                    
                    return response.error(req, res, { msgCode: 'OCR_NOT_MATTACHED' }, httpStatus.BAD_REQUEST);
                }

                let date_of_birth = documentInfo?.data?.date_of_birth;
                let datasubDob = dataSubject.dob;

                // Remove the suffixes from the day part (e.g., '28th' becomes '28')
                let dobWithoutSuffix = date_of_birth.replace(/(\d+)(st|nd|rd|th)/, '$1');

                // Convert date_of_birth to a Date object (DD/MM/YYYY format)
                let dobParts = dobWithoutSuffix.split('/');
                let dob = new Date(dobParts[2], dobParts[1] - 1, dobParts[0]); // months are 0-based in JavaScript Date

                // Convert datasubDob to a Date object
                let dobToCompare = new Date(datasubDob);

                // Normalize both dates to midnight (00:00:00) to ignore time differences
                dob.setHours(0, 0, 0, 0); // Set dob time to 00:00:00
                dobToCompare.setHours(0, 0, 0, 0); // Set dobToCompare time to 00:00:00

              
                // Example comparison (checking if the two dates are equal)
                if (dob.getTime() !== dobToCompare.getTime()) {
                    console.log('The dates do not match.');
                    return response.error(req, res, { msgCode: 'OCR_NOT_MATTACHED' }, httpStatus.BAD_REQUEST);
                } 
            //}          

            return response.success(req, res, { msgCode: "DATA_FETCHED", data: [] }, httpStatus.OK);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);

    }
};

exports.sendEmail = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        console.log('send email')
        console.log(req.body)
         console.log(req.body.req_data.request_id)
        
         const { DataSubject, DsrRequest } = db.models;

            const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : req?.body?.req_data?.request_id}, ['id', 'request_type', 'data_subject_id']);
                    
            if(! dsrReq){
                return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
            }
            
            //send email
            let emailMessage = null;
            let  subject = null;
            if(req?.body?.dsr_email_data?.status ==  'failer'){
                emailMessage = req?.body?.dsr_email_data?.message;
                subject = `DSR request has rejected`;
            } else {
                emailMessage = 'Your request has been verified.';
                subject = `DSR request has approved`;
            }
            

            const sendData = {
                content: emailMessage,                       
            };

            const textTemplate = "dsr_mail.ejs";                    
            const dataSubject = await commonService.findByCondition(DataSubject, { id: dsrReq.data_subject_id});
            

            await sendMail(
                dataSubject?.email,
                sendData,
                subject,
                textTemplate
            );
           

            return response.success(req, res, { msgCode: "TASK_UPDATED", data: [] }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);

    }
};

exports.addWebhook = async (req, res) => {
    try {
        const { ActivepiecesWebhooks } = db.models;
        const { url } = req.body; // Expecting the webhook URL in the body
        
        //get workflow id from url
      
        // Get the last part before any other path segment (if any)
        let workflow_id = url.split('/').filter(Boolean).pop();

        if(workflow_id == 'test'){
            workflow_id = url.split('/').slice(-2, -1)[0];
        }
        
        const check = await commonService.findByCondition(ActivepiecesWebhooks, { activepieces_workflow_id: workflow_id});
        if (check) {
            //if webhook is already created then no need of create and update the webhook.
            //return response.error(req, res, { msgCode: 'DUPLICATE_WEBHOOK' }, httpStatus.BAD_REQUEST);
            //return response.success(req, res, { msgCode: "WEBHOOK_CREATED", data: [] }, httpStatus.CREATED);
            const updateInfo = {
                'url' : url,               
            }
             await commonService.updateData(ActivepiecesWebhooks, updateInfo, { id: check.id });
        } else {
            req.body['activepieces_workflow_id'] = workflow_id
            const insertWebhook = await commonService.addDetail( ActivepiecesWebhooks , req.body);
            if(!insertWebhook){ 
                return response.error( req, res, { msgCode: 'ERROR_CREATING_WEBHOOK' }, httpStatus.BAD_REQUEST);
            }
        }

        console.log(`Webhook URL registered: ${url}`);
        return response.success(req, res, { msgCode: "WEBHOOK_CREATED", data: [] }, httpStatus.CREATED);
    } catch (error) {
        console.error('Error creating webhook:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.deleteWebhook = async (req, res) => {    
    try {
        const { ActivepiecesWebhooks } = db.models;

        const check = await commonService.findByCondition(ActivepiecesWebhooks, { url: req.body.url});
        if (! check) {
            return response.error(req, res, { msgCode: 'WEBHOOK_NOT_EXIST' }, httpStatus.BAD_REQUEST);
        }
        
        const data = await commonService.deleteQuery(ActivepiecesWebhooks, { url: req.body.url });
       
        if (!data) {
            return response.error(req, res, { msgCode: "WEBHOOK_DELETED_ERROR" }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "WEBHOOK_DELETED" }, httpStatus.OK);
    } catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.autoVerifyRequest = async (req, res) => {
    try {
        console.log('auto verify request sent')
         console.log(req.body)
         console.log(req.body.req_data.data_subject_id)
        
        const { DataSubject, DsrRequest } = db.models;

        let dataSubCond = {id : req?.body?.req_data?.data_subject_id}

        const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);

        if(! dataSubject){
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);    
        }

        const pool = new Pool({
            host: '*************',
            port: 5432,
            user: 'postgres',
            password: 'JLIEJmnVjQDwHR3T6MvYY',
            database: 'gotrust_stage',
        });
            
         await dsrService.getOneRecord(DsrRequest, {id : req?.body?.req_data?.body?.request_id}, ['id']);

        const allUsers = await pool.query('SELECT * FROM public."users"'); // Awaiting the promise
        const users = allUsers.rows;  
        
        // Check if the email does not exist in the array
        
        const emailExists = users.some(user => user.email === dataSubject.email);

        if (!emailExists) {
             return response.error(req, res, { msgCode: 'DSRINFO_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);              
        }
        
        return response.success(req, res, { msgCode: "REQUEST_UPDATED", data: [] }, httpStatus.OK);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.autoApprovedDsrAndTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        console.log('approved request and complete the task')
    //    console.log(req.body)
    //     console.log(req.body.req_data.request_id)
        
         const { CustomRequestTask, RequestTypeStages, DsrRequest } = db.models;
            
            const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : 24});
                    
            if(! dsrReq){
                return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            let obj = {
                status : 'APPROVED',
                is_acknowledge_mail_sent : "YES"
            }
            //add DPO as assignee of assignee not added
            if(dsrReq.assignee_id == null){
               // obj['assignee_id'] = req.body.req_data.dpo_id
            }

            const today = new Date();
            
            obj['assigned_date'] = today;
            
            let stepOrder = [['id', 'ASC']];        
            let limit, offset = null;
            const step = await dsrService.getOneRecord(RequestTypeStages, { type_id: dsrReq.request_type}, ['id','type_id', 'step_title'], limit, offset, stepOrder);
        

            obj['workflow_step_id'] = step.id;           

            const update = await commonService.updateData(DsrRequest, obj, { id: dsrReq.id }, dbTrans);
            
            if (!update) {
                return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            //get workflow step and its task and complete them
            let reqStagesCond = {
                type_id : dsrReq.request_type
            }

            let reqTypeOrder = [['id', 'ASC']];

            const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);   
            stages.map(step => step.id);
            let taskCond = {
                // stage_id: {
                //     [Op.in]: stageIds, // This creates the WHERE IN clause
                // },
                request_id : dsrReq.id
            };
            
    
            const getAllTask = await dsrService.getAllRecord(CustomRequestTask, taskCond, ['id', 'title', 'guidance_text', 'stage_id', 'created_by']);   
            if(getAllTask.length > 0){
                for(let task of getAllTask){
                    let updateTask = {
                        start_date : new Date(),
                        completion_date : new Date(),
                        due_date : new Date(),
                        progress : 'COMPLETED'
                    }                        
                    
                    const updateTaskStatus = await commonService.updateData(CustomRequestTask, updateTask, { id: task.id }, dbTrans);

                    if(!updateTaskStatus){
                        return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                }
            }  
           
            return response.success(req, res, { msgCode: "TASK_UPDATED", data: [] }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);

    }
};

exports.identityVerifyWithDbHttpAsStep = async (req, res) => {
     try {
 
          const { CustomRequestTask, RequestTypeStages, DataSubject, DsrRequest } = db.models;
             
             const dsrReqts = await dsrService.getAllRecord(DsrRequest, {
                 status : 'PENDING', 
                 assigned_date: {
                     [Op.is]: null,  // checks for NULL values
                 },
                 assignee_id: {
                     [Op.is]: null,  // checks for NULL values
                 }
             }, ['id', 'request_type', 'assignee_id', 'data_subject_id']);
             
                     
             if(! dsrReqts){
                 return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
             }
             
             const pool = new Pool({
                 host: '*************',
                 port: 5432,
                 user: 'postgres',
                 password: 'JLIEJmnVjQDwHR3T6MvYY',
                 database: 'gotrust_stage',
             });
 
             for(let dsrReq of dsrReqts){
                 //do we have to do dsr  approve
                 //check if ocr verification is enabled and current request ocr verification is done.
                 
 
                 let dataSubCond = {id : dsrReq.data_subject_id}
 
                 const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);
 
                 if(! dataSubject){
                     return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);    
                 }
                     
                 const allUsers = await pool.query('SELECT * FROM public."users"'); // Awaiting the promise
                 const users = allUsers.rows;  
                 
                 // Check if the email does not exist in the array
                 
                 const emailExists = users.some(user => user.email === dataSubject.email);
    
                 if (emailExists) {
                     
                     let obj = {
                         status : 'APPROVED',
                         is_acknowledge_mail_sent : "YES"
                     }
                     //add DPO as assignee of assignee not added
                     if(dsrReq.assignee_id == null){
                     // obj['assignee_id'] = req.body.req_data.dpo_id
                     }
 
                     const today = new Date();
                     
                     obj['assigned_date'] = today;
                     
                     let stepOrder = [['id', 'ASC']];        
                     let limit, offset = null;
                     const step = await dsrService.getOneRecord(RequestTypeStages, { type_id: dsrReq.request_type}, ['id','type_id', 'step_title'], limit, offset, stepOrder);
                 
 
                     obj['workflow_step_id'] = step.id;           
 
                     const update = await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
                     
                     if (!update) {
                         return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST);
                     }
 
                     //get workflow step and its task and complete them
                     let reqStagesCond = {
                         type_id : dsrReq.request_type
                     }
 
                     let reqTypeOrder = [['id', 'ASC']];
 
                     const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);   
                     stages.map(step => step.id);
                     let taskCond = {
                         // stage_id: {
                         //     [Op.in]: stageIds, // This creates the WHERE IN clause
                         // },
                         request_id : dsrReq.id
                     };
                     
             
                     const getAllTask = await dsrService.getAllRecord(CustomRequestTask, taskCond, ['id', 'title', 'guidance_text', 'stage_id', 'created_by']);   
                     if(getAllTask.length > 0){
                         for(let task of getAllTask){
                             let updateTask = {
                                 start_date : new Date(),
                                 completion_date : new Date(),
                                 due_date : new Date(),
                                 progress : 'COMPLETED'
                             }                        
                             
                             const updateTaskStatus = await commonService.updateData(CustomRequestTask, updateTask, { id: task.id });
 
                             if(!updateTaskStatus){
                                 return response.error( req, res, { msgCode: 'ERROR_CREATING_REQUEST' }, httpStatus.BAD_REQUEST);
                             }
                         }
                     }  
                     
                     //send email
                     const sendData = {
                         content: 'Your request has been verified.',                       
                     };
 
                     const textTemplate = "dsr_mail.ejs";                    
                     const subject = `DSR request has approved`;
 
                     await sendMail(
                         dataSubject?.email,
                         sendData,
                         subject,
                         textTemplate
                     );
                 } else {
                     let obj = {
                         status : 'REJECTED_IN_PROGRESS',
                         reject_reason : "Data Subject Identity Verification failed"
                     }
 
                      await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
 
                     //send email
                     let subject = `DSR request has rejected`;
                     let emailMessage = 'Your information does not exist in our record';
                     
                     const sendData = {
                         content: emailMessage,                       
                     };
 
                     const textTemplate = "dsr_mail.ejs";                    
                     
                     await sendMail(
                         dataSubject?.email,
                         sendData,
                         subject,
                         textTemplate
                     );
                 }
             }
 
             return response.success(req, res, { msgCode: "TASK_UPDATED", data: [] }, httpStatus.OK);
     } catch (error) {
         console.error('Error creating workflow:', error);
         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
 
     }
 };

exports.identityVerifyWithDbHttp = async (req, res) => {
    try { 
         req.query.workflow_id;
        console.log('identityVerifyWithDbHttp is called')
     
        let custom_data = req?.body;

        let id = custom_data?.task_id

         const { Role, CustomRequestTask, RequestTypeStages, DataSubject, DsrRequest, User } = db.models;
            
            const task = await commonService.findByCondition(CustomRequestTask, { id : id});
                    
            if(! task){
                return res.json({
                    "success": true,
                    "status_code": 400,
                    "message": "Task does not exist",
                    "result": {}            
                });
            }

            //go employee dev dv
            console.log('=====env host name is===='+process.env.GO_EMS_DB_HOST_INFO);
            const pool = new Pool({
                host: process.env.GO_EMS_DB_HOST_INFO,
                port: process.env.GO_EMS_DB_PORT,
                user: process.env.GO_EMS_DB_USERNAME_INFO,
                password: process.env.GO_EMS_DB_PASSWORD_INFO,
                database: process.env.GO_EMS_DB_DATABASE_INFO,
            });
           

            let taskDueDate = new Date();
            taskDueDate.setDate(taskDueDate.getDate() + 30);
            
           // for(let task of dsrCustomTasks){
                //do we have to do dsr  approve
                //check if ocr verification is enabled and current request ocr verification is done.
                const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : task.request_id});

                let dataSubCond = {id : dsrReq.data_subject_id}

                const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);

                if(! dataSubject){
                    return res.json({
                        "success": true,
                        "status_code": 400,
                        "message": "Request does not exist",
                        "result": {}            
                    });    
                }

                const allUsers = await pool.query(
                    `SELECT * FROM public."users" WHERE "email" = $1`, 
                        [dataSubject.email]
                        );
                        const users = allUsers.rows;
                                        
                        
                const emailExists = users.some(user => user.email === dataSubject.email); 
                if (emailExists) {
                    //get dpo id and assign this request to dpo
                    const roleData = await commonService.findByCondition(Role, {
                        role_name: "Data Protection Officer",
                        customer_id: dsrReq.customer_id
                        
                    }, ['id']);
        
                    if(roleData){                
                        const dpo = await commonService.findByCondition(User, {
                            role_id: roleData.id
                        }, ['id', 'firstName', 'lastName', 'email']);
                        if (dpo) {  
                            await commonService.updateData(DsrRequest, {assignee_id: dpo.id}, { id: dsrReq.id });

                        }

                    }
                    //end assignment
                                         
                    let obj = {
                        status : 'APPROVED',
                       // is_acknowledge_mail_sent : "YES"
                    }
                    //add DPO as assignee of assignee not added
                    if(dsrReq.assignee_id == null){
                    // obj['assignee_id'] = req.body.req_data.dpo_id
                    }

                    const today = new Date();
                    
                    obj['assigned_date'] = today;
                    
                    let stepOrder = [['id', 'ASC']];        
                    let limit, offset = null;
                    const step = await dsrService.getOneRecord(RequestTypeStages, { type_id: dsrReq.request_type}, ['id','type_id', 'step_title'], limit, offset, stepOrder);
                

                    obj['workflow_step_id'] = step.id;    
                    
                    const update = await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
                    
                    if (!update) {
                        return res.json({
                            "success": true,
                            "status_code": 400,
                            "message": "Error in update request",
                            "result": {}            
                        });
                    }

                    //get workflow step and its task and complete them
                    let reqStagesCond = {
                        type_id : dsrReq.request_type
                    }

                    let reqTypeOrder = [['id', 'ASC']];

                    const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);   
                    stages.map(step => step.id);
                         
                    //send email
                    const sendData = {
                        content: `Your DSR request ${dsrReq.dsr_id} has been verified and approved.`,                       
                    };

                    const textTemplate = "dsr_mail.ejs";                    
                    const subject = `Your request ${dsrReq.dsr_id} has been verified.`;

                    await sendMail(
                        dataSubject?.email,
                        sendData,
                        subject,
                        textTemplate
                    );

                    return res.json({
                        "success": true,
                        "status_code": 200,
                        "message": "Task executed successfully",
                        "result": {}            
                    });
                } else {

                    let obj = {
                        status : 'REJECTED_IN_PROGRESS',
                        reject_reason : "Data Subject Identity Verification failed"
                    }
                                        
                    await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });

                    //send email
                    let subject = `Your DSR request (${dsrReq.dsr_id}) has been declined`;
                    let emailMessage = 'Your information does not exist in our record. Please raise another request with updated credentials and supporting documents';
                    
                    const sendData = {
                        content: emailMessage,                       
                    };

                    const textTemplate = "dsr_mail.ejs";                    
                    
                    await sendMail(
                        dataSubject?.email,
                        sendData,
                        subject,
                        textTemplate
                    );

                   
                    return res.json({
                        "success": true,
                        "status_code": 400,
                        "message": "Identity Verification failed",
                        "result": {}            
                    });
                }
           // } 
            
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);

    }
};

exports.dataEraserWithDbHttp = async (req, res) => {
     try { 
            let custom_data = req?.body;

            let id = custom_data?.task_id
 
          const { CustomRequestTask, DataSubject, DsrRequest } = db.models;
             
             const task = await commonService.findByCondition(CustomRequestTask, { id : id});
            
                     
             if(! task){
                return res.json({
                    "success": true,
                    "status_code": 400,
                    "message": "Task id does not exist",
                    "result": {}            
                });
             }
             
             const pool = new Pool({
                host: process.env.GO_EMS_DB_HOST_INFO,
                port: process.env.GO_EMS_DB_PORT,
                user: process.env.GO_EMS_DB_USERNAME_INFO,
                password: process.env.GO_EMS_DB_PASSWORD_INFO,
                database: process.env.GO_EMS_DB_DATABASE_INFO,
            });             
 
             
            // for(let task of dsrCustomTasks){
                 //do we have to do dsr  approve
                 //check if ocr verification is enabled and current request ocr verification is done.
                 const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : task.request_id});
 
                 let dataSubCond = {id : dsrReq.data_subject_id}
 
                 const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);
 
                 if(! dataSubject){
                    return res.json({
                        "success": true,
                        "status_code": 400,
                        "message": "Data subject does not exist",
                        "result": {}            
                    });    
                 }
                     
                 const allUsers = await pool.query(
                    `SELECT * FROM public."users" WHERE "email" = $1`, 
                    [dataSubject.email]
                  );
                  const users = allUsers.rows;
                                 
                 // Check if the email does not exist in the array
                 
                 const emailExists = users.some(user => user.email === dataSubject.email);
                
                 //SET "deletedAt" = NOW()
                 if (emailExists) {
                    const userUdateQuery = `
                        UPDATE "users"                        
                        SET "status" = 'inactive'
                        WHERE email = $1
                    `;
                                        
                     await pool.query(userUdateQuery, [dataSubject.email]);
          
                     //send email
                     
                        const sendData = {
                            content: `I hope you are doing well. We are writing to confirm that we have successfully deleted all personal data.`,                       
                        };
    
                        const textTemplate = "dsr_mail.ejs";                    
                        const subject = `Confirmation of Data Erasure Request Id ${dsrReq.dsr_id}.`;
    
                        await sendMail(
                            dataSubject?.email,
                            sendData,
                            subject,
                            textTemplate
                        );

                        return res.json({
                            "success": true,
                            "status_code": 200,
                            "message": "Data deletion is done",
                            "result": {}            
                        });
                     
                 } else {

                     //send email
                     
                    const sendData = {
                        content: `I hope you are doing well. We are writing to inform you that, after a thorough review, we found that the requested personal data does not exist in our records. As a result, we are unable to delete any data. We apologize for any inconvenience this may cause. Please feel free to reach out if you have any questions or need further clarification.`,
                    };
                    
                    const textTemplate = "dsr_mail.ejs";
                    
                    const subject = `Update on Data Erasure Request Id ${dsrReq.dsr_id}.`;

                    await sendMail(
                        dataSubject?.email,
                        sendData,
                        subject,
                        textTemplate
                    );

                     return res.json({
                        "success": true,
                        "status_code": 400,
                        "message": "Data deletion failed",
                        "result": {}            
                    });
                    
                 }
            // }
    
     } catch (error) {
         console.error('Error creating workflow:', error);
         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
 
     }
 };

exports.ocrVerificationForHttp = async (req, res) => {
    
     try {
    
        let custom_data = req?.body;

        let id = custom_data?.task_id
     
        const { RequestDocument, DSRAuditLog, CustomRequestTask, RequestTypeStages, DataSubject, DsrRequest } = db.models;
            
            const task = await commonService.findByCondition(CustomRequestTask, { id : id});
        
            if(! task){
                return res.json({
                    "success": true,
                    "status_code": 400,
                    "message": "Task id does not exist",
                    "result": {}            
                });
            }                      
 
             let taskDueDate = new Date();
             taskDueDate.setDate(taskDueDate.getDate() + 30);
             let ocrApiUrl = process.env.OCR_API_URL ? process.env.OCR_API_URL : "http://148.113.6.50:6006/analyze-image";
            
                let ocrStatus = true;
                let ocrMessage = null;

                 //do we have to do dsr  approve
                 //check if ocr verification is enabled and current request ocr verification is done.
                 const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : task.request_id});
 
                 let dataSubCond = {id : dsrReq.data_subject_id}
 
                 const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond);
            
                 const dsrReqDoc = await dsrService.getOneRecord(RequestDocument, {dsr_request_id : dsrReq.id, document_type : 'identification_documents'});
                    
                 let data = new FormData();
    
                if(dsrReqDoc?.url){
                    data.append('url', dsrReqDoc?.url);
    
                    let config = {
                        method: 'post',
                        maxBodyLength: Infinity,
                        url: ocrApiUrl,
                        headers: { 
                            ...data.getHeaders()
                        },
                        data : data
                    };
    
            
                    const documentInfo = await axios.request(config);
    
                    if(! documentInfo){
                        ocrStatus = false;
                        ocrMessage = "Document is not attached";
                    }
                
                    let name = dataSubject.first_name+" "+dataSubject.last_name
                    let dataSubName = name.toLowerCase();
                    let documentName = documentInfo?.data?.detected_name.toLowerCase();
                    if(dataSubName != documentName){
                        ocrStatus = false;
                        ocrMessage = "Name does not attached with documents";
                    }
    
                    if(documentInfo?.data?.detected_id_number){
                        let detected_id_number = documentInfo?.data?.detected_id_number.replace(/\s+/g, '');
                        let datasub_id_number = dataSubject?.unique_identification_number.replace(/\s+/g, '');
                        
                        if(datasub_id_number != detected_id_number){
                            ocrStatus = false;
                            ocrMessage = "Unique Identifier Number does not match with documents";
                        }
                    } else {
                        ocrStatus = false;
                        ocrMessage = "Unable to find Unique Identifier Number with documents";    
                    }
    
                    let date_of_birth = documentInfo?.data?.date_of_birth;
                    let datasubDob = dataSubject.dob;
    
                    // Remove the suffixes from the day part (e.g., '28th' becomes '28')
                    let dobWithoutSuffix = date_of_birth.replace(/(\d+)(st|nd|rd|th)/, '$1');
    
                    // Convert date_of_birth to a Date object (DD/MM/YYYY format)
                    let dobParts = dobWithoutSuffix.split('/');
                    let dob = new Date(dobParts[2], dobParts[1] - 1, dobParts[0]); // months are 0-based in JavaScript Date
    
                    // Convert datasubDob to a Date object
                    let dobToCompare = new Date(datasubDob);
    
                    // Normalize both dates to midnight (00:00:00) to ignore time differences
                    dob.setHours(0, 0, 0, 0); // Set dob time to 00:00:00
                    dobToCompare.setHours(0, 0, 0, 0); // Set dobToCompare time to 00:00:00
    
                
                    // Example comparison (checking if the two dates are equal)
                    if (dob.getTime() !== dobToCompare.getTime()) {
                        console.log('The dates do not match.');
                        ocrStatus = false;
                        ocrMessage = "Date of birth does not match with documents"; 
                
                    } 
                } else {
                    ocrStatus = false; 
                }

                if (ocrStatus) { 
                                    
                     let obj = {
                         status : 'APPROVED',
                        // is_acknowledge_mail_sent : "YES"
                     }
                     //add DPO as assignee of assignee not added
                     if(dsrReq.assignee_id == null){
                     // obj['assignee_id'] = req.body.req_data.dpo_id
                     }
 
                     const today = new Date();
                     
                     obj['assigned_date'] = today;
                     
                     let stepOrder = [['id', 'ASC']];        
                     let limit, offset = null;
                     const step = await dsrService.getOneRecord(RequestTypeStages, { type_id: dsrReq.request_type}, ['id','type_id', 'step_title'], limit, offset, stepOrder);
                 
 
                     obj['workflow_step_id'] = step.id;           
 
                     const update = await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
                     
                     if (!update) {
                         return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST);
                     }
 
                     //get workflow step and its task and complete them
                     let reqStagesCond = {
                         type_id : dsrReq.request_type
                     }
 
                     let reqTypeOrder = [['id', 'ASC']];
 
                     const stages = await dsrService.getAllRecord(RequestTypeStages, reqStagesCond, ['id' , 'type_id', 'step_title', 'activepieces_automation_id'], null, null, reqTypeOrder);   
                      stages.map(step => step.id);
                         
                     return res.json({
                        "success": true,
                        "status_code": 200,
                        "message": "OCR Verification success",
                        "result": {}            
                    });
                 } else {
                     let obj = {
                         status : 'REJECTED_IN_PROGRESS',
                         reject_reason : ocrMessage
                     }
 
                     const auditAction = ocrMessage || `OCR Verification failed`;
                     await commonService.addDetail(DSRAuditLog, { type: 'DSR', type_id: dsrReq.id, step: task.stage_id, action: auditAction, action_by_id: dsrReq.user_id, customer_id: dsrReq.customer_id });
                                         
                     await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
 
                     //send email
                     let subject = `DSR request has rejected`;
                     let emailMessage = 'Your information does not exist in our record';
                     
                     const sendData = {
                         content: emailMessage,                       
                     };
 
                     const textTemplate = "dsr_mail.ejs";                    
                     
                     await sendMail(
                         dataSubject?.email,
                         sendData,
                         subject,
                         textTemplate
                     );

                     return res.json({
                        "success": true,
                        "status_code": 400,
                        "message": "OCR verification failed",
                        "result": {}            
                    });
                 }
            // }
 
     } catch (error) {
         console.error('Error creating workflow:', error);
         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
 
     }
 };


exports.validateUser = async (req, res) => {    
    try {
        const authHeader = req.headers['authorization'];
        let token = null;
        if (authHeader) {
            token = authHeader.split(' ')[1];
            if (token) {
                console.log('Bearer Token:', token);
            }  
        }         
        return response.success(req, res, { msgCode: "API_SUCCESS" }, httpStatus.OK);
    } catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.login = async (req, res) => {    
    try {
     
        let data = JSON.stringify({
            "email": process.env.ACTIVEPIECES_USER,
            "password": process.env.ACTIVEPIECES_PASSWORD
          });
          
          let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.ACTIVEPIECES_API_URL}/api/v1/authentication/sign-in`,
            headers: { 
              'Accept': 'application/json, text/plain, */*', 
              'Accept-Language': 'en-US,en;q=0.9', 
              'Connection': 'keep-alive', 
              'Content-Type': 'application/json', 
              'Cookie': '_ga=GA1.1.1167237575.1707808950; _ga_1EJC8L1CJH=GS1.1.1710183257.9.1.1710183341.0.0.0; ajs_user_id=bwrjjBq5aNm2PrJGOZkdT; ajs_anonymous_id=323211a8-bc19-4c7d-ab2b-8dc550023f7c; ph_phc_7F92HoXJPeGnTKmYv0eOw62FurPMRW9Aqr0TPrDzvHh_posthog=%7B%22distinct_id%22%3A%22bwrjjBq5aNm2PrJGOZkdT%22%2C%22%24sesid%22%3A%5B1733945730061%2C%220193b734-180c-714e-9977-14b1da514eeb%22%2C1733945464844%5D%2C%22%24epp%22%3Atrue%7D; PGADMIN_LANGUAGE=en', 
              'Origin': `${process.env.ACTIVEPIECES_API_URL}`, 
              'Referer': `${process.env.ACTIVEPIECES_API_URL}/sign-in`, 
              'Sec-Fetch-Dest': 'empty', 
              'Sec-Fetch-Mode': 'cors', 
              'Sec-Fetch-Site': 'same-origin', 
              'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
              'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"', 
              'sec-ch-ua-mobile': '?0', 
              'sec-ch-ua-platform': '"Linux"'
            },
            data : data
          };

        const login = await axios.request(config);
        
        if(login?.data){        
            return response.success(req, res, { msgCode: "API_SUCCESS", data: login.data }, httpStatus.OK);
        } else {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);

        }
    } catch (error) {
        if (error.response) {
            // Handle HTTP errors (e.g., 401 Unauthorized)
            if (error.response.status === 401) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }
            // Handle other HTTP errors
            return response.error(req, res, { msgCode: 'API_ERROR', error: error.response.data }, error.response.status);
        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};

exports.getWorkflows = async (req, res) => {    
    try {  

        const agent = new https.Agent({
            rejectUnauthorized: false // Disables SSL certificate validation
        });
        console.log('user name is ');
        console.log(process.env.ACTIVEPIECES_USER);
        console.log(process.env.ACTIVEPIECES_PASSWORD);
        console.log(process.env.ACTIVEPIECES_API_URL);
        
        //returning all active workflow 
        let data = JSON.stringify({
            "email": process.env.ACTIVEPIECES_USER,
            "password": process.env.ACTIVEPIECES_PASSWORD
          });
          
          let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.ACTIVEPIECES_API_URL}/api/v1/authentication/sign-in`,
            headers: { 
              'Accept': 'application/json, text/plain, */*', 
              'Accept-Language': 'en-US,en;q=0.9', 
              'Connection': 'keep-alive', 
              'Content-Type': 'application/json', 
              'Cookie': '_ga=GA1.1.1167237575.1707808950; _ga_1EJC8L1CJH=GS1.1.1710183257.9.1.1710183341.0.0.0; ajs_user_id=bwrjjBq5aNm2PrJGOZkdT; ajs_anonymous_id=323211a8-bc19-4c7d-ab2b-8dc550023f7c; ph_phc_7F92HoXJPeGnTKmYv0eOw62FurPMRW9Aqr0TPrDzvHh_posthog=%7B%22distinct_id%22%3A%22bwrjjBq5aNm2PrJGOZkdT%22%2C%22%24sesid%22%3A%5B1733945730061%2C%220193b734-180c-714e-9977-14b1da514eeb%22%2C1733945464844%5D%2C%22%24epp%22%3Atrue%7D; PGADMIN_LANGUAGE=en', 
              'Origin': `${process.env.ACTIVEPIECES_API_URL}`, 
              'Referer': `${process.env.ACTIVEPIECES_API_URL}/sign-in`, 
              'Sec-Fetch-Dest': 'empty', 
              'Sec-Fetch-Mode': 'cors', 
              'Sec-Fetch-Site': 'same-origin', 
              'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
              'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"', 
              'sec-ch-ua-mobile': '?0', 
              'sec-ch-ua-platform': '"Linux"'
            },
            data : data,
            httpsAgent: agent
          };

        const login = await axios.request(config);
        
        if(login?.data){
            let workFlowConfig = {
                method: 'get',
                maxBodyLength: Infinity,
               // url: `${process.env.ACTIVEPIECES_API_URL}/api/v1/flows?projectId=hNrQu6AcgflGUd2LEAHJn&status=ENABLED&limit=10`,
                url: `${process.env.ACTIVEPIECES_API_URL}/api/v1/flows?projectId=${login?.data?.projectId}&status=ENABLED&limit=1000`,
                headers: { 
                  'Accept': 'application/json, text/plain, */*', 
                  'Accept-Language': 'en-US,en;q=0.9', 
                  //'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEifQ.eyJpZCI6Ild5NFdBT3BIckVESUVzampvVm5GTCIsInR5cGUiOiJVU0VSIiwicHJvamVjdElkIjoiaE5yUXU2QWNnZmxHVWQyTEVBSEpuIiwicGxhdGZvcm0iOnsiaWQiOiJmRkpPN1BUTGtMeWczWnU1WHA4UXQifSwidG9rZW5WZXJzaW9uIjoiV2o5S3ZoZUVhNkl2MUd2WkcwYmpVIiwiaWF0IjoxNzM0MzM1NTQxLCJleHAiOjE3MzQ5NDAzNDEsImlzcyI6ImFjdGl2ZXBpZWNlcyJ9.4yKoXf63jOo7KnZZz5eQMxzxjX_my96efsPyrchb6wg', 
                  'Authorization': `Bearer ${login?.data?.token}`, 
                  'Connection': 'keep-alive', 
                  'Cookie': '_ga=GA1.1.1167237575.1707808950; _ga_1EJC8L1CJH=GS1.1.1710183257.9.1.1710183341.0.0.0; ajs_user_id=bwrjjBq5aNm2PrJGOZkdT; ajs_anonymous_id=323211a8-bc19-4c7d-ab2b-8dc550023f7c; ph_phc_7F92HoXJPeGnTKmYv0eOw62FurPMRW9Aqr0TPrDzvHh_posthog=%7B%22distinct_id%22%3A%22bwrjjBq5aNm2PrJGOZkdT%22%2C%22%24sesid%22%3A%5B1733945730061%2C%220193b734-180c-714e-9977-14b1da514eeb%22%2C1733945464844%5D%2C%22%24epp%22%3Atrue%7D; PGADMIN_LANGUAGE=en', 
                //  'Referer': `${process.env.ACTIVEPIECES_API_URL}/projects/hNrQu6AcgflGUd2LEAHJn/flows?cursor=&status=ENABLED&limit=1000`, 
                  'Sec-Fetch-Dest': 'empty', 
                  'Sec-Fetch-Mode': 'cors', 
                  'Sec-Fetch-Site': 'same-origin', 
                  'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
                  'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"', 
                  'sec-ch-ua-mobile': '?0', 
                  'sec-ch-ua-platform': '"Linux"'
                },
                httpsAgent: agent
            };
              
    
            const workflows = await axios.request(workFlowConfig);
            
            if(workflows?.data){        
                return response.success(req, res, { msgCode: "API_SUCCESS", data: workflows.data }, httpStatus.OK);
            } else {
                return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
    
            }        
  
        } else {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);

        }
        
    } catch (error) {
        console.log('error start herer=========');
        console.log(error);
        if (error.response) {
            // Handle HTTP errors (e.g., 401 Unauthorized)
            if (error.response.status === 401) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }
            // Handle other HTTP errors
            return response.error(req, res, { msgCode: 'API_ERROR', error: error.response.data }, error.response.status);
        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};

exports.updateWorkflowStatus = async (req, res) => {    
    try {  
        let workflow_id = req.params.id;

        //returning all active workflow 
        let data = JSON.stringify({
            "email": process.env.ACTIVEPIECES_USER,
            "password": process.env.ACTIVEPIECES_PASSWORD
          });
          
          let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.ACTIVEPIECES_API_URL}/api/v1/authentication/sign-in`,
            headers: { 
              'Accept': 'application/json, text/plain, */*', 
              'Accept-Language': 'en-US,en;q=0.9', 
              'Connection': 'keep-alive', 
              'Content-Type': 'application/json', 
              'Cookie': '_ga=GA1.1.1167237575.1707808950; _ga_1EJC8L1CJH=GS1.1.1710183257.9.1.1710183341.0.0.0; ajs_user_id=bwrjjBq5aNm2PrJGOZkdT; ajs_anonymous_id=323211a8-bc19-4c7d-ab2b-8dc550023f7c; ph_phc_7F92HoXJPeGnTKmYv0eOw62FurPMRW9Aqr0TPrDzvHh_posthog=%7B%22distinct_id%22%3A%22bwrjjBq5aNm2PrJGOZkdT%22%2C%22%24sesid%22%3A%5B1733945730061%2C%220193b734-180c-714e-9977-14b1da514eeb%22%2C1733945464844%5D%2C%22%24epp%22%3Atrue%7D; PGADMIN_LANGUAGE=en', 
              'Origin': `${process.env.ACTIVEPIECES_API_URL}`, 
              'Referer': `${process.env.ACTIVEPIECES_API_URL}/sign-in`, 
              'Sec-Fetch-Dest': 'empty', 
              'Sec-Fetch-Mode': 'cors', 
              'Sec-Fetch-Site': 'same-origin', 
              'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
              'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"', 
              'sec-ch-ua-mobile': '?0', 
              'sec-ch-ua-platform': '"Linux"'
            },
            data : data
          };

        const login = await axios.request(config);
        
        if(login?.data){

            let workFlowUpdateData = JSON.stringify({
                "type": "CHANGE_STATUS",
                "request": {"status" : req.body.request.status}
            });

            let updateConfig = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.ACTIVEPIECES_API_URL}/api/v1/flows/${workflow_id}`,
                headers: { 
                  'Accept': 'application/json, text/plain, */*', 
                  'Accept-Language': 'en-US,en;q=0.9', 
                  'Authorization': `Bearer ${login?.data?.token}`,
                  'Connection': 'keep-alive', 
                  'Content-Type': 'application/json', 
                  'Cookie': '_ga=GA1.1.1167237575.1707808950; _ga_1EJC8L1CJH=GS1.1.1710183257.9.1.1710183341.0.0.0; ajs_user_id=bwrjjBq5aNm2PrJGOZkdT; ajs_anonymous_id=323211a8-bc19-4c7d-ab2b-8dc550023f7c; ph_phc_7F92HoXJPeGnTKmYv0eOw62FurPMRW9Aqr0TPrDzvHh_posthog=%7B%22distinct_id%22%3A%22bwrjjBq5aNm2PrJGOZkdT%22%2C%22%24sesid%22%3A%5B1733945730061%2C%220193b734-180c-714e-9977-14b1da514eeb%22%2C1733945464844%5D%2C%22%24epp%22%3Atrue%7D; PGADMIN_LANGUAGE=en; pga4_session=ae8e5acb-a219-4d8d-9a7f-fb3cd67499d8!6ZuI7Kf6cAm5kkowLX0Vb4hfpYKr0V7XW66zQVY5BXY=', 
                  'Origin': `${process.env.ACTIVEPIECES_API_URL}`, 
                  'Referer': `${process.env.ACTIVEPIECES_API_URL}/projects/hNrQu6AcgflGUd2LEAHJn/flows?cursor=&limit=10`, 
                  'Sec-Fetch-Dest': 'empty', 
                  'Sec-Fetch-Mode': 'cors', 
                  'Sec-Fetch-Site': 'same-origin', 
                  'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
                  'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"', 
                  'sec-ch-ua-mobile': '?0', 
                  'sec-ch-ua-platform': '"Linux"'
                },
                data : workFlowUpdateData
            };
              
    
            const workflows = await axios.request(updateConfig);
            
            if(workflows?.data){        
                return response.success(req, res, { msgCode: "API_SUCCESS", data: workflows.data }, httpStatus.OK);
            } else {
                return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
    
            }        
  
        } else {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);

        }
        
    } catch (error) {
        if (error.response) {
            // Handle HTTP errors (e.g., 401 Unauthorized)
            if (error.response.status === 401) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }
            // Handle other HTTP errors
            return response.error(req, res, { msgCode: 'API_ERROR', error: error.response.data }, error.response.status);
        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};

exports.getWorkflowById = async (req, res) => {    
    try {    
        let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'http://localhost:4200/api/v1/flows/l6nbYhqgqM7eiOxNDwUl3',
        headers: { 
            'Accept': 'application/json, text/plain, */*', 
            'Accept-Language': 'en-US,en;q=0.9', 
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEifQ.eyJpZCI6Ild5NFdBT3BIckVESUVzampvVm5GTCIsInR5cGUiOiJVU0VSIiwicHJvamVjdElkIjoiaE5yUXU2QWNnZmxHVWQyTEVBSEpuIiwicGxhdGZvcm0iOnsiaWQiOiJmRkpPN1BUTGtMeWczWnU1WHA4UXQifSwidG9rZW5WZXJzaW9uIjoiV2o5S3ZoZUVhNkl2MUd2WkcwYmpVIiwiaWF0IjoxNzMzOTg0NTA4LCJleHAiOjE3MzQ1ODkzMDgsImlzcyI6ImFjdGl2ZXBpZWNlcyJ9.EK6OahL9mwQ1YDSyLm0AgrP0mR3cBH88NItYVs37q6c', 
            'Connection': 'keep-alive', 
            'Cookie': '_ga=GA1.1.1167237575.1707808950; _ga_1EJC8L1CJH=GS1.1.1710183257.9.1.1710183341.0.0.0; ajs_user_id=bwrjjBq5aNm2PrJGOZkdT; ajs_anonymous_id=323211a8-bc19-4c7d-ab2b-8dc550023f7c; ph_phc_7F92HoXJPeGnTKmYv0eOw62FurPMRW9Aqr0TPrDzvHh_posthog=%7B%22distinct_id%22%3A%22bwrjjBq5aNm2PrJGOZkdT%22%2C%22%24sesid%22%3A%5B1733945730061%2C%220193b734-180c-714e-9977-14b1da514eeb%22%2C1733945464844%5D%2C%22%24epp%22%3Atrue%7D; PGADMIN_LANGUAGE=en', 
            'Referer': 'http://localhost:4200/projects/hNrQu6AcgflGUd2LEAHJn/flows/l6nbYhqgqM7eiOxNDwUl3', 
            'Sec-Fetch-Dest': 'empty', 
            'Sec-Fetch-Mode': 'cors', 
            'Sec-Fetch-Site': 'same-origin', 
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
            'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"', 
            'sec-ch-ua-mobile': '?0', 
            'sec-ch-ua-platform': '"Linux"'
        }
        };

        const workflows = await axios.request(config);
        
        if(workflows?.data){        
            return response.success(req, res, { msgCode: "API_SUCCESS", data: workflows.data }, httpStatus.OK);
        } else {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);

        }
    } catch (error) {
        if (error.response) {
            // Handle HTTP errors (e.g., 401 Unauthorized)
            if (error.response.status === 401) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }
            // Handle other HTTP errors
            return response.error(req, res, { msgCode: 'API_ERROR', error: error.response.data }, error.response.status);
        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};

exports.updateWorkflow = async (req, res) => {
    try {
        const { CustomRequestTask, RequestTask } = db.models;
     
        let workflowId = req.params.id

        let updateVal = {
            activepieces_automation_id : null,
            activepieces_completed_task : null
        }

        const update = await commonService.updateData(CustomRequestTask, updateVal, { activepieces_automation_id: workflowId });
         await commonService.updateData(RequestTask, updateVal, { activepieces_automation_id: workflowId });
        
        return response.success(req, res, { msgCode: "TASK_UPDATED", data: update }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.sendWhatsAppMessage = async (req, res) => {    
    try {  
        const accessToken = 'EAAIfelEMPygBO9rhOTDRt8PmgGUGumOz7eFzaKCBRBZCoiUEups1JHapcKLAad4qA6mcjwm8z92SXaZBON5ilcZACFWQ9zXhZBA4eG5x0uaXC5U52Uome43aVD65LfL5g5gqEXZApXh0QSZAeCNzPyeEgXqO1Ib8AeM78NU5wh8PHVjJxMFh88T4iCFOKc5ik0FwZDZD';

        let phone_number = req.body.phone_number;
       
        let source_url = req.body.source_id ? req.body.source_id : 'https://dev.gotrust.tech/consent-form?source_id=370128';
        
        const data = {
            messaging_product: "whatsapp",
            to: phone_number, // Replace with the recipient's phone number
            type: "template",
            template: {
                name: "consent_link_send",
                language: {
                    code: "en"
                },
                components: [
                    {
                      type: 'button',
                      sub_type: 'url',
                      index: '0',  // If this is the first button, index = 0
                      parameters: [
                        {
                          type: 'text',
                          text: source_url 
                          // Replace with your desired dynamic URL
                        }
                      ]
                    }
                ]
            }
        };

          await axios.post('https://graph.facebook.com/v22.0/513164111889091/messages', data, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            
        })

        console.log('test');

        return response.success(req, res, { msgCode: "API_SUCCESS", data: []}, httpStatus.OK);
    } catch (error) {
        console.log('error start herer=========');
        console.log(error);
        if (error.response) {
            // Handle HTTP errors (e.g., 401 Unauthorized)
            if (error.response.status === 401) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }
            // Handle other HTTP errors
            return response.error(req, res, { msgCode: 'API_ERROR', error: error.response.data }, error.response.status);
        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};

exports.callActivepiecesWebhook = async (req, res) => {    
    try {  
        let workflow_id = req?.query?.workflow_id;

        const { DataSubject } = db.models;

        let dataSubjectRespo =await dsrService.getOneRecord(DataSubject,{id: 1},['id','email','first_name','last_name', 'unique_identification_type', 'unique_identification_number', 'phone_no', 'dob' ]);
        dataSubjectRespo['task_id'] = 1;

        let data = {                     
            task_id: 1,
            activepieces_automation_id: 123,
            phone_number : '************',
            dsr_request_id: 1,
            data_subject_id: 1  
        } 

        JSON.stringify(data)
        
        
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://workflow-dev.gotrust.tech/api/v1/webhooks/${workflow_id}`,
            //url: `https://workflow-dev.gotrust.tech/api/v1/webhooks/${workflow_id}?phone_number=+************&task_id=1&email=<EMAIL>`,
          //  url: `https://workflow-dev.gotrust.tech/api/v1/webhooks/${workflow_id}?custom_data=${jsonData}&`,
          //  url: `http://localhost:4200/api/v1/webhooks/${workflow_id}??phone_number=+************&task_id=1`,
            //url: webhookurl,
            headers : { 
               // Authorization : "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5RXZJUmJoeTRsMkExcW5lWlRrbHZZZzMwNmlNUEVVM2ZiUWwxeXdaSmhnIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KUgFppPt2YiwE0Py32oP9vKCsi2abLkrQb6hmv0Dyi0bECakJUswsWGrBGN2JP_K8yTfORYIr-6lXxkgJsFmhjGNRzYvXqBzzTrey8QtHLp15GQsGt4rZ52mx5Izuser1EYCMUP7FuRHhb4wNrSSYFPbPPx-0gAmBhmCe9YQLDqnjuUIGo1z68x2KnmhMOTjLM3J2p6usu6M9HUnr08BGa0rgGd__LCxszpT-i6yvotgjkuXa0iWxe8aIaEj5wIHSexM4Fh7WVvBo8Zw16_umVWsWi3PmbePy5vns_nBJh_DG3b8KZ4Y0w6d4TTvUSP-mh6QXZkhVtsdhiR7_yJLJg"
            },
            data : dataSubjectRespo
            /*
            data: {
                phone_number: '+************',  
                email : '<EMAIL>',  
                task_id : 1,
                //user_token : "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5RXZJUmJoeTRsMkExcW5lWlRrbHZZZzMwNmlNUEVVM2ZiUWwxeXdaSmhnIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PjsmEwMmOhiMmX-xVUNp2Tu6_0wVNr_5fQeFCIPMiXhlegHNvjxLbPQFSA_tQOEf5DPrHCR-360EO0leBIJO1u4uvGHQd0cwGh7vQhVPE2idIVfAYZeQkFAeji2azSTPNmWroiDocpmG42_T0n9ivl9j7D9kkVTG1qcMz0o8V3wXD7GCrEEQfVGxBAOLET4UUFRn3E82ZnoTHegC_OVrEAwPPGk82FHNOaqaSwC2XST3glMKKD5-ljI0J0Zsn48iuqJSm77_a9ovddG4TJf0bnsKObCt377BgE7jEKKjplSnsXeQ-F8qqT3uMXqjPjm98aiUZBMwm1eV4HAmFeDpVA"
            }
            */
            
                              
        };
    
        let x = await axios.request(config);
        
        console.log(x);
        return response.success(req, res, { msgCode: "API_SUCCESS", data: []}, httpStatus.OK);
    } catch (error) {
        console.log('error start herer=========');
        console.log(error);
        if (error.response) {
            // Handle HTTP errors (e.g., 401 Unauthorized)
            if (error.response.status === 401) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }
            // Handle other HTTP errors
            return response.error(req, res, { msgCode: 'API_ERROR', error: error.response.data }, error.response.status);
        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};

exports.updateAutomationTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomRequestTask, DataSubject, DsrRequest } = db.models;
     
        if(! req?.query?.task_status){
            return response.error(req, res, { msgCode: 'TASK_STATUS' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const offset = null;

        let custom_data = req?.body;
        let id = custom_data?.task_id;
       
        const task = await commonService.findByCondition(CustomRequestTask, { 'id': id});
        if (!task) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const dsrReq = await commonService.findByCondition(DsrRequest, { 'id': task.request_id});
        if (!dsrReq) {
            return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        let taskDueDate = new Date();
        taskDueDate.setDate(taskDueDate.getDate() + 30);

        let updateTask = {
            start_date : new Date(),
            due_date : taskDueDate,
            //activepieces_completed_task : "identity_verification"
        }  

        if(req?.query?.task_status && req?.query?.task_status == 'completed'){
            updateTask['progress'] = 'COMPLETED';
            updateTask['completion_date'] = new Date();
        } 
        
        if(req?.query?.task_status && req?.query?.task_status == 'rejected'){
            updateTask['progress'] = 'REJECTED';
        }

        const update = await commonService.updateData(CustomRequestTask, updateTask, { id: id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        //update step in request
            let stepReqObj = {
                workflow_step_id : task.stage_id         
            }

            const stepUpdateReq = await commonService.updateData(DsrRequest, stepReqObj, { id: task.request_id }, dbTrans);

            if (!stepUpdateReq) {
                return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        //end update step

        if (dbTrans !== undefined) {
            await dbTrans.commit();
        }

        //move next step start here
        const isStepNotCompleted = await dsrService.getOneRecord(CustomRequestTask, { request_id: dsrReq.id, stage_id : task.stage_id, progress : { [db.Sequelize.Op.in]: ['NOT_STARTED', 'IN_PROGRESS', 'REJECTED'] }});
                    
        let limit = 1;
        let stepOrderForNextStep = [['stage_id', 'ASC']];  
        if(!isStepNotCompleted){
            console.log('all step completed')
            const netStep = await dsrService.getOneRecord(CustomRequestTask, 
                {
                        workflow_id: task.workflow_id,
                        stage_id: { [Sequelize.Op.gt]: task.stage_id},
                        request_id: dsrReq.id
                },
                '',
                limit, offset, stepOrderForNextStep
            );

            if(netStep.stage_id){
                let nextStepIdObj = {
                    workflow_step_id : netStep.stage_id
                }
                 await commonService.updateData(DsrRequest, nextStepIdObj, { id: dsrReq.id });
            }                        
        } 
        //move next step end here

        //Complete request if all task are completed
            const isAllStepNotCompleted = await dsrService.getOneRecord(CustomRequestTask, { request_id: task.request_id, workflow_id : task.workflow_id, progress : { [db.Sequelize.Op.in]: ['NOT_STARTED', 'IN_PROGRESS', 'REJECTED'] }});
            if(!isAllStepNotCompleted){
                let dataSubCond = {id : dsrReq.data_subject_id}
                const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);

                let ReqObj = {
                    status : 'COMPLETED',
                    workflow_step_id : task.stage_id         
                }

                const updateRequest = await commonService.updateData(DsrRequest, ReqObj, { id: task.request_id });

                if (!updateRequest) {
                    return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST);
                }

                const token = await signToken({
                    email: dataSubject?.email,
                    data_subject_id: dataSubject.id
                },36000);

                const generateVerificationToken = (token) => {
                    // Implement token generation logic (e.g., JWT or random string)     
                    const baseUrl = process.env.FRONTEND_BASE_URL;        
                    return `${baseUrl}/data-subject-rights/my-request`;  // Example URL with token
                };
        
                const sendData = {
                    content: `We are pleased to inform you that your Data Subject Request (DSR) for  Request ID ${dsrReq.dsr_id} has been successfully processed and completed.`,    
                    dsr_link: generateVerificationToken(token),  // Add verification link here
                    dsr_link_test : "You can access the portal using the link below."
                };
                
                const textTemplate = "dsr_verify_mail.ejs";                    
                const subject = `DSR Request ${dsrReq.dsr_id} Completed`;
        
                
                await sendMail(
                    dataSubject?.email,
                    sendData,
                    subject,
                    textTemplate
                );
            } 
                    
        //End complete request

        if(req?.query?.task_status && req?.query?.task_status == 'completed'){
            //Automation code start here
                let automationObj = {
                    request_id : dsrReq.id,
                    completed_task_id : id,
                    stage_id : task.stage_id
                }
              
                await automationOnCompleteTask(automationObj)
                
            //Automation code end here

        }
    
        return response.success(req, res, { msgCode: "TASK_UPDATED", data: update[1] }, httpStatus.OK);

        } catch (err) {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
        }
};


exports.identityVerifyWithDbHttpV1 = async (req, res) => {
    
     try { 
         let workflow_id = req.query.workflow_id;
         console.log('identityVerifyWithDbHttp is called')
         console.log('workflow id is ',workflow_id)
 
         let custom_data = req?.query;
 
         let id = custom_data?.task_id
        
          const { Role, DSRAuditLog, CustomRequestTask, RequestTypeStages, DataSubject, DsrRequest, User } = db.models;
             
              
             const task = await commonService.findByCondition(CustomRequestTask, { id : id});
                     
             if(! task){
                 return res.json({
                     "success": true,
                     "status_code": 400,
                     "message": "Task does not exist",
                     "result": {}            
                 });
                 
             }
 
             let taskDueDate = new Date();
             taskDueDate.setDate(taskDueDate.getDate() + 30);
             
            // for(let task of dsrCustomTasks){
                 //do we have to do dsr  approve
                 //check if ocr verification is enabled and current request ocr verification is done.
                 const dsrReq = await dsrService.getOneRecord(DsrRequest, {id : task.request_id});
 
                 let dataSubCond = {id : dsrReq.data_subject_id}
 
                 const dataSubject = await dsrService.getOneRecord(DataSubject, dataSubCond, ['id', 'email']);
 
                 if(! dataSubject){
                     return res.json({
                         "success": true,
                         "status_code": 400,
                         "message": "Request does not exist",
                         "result": {}            
                     });
                         
                 }
                     
                 const emailExists = await dsrService.getOneRecord(User, {email : dataSubject.email});
                  
                 if (emailExists) {
                    
                     //get dpo id and assign this request to dpo
                     const roleData = await commonService.findByCondition(Role, {
                         role_name: "Data Protection Officer",
                         customer_id: dsrReq.customer_id
                         
                     }, ['id']);
         
                     if(roleData){                
                         const dpo = await commonService.findByCondition(User, {
                             role_id: roleData.id
                         }, ['id', 'firstName', 'lastName', 'email']);
                         if (dpo) {  
                             await commonService.updateData(DsrRequest, {assignee_id: dpo.id}, { id: dsrReq.id });
                         }
 
                     }
                     //end assignment
                                          
                     let obj = {
                         status : 'APPROVED',
                        // is_acknowledge_mail_sent : "YES"
                     }
                     //add DPO as assignee of assignee not added
                     if(dsrReq.assignee_id == null){
                     // obj['assignee_id'] = req.body.req_data.dpo_id
                     }
 
                     const today = new Date();
                     
                     obj['assigned_date'] = today;
                     
                     let stepOrder = [['id', 'ASC']];        
                     let limit, offset = null;
                     const step = await dsrService.getOneRecord(RequestTypeStages, { type_id: dsrReq.request_type}, ['id','type_id', 'step_title'], limit, offset, stepOrder);
                 
 
                     obj['workflow_step_id'] = step.id;    
                     
                     const update = await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
                     
                     if (!update) {
                         return res.json({
                             "success": true,
                             "status_code": 400,
                             "message": "Error in update request",
                             "result": {}            
                         });
                        
                     }
 
                                   
                      
                     const auditAction = `Data Subject Identity Verification successfully done`;
                     await commonService.addDetail(DSRAuditLog, { type: 'DSR', type_id: dsrReq.id, step: task.stage_id, action: auditAction, action_by_id: dsrReq.user_id, customer_id: dsrReq.customer_id });                                       
                     
                     //send email


 
                     return res.json({
                         "success": true,
                         "status_code": 200,
                         "message": "Task executed successfully",
                         "result": {}            
                     });
                 } else {
                     let obj = {
                         status : 'REJECTED_IN_PROGRESS',
                         reject_reason : "Data Subject Identity Verification failed"
                     }
 
                     const auditAction = `Data Subject Identity Verification failed`;
                      await commonService.addDetail(DSRAuditLog, { type: 'DSR', type_id: dsrReq.id, step: task.stage_id, action: auditAction, action_by_id: dsrReq.user_id, customer_id: dsrReq.customer_id });
                                         
                      await commonService.updateData(DsrRequest, obj, { id: dsrReq.id });
                       
                    
                     //send email
                     subject = `Your DSR request (${dsrReq.dsr_id}) has been declined`;
                     emailMessage = 'Your information does not exist in our record. Please raise another request with updated credentials and supporting documents';
                     
              
                     
                     return res.json({
                         "success": true,
                         "status_code": 400,
                         "message": "Identity Verification failed",
                         "result": {}            
                     });
                 }
            // }
 
            return res.json({
                 "success": true,
                 "status_code": 400,
                 "message": "Identity Verification failed",
                 "result": {}            
             });
 
             // let returnData = {
                 
             // }  
 
             //return response.success(req, res, { msgCode: "TASK_UPDATED", data: returnData }, httpStatus.OK);
             return response.success(req, res, { msgCode: "TASK_UPDATED", data: returnData }, httpStatus.BAD_REQUEST);
             
     } catch (error) {
         console.error('Error creating workflow:', error);
         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
 
     }
 };



