const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const authService = require('../services/auth');
const { ONBOARDING_STATUS } = require('../constant/common');
const { getPagination } = require('../config/helper');
// const policyService = require('../services/policy');
// const roleService = require('../services/role');
// const { USER_ROLE } = require('../constant/common');
const { Op } = require('sequelize');
// const sequelize = require('sequelize');

exports.industryVertical = async (req, res) => {
    try {

        const { IndustryVertical } = db.models;
        //const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        // get Industry Vertical List
        const industryVerticalList = await commonService.getListWithoutCount(IndustryVertical, {}, ['id', 'name']);
        //, limit, offset, order
        return response.success(req, res, { msgCode: "INDUSTRY_FETCHED", data: industryVerticalList }, httpStatus.OK);
    } catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getQuestionnaires = async (req, res) => {
    try {
        const { Questionnaires, QuestionnairesAnswers } = db.models;
        const { industry_vertical_id, page, size, sort_by = 'ques_number', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];


        // const getQuestionnairesData = await commonService.getListWithoutCount(Questionnaires, { industry_vertical_id: industry_vertical_id }, ['id', 'ques_type', 'ques_number', 'editable', 'question', 'answer_type', 'ans_options', 'industry_vertical_id'], ['answer' , 'other' ,'other_answer'], limit, offset, order);
        // if (!getQuestionnairesData) {
        //     return response.error(req, res, { msgCode: 'QUESTIONS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        // }

        // get Questionnaires data
        const getQuestionnairesData = await commonService.getListAssociateWithoutCount(Questionnaires, QuestionnairesAnswers, { industry_vertical_id: industry_vertical_id }, { user_id: req.data.userId }, ['id', 'ques_type', 'ques_number', 'editable', 'question', 'answer_type', 'ans_options', 'industry_vertical_id'], ['answer' , 'other' ,'other_answer', 'user_id'], limit, offset, order);
        if (!getQuestionnairesData) {
            return response.error(req, res, { msgCode: 'QUESTIONS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        return response.success(req, res, { msgCode: "QUESTIONS_FETCHED", data: getQuestionnairesData }, httpStatus.OK);
    } catch (error) {
        console.log("getQuestionnaires", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.answers = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { QuestionnairesAnswers, User, OnboardingFlow } = db.models;
        // check user
        const userData = await commonService.findByCondition(User, { id: req.data.userId }, ['customer_id']);
        if (!userData) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
        }
        
        // Check if req.body is an array before using forEach
        if (Array.isArray(req.body.answers)) {
            req.body?.answers?.forEach(item => {
                // Assuming req.data.userId and userData.customer_id are available
                item.user_id = req.data.userId;
                item.customer_id = userData.customer_id;
            });
        }
        // insert answers
        const getQuestionnairesData = await authService.BulkData(QuestionnairesAnswers, req.body.answers, dbTrans);
        if (!getQuestionnairesData) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWERS' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // update onboarding flow
        const onboardingFlow = await commonService.updateData(OnboardingFlow, { step: ONBOARDING_STATUS.QUESTIONNAIRES_COMPLETED }, { user_id: req.data.userId }, dbTrans);
        if (!onboardingFlow[1]) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "API_SUCCESS", data: getQuestionnairesData }, httpStatus.OK, dbTrans);
    }
    catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


exports.updateAnswers = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { QuestionnairesAnswers, User } = db.models;
        // check user
        const userData = await commonService.findByCondition(User, { id: req.data.userId }, ['customer_id']);
        if (!userData) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
        }
        // Check if req.body is an array before using forEach
        if (Array.isArray(req.body.answers)) {
            const updatePromises = req.body.answers?.map(async (data) => {
                // Assuming req.data.userId and userData.customer_id are available
                data.user_id = req.data.userId;
                data.customer_id = userData.customer_id;
                // update answers
                const condition = {
                    [Op.and]: [
                        { question_id: data.question_id },
                        { user_id: req.data.userId }
                    ]
                };
                const updateData = await commonService.updateData(QuestionnairesAnswers, data, condition, dbTrans);
                if (!updateData) {
                    return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWERS' }, httpStatus.BAD_REQUEST, dbTrans);
                }
                
            });
            // Wait for all updates to complete
            await Promise.all(updatePromises);
            return response.success(req, res, { msgCode: "API_SUCCESS" }, httpStatus.OK, dbTrans);

        } else {
            return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
        }

    }
    catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


exports.onboarding = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { UserServices, UserAmbitions, UserPackages, UserQuestionnaires, Services, Ambitions, Packages, Questionnaires, OnboardingFlow } = db.models;
        const serviceIds = req.body.service_id;
        const ambitionIds = req.body.ambition_id;
        const questionnaireIds = req.body.questionnaires?.map(q => q.question_id);
        const packageId = req.body.package_id;

        // Check if services exist
        const services = await commonService.getListWithoutCount(Services, { id: serviceIds }, ['id', 'name', 'description'])
        if (services.length !== serviceIds.length) {
            return response.error(req, res, { msgCode: 'SOME_SERVICES_DO_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Check if ambitions exist
        const ambitions = await commonService.getListWithoutCount(Ambitions, { id: ambitionIds }, ['id', 'name', 'description']);
        if (ambitions.length !== ambitionIds.length) {
            return response.error(req, res, { msgCode: 'SOME_AMBITIONS_DO_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Check if questionnaires exist
        const questionnaires = await commonService.getListWithoutCount(Questionnaires, { id: questionnaireIds }, ['id', 'question', 'ambition_id']);
        if (questionnaires.length !== questionnaireIds.length) {
            return response.error(req, res, { msgCode: 'SOME_QUESTIONNAIRES_DO_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Check if package exists
        const package = await commonService.findByCondition(Packages, { id: packageId }, ['id', 'name', 'price', 'description']);
        if (!package) {
            return response.error(req, res, { msgCode: 'PACKAGE_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const servicesData = serviceIds?.map(element => ({
            user_id: req.data.userId,
            service_id: element
        }));

        const ambitionData = ambitionIds?.map(element => ({
            user_id: req.data.userId,
            ambition_id: element
        }));

        const questionnairesData = req.body.questionnaires?.map(element => ({
            user_id: req.data.userId,
            question_id: element.question_id,
            status: element.status
        }));

        // create user services
        const createUserServices = await authService.BulkData(UserServices, servicesData, dbTrans);
        if (!createUserServices) {
            return response.error(req, res, { msgCode: 'ERROR_CREATE_SERVICE' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // create user ambitions
        const createUserAmbitions = await authService.BulkData(UserAmbitions, ambitionData, dbTrans);
        if (!createUserAmbitions) {
            return response.error(req, res, { msgCode: 'ERROR_CREATE_AMBITION' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // create user questionnaires
        const createUserQuestionnaires = await authService.BulkData(UserQuestionnaires, questionnairesData, dbTrans);
        if (!createUserQuestionnaires) {
            return response.error(req, res, { msgCode: 'ERROR_CREATE_QUESTIONNAIRES' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // create user Packages
        const createUserPackages = await commonService.addDetail(UserPackages, { user_id: req.data.userId, package_id: req.body.package_id }, dbTrans);
        if (!createUserPackages) {
            return response.error(req, res, { msgCode: 'ERROR_CREATE_SERVICE' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const onboardingFlow = await commonService.updateData(OnboardingFlow, { step: ONBOARDING_STATUS.ONBOARDING_COMPLETED }, { user_id: req.data.userId }, dbTrans);
        if (!onboardingFlow[1]) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // const result = {
        //     user_services: createUserServices,
        //     user_ambitions: createUserAmbitions,
        //     user_questionnaies: createUserQuestionnaires,
        //     user_packages: createUserPackages
        // }

        return response.success(req, res, { msgCode: "ONBOARDING_COMPLETED" }, httpStatus.OK, dbTrans);

    } catch (error) {
        console.log("onboarding", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getState = async (req, res) => {
    try {
        const { state } = await db.models;

        const { page, size, country_id, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);

        //implementing searching and sorting  in the query itself to reduce complexity of code
        let stateCondition = { country_id: country_id };
        if (search) {
            stateCondition[Op.or] = [
                { state_name: { [Op.iLike]: `%${search}%` } }
            ];
        }

        const order = [[sort_by, sort_order]];

        // get country data
        const getStateData = await commonService.getList(state, stateCondition, { exclude: ['createdAt', 'updatedAt', 'deletedAt'] }, limit, offset, order);
        return response.success(req, res, { msgCode: "STATE_FETCHED", data: getStateData }, httpStatus.OK);
    } catch (error) {
        console.log("getState", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getCountry = async (req, res) => {
    try {
        const { country } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);

        //implementing searching and sorting  in the query itself to reduce complexity of code
        let countryCondition = {};
        if (search) {
            countryCondition[Op.or] = [
                { country_name: { [Op.iLike]: `%${search}%` } }
            ];
        }

        const order = [[sort_by, sort_order]];

        const getCountryData = await commonService.getList(country, countryCondition, { exclude: ['createdAt', 'updatedAt', 'deletedAt'] }, limit, offset, order);
        return response.success(req, res, { msgCode: "COUNTRY_FETCHED", data: getCountryData }, httpStatus.OK);
    } catch (error) {
        console.log("getCountry", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);

    }
};


exports.getLanguage = async (req, res) => {
    try {
        const { language } = await db.models;

        const { page, size, search, sort_by = 'language_name', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);

        // Construct the condition for querying languages
        let languageCondition = {};
        if (search) {
            languageCondition[Op.or] = [
                { language_name: { [Op.iLike]: `%${search}%` } }
            ];
        }

        const order = [[sort_by, sort_order]]; // Define the sorting order
        // Fetch language data based on the condition
        const getLanguageData = await commonService.getList(language, languageCondition, { exclude: ['createdAt', 'updatedAt', 'deletedAt'] }, limit, offset, order);
        return response.success(req, res, { msgCode: "LANGUAGE_FETCHED", data: getLanguageData }, httpStatus.OK);
    } catch (error) {
        console.log("getLanguage", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
