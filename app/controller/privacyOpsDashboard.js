const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const dayjs = require('dayjs');
const commonService = require('../services/common');
const policyService = require('../services/policy');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');

exports.getRiskByStage = async (req, res) => {
  try {
    const { time_frame, entity_id } = req.query;
    const { Risk } = db.models;
    const filterCondition = {};
    const allStage = Risk.rawAttributes.stage.values

    // Time range logic
    if (time_frame) {
      const now = new Date();
      const pastDate = new Date(now.getTime() - parseInt(time_frame) * 24 * 60 * 60 * 1000);
      filterCondition.createdAt = {
        [Op.gte]: pastDate
      };
    }

    if (entity_id) {
      filterCondition.entity_id = entity_id;
    }
    filterCondition.customer_id = req.data.customer_id;

    const result = await commonService.getDistinct(Risk, filterCondition, ['stage', [Sequelize.fn('COUNT', Sequelize.col('stage')), 'count']], ['stage']);

    if (!result) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    console.log(result)
    const countMap = result.reduce((acc, row) => {
      acc[row.stage] = parseInt(row.count);
      return acc;
    }, {});

    // Combine enum modules with counts (use 0 if not present)
    const finalResult = allStage.map(mod => ({
      Stage: mod,
      count: countMap[mod] || 0
    }));

    return response.success(req, res, { msgCode: 'DATA_FECTHED', data: finalResult }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getRiskByCategory = async (req, res) => {
  try {
    const { time_frame, entity_id } = req.query;

    const { Risk } = db.models;
    const filterCondition = {};
    const allCategory = Risk.rawAttributes.category.values
    // Time range logic
    if (time_frame) {
      const now = new Date();
      const pastDate = new Date(now.getTime() - parseInt(time_frame) * 24 * 60 * 60 * 1000);
      filterCondition.createdAt = {
        [Op.gte]: pastDate
      };
    }

    if (entity_id) {
      filterCondition.entity_id = entity_id;
    }
    filterCondition.customer_id = req.data.customer_id;

    const result = await commonService.getDistinct(Risk, filterCondition, ['category', [Sequelize.fn('COUNT', Sequelize.col('category')), 'count']], ['category']);
    if (!result) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    console.log(result)
    const countMap = result.reduce((acc, row) => {
      acc[row.category] = parseInt(row.count);
      return acc;
    }, {});

    // Combine enum modules with counts (use 0 if not present)
    const finalResult = allCategory.map(mod => ({
      category: mod,
      count: countMap[mod] || 0
    }));
    return response.success(req, res, { msgCode: 'DATA_FECTHED', data: finalResult }, httpStatus.OK);
  } catch (error) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getRiskByModule = async (req, res) => {
  try {
    const { time_frame, entity_id } = req.query;

    const { Risk } = db.models;
    const filterCondition = {};
    const allModules = Risk.rawAttributes.module.values;
    // Time range filtering
    if (time_frame) {
      const now = new Date();
      const pastDate = new Date(now.getTime() - parseInt(time_frame) * 24 * 60 * 60 * 1000);
      filterCondition.createdAt = {
        [Op.gte]: pastDate
      };
    }

    if (entity_id) {
      filterCondition.entity_id = entity_id;
    }
    filterCondition.customer_id = req.data.customer_id;

    const result = await commonService.getDistinct(Risk, filterCondition, ['module', [Sequelize.fn('COUNT', Sequelize.col('module')), 'count']], ['module']);
    if (!result) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    console.log(result)
    const countMap = result.reduce((acc, row) => {
      acc[row.module] = parseInt(row.count);
      return acc;
    }, {});

    // Combine enum modules with counts (use 0 if not present)
    const finalResult = allModules.map(mod => ({
      module: mod,
      count: countMap[mod] || 0
    }));
    return response.success(req, res, { msgCode: 'DATA_FECTHED', data: finalResult }, httpStatus.OK);
  } catch (error) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getRegulationComplianceBreakdown = async (req, res) => {
  try {
    const { entity_id, time_frame } = req.query;
    const filterCondition = {};
    const { RegulationComplianceStatus, RegulationsV2 } = db.models;
    // Time frame filtering (90 days for 3 months, 180 for 6 months, etc.)
    if (time_frame) {
      const now = new Date();
      const pastDate = new Date(now.getTime() - parseInt(time_frame) * 24 * 60 * 60 * 1000);
      filterCondition.createdAt = {
        [Op.gte]: pastDate
      };
    }

    if (entity_id) filterCondition.entity_id = entity_id;

    filterCondition.customer_id = req.data.customer_id;
    const result = await commonService.getListAssociateWithGroup2(RegulationComplianceStatus, RegulationsV2, filterCondition, {}, {}, {});
    if (!result) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const formatted = result.map(row => ({
      regulation: row.RegulationsV2?.authoritative_source || 'Unknown',
      status: {
        complied: Math.round(row.compliant_percentage || 0),
        partial_complied: Math.round(row.partial_compliant_percentage || 0),
        not_complied: Math.round(row.non_compliant_percentage || 0)
      }
    }));

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: formatted }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
