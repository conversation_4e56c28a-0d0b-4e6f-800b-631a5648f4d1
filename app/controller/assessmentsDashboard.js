const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const assessmentService = require('../services/assessment');
const constant = require('../constant/PDA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
const { sendMail } = require('../config/email');
const moment = require('moment');






const keyToModels = {
    'lia': 'liaAnswers',
    'tia': 'tiaAnswers',
    'pia': 'piaAnswers',
    'pda': 'pdaAnswers',
    'via': 'ViaAnswers',
    'vea': 'VeaAnswers',
    'veaMitigation': 'ReviewVEA',
    'veaTemplate' : 'VeaTemplate',
    'ropa': 'Answers'
};


exports.deleteDocuments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        // Check if req.body.key exists and is valid
        let modelName;
        if (req.body && req.body.key && keyToModels.hasOwnProperty(req.body.key)) {
            modelName = keyToModels[req.body.key];
        } else {
            return response.error(req, res, { msgCode: 'INVALID_KEY' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Get the actual model object from db.models using the modelName
        const model = db.models[modelName];
        if (!model) {
            return response.error(req, res, { msgCode: 'MODEL_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        let data;
        let check;
        if(req.body.key === 'veaMitigation' ){
            data = {
                attachment: null
            }
            check = await commonService.findByCondition(model, { attachment : req.body.url }, ['id']);
        }else{
            data = {
                attachment_link: null,
                raw_url: null
            }
            check = await commonService.findByCondition(model, { attachment_link: req.body.url }, ['id']);
        }
        
        if (!check) {
            return response.error(req, res, { msgCode: 'DOCUMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const update = await commonService.updateData(model, data, { id: check.id }, dbTrans);
        if (!update[1]) {
            return response.error(req, res, { msgCode: 'DOCUMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "DOCUMENT_DELETED" , data: update[1] }, httpStatus.OK, dbTrans);
    }
    catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}




exports.assessments = async (req, res) => {
    try {
        const { Assessments } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        const list = await commonService.getList(Assessments, {}, ['id', 'type', 'assessment_name', 'key'], limit, offset, order);
        if (!list) {
            return response.error(req, res, { msgCode: "LIST_NOT_FOUND" }, httpStatus.BAD_REQUEST);
        }
        // // Initialize an empty object to store grouped assessments
        // const groupedAssessments = {};
        // // Iterate over each assessment and group by type
        // list?.rows.forEach(assessment => {
        //     // Check if the type key exists in groupedAssessments
        //     if (!groupedAssessments.hasOwnProperty(assessment.type)) {
        //         // Initialize an empty array for this type
        //         groupedAssessments[assessment.type] = [];
        //     }
        //     // Push the assessment details to the corresponding type array
        //     groupedAssessments[assessment.type].push({
        //         id: assessment.id,
        //         assessment_name: assessment.assessment_name,
        //         key: assessment.key
        //     });
        // });
        return response.success(req, res, { msgCode: "ASSESSMENT_LIST", data: list }, httpStatus.OK);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST);
    }
};


exports.taskOverview = async (req, res) => {
    try {
        const { User, Group, Departments, Processes, Assessments, CustomerAssessments } = db.models;
        const { liaCollaborator, piaCollaborator, tiaCollaborator, pdaCollaborator } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let userType = null;
        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};
        let searchCondition = {};
        const groupIds = [req.params.entity_id];

        if (search && !search_key) {
            searchCondition = {
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Assessment.assessment_name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                ]
            };
        };

        if (search && search_key) {
            if (search_key === 'Department') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Process') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Assessment') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Assessment.assessment_name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'AssignedTo') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Approver') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            }
            else if (search_key === 'Owner') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Status') {
                searchCondition = {
                    status: sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'),
                    { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'Risks') {
                searchCondition = {
                    risks: sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'),
                    { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'is_assigned') {
                searchCondition = {
                    is_Assigned: true
                };
            }
            // } else if (search_key === 'Status') {
            //     searchCondition = {
            //         status: sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
            //     };
            // } else if (search_key === 'Risks') {
            //     searchCondition = {
            //         risks: sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
            //     };
            // }
        }

        const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
        const departmentIds = departments?.rows?.map(department => department.id);
        const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
        const processIds = processes?.rows?.map(process => process.id);

        if (req.data.roleName === authConstant.USER_ROLE[2]) {
            userType = 'DPO';
        } else if (departmentIds.length > 0) {
            userType = 'Department Head';
        } else {
            userType = 'Employee';
        }

        const collaboratorLia = await commonService.getList(liaCollaborator, { user_id: req.data.userId }, ['lia_id']);
        const collaboratorLiaIds = collaboratorLia?.rows?.map(collaborator => collaborator.lia_id);
        const collaboratorPia = await commonService.getList(piaCollaborator, { user_id: req.data.userId }, ['pia_id']);
        const collaboratorPiaIds = collaboratorPia?.rows?.map(collaborator => collaborator.pia_id);
        const collaboratorTia = await commonService.getList(tiaCollaborator, { user_id: req.data.userId }, ['tia_id']);
        const collaboratorTiaIds = collaboratorTia?.rows?.map(collaborator => collaborator.tia_id);
        const collaboratorPda = await commonService.getList(pdaCollaborator, { user_id: req.data.userId }, ['pda_id']);
        const collaboratorPdaIds = collaboratorPda?.rows?.map(collaborator => collaborator.pda_id);

        const collaboratorIds = [
            ...collaboratorLiaIds,
            ...collaboratorPiaIds,
            ...collaboratorTiaIds,
            ...collaboratorPdaIds
        ]

        let assessmentFilter = { entity_id: req.params.entity_id };

        if (userType === 'DPO') {
            assessmentFilter = { ...assessmentFilter };
        } else if (userType === 'Department Head') {
            assessmentFilter = {
                ...assessmentFilter,
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { approver: req.data.userId },
                    { id: { [Op.in]: collaboratorIds } },
                    { department_id: { [Op.in]: departmentIds } },
                    { process_id: { [Op.in]: processIds } }
                ]
            }
        } else {
            assessmentFilter = {
                ...assessmentFilter,
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { approver: req.data.userId },
                    { id: { [Op.in]: collaboratorIds } }
                ]
            }
        }

        const list = await assessmentService.getListWithMultipleAssociates(CustomerAssessments, Assessments, Departments, Processes, User, 'AssignedTo', User, 'Approver', User, 'Owner', Group,
            {
                [Op.and]: [
                    assessmentFilter,
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('CustomerAssessments.entity_id'), { [Op.ne]: null })
                        ]
                    },
                    is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
                    searchCondition
                ]
            },
            {},
            {},
            customerFilter,
            userFilter,
            {},
            {},
            {},
            ['id', 'assessment_id', 'risks', 'progress', 'start_date', 'end_date', 'tentative_date', 'status'], ['id', 'type', 'assessment_name', 'key'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], ['id', 'firstName', 'lastName'], ['id', 'firstName', 'lastName'], ['id', 'name'], limit, offset, order);


        if (!list) {
            return response.error(req, res, { msgCode: "LIST_NOT_FOUND" }, httpStatus.BAD_REQUEST);
        }


        const taskOverview = {
            user_type: userType,
            rows: list?.rows.map(listItem => {
                // if (listItem.Process) {
                //     listItem.Department = listItem.Process.Department;
                //     delete listItem.Process.Department;
                //     delete listItem.Process.User;
                // }

                if (listItem.AssignedTo) {
                    if (listItem?.AssignedTo?.id === req.data.userId) {
                        listItem.isAssigned = true;
                    } else {
                        listItem.isAssigned = false;
                    }
                    listItem.AssignedTo.name = `${listItem?.AssignedTo?.firstName} ${listItem?.AssignedTo?.lastName}`;
                    delete listItem.AssignedTo.firstName;
                    delete listItem.AssignedTo.lastName;
                }

                if (listItem?.Approver) {
                    listItem.Approver.name = `${listItem?.Approver?.firstName} ${listItem?.Approver?.lastName}`;
                    delete listItem.Approver.firstName;
                    delete listItem.Approver.lastName;
                }

                if (listItem.Owner) {
                    listItem.Owner.name = `${listItem?.Owner?.firstName} ${listItem?.Owner?.lastName}`;
                    delete listItem.Owner.firstName;
                    delete listItem.Owner.lastName;
                }

                // listItem.SPOC = { id: listItem.Department.User.id, name: `${tiaItem.Department.User.firstName} ${tiaItem.Department.User.lastName}` },
                listItem.isCollaborator = collaboratorIds.includes(listItem.id);
                // delete tiaItem.Department.User;

                return listItem;
            }),
            count: list.count
        };

        return response.success(req, res, { msgCode: "TASK_OVERVIEW_FETCHED", data: taskOverview }, httpStatus.OK);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST);
    }
};

exports.createAssessments = async (req, res) => {
    let dbTrans = await db.transaction();
    try {

        const { CustomerAssessments, Assessments, Departments, Processes, User, Group } = db.models;
        req.body.owner_id = req.data.userId;
        req.body.customer_id = req.data.customer_id;
        req.body.approver = req.body.reviewer_id;
        delete req.body.reviewer_id;
        let assessmentName = null;

        const assessment = await commonService.addDetail(CustomerAssessments, req.body, dbTrans);
        if (!assessment) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_ASSESSMENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        await dbTrans.commit();
        dbTrans=undefined;

        const data = await assessmentService.getDataWithMultipleAssociates(CustomerAssessments, Assessments, Departments, Processes, User, 'AssignedTo', User, 'Approver', User, 'Owner', { id: assessment.id },{},{},{},{},{},{}, ['id', 'tentative_date', 'status'], ['id', 'key'], ['id', 'name'], ['id', 'name'], ['id', 'email', 'firstName', 'lastName'], ['id', 'email', 'firstName', 'lastName'], ['id', 'email', 'firstName', 'lastName']);
        if(!data){
            return response.success(req, res, { msgCode: "ASSESSMENT_MAIL_NOT_TRIGGRED", data: assessment }, httpStatus.OK, dbTrans);
        }
        if (data?.Department) {
            assessmentName = data?.Department?.name;
        } else if (data?.Process) {
            assessmentName = data?.Process?.name;
        }
        let textTemplate = "";
        let textTemplate1 = "";
        let sendData = {};
        let sendData1 = {};
        const currentDate = moment().tz('Asia/Kolkata');
        const completionDate = moment(data?.tentative_date);
        const daysUntilCompletion = completionDate.diff(currentDate, 'days');
        const subject = `You have been assigned ${assessmentName} assessment : We Need Your Input!`;
        const subject1 = `You have been assigned as Reviewer for ${assessmentName} assessment : We Need Your Input!`;
        const baseUrl = req.protocol + '://' + req.get('host');
        const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "https://dev.gotrust.tech";
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";

        // console.log('assessment list', data );
        if(data.Assessment.key == 'tia'){
            textTemplate = "tia_assigned.ejs";
            sendData = {
                assignee: `${data?.AssignedTo?.firstName} ${data?.AssignedTo?.lastName}`,
                tiaName: assessmentName,
                assigner: `${data?.Owner?.firstName} ${data?.Owner?.lastName}`,
                url: `${frontEndUrl}/assessment-management/task-overview/`,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                daysLeft: daysUntilCompletion
            };
            
            textTemplate1= "tia_reviewer.ejs";
            sendData1 = {
                reviewer: `${data?.Approver?.firstName} ${data?.Approver?.lastName}`,
                tiaName: assessmentName,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                // assigner: `${assigner.firstName} ${assigner.lastName}`,
                url: `${process.env.SERVER_IP}/privacy/tia/`,
            };
        } else if(data.Assessment.key == 'lia'){
            textTemplate = "lia_assigned.ejs";
            sendData = {
                assignee: `${data?.AssignedTo?.firstName} ${data?.AssignedTo?.lastName}`,
                liaName: assessmentName,
                assigner: `${data?.Owner?.firstName} ${data?.Owner?.lastName}`,
                url: `${frontEndUrl}/assessment-management/task-overview/`,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                daysLeft: daysUntilCompletion
            };

            textTemplate1= "lia_reviewer.ejs";
            sendData1 = {
                reviewer: `${data?.Approver?.firstName} ${data?.Approver?.lastName}`,
                liaName: assessmentName,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                // assigner: `${assigner.firstName} ${assigner.lastName}`,
                url: `${process.env.SERVER_IP}/privacy/tia/`,
            };
        } else if(data.Assessment.key == 'pia'){
            textTemplate = "pia_assigned.ejs";
            sendData = {
                assignee: `${data?.AssignedTo?.firstName} ${data?.AssignedTo?.lastName}`,
                piaName: assessmentName,
                assigner: `${data?.Owner?.firstName} ${data?.Owner?.lastName}`,
                url: `${frontEndUrl}/assessment-management/task-overview/`,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                daysLeft: daysUntilCompletion
            };

            textTemplate1= "pia_reviewer.ejs";
            sendData1 = {
                reviewer: `${data?.Approver?.firstName} ${data?.Approver?.lastName}`,
                piaName: assessmentName,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                // assigner: `${assigner.firstName} ${assigner.lastName}`,
                url: `${process.env.SERVER_IP}/privacy/tia/`,
            };
        } else {
            textTemplate = "pda_assigned.ejs";
            sendData = {
                assignee: `${data?.AssignedTo?.firstName} ${data?.AssignedTo?.lastName}`,
                pdaName: assessmentName,
                assigner: `${data?.Owner?.firstName} ${data?.Owner?.lastName}`,
                url: `${frontEndUrl}/assessment-management/task-overview/`,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                daysLeft: daysUntilCompletion
            };

            textTemplate1= "pda_reviewer.ejs";
            sendData1 = {
                reviewer: `${data?.Approver?.firstName} ${data?.Approver?.lastName}`,
                pdaName: assessmentName,
                logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                // assigner: `${assigner.firstName} ${assigner.lastName}`,
                url: `${process.env.SERVER_IP}/privacy/tia/`,
            };
        }

        await sendMail(
            data?.AssignedTo?.email,
            sendData,
            subject,
            textTemplate,
        );

        await sendMail(
            data?.Approver?.email,
            sendData1,
            subject1,
            textTemplate1,
        );

        return response.success(req, res, { msgCode: "ASSESSMENT_CREATED", data: assessment }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
    }
};

exports.updateAssessments = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessment } = db.models;
        const checkAssessment = await commonService.findByCondition(CustomerAssessment, { id: req.params.assessment_id}, ['id', 'owner_id']);
        if (!checkAssessment) {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        if(req.data.userId !== checkAssessment.owner_id){
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' } , httpStatus.UNAUTHORIZED, dbTrans);
        }
        const updateAssessment = await commonService.updateData(CustomerAssessment, req.body , { id: req.params.assessment_id }, dbTrans);
        if (!updateAssessment[1]) {
            return response.error(req, res, { msgCode: 'FAILED_TO_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "ASSESSMENT_UPDATED", data: updateAssessment[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
    }
};



///--------------->>>>> Assessments Dashboard <<<<<-------------------\\\

exports.countByRisks = async (req, res) => {
    try {
        const { CustomerAssessment } = db.models;
        // counting the assessments using GroupBy "RISKS"
        const assessmentData = await commonService.getListGroupBy(CustomerAssessment, { customer_id: req.data.customer_id }, ['risks', [sequelize.fn('COUNT', 'id'), 'count']], ['risks']);
        if (!assessmentData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        let count = 0;
        assessmentData.forEach(row => {
            count = count + Number(row.count);
        });
        assessmentData.push({ risks: 'Total', count: String(count) });
        return response.success(req, res, { msgCode: "ASSESSMENTS_RISK_COUNT", data: assessmentData }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByOwner = async (req, res) => {
    try {
        const { CustomerAssessment, User } = db.models;
        // counting the assessments using GroupBy "OWNER"
        const assessmentData = await commonService.getListGroupBy(CustomerAssessment, { customer_id: req.data.customer_id }, ['owner_id', [sequelize.fn('COUNT', 'id'), 'count']], ['owner_id']);
        if (!assessmentData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        // Extract author emails into an array
        const ids = assessmentData?.map(assessment => assessment.owner_id);

        // Fetch author details from User model
        const ownerDetails = await commonService.getListWithoutCount(User, { id: { [Op.in]: ids } }, ['firstName', 'lastName', 'id']);

        // Update assessment data with owner details
        const updatedAssessmentData = assessmentData?.map(assessment => {
            const ownerDetail = ownerDetails?.find(detail => detail.id === assessment.owner_id);
            if (ownerDetail) {
                return {
                    ...assessment,
                    owner: ownerDetail.firstName + " " + ownerDetail.lastName
                };
            } else {
                return { ...assessment }; // If no match found, keep the email as it is
            }
        });
        return response.success(req, res, { msgCode: "ASSESSMENTS_OWNER_COUNT", data: updatedAssessmentData }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByAssessmentName = async (req, res) => {
    try {
        const { CustomerAssessment, Assessments } = db.models;
        // counting the assessments using GroupBy "Assessment name"
        const assessmentData = await commonService.getListGroupBy(CustomerAssessment, { customer_id: req.data.customer_id }, ['assessment_id', [sequelize.fn('COUNT', 'id'), 'count']], ['assessment_id']);
        if (!assessmentData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        // Extract author emails into an array
        const ids = assessmentData?.map(assessment => assessment.assessment_id);

        // Fetch author details from User model
        const assessmentDetails = await commonService.getListWithoutCount(Assessments, { id: { [Op.in]: ids } }, ['assessment_name', 'key', 'id']);

        // Update assessment data with owner details
        const updatedAssessmentData = assessmentData?.map(assessment => {
            const assessmentDetail = assessmentDetails?.find(detail => detail.id === assessment.assessment_id);
            if (assessmentDetail) {
                return {
                    ...assessment,
                    assessment_name: assessmentDetail.assessment_name,
                    assessment_key: assessmentDetail.key
                };
            } else {
                return { ...assessment }; // If no match found, keep the email as it is
            }
        });
        return response.success(req, res, { msgCode: "ASSESSMENTS_NAME_COUNT", data: updatedAssessmentData }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.assessmentsList = async (req, res) => {
    try {
        const { User, Group, Departments, Processes, Assessments, CustomerAssessment, AssessmentTemplate } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(1, 5);
        const order = [[sort_by, sort_order]];

        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};

        const list = await assessmentService.getListWithMultipleAssociates(CustomerAssessment, Assessments, AssessmentTemplate, Departments, Processes, User, 'AssignedTo', User, 'Approver', User, 'Owner', Group,
            { customer_id: req.data.customer_id },
            {},
            {},
            {},
            customerFilter,
            userFilter,
            {},
            {},
            {},
            ['id', 'assessment_id', 'risks', 'progress', 'start_date', 'end_date', 'status'], ['id', 'type', 'assessment_name', 'key'],['id','name','key'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], ['id', 'firstName', 'lastName'], ['id', 'firstName', 'lastName'], ['id', 'name'], limit, offset, order);


        if (!list) {
            return response.error(req, res, { msgCode: "LIST_NOT_FOUND" }, httpStatus.BAD_REQUEST);
        }


        const taskOverview = {
            rows: list?.rows.map(listItem => {
                if (listItem.AssignedTo) {
                    if (listItem?.AssignedTo?.id === req.data.userId) {
                        listItem.isAssigned = true;
                    } else {
                        listItem.isAssigned = false;
                    }
                    listItem.AssignedTo.name = `${listItem?.AssignedTo?.firstName} ${listItem?.AssignedTo?.lastName}`;
                    delete listItem.AssignedTo.firstName;
                    delete listItem.AssignedTo.lastName;
                }

                if (listItem?.Approver) {
                    listItem.Approver.name = `${listItem?.Approver?.firstName} ${listItem?.Approver?.lastName}`;
                    delete listItem.Approver.firstName;
                    delete listItem.Approver.lastName;
                }

                if (listItem.Owner) {
                    listItem.Owner.name = `${listItem?.Owner?.firstName} ${listItem?.Owner?.lastName}`;
                    delete listItem.Owner.firstName;
                    delete listItem.Owner.lastName;
                }

                // listItem.SPOC = { id: listItem.Department.User.id, name: `${tiaItem.Department.User.firstName} ${tiaItem.Department.User.lastName}` },
                // listItem.isCollaborator = collaboratorIds.includes(listItem.id);
                // delete tiaItem.Department.User;

                return listItem;
            }),
        };

        return response.success(req, res, { msgCode: "TASK_OVERVIEW_FETCHED", data: taskOverview }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};