const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const piaService = require('../services/pia');
const assessmentService = require('../services/assessment');
const constant = require('../constant/PIA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createAssessmentExcelFile } = require('../utils/helper');
const moment = require('moment');



exports.getPIAList = async (req, res) => {
    try {
        const { Departments, Processes, PIA, User, piaCollaborator } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let userType = null;
        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};

        let searchCondition = {};

        if (search && !search_key) {
            searchCondition = {
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('PIA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('PIA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                ]
            };
        };

        if (search && search_key) {
            if (search_key === 'Department') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Process') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'AssignedTo') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Reviewer') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'SPOC') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Status') {
                searchCondition = {
                    status: sequelize.where(sequelize.cast(sequelize.col('PIA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'Risks') {
                searchCondition = {
                    risks: sequelize.where(sequelize.cast(sequelize.col('PIA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            }
        }

        const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
        const departmentIds = departments?.rows?.map(department => department.id);
        const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
        const processIds = processes?.rows?.map(process => process.id);

        if (req.data.roleName === authConstant.USER_ROLE[2]) {
            userType = 'DPO';
        } else if (departmentIds.length > 0) {
            userType = 'Department Head';
        } else {
            userType = 'Employee';
        }

        const collaboratorPia = await commonService.getList(piaCollaborator, { user_id: req.data.userId }, ['pia_id']);
        const collaboratorPiaIds = collaboratorPia?.rows?.map(collaborator => collaborator.pia_id);

        let piaFilter = {}

        if (userType === 'DPO') {
            piaFilter = {};
        } else if (userType === 'Department Head') {
            piaFilter = {
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { id: { [Op.in]: collaboratorPiaIds } },
                    { department_id: { [Op.in]: departmentIds } },
                    { process_id: { [Op.in]: processIds } }
                ]
            }
        } else {
            piaFilter = {
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { id: { [Op.in]: collaboratorPiaIds } }
                ]
            }
        }

        const pia = await piaService.getEmployeePIA(PIA, Departments, Processes, User, {
            [Op.and]: [
                piaFilter,
                {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                        sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                    ]
                },
                is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
                searchCondition
            ]
        }, { ...customerFilter, group_id: req.params.entity_id }, customerFilter, userFilter, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }

        const piaStr = {
            user_type: userType,
            rows: pia.rows.map(piaItem => {
                if (piaItem.Process) {
                    piaItem.Department = piaItem.Process.Department;
                    delete piaItem.Process.Department;
                    delete piaItem.Process.User;
                }

                if (piaItem.AssignedTo) {
                    if (piaItem.AssignedTo.id === req.data.userId) {
                        piaItem.isAssigned = true;
                    } else {
                        piaItem.isAssigned = false;
                    }
                    piaItem.AssignedTo.name = `${piaItem.AssignedTo.firstName} ${piaItem.AssignedTo.lastName}`;
                    delete piaItem.AssignedTo.firstName;
                    delete piaItem.AssignedTo.lastName;
                }

                if (piaItem.Approver) {
                    piaItem.Approver.name = `${piaItem.Approver.firstName} ${piaItem.Approver.lastName}`;
                    delete piaItem.Approver.firstName;
                    delete piaItem.Approver.lastName;
                }

                piaItem.SPOC = { id: piaItem.Department.User.id, name: `${piaItem.Department.User.firstName} ${piaItem.Department.User.lastName}` },
                    piaItem.isCollaborator = collaboratorPiaIds.includes(piaItem.id);
                delete piaItem.Department.User;

                return piaItem;
            }),
            count: pia.count
        };

        return response.success(req, res, { msgCode: "PIA_FETCHED", data: piaStr }, httpStatus.OK);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getDepartmentsPIA = async (req, res) => {
    try {
        const { Departments, Processes, CustomerAssessments , User, piaCollaborator } = db.models;
        const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let piaCondition = { [Op.not]: { department_id: null } };
        let empCheck = false;
        let roleBasedData = [];
        let filter = { customer_id: req.data.customer_id };

        if (search) {
            filter[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
            ];
        }

        // DPO can see all data, so no changes to piaCondition
        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            // Fetch department details
            const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);

            if (departments && departments.rows.length > 0) {
                // Department head can only see their own departments
                const departmentIds = departments?.rows?.map(department => department.id);
                piaCondition.department_id = { [Op.in]: departmentIds };
            } else {
                // Employee can only see their own PIA
                empCheck = true;
            }
        }

        if (empCheck) {
            const collaboratorPia = await commonService.getList(piaCollaborator, { user_id: req.data.userId }, ['pia_id']);
            const collaboratorPiaIds = collaboratorPia?.rows?.map(collaborator => collaborator.pia_id);
            const pia = await piaService.getEmployeePIA(CustomerAssessments, Departments, Processes, User, {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { assigned_to: req.data.userId },
                            { id: { [Op.in]: collaboratorPiaIds } }
                        ]
                    },
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                        ]
                    }
                ]
            }, { ...filter, group_id: req.params.entity_id }, filter, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (!pia) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
            }

            roleBasedData = pia;
            roleBasedData.rows = roleBasedData?.rows?.map(piaItem => {
                if (piaItem.Process) {
                    piaItem.department_name = piaItem.Process.Department.name;
                    delete piaItem.Process.Department;
                } else {
                    piaItem.department_name = piaItem.Department.name;
                }

                return {
                    ...piaItem,
                    isCollaborator: collaboratorPiaIds.includes(piaItem.id)
                };
            });
        } else {
            const pia = await piaService.getDepartmentsPIA(CustomerAssessments, Departments, User, piaCondition, { ...filter, group_id: req.params.entity_id }, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (!pia) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
            }
            roleBasedData = pia;

            // Add assigned PIAs
            const collaboratorPia = await commonService.getList(piaCollaborator, { user_id: req.data.userId }, ['pia_id']);
            const collaboratorPiaIds = collaboratorPia?.rows?.map(collaborator => collaborator.pia_id);
            const assignedPia = await piaService.getEmployeePIA(CustomerAssessments, Departments, Processes, User, {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { assigned_to: req.data.userId },
                            { id: { [Op.in]: collaboratorPiaIds } }
                        ]
                    },
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                        ]
                    },
                ], status: { [Op.ne]: constant.status.COMPLETED }
            }, { ...filter, group_id: req.params.entity_id }, filter, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (assignedPia) {
                roleBasedData.assigned = assignedPia.rows?.map(piaItem => {
                    if (piaItem.Process) {
                        piaItem.department_name = piaItem.Process.Department.name;
                        delete piaItem.Process.Department;
                    } else {
                        piaItem.department_name = piaItem.Department.name;
                    }

                    return {
                        ...piaItem,
                        isCollaborator: collaboratorPiaIds.includes(piaItem.id)
                    };
                });
            }
        }
        return response.success(req, res, { msgCode: "PIA_FETCHED", data: roleBasedData }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getProcessesPIA = async (req, res) => {
    try {
        const { Processes, CustomerAssessments, User } = db.models;
        const department_id = req.params.department_id;
        const pia = await piaService.getProcessesPIA(CustomerAssessments, Processes, User, { [Op.not]: { process_id: null } }, { customer_id: req.data.customer_id, department_id: department_id }, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'firstName', 'lastName'], [['id', 'ASC']]);
        // const pia = await privacyService.getProcessesPIA(req.data.customer_id, department_id);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }
        return response.success(req, res, { msgCode: "PIA_FETCHED", data: pia }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.assignPIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, Departments, Processes, AuditLog, User } = db.models;
        let piaName = null;
        // let approverId = null;
        let assignedToName = null;
        let dept_id = null;

        if(req.data.roleName !== authConstant.USER_ROLE[2]){
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }
        
        const checkPIA = await piaService.getPIA(CustomerAssessments, Departments, Processes, User, { id: req.body.pia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (checkPIA.Department) {
            // approverId = checkPIA.Department.spoc_id;
            piaName = checkPIA.Department.name;
            dept_id = checkPIA.Department.id;
        } else if (checkPIA.Process) {
            // approverId = checkPIA.Process.Department.spoc_id;
            piaName = checkPIA.Process.name;
            dept_id = checkPIA.Process.Department.id;
        }

        const user = await commonService.findByCondition(User, {
            id: req.body.user_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        assignedToName = `${user.firstName} ${user.lastName}`;

        const assigner = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName']);
        if (!assigner) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
        const pia = await commonService.updateData(CustomerAssessments, { assigned_to: req.body.user_id }, { id: req.body.pia_id }, dbTrans);
        if (!pia[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const currentDate = moment().tz('Asia/Kolkata');
        const completionDate = moment(checkPIA?.tentative_date);
        const daysUntilCompletion = completionDate.diff(currentDate, 'days');

        const subject = `You have been assigned ${piaName} assessment: We Need Your Input!`;
        const textTemplate = "pia_assigned.ejs";
        const baseUrl = req.protocol + '://' + req.get('host');
        const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "https://dev.gotrust.tech";
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";
        
        const sendData = {
            assignee: `${user.firstName} ${user.lastName}`,
            piaName: piaName,
            assigner: `${assigner.firstName} ${assigner.lastName}`,
            url: `${frontEndUrl}/assessment-management/task-overview/`,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
            daysLeft: daysUntilCompletion
        };

        sendMail(
            user.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Assigned ${piaName} PIA to ${assignedToName}`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PIA', type_id: req.body.pia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PIA_ASSIGNED", data: pia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.reviewerPIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, Departments, Processes, AuditLog, User } = db.models;
        let piaName = null;
        // let approver = null;
        let reviewerName = null;
        let dept_id = null;

        const checkPIA = await piaService.getPIA(CustomerAssessments, Departments, Processes, User, { id: req.body.pia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (checkPIA.Department) {
            // approverId = checkTIA.Department.spoc_id;
            piaName = checkPIA.Department.name;
            dept_id = checkPIA.Department.id;
        } else if (checkPIA.Process) {
            // approverId = checkTIA.Process.Department.spoc_id;
            piaName = checkPIA.Process.name;
            dept_id = checkPIA.Process.Department.id;
        }

        const user = await commonService.findByCondition(User, {
            id: req.body.user_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        reviewerName = `${user.firstName} ${user.lastName}`;

        // const reviewer = await commonService.findByCondition(User, {
        //     id: req.data.userId
        // }, ['firstName', 'lastName']);
        // if (!reviewer) {
        //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        // }
        const pia = await commonService.updateData(CustomerAssessments, { approver: req.body.user_id }, { id: req.body.pia_id }, dbTrans);
        if (!pia[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const subject = `You have been assigned as Reviewer for ${piaName} - PIA : We Need Your Input!`;
        const textTemplate = "pia_reviewer.ejs";
        const sendData = {
            reviewer: `${reviewerName}`,
            piaName: piaName,
            // assigner: `${assigner.firstName} ${assigner.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/pia/`,
        };

        sendMail(
            user.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Added Reviewer to  ${piaName} PIA to ${reviewerName}`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PIA', type_id: req.body.pia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PIA_REVIEWER_ADDED", data: pia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.startPIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, piaControls, piaCustomerControls, piaCollaborator, AuditLog, Departments, Processes, User } = db.models;

        const checkPIA = await piaService.getPIA(CustomerAssessments, Departments, Processes, User, { id: req.params.pia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.status === constant.status.STARTED || checkPIA.status === constant.status.CHANGES_REQUESTED) {
            if (checkPIA.assigned_to && checkPIA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                const collaborator = await commonService.findByCondition(piaCollaborator, { pia_id: req.params.pia_id, user_id: req.data.userId }, ['id']);
                if (!collaborator) {
                    return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED, dbTrans);
                }
            }
            return response.success(req, res, { msgCode: "PIA_STARTED" }, httpStatus.OK, dbTrans);
        } else if (checkPIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let piaName = null;
        let dept_id = null;
        // let approverId = null;

        if (checkPIA.Department) {
            piaName = checkPIA.Department.name;
            dept_id = checkPIA.Department.id;
            // approverId = checkPIA.Department.spoc_id;
        } else if (checkPIA.Process) {
            piaName = checkPIA.Process.name;
            dept_id = checkPIA.Process.Department.id;
            // approverId = checkPIA.Process.Department.spoc_id;
        }

        if (checkPIA.assigned_to && checkPIA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(piaCollaborator, { pia_id: req.params.pia_id, user_id: req.data.userId }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED, dbTrans);
            }
        }

        const pia = await commonService.updateData(CustomerAssessments, { status: constant.status.STARTED, start_date: Date() }, { id: req.params.pia_id }, dbTrans);
        if (!pia[1]) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let controls = await commonService.getList(piaControls, { industry_vertical_id: 1, customer_id: req.data.customer_id }, ['id', 'category_id', 'parent_id', 'customer_id']);
        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (controls.count == 0) {
            controls = await commonService.getList(piaControls, { industry_vertical_id: 1, customer_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
        }

        const parentControls = controls?.rows?.filter(control => control.parent_id === null);
        const childControls = controls?.rows?.filter(control => control.parent_id !== null);

        const customerControlsParents = parentControls?.map(control => {
            return {
                question_id: control.id,
                customer_id: control.customer_id,
                category_id: control.category_id,
                pia_id: Number(req.params.pia_id),
                is_custom: false
            }
        });

        const newCustomerControlsParents = await commonService.bulkAdd(piaCustomerControls, customerControlsParents, dbTrans);
        if (!newCustomerControlsParents) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
            map[parentControls[index].id] = control.id;
            return map;
        }, {});

        const customerControlsChildren = childControls?.map(control => {
            return {
                question_id: control.id,
                category_id: control.category_id,
                customer_id: control.customer_id,
                pia_id: req.params.pia_id,
                parent_id: parentIdMap[control.parent_id],
                is_custom: false
            }
        });

        const newCustomerControlsChildren = await commonService.bulkAdd(piaCustomerControls, customerControlsChildren, dbTrans);
        if (!newCustomerControlsChildren) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditAction = `${user?.firstName} ${user?.lastName} started ${piaName} PIA`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PIA', type_id: req.params.pia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PIA_STARTED", data: pia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getProgress = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaCustomerControls, piaAnswers, CustomerAssessments, ReviewPIA, piaCollaborator } = db.models;
        const pia_id = req.params.pia_id;

        const pia = await commonService.findByCondition(CustomerAssessments, { id: pia_id }, ['status', 'approver', 'assigned_to']);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
        }

        const status = pia.status;
        let controls = null;
        let totalControls = 0;
        let answeredControls = 0;
        let childControls = [];
        let progress = 0;

        if (pia.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && pia.approver !== req.data.userId) {
            const collaborator = await commonService.getList(piaCollaborator, { pia_id: pia_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED , dbTrans);
            }

            const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

            controls = await commonService.getListAssociateWithoutCount(piaCustomerControls, piaAnswers, { pia_id: pia_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.parent_id === null && control.piaAnswer).length;
            // childControls = controls?.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.piaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.piaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });

            progress = (answeredControls / totalControls) * 100;
            progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

            return response.success(req, res, { msgCode: "PROGRESS_FETCHED", data: { totalControls, answeredControls, progress } }, httpStatus.OK ,dbTrans);
        }

        if (status === constant.status.STARTED) {
            controls = await commonService.getListAssociateWithoutCount(piaCustomerControls, piaAnswers, { pia_id: pia_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.parent_id === null && control.piaAnswer).length;
            // childControls = controls?.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.piaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.piaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.UNDER_REVIEW) {
            controls = await commonService.getListAssociateWithoutCountWithAlias(piaCustomerControls, ReviewPIA, 'ReviewPIA', { pia_id: pia_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
            }
            // totalControls = controls.filter(control => control.parent_id === null).length;
            // answeredControls = controls.filter(control => control.ReviewPIA).length;
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                }
                if (control.ReviewPIA) {
                    answeredControls++;
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.CHANGES_REQUESTED) {
            controls = await piaService.getControlsWithAnswersAndReviews(piaCustomerControls, piaAnswers, ReviewPIA, { pia_id: pia_id }, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
            }
            // totalControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewPIA && control.ReviewPIA.accurate_information === 0).length;
            // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.piaAnswer?.updatedAt > control.ReviewPIA?.updatedAt).length;
            // childControls = controls.rows?.filter(control => control.parent_id !== null);
            controls.rows?.forEach(control => {
                if (control.parent_id === null) {
                    if (control.ReviewPIA && control.ReviewPIA.accurate_information === 0) {
                        totalControls++;
                    }
                    if (control.piaAnswer?.updatedAt > control.ReviewPIA?.updatedAt && control.ReviewPIA.accurate_information === 0) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});
            Object.entries(childControlsByParent).forEach(([parentId, childControls]) => {
                const parentControl = controls.rows.find(control => control.id == parentId);
                if (parentControl && childControls.every(control => control.piaAnswer.updatedAt > parentControl.ReviewPIA.updatedAt)) {
                    answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewPIA
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.COMPLETED) {
            controls = await commonService.getListAssociateWithoutCount(piaCustomerControls, piaAnswers, { pia_id: pia_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
            }

            // totalControls = controls.filter(control => control.parent_id === null).length;
            // answeredControls = controls.filter(control => control.parent_id === null && control.piaAnswer).length;
            // childControls = controls.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.piaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.piaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });
            progress = (answeredControls / totalControls) * 100;
            
        }

        progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

        const updateProgress = await commonService.updateData(CustomerAssessments ,{ progress : progress} , {id: pia_id} , dbTrans);
        if (!updateProgress) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans );
        }

        progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

        return response.success(req, res, { msgCode: "PROGRESS_FETCHED", data: { totalControls, answeredControls, progress } }, httpStatus.OK , dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


exports.getCategories = async (req, res) => {
    try {
        const { piaCategory, piaCollaborator, CustomerAssessments, piaCustomerControls, ReviewPIA } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let piaLevel = req.params.pia_level;
        const pia_id = req.query.pia_id;
        piaLevel = piaLevel.charAt(0).toUpperCase() + piaLevel.slice(1);

        let categoryCondition = { pia_level: piaLevel };
        let conditions = [];

        const pia = await commonService.findByCondition(CustomerAssessments, { id: pia_id }, ['status', 'assigned_to', 'approver']);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pia.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && pia.approver !== req.data.userId) {
            const collaborators = await commonService.getList(piaCollaborator, { pia_id: pia_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborators) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
            // categoryCondition.id = { [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) };
            conditions.push({ [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) });
        }

        if (pia.status === constant.status.CHANGES_REQUESTED) {
            const changeReqCategories = await commonService.getListAssociateWithAlias(piaCustomerControls, ReviewPIA, 'ReviewPIA', { pia_id: pia_id }, { accurate_information: 0 }, ['category_id']);
            if (!changeReqCategories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
            // categoryCondition.id = { [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) };
            conditions.push({ [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) });
        }else {
            categoryCondition.customer_id = req.data.customer_id;
        }
        
        if (conditions.length > 0) {
            categoryCondition.id = {
                [Op.and]: conditions
            };
        }

        let categories = await commonService.getList(piaCategory, categoryCondition, ['id', 'name'] , limit , offset ,order);
        if (!categories) {
            return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if(categories.count==0){
            categoryCondition.customer_id = null;
            categories = await commonService.getList(piaCategory, categoryCondition, ['id', 'name'] , limit , offset ,order);
            if (!categories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
        }

        // if (!categories) {
        //     return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        // }
        return response.success(req, res, { msgCode: "CATEGORIES_FETCHED", data: categories }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getControls = async (req, res) => {
    try {
        const { piaControls, piaCustomerControls, piaAnswers, User, CustomerAssessments, piaCollaborator, ReviewPIA } = db.models;
        const pia_id = req.params.pia_id;
        const category_id = req.query.category_id;

        const pia = await commonService.findByCondition(CustomerAssessments, { id: pia_id }, ['status', 'assigned_to', 'approver']);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pia.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PIA_NOT_STARTED" }, httpStatus.BAD_REQUEST);
        }

        if (pia.assigned_to !== req.data.userId && pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(piaCollaborator, { pia_id: pia_id, user_id: req.data.userId, category_id: category_id }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
        }

        let controls;
        
        const controlsAttributes = [
            [
                sequelize.literal(`"piaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."title" ELSE "piaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."description" ELSE "piaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN CAST("piaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("piaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."is_attachment" ELSE "piaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."question" ELSE "piaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."fields" ELSE "piaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."extra_input" ELSE "piaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN CAST("piaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("piaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."extra_input_fields" ELSE "piaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."question_id" IS NOT NULL THEN "piaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        
        if (pia.status === constant.status.UNDER_REVIEW || pia.status === constant.status.CHANGES_REQUESTED || pia.status === constant.status.COMPLETED) {
            if (pia.status === constant.status.UNDER_REVIEW && (pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2])) {
                return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
            }
            controls = await piaService.getControlsWithReview(piaCustomerControls, piaControls, piaAnswers, User, ReviewPIA, { pia_id: pia_id, category_id: category_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'accurate_information', 'comments'], [['question_id', 'ASC']]);
        } else {
            controls = await piaService.getControls(piaCustomerControls, piaControls, piaAnswers, User, { pia_id: pia_id, category_id: category_id }, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], [['question_id', 'ASC']]);
        }
        
        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        for (let control of controls) {
            control.Answer = control.piaAnswer;
            delete control.piaAnswer;
            control.Review = control.ReviewPIA;
            delete control.ReviewPIA;
            if (control.Answer) {
                control.answered = true;
                if (control.Answer.extra_answer) {
                    control.Answer.extra_answered = true;
                } else {
                    control.Answer.extra_answered = false;
                }
            } else {
                control.answered = false;
            }

            if (control.Review) {
                control.reviewed = true;
            } else {
                control.reviewed = false;
            }

            if (pia.assigned_to !== req.data.userId && pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                control.is_collaborator = true;
            } else {
                control.is_collaborator = false;
            }
        }

        let parents = controls?.filter(control => control.parent_id === null);
        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        if (pia.status === constant.status.CHANGES_REQUESTED) {
            parents = parents?.filter(parent => parent.Review?.accurate_information === 0);
        }

        return response.success(req, res, { msgCode: "CONTROLS_FETCHED", data: { status: pia.status, controls: parents } }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getArtifactTypes = async (req, res) => {
    try {
        const { piaControls } = db.models;
        const artifactTypes = piaControls.rawAttributes.artifact_type.values;
        if (!artifactTypes) {
            return response.error(req, res, { msgCode: "ARTIFACT_TYPES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        return response.success(req, res, { msgCode: "ARTIFACT_TYPES_FETCHED", data: artifactTypes }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.addCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaCustomerControls } = db.models;
        req.body.is_custom = true;

        const addedControls = await commonService.addDetail(piaCustomerControls, req.body, dbTrans);
        if (!addedControls) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_CREATED", data: addedControls }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.updateControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaControls, piaCustomerControls } = db.models;
        const { title, description, artifact_type, question, fields, is_attachment } = req.body;

        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        let raw_question = null;
        const originalQuestion = await commonService.getDataAssociate(piaControls, piaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (originalQuestion) {
            raw_question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(piaCustomerControls, { id: req.params.customer_control_id }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            raw_question = customQuestion;
        }

        const updatedValues = {
            title: title || raw_question.title,
            description: description || raw_question.description,
            artifact_type: artifact_type || raw_question.artifact_type,
            is_attachment: is_attachment || raw_question.is_attachment,
            question: question || raw_question.question,
            fields: fields || raw_question.fields,
            is_custom: true
        }

        const updatedControls = await commonService.updateData(piaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.updateFields = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaControls, piaCustomerControls } = db.models;
        const { fields } = req.body;
        let question = null;

        const originalQuestion = await commonService.getDataAssociate(piaControls, piaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (originalQuestion) {
            question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(piaCustomerControls, { id: req.params.customer_control_id }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            question = customQuestion;
        }

        const updatedValues = {
            title: question.title,
            description: question.description,
            artifact_type: question.artifact_type,
            is_attachment: question.is_attachment,
            question: question.question,
            fields: fields || question.fields,
            is_custom: true
        }

        const updatedControls = await commonService.updateData(piaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.deleteCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaCustomerControls } = db.models;

        const deletedControls = await commonService.deleteQuery(piaCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, dbTrans);
        if (!deletedControls) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_DELETED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getpiaCollaborators = async (req, res) => {
    try {
        const { piaCollaborator, User, CustomerAssessments, piaCategory } = db.models;
        const pia_id = req.params.pia_id;

        const pia = await piaService.getPIAWithAssignee(CustomerAssessments, User, { id: pia_id }, {}, ['status', 'assigned_to', 'approver'], ['firstName', 'lastName']);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pia.assigned_to !== req.data.userId && pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
        }

        const collaborators = await piaService.getpiaCollaborators(piaCategory, piaCollaborator, User, {}, { pia_id: pia_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
        if (!collaborators) {
            return response.error(req, res, { msgCode: "COLLABORATORS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        collaborators?.forEach(collaborator => {
            collaborator.Collaborators = collaborator.piaCollaborators;
            delete collaborator.piaCollaborators;
        });

        const assignee = `${pia.AssignedTo?.firstName} ${pia.AssignedTo?.lastName}`;

        return response.success(req, res, { msgCode: "COLLABORATORS_FETCHED", data: { assignee: assignee, collaborators } }, httpStatus.OK);
    } catch (err) {
        console.log("Error--->>>>",err)
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.addpiaCollaborator = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaCollaborator, AuditLog, CustomerAssessments, Departments, Processes, User, piaCategory } = db.models;

        const checkPIA = await piaService.getPIA(CustomerAssessments, Departments, Processes, User, { id: req.body.pia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.assigned_to !== req.data.userId && checkPIA.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        const userList = req.body.collaborators?.flatMap(collaborator => collaborator.users.map(user => user.id));

        const users = await commonService.getListWithoutCount(User, {
            id: { [Op.in]: userList }
        }, ['id', 'firstName', 'lastName', 'email']);
        if (!users) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const categories = await commonService.getListWithoutCount(piaCategory, {
            id: { [Op.in]: req.body.collaborators.map(collaborator => collaborator.category_id) }
        }, ['id', 'name']);
        if (!categories) {
            return response.error(req, res, { msgCode: "CATEGORY_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!invitee) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let collaboratorData = [];
        const auditData = [];
        let piaName = null;
        let dept_id = null;
        if (checkPIA.Department) {
            piaName = checkPIA.Department.name;
            dept_id = checkPIA.Department.id;
        } else if (checkPIA.Process) {
            piaName = checkPIA.Process.name;
            dept_id = checkPIA.Process.Department.id;
        }

        // const names = [];
        for (let collaborator of req.body.collaborators) {
            const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

            for (let collaboratingUser of collaborator.users) {
                const user = users?.find(user => user.id === collaboratingUser.id);
                let userName = `${user?.firstName} ${user?.lastName}`;
                // names.push(userName);

                if (collaboratingUser.action === 'add') {
                    collaboratorData.push({
                        pia_id: req.body.pia_id,
                        user_id: collaboratingUser.id,
                        category_id: collaborator.category_id
                    });

                    auditData.push({
                        type: 'PIA',
                        type_id: req.body.pia_id,
                        action: `Added ${userName} as a collaborator for ${piaName} PIA under ${categoryName} category`,
                        action_by_id: req.data.userId,
                        dept_id: dept_id,
                        customer_id: req.data.customer_id
                    });

                    const currentDate = moment().tz('Asia/Kolkata');
                    const completionDate = moment(checkPIA?.tentative_date);
                    const daysUntilCompletion = completionDate.diff(currentDate, 'days');

                    //send mail
                    const subject = `Collaboration Request: Assistance Needed with PIA in ${categoryName}`;
                    const textTemplate = "pia_collaborator.ejs";
                    const baseUrl = req.protocol + '://' + req.get('host');
                    const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "https://dev.gotrust.tech";
                    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";
                    
                    const sendData = {
                        collaboratorName: userName,
                        inviteeName: `${invitee.firstName} ${invitee.lastName}`,
                        piaName: piaName,
                        categoryName: categoryName,
                        url: `${frontEndUrl}/assessment-management/task-overview/`,
                        logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                        email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                        daysLeft: daysUntilCompletion
                    };

                    sendMail(
                        user.email,
                        sendData,
                        subject,
                        textTemplate,
                    );
                } else if (collaboratingUser.action === 'remove') {
                    const oldpiaCollaborator = await commonService.deleteQuery(piaCollaborator, { pia_id: req.body.pia_id, user_id: collaboratingUser.id, category_id: collaborator.category_id }, dbTrans, true);
                    if (!oldpiaCollaborator) {
                        return response.error(req, res, { msgCode: "ERROR_DELETING_COLLABORATOR" }, httpStatus.BAD_REQUEST, dbTrans);
                    }

                    auditData.push({
                        type: 'PIA',
                        type_id: req.body.pia_id,
                        action: `Removed ${userName} as a collaborator for ${piaName} PIA under ${categoryName} category`,
                        action_by_id: req.data.userId,
                        dept_id: dept_id,
                        customer_id: req.data.customer_id
                    });
                }
            }
        }

        const newpiaCollaborators = await commonService.bulkAdd(piaCollaborator, collaboratorData, dbTrans);
        if (!newpiaCollaborators) {
            return response.error(req, res, { msgCode: "ERROR_ADDING_COLLABORATOR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "COLLABORATOR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.createOrUpdateAnswers = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaAnswers, CustomerAssessments } = db.models;
        const answers = req.body.answers;
        const pia_id = req.body.pia_id;

        const checkPIA = await commonService.findByCondition(CustomerAssessments, { id: pia_id }, ['status']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PIA_NOT_STARTED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Separate answers into two arrays based on the type
        const addAnswers = answers?.filter(answer => answer.type === 'add');
        const updateAnswers = answers?.filter(answer => answer.type === 'update');

        // Add 'answered_by' field to all answers
        addAnswers?.forEach(answer => answer.answered_by = req.data.userId);
        updateAnswers?.forEach(answer => answer.answered_by = req.data.userId);
        console.log(addAnswers)
        console.log(updateAnswers)
        // Bulk add or update answers
        if (addAnswers.length > 0) {
            const addNewAnswers = await commonService.bulkAdd(piaAnswers, addAnswers, dbTrans);
            if (!addNewAnswers) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        if (updateAnswers.length > 0) {
            for (let answer of updateAnswers) {
                const updateAnswers = await commonService.updateData(piaAnswers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
                if (!updateAnswers[1]) {
                    return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        return response.success(req, res, { msgCode: "ANSWER_CREATED_OR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
};

exports.submitPIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, AuditLog, Departments, Processes, User, piaAnswers, piaCustomerControls } = db.models;
        const checkPIA = await piaService.getPIA(CustomerAssessments, Departments, Processes, User, { id: req.params.pia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PIA_NOT_STARTED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const checkAnswerStatus = await commonService.getListAssociateWithoutCount(piaCustomerControls, piaAnswers, { pia_id: req.params.pia_id }, {}, [[sequelize.literal(`"piaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], {});

        const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
        const childrenMap = checkAnswerStatus?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        const unansweredQuestions = parents?.reduce((acc, parent) => {
            if (parent.children.length > 0) {
                parent.children.forEach(child => {
                    if (child.piaAnswer === null) {
                        acc.push({ customer_question_id: child.customer_question_id });
                    }
                });
            } else if (parent.piaAnswer === null) {
                acc.push({ customer_question_id: parent.customer_question_id });
            }
            return acc;
        }, []);

        if (unansweredQuestions.length > 0) {
            return response.error(req, res, { msgCode: "ALL_NOT_ANSWERED", data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const pia = await commonService.updateData(CustomerAssessments, { status: constant.status.UNDER_REVIEW }, { id: req.params.pia_id }, dbTrans);
        if (!pia[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let piaName = null;
        let dept_id = null;

        if (checkPIA.Department) {
            piaName = checkPIA.Department.name;
            dept_id = checkPIA.Department.id;
        } else if (checkPIA.Process) {
            piaName = checkPIA.Process.name;
            dept_id = checkPIA.Process.Department.id;
        }


        const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!submitter) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const submitterName = `${submitter.firstName} ${submitter.lastName}`;
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";

        const subject = `PIA ${piaName}Ready for Review`;
        const textTemplate = "pia_submit.ejs";
        const sendData = {
            assignee: submitterName,
            piaName: piaName,
            reviewer: `${checkPIA.Approver?.firstName} ${checkPIA.Approver?.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/pia/`,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
        };

        sendMail(
            checkPIA.Approver.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Submitted ${piaName} PIA for review`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PIA', type_id: req.params.pia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PIA_SUBMITTED", data: pia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.reviewPIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { ReviewPIA } = db.models;
        const reviews = req.body.reviews;

        // Separate answers into two arrays based on the type
        const addReviews = reviews?.filter(answer => answer.type === 'add');
        const updateReviews = reviews?.filter(answer => answer.type === 'update');

        // Add 'areviewer_id' field to all answers
        addReviews?.forEach(review => review.reviewer_id = req.data.userId);
        updateReviews?.forEach(review => review.reviewer_id = req.data.userId);

        // Bulk add or update reviews
        if (addReviews.length > 0) {
            const addNewReviews = await commonService.bulkAdd(ReviewPIA, addReviews, dbTrans);
            if (!addNewReviews) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        if (updateReviews.length > 0) {
            for (let review of updateReviews) {
                const updateReviews = await commonService.updateData(ReviewPIA, review, { customer_question_id: review.customer_question_id }, dbTrans);
                if (!updateReviews[1]) {
                    return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
        return response.success(req, res, { msgCode: "REVIEW_CREATED_OR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.submitReview = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, piaCustomerControls, piaAnswers, AuditLog, Departments, Processes, User, ReviewPIA } = db.models;
        const checkPIA = await piaService.getPIA(CustomerAssessments, Departments, Processes, User, { id: req.params.pia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkPIA) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPIA.status !== constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PIA_NOT_SUBMITTED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let piaName = null;
        let dept_id = null;

        if (checkPIA.Department) {
            piaName = checkPIA.Department.name;
            dept_id = checkPIA.Department.id;
        } else if (checkPIA.Process) {
            piaName = checkPIA.Process.name;
            dept_id = checkPIA.Process.Department.id;
        }

        let status = constant.status.COMPLETED;
        let end_date = Date();
        const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(piaCustomerControls, ReviewPIA, 'ReviewPIA', { pia_id: req.params.pia_id }, {}, [[sequelize.literal(`"piaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], ['accurate_information']);

        const unreviewedControls = checkReviewStatus?.filter(review => review.ReviewPIA === null && review.parent_id === null);
        if (unreviewedControls.length > 0) {
            return response.error(req, res, { msgCode: "ALL_NOT_REVIEWED", data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const unapprovedControls = checkReviewStatus?.filter(review => review.ReviewPIA?.accurate_information === 0);
        if (unapprovedControls.length > 0) {
            status = constant.status.CHANGES_REQUESTED;
            end_date = null;
        }

        let riskLevel = null;

        if (status === constant.status.COMPLETED) {
            // risk assessment logic
            riskLevel = 'Low';

            // const question_id = [constant.question.CHILDREN_DATA_COLLECTION, constant.question.CROSS_BORDER_DATA_TRANSFER, constant.question.DATA_SUBJECTS_CATEGORIES, constant.question.PERSONAL_DATA, constant.question.SENSITIVE_DATA, constant.question.THIRD_PARTY_VENDORS];

            // const answers = await commonService.getListAssociate(piaAnswers, piaCustomerControls, {}, { pia_id: req.params.pia_id, question_id: { [Op.in]: question_id } }, {}, {});

            // //create a map with question_id as key and answer as value
            // const answerMap = answers?.reduce((map, answer) => {
            //     map[answer.CustomerControl.question_id] = answer.answer;
            //     return map;
            // }, {});

            // const riskAttributes = {
            //     PERSONAL_DATA: 0,
            //     SENSITIVE_DATA: 0,
            //     DATA_SUBJECTS_CATEGORIES: 0,
            //     CHILDREN_DATA_COLLECTION: 0,
            //     THIRD_PARTY_VENDORS: 0,
            //     CROSS_BORDER_DATA_TRANSFER: 0
            // }

            // for (let key in answerMap) {
            //     switch (parseInt(key)) {
            //         case constant.question.PERSONAL_DATA:
            //             riskAttributes.PERSONAL_DATA = answerMap[key].length;
            //             break;
            //         case constant.question.SENSITIVE_DATA:
            //             riskAttributes.SENSITIVE_DATA = answerMap[key].length;
            //             break;
            //         case constant.question.DATA_SUBJECTS_CATEGORIES:
            //             riskAttributes.DATA_SUBJECTS_CATEGORIES = answerMap[key].length;
            //             break;
            //         case constant.question.CHILDREN_DATA_COLLECTION:
            //             riskAttributes.CHILDREN_DATA_COLLECTION = answerMap[key];
            //             break;
            //         case constant.question.THIRD_PARTY_VENDORS:
            //             riskAttributes.THIRD_PARTY_VENDORS = answerMap[key].length;
            //             break;
            //         case constant.question.CROSS_BORDER_DATA_TRANSFER:
            //             riskAttributes.CROSS_BORDER_DATA_TRANSFER = answerMap[key].length;
            //             break;
            //     }
            // }

            // if (riskAttributes.PERSONAL_DATA > 7 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 7 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 5) {
            //     riskLevel = 'High';
            // } else if (riskAttributes.PERSONAL_DATA > 3 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 3 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 2) {
            //     riskLevel = 'Medium';
            // }

            // if (riskAttributes.CHILDREN_DATA_COLLECTION[0] === '0') {
            //     riskLevel = 'High';
            // }

            // if (riskAttributes.SENSITIVE_DATA > 1 || riskAttributes.THIRD_PARTY_VENDORS > 1) {
            //     riskLevel = 'High';
            // }

            const subject = `PIA Completed: Review Suggested Risks for Compliance Enhancement`;
            const textTemplate = "pia_review_submit.ejs";
            const sendDataAssignedTo = {
                recipient: `${checkPIA.AssignedTo.firstName} ${checkPIA.AssignedTo.lastName}`,
                piaName: piaName,
                url: `${process.env.SERVER_IP}/privacy/pia/`
            };

            sendMail(
                checkPIA.AssignedTo.email,
                sendDataAssignedTo,
                subject,
                textTemplate,
            );

            const sendDataApprover = {
                assignee: `${checkPIA.Approver.firstName} ${checkPIA.Approver.lastName}`,
                piaName: piaName,
                url: `${process.env.SERVER_IP}/privacy/pia/`
            };

            sendMail(
                checkPIA.AssignedTo.email,
                sendDataApprover,
                subject,
                textTemplate,
            );
        }

        const pia = await commonService.updateData(CustomerAssessments, { status: status, end_date: end_date, risks: riskLevel }, { id: req.params.pia_id }, dbTrans);
        if (!pia[1]) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditAction = `Submitted review for ${piaName} PIA with status '${status}'`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PIA', type_id: req.params.pia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PIA_REVIEWED", data: pia[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getAuditLog = async (req, res) => {
    try {
        const { AuditLog, User, Departments } = db.models;

        const { pia_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        const auditCondition = { type: 'PIA', customer_id: req.data.customer_id };
        const userCondition = {};

        if (search) {
            userCondition[Op.or] = [
                { firstName: { [Op.iLike]: `%${search}%` } },
                { lastName: { [Op.iLike]: `%${search}%` } },
                { email: { [Op.iLike]: `%${search}%` } }
            ];
        }

        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
            if (!deptHead) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }

            const deptIds = deptHead.rows?.map(dept => dept.id);
            auditCondition.dept_id = { [Op.in]: deptIds };
        }

        if (pia_id) {
            auditCondition.type_id = pia_id;
        }

        const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // getting name initials and added to the audit data
        auditData.rows?.map(row => {
            const name = row?.User?.firstName + ' ' + row?.User?.lastName;
            row.name = name;
            const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
            row.initials = initials;
            delete row.User;
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.uploadControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { piaControls, piaCategory, Assessments, CustomerAssessments, piaCustomerControls } = db.models;
        const controls = [];
        let parentId = null;
        const childControlsData = [];
        const uniqueCategories = new Map(); // Map to store unique categories
        const artifactTypeMapping = {
            'radio': 'radio',
            'dropdown': 'select',
            'table': 'table',
            'text box': 'textarea',
            'upload attachment': 'attachment'
        };

        // const checkPia =  await commonService.getList(piaControls, { customer_id: req.data.customer_id }, {} );
        // if(checkPia.rows[0]){
        //     deleteFile(req.files[0].path);
        //     return response.error(req, res, { msgCode: "ALREADY_EXIST_TEMPLATE" }, httpStatus.BAD_REQUEST, dbTrans);
        // }
        const transaction = await db.transaction();
        try{
            const assessmentTypeId = await commonService.getList(Assessments,{key:'pia'});
            const listCustomerAssessment = await commonService.getList(CustomerAssessments,{customer_id:req.data.customer_id, assessment_id:assessmentTypeId.rows[0].id},['id','status']);
            const piaIds =  listCustomerAssessment?.rows?.map(item => item.id);

            const checkPiaControls = await commonService.getList(piaCustomerControls,{ pia_id: { [Op.in]: piaIds }});
            if (checkPiaControls?.rows?.length > 0) {
                await commonService.deleteQuery(piaCustomerControls, { pia_id: { [Op.in]: piaIds } }, transaction ,true);
            }
            const checkControls = await commonService.getList(piaControls, { customer_id: req.data.customer_id });

            if (checkControls?.rows?.length > 0) {
                await commonService.deleteQuery(piaControls, { customer_id: req.data.customer_id }, transaction ,true);
            }

            const checkCategory = await commonService.getList(piaCategory, { customer_id: req.data.customer_id });
            if (checkCategory?.rows?.length > 0) {
                await commonService.deleteQuery(piaCategory, { customer_id: req.data.customer_id }, transaction ,true);
            }

            for (const { id, status } of listCustomerAssessment.rows) {
                await commonService.updateData(CustomerAssessments, {
                    start_date: null,
                    end_date:null,
                    // assigned_to: null,
                    // approver: null,
                    progress: null,
                    tentative_date: null,
                    status: 'Yet to Start'
                }, { id: id }, transaction);
            }
         
            // Commit the transaction
            await transaction.commit();
        }
        catch(error){
            await transaction.rollback();
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: "TRANSACTION_FAILED" }, httpStatus.INTERNAL_SERVER_ERROR);
        }


        const requiredHeaders = ['PIA Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
        const { isValid, missingHeader } = await piaService.validateHeaders(req.files[0].path, requiredHeaders);
        
        if (!isValid) {
            deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: "INVALID_HEADER", data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
        }

        fs.createReadStream(req.files[0].path)
            .pipe(csv())
            .on('data', async (row) => {
                const controlData = {
                    PIALevel: row['PIA Level'],
                    piaCategory: row['Category'],
                    title: row['Title'],
                    description: row['Explanation'],
                    artifact_type: row['Artifact Type'],
                    question: row['Question'],
                    fields: row['Fields'],
                    is_attachment: row['Has Attachment'],
                    extra_input: row['Extra Input Required'],
                    customer_id: req.data.customer_id,
                    extra_input_type: row['Extra Input Type'],
                    extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields'],
                };

                // Check if all properties of controlData are empty
                if (!Object.values(controlData).every(x => (x === ''))) {
                    if (controlData.title === '' && controlData.question === '') {
                        await deleteFile(req.files[0].path);
                        return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                    controls.push(controlData);
                }
            })
            .on('end', async () => {
                // Insert the data into the database
                for (let row of controls) {
                    if (!row['PIALevel'] || !row['piaCategory']) {
                        await deleteFile(req.files[0].path);
                        return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                    const key = `${row['PIALevel']}_${row['piaCategory']}`;
                    if (row['title']) { // Parent question
                        if (!uniqueCategories.has(key)) {
                            // Create or retrieve category and store in the map
                            const [pia_level, name] = key.split('_');
                            let category = await commonService.findByCondition(piaCategory, { pia_level, name, customer_id: req.data.customer_id }, {});
                            if (!category) {
                                category = await piaCategory.create({ name, pia_level, customer_id: req.data.customer_id }, { transaction: dbTrans });
                            }
                            uniqueCategories.set(key, category);
                        }

                        let artifactType = null;
                        if (row['artifact_type'] !== '') {
                            artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase().trim()];
                            if (!artifactType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }

                        let extraInputType = null;
                        if (row['extra_input_type'] !== '') {
                            extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                            if (!extraInputType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }

                        // Create parent control
                        const control = await commonService.addDetail(piaControls, {
                            title: row['title'],
                            description: row['description'],
                            artifact_type: artifactType,
                            customer_id: req.data.customer_id,
                            question: row['question'],
                            fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                            is_attachment: row['is_attachment'] === 'Yes',
                            extra_input: row['extra_input'] === 'Yes',
                            extra_input_type: extraInputType,
                            extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                            category_id: uniqueCategories.get(key).id,
                            parent_id: null,
                            industry_vertical_id: 1
                        }, dbTrans);

                        if (!control) {
                            await deleteFile(req.files[0].path);
                            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
                        }

                        // Update parent ID for potenpial child questions
                        parentId = control.id;
                    } else { // Child question
                        if (parentId) {
                            // Create child control
                            let artifactType = null;
                            if (row['artifact_type'] !== '') {
                                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                                if (!artifactType) {
                                    await deleteFile(req.files[0].path);
                                    return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                                }
                            }

                            let extraInputType = null;
                            if (row['extra_input_type'] !== '') {
                                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                                if (!extraInputType) {
                                    await deleteFile(req.files[0].path);
                                    return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                                }
                            }

                            childControlsData.push({
                                title: null,
                                description: null,
                                artifact_type: artifactType,
                                customer_id: req.data.customer_id,
                                question: row['question'],
                                fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                                is_attachment: row['is_attachment'] === 'Yes',
                                extra_input: row['extra_input'] === 'Yes',
                                extra_input_type: extraInputType,
                                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                                category_id: uniqueCategories.get(key).id,
                                parent_id: parentId,
                                industry_vertical_id: 1
                            });
                        } else {
                            await deleteFile(req.files[0].path);
                            return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                        }
                    }
                }
                // Batch create child controls
                const childControls = await commonService.bulkAdd(piaControls, childControlsData, dbTrans);
                if (!childControls) {
                    await deleteFile(req.files[0].path);
                    return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
                }

                await deleteFile(req.files[0].path);

                return response.success(req, res, { msgCode: "CONTROLS_UPLOADED" }, httpStatus.OK, dbTrans);
            });
    }
    catch (err) {
        console.log('error', err);
        await deleteFile(req.files[0].path);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getPiaData = async (req, res) => {
    try {
        const { piaControls, piaCustomerControls, piaAnswers, User, CustomerAssessments, piaCategory, Customer, Departments, Processes } = db.models;
        const pia_id = req.params.pia_id;

        const pia = await commonService.findByCondition(CustomerAssessments, { id: pia_id }, ['status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks']);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        if (pia.status !== constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PIA_NOT_COMPLETED" }, httpStatus.BAD_REQUEST);
        }

        const controlsAttributes = [
            [
                sequelize.literal(`"piaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."title" ELSE "piaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."description" ELSE "piaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN CAST("piaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("piaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."is_attachment" ELSE "piaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."question" ELSE "piaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."fields" ELSE "piaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."extra_input" ELSE "piaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN CAST("piaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("piaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."extra_input_fields" ELSE "piaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."question_id" IS NOT NULL THEN "piaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        const controls = await assessmentService.getControlsWithCategory(piaCustomerControls, piaControls, piaAnswers, User, piaCategory, { pia_id: pia_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'name'], [['question_id', 'ASC']]);
        for (let control of controls) {
            control.Answer = control.piaAnswer;
            delete control.piaAnswer;
            control.Category = control.piaCategory;
            delete control.piaCategory;
        }
        let parents = controls?.filter(control => control.parent_id === null);

        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });


        const excelData = transformData(parents);

        const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

        const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
        if (!customer) {
            return response.error(req, res, { msgCode: "CUSTOMER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        let deptName = '';
        let procName = '';

        if (pia.department_id) {
            const dept = await commonService.findByCondition(Departments, { id: pia.department_id }, ['name']);
            deptName = dept?.name;
        } else if (pia.process_id) {
            const proc = await commonService.getDataAssociate(Processes, Departments, { id: pia.process_id }, {}, ['name'], ['name']);
            procName = proc?.name;
            deptName = `${proc?.Department?.name} - ${procName}`;
        }
        
        const excelFile = await createAssessmentExcelFile(excelData, 'PIA (Privacy Impact Assessment)', date, customer?.name, deptName, procName);

        await sendMailWithAttach(
            req.data.email,
            { name: `${user?.firstName} ${user?.lastName}` },
            `PIA ${deptName}  Successfully Completed `,
            'pia_download.ejs',
            excelFile
        );

        return response.success(req, res, { msgCode: "PIA_DOWNLOADED", data: "PIA data sent via E-mail" }, httpStatus.OK);

    } catch (err) {
        console.log('assessmentDownloadError', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};