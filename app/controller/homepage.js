const httpStatus = require('http-status');
const commonService = require('../services/common');
const response = require('../response');
const { getPagination } = require("../utils/helper");
const db = require('../models/index').sequelize;

exports.getRegions = async (req, res) => {
  try {
    const { regions } = await db.models;

    const { page, size } = req.query;
    const { limit, offset } = getPagination(page, size);
    // get regions data
    const getRegionsData = await commonService.getList(regions, {}, {}, limit, offset);

    return response.success(req, res, { msgCode: "REGIONS_FETCHED", data: getRegionsData }, httpStatus.OK);
  } catch (error) {
    console.log("getRegions", error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getBlogsCategories = async (req, res) => {
  try {
    const { Blogs } = await db.models;

    // get blog categories
    const getBlogCategories = await commonService.getDistinct(Blogs, {}, ['category'], ['category']);

    return response.success(req, res, { msgCode: "BLOG_CATEGORIES_FETCHED", data: getBlogCategories }, httpStatus.OK);
  } catch (error) {
    console.log("getBlogsCategories", error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
}

exports.getBlogs = async (req, res) => {
  try {
    const { Blogs } = await db.models;

    const { page, size } = req.query;
    const { limit, offset } = getPagination(page, size);
    const category = req.query.category;

    const blogsCondition = category ? { category: category } : {};

    // get blogs data
    const getBlogsData = await commonService.getList(Blogs, blogsCondition, ['id', 'title', 'description', 'image', 'createdAt', 'author_name'], limit, offset);

    return response.success(req, res, { msgCode: "BLOGS_FETCHED", data: getBlogsData }, httpStatus.OK);
  } catch (error) {
    console.log("getBlogs", error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
}

exports.getBlog = async (req, res) => {
  try {
    const { Blogs } = await db.models;

    const blogId = req.params.blogId;

    // get blog data
    const getBlogData = await commonService.findByCondition(Blogs, { id: blogId });

    return response.success(req, res, { msgCode: "BLOG_FETCHED", data: getBlogData }, httpStatus.OK);
  } catch (error) {
    console.log("getBlog", error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
}