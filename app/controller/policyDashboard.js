const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const policyService = require('../services/policy');
// const roleService = require('../services/role');
// const { USER_ROLE } = require('../constant/common');
const { Op } = require('sequelize');
const sequelize = require('sequelize');



exports.countPolicies = async (req, res) => {
    try {
        const { Policy } = db.models;

        // Calculate the date 60 days from now
        const today = new Date();
        const futureDate = new Date(today);
        futureDate.setDate(futureDate.getDate() + 60);

        //calculating the count of Policies expiring in nextr 60 days
        const expiringPolicies = await commonService.getList(Policy, { customer_id: req.data.customer_id, renewal_date: { [Op.between]: [today, futureDate] } }, ['name'])

        // const policyCounts = await Policy.findAll({
        //     attributes: ['status', [db.fn('COUNT', 'id'), 'count']],
        //     group: ['status']
        //   });  

        // counting the policy using GroupBy "status"
        const policyData = await policyService.getListGroupBy(Policy, { customer_id: req.data.customer_id });
        if (!policyData) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // Create an object representing the count of Policies Expiring in next 60 days
        const countObject = { status: 'EXPIRING_POLICIES', count: expiringPolicies?.count.toString() };
        if (policyData.length > 0) {
            policyData.push(countObject);
        }
        return response.success(req, res, { msgCode: "POLICY_COUNT_FETCHED", data: policyData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.countPoliciesByAuthor = async (req, res) => {
    try {
        const { Policy, User } = db.models;

        // const policyCounts = await Policy.findAll({
        //     attributes: ['author', [db.fn('COUNT', 'id'), 'count']],
        //     group: ['author']
        //   });

        //counting the policy using GroupBy "author"
        const policyData = await policyService.getListGroupBy(Policy, { customer_id: req.data.customer_id }, ['author_id', [sequelize.fn('COUNT', 'id'), 'count']], ['author_id']);
        if (!policyData) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        // Extract author emails into an array
        const ids = policyData?.map(policy => policy.author_id);

        // Fetch author details from User model
        const authorDetails = await commonService.getListWithoutCount(User, { id: { [Op.in]: ids } }, ['firstName', 'lastName', 'id']);

        // Update policy data with author details
        const updatedPolicyData = policyData?.map(policy => {
            const authorDetail = authorDetails?.find(detail => detail.id === policy.author_id);
            if (authorDetail) {
                return {
                    ...policy,
                    author: authorDetail.firstName + " " + authorDetail.lastName
                };
            } else {
                return { ...policy }; // If no match found, keep the email as it is
            }
        });

        return response.success(req, res, { msgCode: "POLICY_COUNT_FETCHED", data: updatedPolicyData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countPoliciesByApprover = async (req, res) => {
    try {
        const { Policy, User } = db.models;

        // const policyCounts = await Policy.findAll({
        //     attributes: ['approver', [db.fn('COUNT', 'id'), 'count']],
        //     group: ['approver']
        //   });

        // counting the policy using GroupBy "approver"
        const policyData = await policyService.getListGroupBy(Policy, { customer_id: req.data.customer_id }, ['approver_id', [sequelize.fn('COUNT', 'id'), 'count']], ['approver_id']);
        if (!policyData) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        // Extract author emails into an array
        const ids = policyData?.map(policy => policy.approver_id);

        // Fetch author details from User model
        const approverDetails = await commonService.getListWithoutCount(User, { id: { [Op.in]: ids } }, ['firstName', 'lastName', 'id']);

        // Update policy data with author details
        const updatedPolicyData = policyData?.map(policy => {
            const approverDetail = approverDetails?.find(detail => detail.id === policy.approver_id);
            if (approverDetail) {
                return {
                    ...policy,
                    approver: approverDetail.firstName + " " + approverDetail.lastName
                };
            } else {
                return { ...policy }; // If no match found, keep the email as it is
            }
        });

        return response.success(req, res, { msgCode: "POLICY_COUNT_FETCHED", data: updatedPolicyData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.countPoliciesByReviewer = async (req, res) => {
    try {
        const { Policy, User } = db.models;

        // const policyCounts = await Policy.findAll({
        //     attributes: ['reviewer', [db.fn('COUNT', 'id'), 'count']],
        //     group: ['reviewer']
        // });

        // counting the policy using GroupBy "reviewer"
        const policyData = await policyService.getListGroupBy(Policy, { customer_id: req.data.customer_id }, ['reviewer_id', [sequelize.fn('COUNT', 'id'), 'count']], ['reviewer_id']);
        if (!policyData) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        console.log(policyData);

        // Initialize an empty map
        const reviewerCountMap = new Map();

        // Loop through the data and populate the map
        policyData?.forEach(item => {
            if (item.reviewer_id !== null) {
                item.reviewer_id?.forEach(id => {
                    if (reviewerCountMap.has(id)) {
                        reviewerCountMap.set(id, reviewerCountMap.get(id) + parseInt(item.count));
                    } else {
                        reviewerCountMap.set(id, parseInt(item.count));
                    }
                });
            }
        });
        // Extract author ids into an array
        const ids = policyData?.map(policy => policy.reviewer_id);

        // Flatten the array and remove duplicates
        const flatArray = [...new Set(ids.flat())];

        //console.log(flatArray); // Outputs: [661, 626, 564]
        const reviewerData = await commonService.getList(User, { id: { [Op.in]: flatArray } }, ['id', 'firstName', 'lastName']);

        const reviewerDetailsWithCount = reviewerData.rows?.map(reviewer => {
            const count = reviewerCountMap.get(reviewer.id) || 0;
            return { ...reviewer, count };
        });
        //reviewerDetailsWithCount.nullReviewer =policyData[policyData.length - 1];
        // reviewerDetailsWithCount[reviewerDetailsWithCount.length] = policyData[policyData.length - 1];
        return response.success(req, res, { msgCode: "POLICY_COUNT_FETCHED", data: reviewerDetailsWithCount }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.countPoliciesByDepartments = async (req, res) => {
    try {
        const { Policy, Departments } = db.models;
        const attributes = [
            [sequelize.col('Department.name'), 'departmentName'],
            [sequelize.fn('COUNT', sequelize.col('Policy.id')), 'count']
        ]
        // count the policies
        const policyCounts = await policyService.findAndCount(Policy, Departments, { customer_id: req.data.customer_id }, {}, attributes, ['Department.name']);
        if (!policyCounts) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        return response.success(req, res, { msgCode: "POLICY_COUNT_FETCHED", data: policyCounts }, httpStatus.OK);

    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.countPoliciesByEntity = async (req, res) => {
    try {
        const { Policy, Group } = db.models;
        const attributes = [
            [sequelize.col('Group.name'), 'entityName'],
            [sequelize.fn('COUNT', sequelize.col('Policy.id')), 'count']
        ]
        // count the policies
        const policyCounts = await policyService.findAndCount(Policy, Group, { customer_id: req.data.customer_id }, {}, attributes, ['Group.name']);
        if (!policyCounts) {
            return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        return response.success(req, res, { msgCode: "POLICY_COUNT_FETCHED", data: policyCounts }, httpStatus.OK);

    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};