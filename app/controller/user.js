const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { Op } = require('sequelize');
const { USER_ROLE } = require('../constant/common');
const userService = require('../services/user');
const authService = require('../services/auth');
const { getPagination } = require('../config/helper');
const bcrypt = require('bcryptjs');
const salt = bcrypt.genSaltSync(10);
const crypto = require('crypto');
const { sendMail } = require('../config/email');
const keycloakService = require('../services/keycloak');

exports.getUserList = async (req, res) => {
    try {
        const { User, Customer, Role, Group, GroupUser, Departments, Processes, country } = db.models;
        let { page, size, customer_id, search, sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date, id, type, user_type } = req.query;

        const { limit, offset } = getPagination(page, size);
        let userCondition = { customer_id: customer_id };
        if (search) {
            userCondition[Op.or] = [
                { firstName: { [Op.iLike]: `%${search}%` } },
                { lastName: { [Op.iLike]: `%${search}%` } },
                { email: { [Op.iLike]: `%${search}%` } }
            ];
        }

        if (user_type) {
            userCondition.status = user_type;
        }

        // Add date range condition
        if (start_date && end_date) {
            start_date = new Date(start_date);
            start_date.setHours(0, 0, 0, 0);
            start_date = start_date.toISOString();

            end_date = new Date(end_date);
            end_date.setHours(23, 59, 32, 312);
            end_date = end_date.toISOString();

            userCondition.createdAt = {
                [Op.between]: [start_date, end_date]
            };
        }
        if (!start_date && end_date) {
            end_date = new Date(end_date);
            end_date.setHours(23, 59, 32, 312);
            end_date = end_date.toISOString();

            userCondition.createdAt = {
                [Op.lte]: end_date // Less than end date
            };
        }
        if (start_date && !end_date) {
            start_date = new Date(start_date);
            start_date.setHours(0, 0, 0, 0);
            start_date = start_date.toISOString();

            userCondition.createdAt = {
                [Op.gte]: start_date // Greater than start date
            };
        }
        let userId = [], parentGroupData, groupData;
        if (id) {
            let parentData;
            if (type == 'Group') {
                const userData = await commonService.getListWithoutCount1(GroupUser, { group_id: id }, ['user_id']);
                userId = userData?.map(items => { return items.user_id });
                userCondition.id = { [Op.in]: userId };

                // get parent Group Data
                groupData = await commonService.findByCondition(Group, { id: id }, ['id', 'name', 'parent_id', 'customer_id']);
                if (!groupData) {
                    return response.error(req, res, { msgCode: 'BUSINESS_UNIT_NOT_FOUND' }, httpStatus.BAD_REQUEST);
                }
                parentGroupData = await commonService.findByCondition(Group, { id: groupData.parent_id }, ['id', 'name', 'parent_id', 'customer_id']);
            }
            else if (type == 'Department') {
                const departmentData = await commonService.findByCondition(Departments, { id });
                if (!departmentData) {
                    return response.error(req, res, { msgCode: 'DEPARTMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST);
                }
                if (departmentData.parent_id != null) {
                    parentData = await commonService.findByCondition(Departments, { parent_id: departmentData.parent_id });
                } else {
                    parentData = await commonService.findByCondition(Group, { id: departmentData.group_id });
                }
                groupData = departmentData;
                parentGroupData = parentData;
            }
            else if (type == 'Processes') {
                const ProcessData = await commonService.findByCondition(Processes, { id });
                if (!ProcessData) {
                    return response.error(req, res, { msgCode: 'PROCESS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
                }
                if (ProcessData.parent_id != null) {
                    parentData = await commonService.findByCondition(Processes, { parent_id: ProcessData.parent_id });
                } else {
                    parentData = await commonService.findByCondition(Departments, { id: ProcessData.department_id });
                }
                groupData = ProcessData;
                parentGroupData = parentData;
            }
        }

        const order = [[sort_by, sort_order]];

        // get customer List
        let userList = await userService.getUserListWithCount(User, Customer, Role, GroupUser, Group, userCondition, {}, { role_name: { [Op.not]: USER_ROLE[7] } }, {}, {}, ['id', 'firstName', 'lastName', 'role_id', 'email', 'country_code', 'phone', 'customer_id', 'status', 'address', 'group_access', 'createdAt', 'updatedAt'], ['id', 'name', 'email', 'address'], ['id', 'role_name', 'status', 'customer_id'], ['id', 'group_id', 'user_id'], ['id', 'name', 'parent_id', 'customer_id', 'user_id', 'status'], limit, offset, order);
        const countryData = await commonService.getListWithoutCount(country, {}, ['id','country_name', 'country_code']);
        const countryCodeToName = {};
        countryData?.forEach(country => {
            if(country.country_code.charAt(0) !== '+')
            {
                countryCodeToName[`+${country.country_code}`] = {
                    id: country.id,
                    country_code: `+${country.country_code}`,
                    country_name: country.country_name,
                };
            }
            else{
                countryCodeToName[`+${country.country_code}`] = {
                    id: country.id,
                    country_code: country.country_code,
                    country_name: country.country_name,
                };
            }
        });
        userList.rows = userList.rows?.map((obj) => {
            obj.country = countryCodeToName[obj.country_code];
            return obj;
        });

        if (id && type) {
            userList.parent_group = parentGroupData;
            userList.group_data = groupData;
        }
        userList.rows = userList.rows?.map(user => {
            // Map over GroupUsers and extract the Group data
            const group_data = user.GroupUsers?.map(groupUser => groupUser.Group);

            // Remove the GroupUsers key
            delete user.GroupUsers;

            // Add the group_data key
            user.group_data = group_data;

            return user;
        });

        return response.success(req, res, { msgCode: "USER_FETCHED", data: userList }, httpStatus.OK);
    } catch (error) {
        console.log("getUserList", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateUser = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { User, GroupUser } = db.models;
        const group_access = req.body.group_access;
        delete req.body.group_access;

        // check User
        const checkUser = await commonService.findByCondition(User, { id: req.params.id });
        if (!checkUser) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // update User
        const updateUser = await commonService.updateData(User, req.body, { id: req.params.id }, dbTrans);
        if (!updateUser[1]) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (group_access) {
            // delete Group User
            await commonService.deleteQuery(GroupUser, { user_id: req.params.id }, dbTrans, true);

            const group_user_array = group_access?.map(group => {
                return { group_id: group, user_id: req.params.id }
            })

            // create User Group
            const createUserGroup = await authService.BulkData(GroupUser, group_user_array, dbTrans);
            if (!createUserGroup) {
                return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }

        return response.success(req, res, { msgCode: "USER_UPDATED", data: updateUser[1] }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.log("updateUser", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.createUser = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { User, GroupUser } = db.models;
        const group_access = req.body.group_access;

        delete req.body.group_access;
        // check email
        const checkEmail = await commonService.findByCondition(User, { email: req.body.email }, ['id', 'email']);
        if (checkEmail) {
            return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const password = crypto.randomBytes(8).toString('hex');

        // Encrypt the password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Add the hashed password to the request body
        req.body.password = hashedPassword;

        // create User
        const createUser = await commonService.addDetail(User, req.body, dbTrans);
        if (!createUser) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const group_user_array = group_access?.map(group => {
            return { group_id: group, user_id: createUser.id }
        })

        // create User Group
        const createUserGroup = await authService.BulkData(GroupUser, group_user_array, dbTrans);
        if (!createUserGroup) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const token = await keycloakService.getToken();
        if (!token) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const keycloakUser = {
            username: req.body.email,
            email: req.body.email,
            enabled: true,
            emailVerified: true,
            firstName: req.body.firstName,
            lastName: req.body.lastName,
            attributes: {
                firstLogin: ['true']
            },
            credentials: [{ type: 'password', value: password, temporary: true }]
        };

        const keycloakUserResponse = await keycloakService.createUser(token.access_token, keycloakUser);
        if (keycloakUserResponse.statusText === 'Conflict') {
            return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXIST_IN_KEYCLOAK' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        if (!keycloakUserResponse) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const subject = `Welcome to GoTrust`;
        const textTemplate = "new-user-password-email.ejs";
        const baseUrl = req.protocol + '://' + req.get('host');
        const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "https://dev.gotrust.tech";
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";

        const sendData = {
            name: `${req.body.firstName} ${req.body.lastName}`,
            email: req.body.email,
            password: password,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            login_url: `${frontEndUrl}`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
        };

        sendMail(
            req.body.email,
            sendData,
            subject,
            textTemplate,
        );

        return response.success(req, res, { msgCode: "SIGNUP_SUCCESSFUL", data: createUser }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.log("createUser", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.getUserDetail = async (req, res) => {
    try {
        const { User, Customer, Role, Group, GroupUser, Departments } = db.models;
        // get User Details
        const userList = await userService.getUserDetails(User, Customer, Role, Departments, { id: req.params.id }, {}, { role_name: { [Op.not]: USER_ROLE[7] } }, {}, ['id', 'firstName', 'lastName', 'role_id', 'email', 'country_code', 'phone', 'customer_id', 'status', 'address', 'group_access', 'createdAt', 'updatedAt'], ['id', 'name', 'email', 'address', 'city', 'state'], {}, ['id', 'name']);

        if (!userList) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        const group_data = await commonService.getListAssociateWithoutCount(GroupUser, Group, { user_id: req.params.id });
        userList.group_data = group_data.map(group => group.Group);

        return response.success(req, res, { msgCode: "USER_FETCHED", data: userList }, httpStatus.OK);
    } catch (error) {
        console.log("getUserDetail", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

//       ============Updating Profile APIs====================

exports.profile = async (req, res) => {
    try {
        const { User, Customer } = db.models;
        // Fetch the user from the database
        const user = await commonService.findByCondition(User, { id: req.params.id }, ['id', 'profile_image', 'firstName', 'lastName', 'email', 'country_code', 'phone', 'customer_id']);
        if (!user) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        const org = await commonService.findByCondition(Customer, { id: user?.customer_id }, ['name', 'address', 'city', 'state', 'country']);
        if (!org) {
            return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        user.oragnisation = org;
        return response.success(req, res, { msgCode: "USER_FETCHED", data: user }, httpStatus.OK);
    } catch (error) {
        console.error('Error fetching user profile:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateProfile = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { User } = db.models;

        // Fetch the user from the database
        let user = await commonService.findByCondition(User, { id: req.params.id });

        if (!user) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        const updateUser = await commonService.updateData(User, req.body, { id: req.params.id }, dbTrans);
        if (!updateUser[1]) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "USER_UPDATED", data: updateUser[1] }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.error('Error updating user profile:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.uploadProfile = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { User } = db.models;
        let user = await commonService.findByCondition(User, { id: req.params.id });
        if (!user) {
            return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        req.body.profile_image = req.body.url;
        const originalName = req.body.originalName;
        delete req.body.url;
        delete req.body.originalName;

        const documentUpload = await commonService.updateData(User, req.body, { id: req.params.id }, dbTrans);
        if (!documentUpload) {
            return response.error(req, res, { msgCode: 'UPLOAD_FAILED' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        documentUpload.originalName = originalName;

        return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: documentUpload }, httpStatus.OK, dbTrans);
    } catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};