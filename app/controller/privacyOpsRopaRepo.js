const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const ropaService = require('../services/ropa');
const constant = require('../constant/ROPA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');
const { question } = require('../constant/ROPA');
const csv = require('csv-parser');
const fs = require('fs');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createExcelFile, transformBasicInfo, exportRopa } = require('../utils/helper');
const { IAM } = require('aws-sdk');
const { createClient } = require('@clickhouse/client');
const user = require('../config/user');


exports.countRopa = async (req, res) => {
    try {
        const { ROPA } = db.models;
        const ropaData = await commonService.getListGroupBy(
            ROPA, 
            { customer_id: req.data.customer_id }, 
            ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
            ['status']
        );
        if (!ropaData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        let totalCount = 0;
        let yetToStartCount = 0;
        let inProgressCount = 0;
        let completedCount = 0;
        ropaData.forEach(row => {
            const count = parseInt(row.count || 0);
            totalCount += count;

            switch(row.status) {
                case 'Yet to Start':
                    yetToStartCount = count;
                    break;
                case 'Started':
                case 'Under Review':
                case 'Changes Request':
                    inProgressCount += count;
                    break;
                case 'Completed':
                    completedCount = count;
                    break;
            }
        });
        const data = [
            { 
                ropa: 'TOTAL', 
                count: String(totalCount)
            },
            { 
                ropa: 'YET_TO_START', 
                count: String(yetToStartCount)
            },
            { 
                ropa: 'IN_PROGRESS', 
                count: String(inProgressCount)
            },
            { 
                ropa: 'COMPLETED', 
                count: String(completedCount)
            }
        ];
        // let count = 0;
        // ropaData?.forEach(row => {
        //     count = count + parseInt(row.count);
        // });
        // let data = [];
        // if (ropaData[0]?.is_already_performed === true) {
        //     data.push({ ropa: 'UPLOADED', count: String(ropaData[0]?.count ?? 0 ) });
        //     data.push({ ropa: 'CREATED', count: String(ropaData[1]?.count ?? 0 ) });
        // } else {
        //     data.push({ ropa: 'UPLOADED', count: String(ropaData[1]?.count ?? 0 ) });
        //     data.push({ ropa: 'CREATED', count: String(ropaData[0]?.count ?? 0 ) });
        // }

        // data.push({ ropa: 'TOTAL', count: String(count) ?? 0 });


        return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);
    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.ROPAList = async (req, res) => {
    try {
        const { Departments, Processes, ROPA, User, Collaborator, Group, RopaDocuments } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let userType = null;
        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};

        let searchCondition = {};

        if (search && !search_key) {
            searchCondition = {
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                ]
            };
        };

        if (search && search_key) {
            if (search_key === 'Department') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Process') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'AssignedTo') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Reviewer') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'SPOC') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Status') {
                searchCondition = {
                    status: sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'Risks') {
                searchCondition = {
                    risks: sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            }
        }

        if (search_key && search_key === 'Department') {
            searchCondition = {
                ...searchCondition,
                department_id: { [Op.ne]: null }
            };
        } else if (search_key && search_key === 'Process') {
            searchCondition = {
                ...searchCondition,
                process_id: { [Op.ne]: null }
            };
        }

        // Uncomment below lines to get all the ROPAs of the group and its child groups

        // // To get the group hierarchy (parent group with all its child groups)
        // const groups = await commonService.getGroupHierarchy(db, req.params.entity_id);
        // if (!groups) {
        //     return response.error(req, res, { msgCode: "GROUP_NOT_FOUND" }, httpStatus.NOT_FOUND);
        // }
        // const groupIds = groups.map(group => group.id);

        const groupIds = [req.params.entity_id];

        const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
        const departmentIds = departments?.rows?.map(department => department.id);
        const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
        const processIds = processes?.rows?.map(process => process.id);

        if (req.data.roleName === authConstant.USER_ROLE[2]) {
            userType = 'DPO';
        } else if (departmentIds.length > 0) {
            userType = 'Department Head';
        } else {
            userType = 'Employee';
        }

        // const collaboratorRopa = await commonService.getList(Collaborator, { user_id: req.data.userId }, ['ropa_id']);
        // const collaboratorRopaIds = collaboratorRopa?.rows?.map(collaborator => collaborator.ropa_id);

        // let ropaFilter = {}

        // if (userType === 'DPO') {
        //     ropaFilter = {};
        // } else if (userType === 'Department Head') {
        //     ropaFilter = {
        //         [Op.or]: [
        //             { assigned_to: req.data.userId },
        //             { id: { [Op.in]: collaboratorRopaIds } },
        //             { department_id: { [Op.in]: departmentIds } },
        //             { process_id: { [Op.in]: processIds } }
        //         ]
        //     }
        // } else {
        //     ropaFilter = {
        //         [Op.or]: [
        //             { assigned_to: req.data.userId },
        //             { id: { [Op.in]: collaboratorRopaIds } }
        //         ]
        //     }
        // }
        const ropaConditon = {[Op.and]: [
            {
                
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                    sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                ]
            },
            customerFilter,
            searchCondition
        ]}
        const ropa = await ropaService.getEmployeeROPAWithDocs(ROPA, Departments, Processes, User, RopaDocuments, ropaConditon, { ...customerFilter }, customerFilter, userFilter, {}, ['id', 'start_date', 'end_date', 'status', 'risks', 'progress', 'is_already_performed','createdAt'], ['id', 'name', 'group_id'], ['id', 'name'], ['id', 'firstName', 'lastName'], {}, limit, offset, order);
        
        if (!ropa) {
            return response.error(req, res, { msgCode: "ROPA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }

        const ropaData = {
            count: ropa.count,
            user_type: userType,
            rows: ropa?.rows?.map(item => {
                const hasProcess = item.Process;
                const hasDepartment = item.Department;
        
                // Correct ropa_name assignment
                item.ropa_name = hasDepartment && hasProcess
                ? `${item.Department.name} - ${item.Process.name}`
                : hasDepartment
                    ? item.Department.name
                    : hasProcess
                        ? item.Process.name
                        : null;
        
                // Assign Department from Process if available
                if (hasProcess) {
                    item.Department = item.Process.Department;
                    delete item.Process.Department;
                    delete item.Process.User;
                }
        
                // Format AssignedTo details
                if (item.AssignedTo) {
                    item.isAssigned = item.AssignedTo.id === req.data.userId;
                    item.AssignedTo.name = `${item.AssignedTo.firstName} ${item.AssignedTo.lastName}`;
                    delete item.AssignedTo.firstName;
                    delete item.AssignedTo.lastName;
                }
        
                // Format Approver details
                if (item.Approver) {
                    item.Approver.name = `${item.Approver.firstName} ${item.Approver.lastName}`;
                    delete item.Approver.firstName;
                    delete item.Approver.lastName;
                }

               

                item.SPOC = { id: item.Department?.User?.id, name: `${item.Department?.User?.firstName} ${item.Department?.User?.lastName}`, intials:item.Department?.User?.firstName.charAt(0).toUpperCase()+item.Department?.User?.lastName.charAt(0).toUpperCase() },
                    // ropaItem.isCollaborator = collaboratorRopaIds.includes(ropaItem.id);
                delete item.Department.User;

                item.group_id = item.Department.group_id;
                delete item.Department.group_id;

               
        
                return item;
            })
        };
        
        // const ropaStr = {
        //     user_type: userType,
        //     rows: ropa.rows?.map(ropaItem => {
        //         if (ropaItem.Process) {
        //             ropaItem.Department = ropaItem.Process.Department;
        //             delete ropaItem.Process.Department;
        //             delete ropaItem.Process.User;
        //         }

        //         if (ropaItem.AssignedTo) {
        //             if (ropaItem.AssignedTo.id === req.data.userId) {
        //                 ropaItem.isAssigned = true;
        //             } else {
        //                 ropaItem.isAssigned = false;
        //             }
        //             ropaItem.AssignedTo.name = `${ropaItem.AssignedTo.firstName} ${ropaItem.AssignedTo.lastName}`;
        //             delete ropaItem.AssignedTo.firstName;
        //             delete ropaItem.AssignedTo.lastName;
        //         }

        //         if (ropaItem.Approver) {
        //             ropaItem.Approver.name = `${ropaItem.Approver.firstName} ${ropaItem.Approver.lastName}`;
        //             delete ropaItem.Approver.firstName;
        //             delete ropaItem.Approver.lastName;
        //         }

        //         ropaItem.SPOC = { id: ropaItem.Department.User.id, name: `${ropaItem.Department.User.firstName} ${ropaItem.Department.User.lastName}` },
        //             // ropaItem.isCollaborator = collaboratorRopaIds.includes(ropaItem.id);
        //         delete ropaItem.Department.User;

        //         ropaItem.group_id = ropaItem.Department.group_id;
        //         delete ropaItem.Department.group_id;

        //         return ropaItem;
        //     }),
        //     count: ropa.count
        // };

        return response.success(req, res, { msgCode: "ROPA_FETCHED", data: ropaData}, httpStatus.OK);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.viewRopa = async (req, res) => {
    try {
        const { Controls, CustomerControls, Answers, User, ROPA, Collaborator, ReviewROPA } = db.models;
        const ropa_id = req.params.ropa_id;
        const category_id = req.query.category_id;

        const ropa = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status', 'assigned_to', 'approver']);
        if (!ropa) {
            return response.error(req, res, { msgCode: "ROPA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (ropa.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "ROPA_NOT_STARTED" }, httpStatus.BAD_REQUEST);
        }

        // if (ropa.assigned_to !== req.data.userId && ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        //     const collaborator = await commonService.findByCondition(Collaborator, { ropa_id: ropa_id, user_id: req.data.userId, category_id: category_id }, ['id']);
        //     if (!collaborator) {
        //         return response.error(req, res, { msgCode: "ROPA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
        //     }
        // }

        let controls;
        const controlsAttributes = [
            [
                sequelize.literal(`"CustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."title" ELSE "Control"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."description" ELSE "Control"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."artifact_type" AS TEXT) ELSE CAST("Control"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."is_attachment" ELSE "Control"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."question" ELSE "Control"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."fields" ELSE "Control"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input" ELSE "Control"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."extra_input_type" AS TEXT) ELSE CAST("Control"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input_fields" ELSE "Control"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "CustomerControls"."question_id" IS NOT NULL THEN "Control"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        if (ropa.status === constant.status.COMPLETED) {
            // if (ropa.status === constant.status.UNDER_REVIEW && (ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2])) {
            //     return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
            // }
            controls = await ropaService.getControlsWithReview(CustomerControls, Controls, Answers, User, ReviewROPA, { ropa_id: ropa_id, category_id: category_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'accurate_information', 'comments'], [['question_id', 'ASC']]);
        }
        else {
            // controls = await ropaService.getControls(CustomerControls, Controls, Answers, User, { ropa_id: ropa_id, category_id: category_id }, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], [['question_id', 'ASC']]);
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        for (let control of controls) {
            if (control.Answer) {
                control.answered = true;
                if (control.Answer.extra_answer) {
                    control.Answer.extra_answered = true;
                } else {
                    control.Answer.extra_answered = false;
                }
            } else {
                control.answered = false;
            }

            if (control.ReviewROPA) {
                control.reviewed = true;
            } else {
                control.reviewed = false;
            }

            if (ropa.assigned_to !== req.data.userId && ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                control.is_collaborator = true;
            } else {
                control.is_collaborator = false;
            }
        }

        let parents = controls?.filter(control => control.parent_id === null);
        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        if (ropa.status === constant.status.CHANGES_REQUESTED) {
            parents = parents.filter(parent => parent.ReviewROPA?.accurate_information === 0);
        }

        return response.success(req, res, { msgCode: "CONTROLS_FETCHED", data: { status: ropa.status, controls: parents } }, httpStatus.OK);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

