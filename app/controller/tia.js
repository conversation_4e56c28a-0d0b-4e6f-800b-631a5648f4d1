const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const tiaService = require('../services/tia');
const assessmentService = require('../services/assessment');
const constant = require('../constant/TIA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createAssessmentExcelFile } = require('../utils/helper');
const moment = require('moment');



exports.getTIAList = async (req, res) => {
    try {
        const { Departments, Processes, TIA, User, tiaCollaborator } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let userType = null;
        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};

        let searchCondition = {};

        if (search && !search_key) {
            searchCondition = {
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('TIA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('TIA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                ]
            };
        };

        if (search && search_key) {
            if (search_key === 'Department') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Process') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'AssignedTo') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Reviewer') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'SPOC') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Status') {
                searchCondition = {
                    status: sequelize.where(sequelize.cast(sequelize.col('TIA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'Risks') {
                searchCondition = {
                    risks: sequelize.where(sequelize.cast(sequelize.col('TIA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            }
        }

        const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
        const departmentIds = departments?.rows?.map(department => department.id);
        const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
        const processIds = processes?.rows?.map(process => process.id);

        if (req.data.roleName === authConstant.USER_ROLE[2]) {
            userType = 'DPO';
        } else if (departmentIds.length > 0) {
            userType = 'Department Head';
        } else {
            userType = 'Employee';
        }

        const collaboratorTia = await commonService.getList(tiaCollaborator, { user_id: req.data.userId }, ['tia_id']);
        const collaboratorTiaIds = collaboratorTia?.rows?.map(collaborator => collaborator.tia_id);

        let tiaFilter = {}

        if (userType === 'DPO') {
            tiaFilter = {};
        } else if (userType === 'Department Head') {
            tiaFilter = {
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { id: { [Op.in]: collaboratorTiaIds } },
                    { department_id: { [Op.in]: departmentIds } },
                    { process_id: { [Op.in]: processIds } }
                ]
            }
        } else {
            tiaFilter = {
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { id: { [Op.in]: collaboratorTiaIds } }
                ]
            }
        }

        const tia = await tiaService.getEmployeeTIA(TIA, Departments, Processes, User, {
            [Op.and]: [
                tiaFilter,
                {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                        sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                    ]
                },
                is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
                searchCondition
            ]
        }, { ...customerFilter, group_id: req.params.entity_id }, customerFilter, userFilter, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }

        const tiaStr = {
            user_type: userType,
            rows: tia.rows?.map(tiaItem => {
                if (tiaItem.Process) {
                    tiaItem.Department = tiaItem.Process.Department;
                    delete tiaItem.Process.Department;
                    delete tiaItem.Process.User;
                }

                if (tiaItem.AssignedTo) {
                    if (tiaItem.AssignedTo.id === req.data.userId) {
                        tiaItem.isAssigned = true;
                    } else {
                        tiaItem.isAssigned = false;
                    }
                    tiaItem.AssignedTo.name = `${tiaItem.AssignedTo.firstName} ${tiaItem.AssignedTo.lastName}`;
                    delete tiaItem.AssignedTo.firstName;
                    delete tiaItem.AssignedTo.lastName;
                }

                if (tiaItem.Approver) {
                    tiaItem.Approver.name = `${tiaItem.Approver.firstName} ${tiaItem.Approver.lastName}`;
                    delete tiaItem.Approver.firstName;
                    delete tiaItem.Approver.lastName;
                }

                tiaItem.SPOC = { id: tiaItem.Department.User.id, name: `${tiaItem.Department.User.firstName} ${tiaItem.Department.User.lastName}` },
                    tiaItem.isCollaborator = collaboratorTiaIds.includes(tiaItem.id);
                delete tiaItem.Department.User;

                return tiaItem;
            }),
            count: tia.count
        };

        return response.success(req, res, { msgCode: "TIA_FETCHED", data: tiaStr }, httpStatus.OK);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.getDepartmentsTIA = async (req, res) => {
    try {
        const { Departments, Processes, CustomerAssessments, User, tiaCollaborator } = db.models;
        const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let tiaCondition = { [Op.not]: { department_id: null } };
        let empCheck = false;
        let roleBasedData = [];
        let filter = { customer_id: req.data.customer_id };

        if (search) {
            filter[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
            ];
        }

        // DPO can see all data, so no changes to tiaCondition
        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            // Fetch department details
            const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);

            if (departments && departments.rows.length > 0) {
                // Department head can only see their own departments
                const departmentIds = departments?.rows?.map(department => department.id);
                tiaCondition.department_id = { [Op.in]: departmentIds };
            } else {
                // Employee can only see their own TIA
                empCheck = true;
            }
        }

        if (empCheck) {
            const collaboratorTia = await commonService.getList(tiaCollaborator, { user_id: req.data.userId }, ['tia_id']);
            const collaboratorTiaIds = collaboratorTia?.rows?.map(collaborator => collaborator.tia_id);
            const tia = await tiaService.getEmployeeTIA(TIA, Departments, Processes, User, {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { assigned_to: req.data.userId },
                            { id: { [Op.in]: collaboratorTiaIds } }
                        ]
                    },
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                        ]
                    }
                ]
            }, { ...filter, group_id: req.params.entity_id }, filter, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (!tia) {
                return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
            }

            roleBasedData = tia;
            roleBasedData.rows = roleBasedData.rows?.map(tiaItem => {
                if (tiaItem.Process) {
                    tiaItem.department_name = tiaItem.Process.Department.name;
                    delete tiaItem.Process.Department;
                } else {
                    tiaItem.department_name = tiaItem.Department.name;
                }

                return {
                    ...tiaItem,
                    isCollaborator: collaboratorTiaIds.includes(tiaItem.id)
                };
            });
        } else {
            const tia = await tiaService.getDepartmentsTIA(CustomerAssessments, Departments, User, tiaCondition, { ...filter, group_id: req.params.entity_id }, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (!tia) {
                return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
            }
            roleBasedData = tia;

            // Add assigned TIAs
            const collaboratorTia = await commonService.getList(tiaCollaborator, { user_id: req.data.userId }, ['tia_id']);
            const collaboratorTiaIds = collaboratorTia?.rows?.map(collaborator => collaborator.tia_id);
            const assignedTia = await tiaService.getEmployeeTIA(TIA, Departments, Processes, User, {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { assigned_to: req.data.userId },
                            { id: { [Op.in]: collaboratorTiaIds } }
                        ]
                    },
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                        ]
                    },
                ], status: { [Op.ne]: constant.status.COMPLETED }
            }, { ...filter, group_id: req.params.entity_id }, filter, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (assignedTia) {
                roleBasedData.assigned = assignedTia.rows?.map(tiaItem => {
                    if (tiaItem.Process) {
                        tiaItem.department_name = tiaItem.Process.Department.name;
                        delete tiaItem.Process.Department;
                    } else {
                        tiaItem.department_name = tiaItem.Department.name;
                    }

                    return {
                        ...tiaItem,
                        isCollaborator: collaboratorTiaIds.includes(tiaItem.id)
                    };
                });
            }
        }
        return response.success(req, res, { msgCode: "TIA_FETCHED", data: roleBasedData }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.getProcessesTIA = async (req, res) => {
    try {
        const { Processes, CustomerAssessments, User } = db.models;
        const department_id = req.params.department_id;
        const tia = await tiaService.getProcessesTIA(CustomerAssessments, Processes, User, { [Op.not]: { process_id: null } }, { customer_id: req.data.customer_id, department_id: department_id }, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'firstName', 'lastName'], [['id', 'ASC']]);
        // const tia = await privacyService.getProcessesTIA(req.data.customer_id, department_id);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }
        return response.success(req, res, { msgCode: "TIA_FETCHED", data: tia }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.assignTIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, Departments, Processes, AuditLog, User } = db.models;
        let tiaName = null;
        // let approverId = null;
        let assignedToName = null;
        let dept_id = null;

        if(req.data.roleName !== authConstant.USER_ROLE[2]){
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }


        const checkTIA = await tiaService.getTIA(CustomerAssessments, Departments, Processes, User, { id: req.body.tia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "TIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "TIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (checkTIA.Department) {
            // approverId = checkTIA.Department.spoc_id;
            tiaName = checkTIA.Department.name;
            dept_id = checkTIA.Department.id;
        } else if (checkTIA.Process) {
            // approverId = checkTIA.Process.Department.spoc_id;
            tiaName = checkTIA.Process.name;
            dept_id = checkTIA.Process.Department.id;
        }

        const user = await commonService.findByCondition(User, {
            id: req.body.user_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        assignedToName = `${user.firstName} ${user.lastName}`;

        const assigner = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName']);
        if (!assigner) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
        const tia = await commonService.updateData(CustomerAssessments, { assigned_to: req.body.user_id }, { id: req.body.tia_id }, dbTrans);
        if (!tia[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        const currentDate = moment().tz('Asia/Kolkata');
        const completionDate = moment(checkTIA?.tentative_date);
        const daysUntilCompletion = completionDate.diff(currentDate, 'days');

        const subject = `You have been assigned ${tiaName} assessment: We Need Your Input!`;
        const textTemplate = "tia_assigned.ejs";
        const baseUrl = req.protocol + '://' + req.get('host');
        const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "https://dev.gotrust.tech";
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";
        
        const sendData = {
            assignee: `${user.firstName} ${user.lastName}`,
            tiaName: tiaName,
            assigner: `${assigner.firstName} ${assigner.lastName}`,
            url: `${frontEndUrl}/assessment-management/task-overview/`,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
            daysLeft: daysUntilCompletion
        };

        await sendMail(
            user.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Assigned ${tiaName} TIA to ${assignedToName}`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'TIA', type_id: req.body.tia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TIA_ASSIGNED", data: checkTIA }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.reviewerTIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, Departments, Processes, AuditLog, User } = db.models;
        let tiaName = null;
        // let approver = null;
        let reviewerName = null;
        let dept_id = null;

        const checkTIA = await tiaService.getTIA(CustomerAssessments, Departments, Processes, User, { id: req.body.tia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "TIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "TIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (checkTIA.Department) {
            // approverId = checkTIA.Department.spoc_id;
            tiaName = checkTIA.Department.name;
            dept_id = checkTIA.Department.id;
        } else if (checkTIA.Process) {
            // approverId = checkTIA.Process.Department.spoc_id;
            tiaName = checkTIA.Process.name;
            dept_id = checkTIA.Process.Department.id;
        }

        const user = await commonService.findByCondition(User, {
            id: req.body.user_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        reviewerName = `${user.firstName} ${user.lastName}`;

        // const reviewer = await commonService.findByCondition(User, {
        //     id: req.data.userId
        // }, ['firstName', 'lastName']);
        // if (!reviewer) {
        //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        // }
        const tia = await commonService.updateData(CustomerAssessments, { approver: req.body.user_id }, { id: req.body.tia_id }, dbTrans);
        if (!tia[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const subject = `You have been assigned as Reviewer for ${tiaName} - TIA : We Need Your Input!`;
        const textTemplate = "tia_reviewer.ejs";
        const sendData = {
            reviewer: `${reviewerName}`,
            tiaName: tiaName,
            // assigner: `${assigner.firstName} ${assigner.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/tia/`,
        };

         await sendMail(
            user.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Added Reviewer to  ${tiaName} TIA to ${reviewerName}`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'TIA', type_id: req.body.tia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TIA_REVIEWER_ADDED", data: tia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.startTIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, tiaControls, tiaCustomerControls, tiaCollaborator, AuditLog, Departments, Processes, User } = db.models;

        const checkTIA = await tiaService.getTIA(CustomerAssessments, Departments, Processes, User, { id: req.params.tia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.status === constant.status.STARTED || checkTIA.status === constant.status.CHANGES_REQUESTED) {
            if (checkTIA.assigned_to && checkTIA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                const collaborator = await commonService.findByCondition(tiaCollaborator, { tia_id: req.params.tia_id, user_id: req.data.userId }, ['id']);
                if (!collaborator) {
                    return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED, dbTrans);
                }
            }
            return response.success(req, res, { msgCode: "TIA_STARTED" }, httpStatus.OK, dbTrans);
        } else if (checkTIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "TIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "TIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let tiaName = null;
        let dept_id = null;
        // let approverId = null;

        if (checkTIA.Department) {
            tiaName = checkTIA.Department.name;
            dept_id = checkTIA.Department.id;
            // approverId = checkTIA.Department.spoc_id;
        } else if (checkTIA.Process) {
            tiaName = checkTIA.Process.name;
            dept_id = checkTIA.Process.Department.id;
            // approverId = checkTIA.Process.Department.spoc_id;
        }

        if (checkTIA.assigned_to && checkTIA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(tiaCollaborator, { tia_id: req.params.tia_id, user_id: req.data.userId }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED, dbTrans);
            }
        }

        const tia = await commonService.updateData(CustomerAssessments, { status: constant.status.STARTED, start_date: Date() }, { id: req.params.tia_id }, dbTrans);
        if (!tia[1]) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let controls = await commonService.getList(tiaControls, { industry_vertical_id: 1, customer_id: req.data.customer_id }, ['id', 'category_id', 'parent_id', 'customer_id']);
        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (controls.count == 0) {
            controls = await commonService.getList(tiaControls, { industry_vertical_id: 1, customer_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
        }

        const parentControls = controls?.rows?.filter(control => control.parent_id === null);
        const childControls = controls?.rows?.filter(control => control.parent_id !== null);

        const customerControlsParents = parentControls?.map(control => {
            return {
                question_id: control.id,
                customer_id: control.customer_id,
                category_id: control.category_id,
                tia_id: Number(req.params.tia_id),
                is_custom: false
            }
        });

        const newCustomerControlsParents = await commonService.bulkAdd(tiaCustomerControls, customerControlsParents, dbTrans);
        if (!newCustomerControlsParents) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
            map[parentControls[index].id] = control.id;
            return map;
        }, {});

        const customerControlsChildren = childControls?.map(control => {
            return {
                question_id: control.id,
                category_id: control.category_id,
                customer_id: control.customer_id,
                tia_id: req.params.tia_id,
                parent_id: parentIdMap[control.parent_id],
                is_custom: false
            }
        });

        const newCustomerControlsChildren = await commonService.bulkAdd(tiaCustomerControls, customerControlsChildren, dbTrans);
        if (!newCustomerControlsChildren) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditAction = `${user?.firstName} ${user?.lastName} started ${tiaName} TIA`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'TIA', type_id: req.params.tia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TIA_STARTED", data: tia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getProgress = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaCustomerControls, tiaAnswers, CustomerAssessments, ReviewTIA, tiaCollaborator } = db.models;
        const tia_id = req.params.tia_id;

        const tia = await commonService.findByCondition(CustomerAssessments, { id: tia_id }, ['status', 'approver', 'assigned_to']);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
        }

        const status = tia.status;
        let controls = null;
        let totalControls = 0;
        let answeredControls = 0;
        let childControls = [];
        let progress = 0;

        if (tia.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && tia.approver !== req.data.userId) {
            const collaborator = await commonService.getList(tiaCollaborator, { tia_id: tia_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED , dbTrans);
            }

            const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

            controls = await commonService.getListAssociateWithoutCount(tiaCustomerControls, tiaAnswers, { tia_id: tia_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.parent_id === null && control.tiaAnswer).length;
            // childControls = controls?.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.tiaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.tiaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });

            progress = (answeredControls / totalControls) * 100;
            progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

            return response.success(req, res, { msgCode: "PROGRESS_FETCHED", data: { totalControls, answeredControls, progress } }, httpStatus.OK , dbTrans);
        }

        if (status === constant.status.STARTED) {
            controls = await commonService.getListAssociateWithoutCount(tiaCustomerControls, tiaAnswers, { tia_id: tia_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.parent_id === null && control.tiaAnswer).length;
            // childControls = controls?.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.tiaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.tiaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.UNDER_REVIEW) {
            controls = await commonService.getListAssociateWithoutCountWithAlias(tiaCustomerControls, ReviewTIA, 'ReviewTIA' , { tia_id: tia_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.ReviewTIA).length;
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                }
                if (control.ReviewTIA) {
                    answeredControls++;
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.CHANGES_REQUESTED) {
            controls = await tiaService.getControlsWithAnswersAndReviews(tiaCustomerControls, tiaAnswers, ReviewTIA , { tia_id: tia_id }, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            // totalControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewTIA && control.ReviewTIA.accurate_information === 0).length;
            // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.tiaAnswer?.updatedAt > control.ReviewTIA?.updatedAt).length;
            // childControls = controls.rows?.filter(control => control.parent_id !== null);
            controls.rows?.forEach(control => {
                if (control.parent_id === null) {
                    if (control.ReviewTIA && control.ReviewTIA.accurate_information === 0) {
                        totalControls++;
                    }
                    if (control.tiaAnswer?.updatedAt > control.ReviewTIA?.updatedAt && control.ReviewTIA.accurate_information === 0) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});
            Object.entries(childControlsByParent)?.forEach(([parentId, childControls]) => {
                const parentControl = controls.rows?.find(control => control.id == parentId);
                if (parentControl && childControls.every(control => control.tiaAnswer.updatedAt > parentControl.ReviewTIA.updatedAt)) {
                    answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewTIA
                }
            });
            progress = (answeredControls / totalControls) * 100;
        }else if (status === constant.status.COMPLETED) {
            controls = await commonService.getListAssociateWithoutCount(tiaCustomerControls, tiaAnswers, { tia_id: tia_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.parent_id === null && control.tiaAnswer).length;
            // childControls = controls?.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.tiaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.tiaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });
            progress = (answeredControls / totalControls) * 100;
            
        }

        progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

        const updateProgress = await commonService.updateData(CustomerAssessments ,{ progress : progress} , {id: tia_id} , dbTrans);
        if (!updateProgress) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans );
        }

        return response.success(req, res, { msgCode: "PROGRESS_FETCHED", data: { totalControls, answeredControls, progress } }, httpStatus.OK ,dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.BAD_REQUEST, dbTrans );
    }
}


exports.getCategories = async (req, res) => {
    try {
        const { tiaCategory, tiaCollaborator, CustomerAssessments, tiaCustomerControls, ReviewTIA } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let tiaLevel = req.params.tia_level;
        const tia_id = req.query.tia_id;
        tiaLevel = tiaLevel.charAt(0).toUpperCase() + tiaLevel.slice(1);

        let categoryCondition = { tia_level: tiaLevel };
        let conditions = [];

        const tia = await commonService.findByCondition(CustomerAssessments, { id: tia_id }, ['status', 'assigned_to', 'approver']);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (tia.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && tia.approver !== req.data.userId) {
            const collaborators = await commonService.getList(tiaCollaborator, { tia_id: tia_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborators) {
                return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
            // categoryCondition.id = { [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) };
            conditions.push({ [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) });
            
        }

        if (tia.status === constant.status.CHANGES_REQUESTED) {
            const changeReqCategories = await commonService.getListAssociateWithAlias(tiaCustomerControls, ReviewTIA, 'ReviewTIA', { tia_id: tia_id }, { accurate_information: 0 }, ['category_id']);
            if (!changeReqCategories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
            // categoryCondition.id = { [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) };
            conditions.push({ [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) });

        }else {
            categoryCondition.customer_id = req.data.customer_id;
        }

        if (conditions.length > 0) {
            categoryCondition.id = {
                [Op.and]: conditions
            };
        }

        let categories = await commonService.getList(tiaCategory, categoryCondition, ['id', 'name'] , limit , offset ,order);
        if (!categories) {
            return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if(categories.count==0){
            categoryCondition.customer_id = null;
            categories = await commonService.getList(tiaCategory, categoryCondition, ['id', 'name'] , limit , offset ,order);
            if (!categories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
        }


        return response.success(req, res, { msgCode: "CATEGORIES_FETCHED", data: categories }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.getControls = async (req, res) => {
    try {
        const { tiaControls, tiaCustomerControls, tiaAnswers, User, CustomerAssessments, tiaCollaborator, ReviewTIA } = db.models;
        const tia_id = req.params.tia_id;
        const category_id = req.query.category_id;

        const tia = await commonService.findByCondition(CustomerAssessments, { id: tia_id }, ['status', 'assigned_to', 'approver']);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        if (tia.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "TIA_NOT_STARTED" }, httpStatus.BAD_REQUEST);
        }

        if (tia.assigned_to !== req.data.userId && tia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(tiaCollaborator, { tia_id: tia_id, user_id: req.data.userId, category_id: category_id }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "TIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
        }

        let controls;
        const controlsAttributes = [
            [
                sequelize.literal(`"tiaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."title" ELSE "tiaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."description" ELSE "tiaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN CAST("tiaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("tiaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."is_attachment" ELSE "tiaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."question" ELSE "tiaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."fields" ELSE "tiaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."extra_input" ELSE "tiaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN CAST("tiaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("tiaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."extra_input_fields" ELSE "tiaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."question_id" IS NOT NULL THEN "tiaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];
    
        if (tia.status === constant.status.UNDER_REVIEW || tia.status === constant.status.CHANGES_REQUESTED || tia.status === constant.status.COMPLETED) {
            if (tia.status === constant.status.UNDER_REVIEW && (tia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2])) {
                return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
            }
            controls = await tiaService.getControlsWithReview(tiaCustomerControls, tiaControls, tiaAnswers, User, ReviewTIA, { tia_id: tia_id, category_id: category_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'accurate_information', 'comments'], [['question_id', 'ASC']]);
        } else {
            controls = await tiaService.getControls(tiaCustomerControls, tiaControls, tiaAnswers, User, { tia_id: tia_id, category_id: category_id }, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], [['question_id', 'ASC']]);
        }

        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        
        for (let control of controls) {
            control.Answer = control.tiaAnswer;
            delete control.tiaAnswer;
            control.Review = control.ReviewTIA;
            delete control.ReviewTIA;
            if (control.Answer) {
                control.answered = true;
                if (control.Answer.extra_answer) {
                    control.Answer.extra_answered = true;
                } else {
                    control.Answer.extra_answered = false;
                }
            } else {
                control.answered = false;
            }

            if (control.Review) {
                control.reviewed = true;
            } else {
                control.reviewed = false;
            }

            if (tia.assigned_to !== req.data.userId && tia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                control.is_collaborator = true;
            } else {
                control.is_collaborator = false;
            }
        }

        let parents = controls?.filter(control => control.parent_id === null);
        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        if (tia.status === constant.status.CHANGES_REQUESTED) {
            parents = parents.filter(parent => parent.Review?.accurate_information === 0);
        }

        return response.success(req, res, { msgCode: "CONTROLS_FETCHED", data: { status: tia.status, controls: parents } }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.getArtifactTypes = async (req, res) => {
    try {
        const { tiaControls } = db.models;
        const artifactTypes = tiaControls.rawAttributes.artifact_type.values;
        if (!artifactTypes) {
            return response.error(req, res, { msgCode: "ARTIFACT_TYPES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        return response.success(req, res, { msgCode: "ARTIFACT_TYPES_FETCHED", data: artifactTypes }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.addCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaCustomerControls } = db.models;
        req.body.is_custom = true;

        const addedControls = await commonService.addDetail(tiaCustomerControls, req.body, dbTrans);
        if (!addedControls) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_CREATED", data: addedControls }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.updateControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaControls, tiaCustomerControls } = db.models;
        const { title, description, artifact_type, question, fields, is_attachment } = req.body;

        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }
        let raw_question = null;
        const originalQuestion = await commonService.getDataAssociate(tiaControls, tiaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (originalQuestion) {
            raw_question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(tiaCustomerControls, { id: req.params.customer_control_id }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            raw_question = customQuestion;
        }

        const updatedValues = {
            title: title || raw_question?.title,
            description: description || raw_question?.description,
            artifact_type: artifact_type || raw_question?.artifact_type,
            is_attachment: is_attachment || raw_question?.is_attachment,
            question: question || raw_question?.question,
            fields: fields || raw_question?.fields,
            is_custom: true
        }

        const updatedControls = await commonService.updateData(tiaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.updateFields = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaControls, tiaCustomerControls } = db.models;
        const { fields } = req.body;
        let question = null;

        const originalQuestion = await commonService.getDataAssociate(tiaControls, tiaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (originalQuestion) {
            question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(tiaCustomerControls, { id: req.params.customer_control_id }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            question = customQuestion;
        }
        // console.log("=======>>>>>>>",question);

        const updatedValues = {
            title: question.title,
            description: question.description,
            artifact_type: question.artifact_type,
            is_attachment: question.is_attachment,
            question: question.question,
            fields: fields || question.fields,
            is_custom: true
        }
        const updatedControls = await commonService.updateData(tiaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.deleteCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaCustomerControls } = db.models;

        const deletedControls = await commonService.deleteQuery(tiaCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, dbTrans);
        if (!deletedControls) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_DELETED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.gettiaCollaborators = async (req, res) => {
    try {
        const { tiaCollaborator, User, CustomerAssessments, tiaCategory } = db.models;
        const tia_id = req.params.tia_id;

        const tia = await tiaService.getTIAWithAssignee(CustomerAssessments, User, { id: tia_id }, {}, ['status', 'assigned_to', 'approver'], ['firstName', 'lastName']);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        
        if (tia.assigned_to !== req.data.userId && tia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
        }
        const collaborators = await tiaService.gettiaCollaborators(tiaCategory, tiaCollaborator, User, {}, { tia_id: tia_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
        if (!collaborators) {
            return response.error(req, res, { msgCode: "COLLABORATORS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        collaborators?.forEach(collaborator => {
            collaborator.Collaborators = collaborator.tiaCollaborators;
            delete collaborator.tiaCollaborators;
        });

        const assignee = `${tia.AssignedTo?.firstName} ${tia.AssignedTo?.lastName}`;

        return response.success(req, res, { msgCode: "COLLABORATORS_FETCHED", data: { assignee: assignee, collaborators } }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR );
    }
}

exports.addtiaCollaborator = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaCollaborator, AuditLog, CustomerAssessments, Departments, Processes, User, tiaCategory } = db.models;

        const checkTIA = await tiaService.getTIA(CustomerAssessments, Departments, Processes, User, { id: req.body.tia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.assigned_to !== req.data.userId && checkTIA.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        const userList = req.body.collaborators?.flatMap(collaborator => collaborator.users?.map(user => user.id));

        const users = await commonService.getListWithoutCount(User, {
            id: { [Op.in]: userList }
        }, ['id', 'firstName', 'lastName', 'email']);
        if (!users) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const categories = await commonService.getListWithoutCount(tiaCategory, {
            id: { [Op.in]: req.body.collaborators.map(collaborator => collaborator.category_id) }
        }, ['id', 'name']);
        if (!categories) {
            return response.error(req, res, { msgCode: "CATEGORY_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!invitee) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let collaboratorData = [];
        const auditData = [];
        let tiaName = null;
        let dept_id = null;
        if (checkTIA.Department) {
            tiaName = checkTIA.Department.name;
            dept_id = checkTIA.Department.id;
        } else if (checkTIA.Process) {
            tiaName = checkTIA.Process.name;
            dept_id = checkTIA.Process.Department.id;
        }

        // const names = [];
        for (let collaborator of req.body.collaborators) {
            const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

            for (let collaboratingUser of collaborator.users) {
                const user = users?.find(user => user.id === collaboratingUser.id);
                let userName = `${user?.firstName} ${user?.lastName}`;
                // names.push(userName);

                if (collaboratingUser.action === 'add') {
                    collaboratorData.push({
                        tia_id: req.body.tia_id,
                        user_id: collaboratingUser.id,
                        category_id: collaborator.category_id
                    });

                    auditData.push({
                        type: 'TIA',
                        type_id: req.body.tia_id,
                        action: `Added ${userName} as a collaborator for ${tiaName} TIA under ${categoryName} category`,
                        action_by_id: req.data.userId,
                        dept_id: dept_id,
                        customer_id: req.data.customer_id
                    });

                    const currentDate = moment().tz('Asia/Kolkata');
                    const completionDate = moment(checkTIA?.tentative_date);
                    const daysUntilCompletion = completionDate.diff(currentDate, 'days');

                    //send mail
                    const subject = `Collaboration Request: Assistance Needed with TIA in ${categoryName}`;
                    const textTemplate = "tia_collaborator.ejs";
                    const baseUrl = req.protocol + '://' + req.get('host');
                    const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "https://dev.gotrust.tech";
                    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";
                    
                    const sendData = {
                        collaboratorName: userName,
                        inviteeName: `${invitee.firstName} ${invitee.lastName}`,
                        tiaName: tiaName,
                        categoryName: categoryName,
                        url: `${frontEndUrl}/assessment-management/task-overview/`,
                        logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
                        email_logo_url: `${backEndUrl}/app/public/email_log.png`,
                        daysLeft: daysUntilCompletion
                    };

                    await sendMail(
                        user.email,
                        sendData,
                        subject,
                        textTemplate,
                    );
                } else if (collaboratingUser.action === 'remove') {
                    const oldtiaCollaborator = await commonService.deleteQuery(tiaCollaborator, { tia_id: req.body.tia_id, user_id: collaboratingUser.id, category_id: collaborator.category_id }, dbTrans, true);
                    if (!oldtiaCollaborator) {
                        return response.error(req, res, { msgCode: "ERROR_DELETING_COLLABORATOR" }, httpStatus.BAD_REQUEST, dbTrans);
                    }

                    auditData.push({
                        type: 'TIA',
                        type_id: req.body.tia_id,
                        action: `Removed ${userName} as a collaborator for ${tiaName} TIA under ${categoryName} category`,
                        action_by_id: req.data.userId,
                        dept_id: dept_id,
                        customer_id: req.data.customer_id
                    });
                }
            }
        }

        const newtiaCollaborators = await commonService.bulkAdd(tiaCollaborator, collaboratorData, dbTrans);
        if (!newtiaCollaborators) {
            return response.error(req, res, { msgCode: "ERROR_ADDING_COLLABORATOR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "COLLABORATOR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.createOrUpdateAnswers = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaAnswers, CustomerAssessments } = db.models;
        const answers = req.body.answers;
        const tia_id = req.body.tia_id;

        const checkTIA = await commonService.findByCondition(CustomerAssessments, { id: tia_id }, ['status']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "TIA_NOT_STARTED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "TIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "TIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Separate answers into two arrays based on the type
        const addAnswers = answers?.filter(answer => answer.type === 'add');
        const updateAnswers = answers?.filter(answer => answer.type === 'update');

        // Add 'answered_by' field to all answers
        addAnswers?.forEach(answer => answer.answered_by = req.data.userId);
        updateAnswers?.forEach(answer => answer.answered_by = req.data.userId);

        // Bulk add or update answers
        if (addAnswers.length > 0) {
            const addNewAnswers = await commonService.bulkAdd(tiaAnswers, addAnswers, dbTrans);
            if (!addNewAnswers) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        if (updateAnswers.length > 0) {
            for (let answer of updateAnswers) {
                const updateAnswers = await commonService.updateData(tiaAnswers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
                if (!updateAnswers[1]) {
                    return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        return response.success(req, res, { msgCode: "ANSWER_CREATED_OR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
};

exports.submitTIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, AuditLog, Departments, Processes, User, tiaAnswers, tiaCustomerControls } = db.models;
        const checkTIA = await tiaService.getTIA(CustomerAssessments, Departments, Processes, User, { id: req.params.tia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "TIA_NOT_STARTED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "TIA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkTIA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "TIA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const checkAnswerStatus = await commonService.getListAssociateWithoutCount(tiaCustomerControls, tiaAnswers, { tia_id: req.params.tia_id }, {}, [[sequelize.literal(`"tiaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], {});

        const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
        const childrenMap = checkAnswerStatus?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        const unansweredQuestions = parents?.reduce((acc, parent) => {
            if (parent.children.length > 0) {
                parent.children.forEach(child => {
                    if (child.tiaAnswer === null) {
                        acc.push({ customer_question_id: child.customer_question_id });
                    }
                });
            } else if (parent.tiaAnswer === null) {
                acc.push({ customer_question_id: parent.customer_question_id });
            }
            return acc;
        }, []);

        if (unansweredQuestions.length > 0) {
            return response.error(req, res, { msgCode: "ALL_NOT_ANSWERED", data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const tia = await commonService.updateData(CustomerAssessments, { status: constant.status.UNDER_REVIEW }, { id: req.params.tia_id }, dbTrans);
        if (!tia[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let tiaName = null;
        let dept_id = null;

        if (checkTIA.Department) {
            tiaName = checkTIA.Department.name;
            dept_id = checkTIA.Department.id;
        } else if (checkTIA.Process) {
            tiaName = checkTIA.Process.name;
            dept_id = checkTIA.Process.Department.id;
        }


        const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!submitter) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const submitterName = `${submitter.firstName} ${submitter.lastName}`;
        const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : "https://devapi.gotrust.tech";

        const subject = ` TIA ${tiaName} Ready for Review`;
        const textTemplate = "tia_submit.ejs";
        const sendData = {
            assignee: submitterName,
            tiaName: tiaName,
            reviewer: `${checkTIA.Approver?.firstName} ${checkTIA.Approver?.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/tia/`,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
        };

        await sendMail(
            checkTIA.Approver.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Submitted ${tiaName} TIA for review`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'TIA', type_id: req.params.tia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TIA_SUBMITTED", data: tia[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.reviewTIA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { ReviewTIA } = db.models;
        const reviews = req.body.reviews;

        // Separate answers into two arrays based on the type
        const addReviews = reviews?.filter(answer => answer.type === 'add');
        const updateReviews = reviews?.filter(answer => answer.type === 'update');

        // Add 'areviewer_id' field to all answers
        addReviews?.forEach(review => review.reviewer_id = req.data.userId);
        updateReviews?.forEach(review => review.reviewer_id = req.data.userId);

        // Bulk add or update reviews
        if (addReviews.length > 0) {
            const addNewReviews = await commonService.bulkAdd(ReviewTIA, addReviews, dbTrans);
            if (!addNewReviews) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        if (updateReviews.length > 0) {
            for (let review of updateReviews) {
                const updateReviews = await commonService.updateData(ReviewTIA, review, { customer_question_id: review.customer_question_id }, dbTrans);
                if (!updateReviews[1]) {
                    return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
        return response.success(req, res, { msgCode: "REVIEW_CREATED_OR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.submitReview = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, tiaCustomerControls, AuditLog, Departments, Processes, User, ReviewTIA } = db.models;
        const checkTIA = await tiaService.getTIA(CustomerAssessments, Departments, Processes, User, { id: req.params.tia_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkTIA) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkTIA.status !== constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "TIA_NOT_SUBMITTED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let tiaName = null;
        let dept_id = null;

        if (checkTIA.Department) {
            tiaName = checkTIA.Department.name;
            dept_id = checkTIA.Department.id;
        } else if (checkTIA.Process) {
            tiaName = checkTIA.Process.name;
            dept_id = checkTIA.Process.Department.id;
        }

        let status = constant.status.COMPLETED;
        let end_date = Date();
        const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(tiaCustomerControls, ReviewTIA, 'ReviewTIA', { tia_id: req.params.tia_id }, {}, [[sequelize.literal(`"tiaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], ['accurate_information']);

        const unreviewedControls = checkReviewStatus?.filter(review => review.ReviewTIA === null && review.parent_id === null);
        if (unreviewedControls.length > 0) {
            return response.error(req, res, { msgCode: "ALL_NOT_REVIEWED", data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const unapprovedControls = checkReviewStatus?.filter(review => review.ReviewTIA?.accurate_information === 0);
        if (unapprovedControls.length > 0) {
            status = constant.status.CHANGES_REQUESTED;
            end_date = null;
        }

        let riskLevel = null;

        if (status === constant.status.COMPLETED) {
            // risk assessment logic
            riskLevel = 'Low';

            // const question_id = [constant.question.CHILDREN_DATA_COLLECTION, constant.question.CROSS_BORDER_DATA_TRANSFER, constant.question.DATA_SUBJECTS_CATEGORIES, constant.question.PERSONAL_DATA, constant.question.SENSITIVE_DATA, constant.question.THIRD_PARTY_VENDORS];

            // const answers = await commonService.getListAssociate(tiaAnswers, tiaCustomerControls, {}, { tia_id: req.params.tia_id, question_id: { [Op.in]: question_id } }, {}, {});

            // //create a map with question_id as key and answer as value
            // const answerMap = answers?.reduce((map, answer) => {
            //     map[answer.CustomerControl.question_id] = answer.answer;
            //     return map;
            // }, {});

            // const riskAttributes = {
            //     PERSONAL_DATA: 0,
            //     SENSITIVE_DATA: 0,
            //     DATA_SUBJECTS_CATEGORIES: 0,
            //     CHILDREN_DATA_COLLECTION: 0,
            //     THIRD_PARTY_VENDORS: 0,
            //     CROSS_BORDER_DATA_TRANSFER: 0
            // }

            // for (let key in answerMap) {
            //     switch (parseInt(key)) {
            //         case constant.question.PERSONAL_DATA:
            //             riskAttributes.PERSONAL_DATA = answerMap[key].length;
            //             break;
            //         case constant.question.SENSITIVE_DATA:
            //             riskAttributes.SENSITIVE_DATA = answerMap[key].length;
            //             break;
            //         case constant.question.DATA_SUBJECTS_CATEGORIES:
            //             riskAttributes.DATA_SUBJECTS_CATEGORIES = answerMap[key].length;
            //             break;
            //         case constant.question.CHILDREN_DATA_COLLECTION:
            //             riskAttributes.CHILDREN_DATA_COLLECTION = answerMap[key];
            //             break;
            //         case constant.question.THIRD_PARTY_VENDORS:
            //             riskAttributes.THIRD_PARTY_VENDORS = answerMap[key].length;
            //             break;
            //         case constant.question.CROSS_BORDER_DATA_TRANSFER:
            //             riskAttributes.CROSS_BORDER_DATA_TRANSFER = answerMap[key].length;
            //             break;
            //     }
            // }

            // if (riskAttributes.PERSONAL_DATA > 7 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 7 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 5) {
            //     riskLevel = 'High';
            // } else if (riskAttributes.PERSONAL_DATA > 3 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 3 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 2) {
            //     riskLevel = 'Medium';
            // }

            // if (riskAttributes.CHILDREN_DATA_COLLECTION[0] === '0') {
            //     riskLevel = 'High';
            // }

            // if (riskAttributes.SENSITIVE_DATA > 1 || riskAttributes.THIRD_PARTY_VENDORS > 1) {
            //     riskLevel = 'High';
            // }

            const subject = `TIA Completed: Review Suggested Risks for Comptiance Enhancement`;
            const textTemplate = "tia_review_submit.ejs";
            const sendDataAssignedTo = {
                assignee: `${checkTIA.AssignedTo.firstName} ${checkTIA.AssignedTo.lastName}`,
                tiaName: tiaName,
                url: `${process.env.SERVER_IP}/privacy/tia/`
            };
             console.log("sendDataAssignedTo====>>>>",sendDataAssignedTo);

            await sendMail(
                checkTIA.AssignedTo.email,
                sendDataAssignedTo,
                subject,
                textTemplate,
            );

            const sendDataApprover = {
                assignee: `${checkTIA.Approver.firstName} ${checkTIA.Approver.lastName}`,
                tiaName: tiaName,
                url: `${process.env.SERVER_IP}/privacy/tia/`
            };
            console.log("sendDataApprover====>>>>",sendDataApprover);
            await sendMail(
                checkTIA.AssignedTo.email,
                sendDataApprover,
                subject,
                textTemplate,
            );
        }

        const tia = await commonService.updateData(CustomerAssessments, { status: status, end_date: end_date, risks: riskLevel }, { id: req.params.tia_id }, dbTrans);
        if (!tia[1]) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditAction = `Submitted review for ${tiaName} TIA with status '${status}'`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'TIA', type_id: req.params.tia_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TIA_REVIEWED", data: tia[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getAuditLog = async (req, res) => {
    try {
        const { AuditLog, User, Departments } = db.models;

        const { tia_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        const auditCondition = { type: 'TIA', customer_id: req.data.customer_id };
        const userCondition = {};

        if (search) {
            userCondition[Op.or] = [
                { firstName: { [Op.iLike]: `%${search}%` } },
                { lastName: { [Op.iLike]: `%${search}%` } },
                { email: { [Op.iLike]: `%${search}%` } }
            ];
        }

        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
            if (!deptHead) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }

            const deptIds = deptHead.rows?.map(dept => dept.id);
            auditCondition.dept_id = { [Op.in]: deptIds };
        }

        if (tia_id) {
            auditCondition.type_id = tia_id;
        }

        const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // getting name initials and added to the audit data
        auditData.rows?.map(row => {
            const name = row?.User?.firstName + ' ' + row?.User?.lastName;
            row.name = name;
            const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
            row.initials = initials;
            delete row.User;
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.uploadControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { tiaControls, tiaCategory,Assessments, CustomerAssessments,tiaCustomerControls } = db.models;
        const controls = [];
        let parentId = null;
        const childControlsData = [];
        const uniqueCategories = new Map(); // Map to store unique categories
        const artifactTypeMapping = {
            'radio': 'radio',
            'dropdown': 'select',
            'table': 'table',
            'text box': 'textarea',
            'upload attachment': 'attachment'
        };

        // const checkTia =  await commonService.getList(tiaControls, { customer_id: req.data.customer_id }, {} );
        // if(checkTia.rows[0]){
        //     deleteFile(req.files[0].path);
        //     return response.error(req, res, { msgCode: "ALREADY_EXIST_TEMPLATE" }, httpStatus.BAD_REQUEST, dbTrans);
        // }

        const transaction = await db.transaction();
        try{
            const assessmentTypeId = await commonService.getList(Assessments,{key:'tia'});
            const listCustomerAssessment = await commonService.getList(CustomerAssessments,{customer_id:req.data.customer_id, assessment_id:assessmentTypeId.rows[0].id},['id','status']);
            const tiaIds =  listCustomerAssessment?.rows?.map(item => item.id);

            const checkLiaControls = await commonService.getList(tiaCustomerControls,{ tia_id: { [Op.in]: tiaIds }});
            if (checkLiaControls?.rows?.length > 0) {
                await commonService.deleteQuery(tiaCustomerControls, { tia_id: { [Op.in]: tiaIds }}, transaction ,true);
            }

            const checkControls = await commonService.getList(tiaControls, { customer_id: req.data.customer_id });

            if (checkControls?.rows?.length > 0) {
                await commonService.deleteQuery(tiaControls, { customer_id: req.data.customer_id }, transaction ,true);
            }

            const checkCategory = await commonService.getList(tiaCategory, { customer_id: req.data.customer_id });
            if (checkCategory?.rows?.length > 0) {
                await commonService.deleteQuery(tiaCategory, { customer_id: req.data.customer_id }, transaction ,true);
            }

            for (const { id, status } of listCustomerAssessment.rows) {
                await commonService.updateData(CustomerAssessments, {
                    start_date: null,
                    end_date:null,
                    // assigned_to: null,
                    // approver: null,
                    progress: null,
                    tentative_date: null,
                    status: 'Yet to Start'
                }, { id: id }, transaction);
            }
         
            // Commit the transaction
            await transaction.commit();
        }
        catch(error){
            await transaction.rollback();
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: "TRANSACTION_FAILED" }, httpStatus.INTERNAL_SERVER_ERROR);
        }

        const requiredHeaders = ['TIA Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
        const { isValid, missingHeader } = await tiaService.validateHeaders(req.files[0].path, requiredHeaders);
        if (!isValid) {
            deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: "INVALID_HEADER", data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
        }

        fs.createReadStream(req.files[0].path)
            .pipe(csv())
            .on('data', async (row) => {
                const controlData = {
                    TIALevel: row['TIA Level'],
                    tiaCategory: row['Category'],
                    title: row['Title'],
                    description: row['Explanation'],
                    artifact_type: row['Artifact Type'],
                    question: row['Question'],
                    customer_id: req.data.customer_id,
                    fields: row['Fields'],
                    is_attachment: row['Has Attachment'],
                    extra_input: row['Extra Input Required'],
                    extra_input_type: row['Extra Input Type'],
                    extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields'],
                };
                
                // Check if all properties of controlData are empty
                if (!Object.values(controlData).every(x => (x === ''))) {
                    if (controlData.title === '' && controlData.question === '') {
                        await deleteFile(req.files[0].path);
                        return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                    controls.push(controlData);
                }
            })
            .on('end', async () => {
                // Insert the data into the database
                for (let row of controls) {
                    if (!row['TIALevel'] || !row['tiaCategory']) {
                        await deleteFile(req.files[0].path);
                        return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                    const key = `${row['TIALevel']}_${row['tiaCategory']}`;
                    if (row['title']) { // Parent question
                        if (!uniqueCategories.has(key)) {
                            // Create or retrieve category and store in the map
                            const [tia_level, name] = key.split('_');
                            let category = await commonService.findByCondition(tiaCategory, { tia_level, name, customer_id: req.data.customer_id, }, {});
                            if (!category) {
                                category = await tiaCategory.create({ name, tia_level, customer_id: req.data.customer_id, }, { transaction: dbTrans });
                            }
                            uniqueCategories.set(key, category);
                        }

                        let artifactType = null;
                        if (row['artifact_type'] !== '') {
                            artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                            if (!artifactType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }

                        let extraInputType = null;
                        if (row['extra_input_type'] !== '') {
                            extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                            if (!extraInputType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }

                        // Create parent control
                        const control = await commonService.addDetail(tiaControls, {
                            title: row['title'],
                            description: row['description'],
                            artifact_type: artifactType,
                            customer_id: req.data.customer_id,
                            question: row['question'],
                            fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                            is_attachment: row['is_attachment'] === 'Yes',
                            extra_input: row['extra_input'] === 'Yes',
                            extra_input_type: extraInputType,
                            extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                            category_id: uniqueCategories.get(key).id,
                            parent_id: null,
                            industry_vertical_id: 1
                        }, dbTrans);

                        if (!control) {
                            await deleteFile(req.files[0].path);
                            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
                        }

                        // Update parent ID for potential child questions
                        parentId = control.id;
                    } else { // Child question
                        if (parentId) {
                            // Create child control
                            let artifactType = null;
                            if (row['artifact_type'] !== '') {
                                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                                if (!artifactType) {
                                    await deleteFile(req.files[0].path);
                                    return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                                }
                            }

                            let extraInputType = null;
                            if (row['extra_input_type'] !== '') {
                                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                                if (!extraInputType) {
                                    await deleteFile(req.files[0].path);
                                    return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                                }
                            }

                            childControlsData.push({
                                title: null,
                                description: null,
                                artifact_type: artifactType,
                                customer_id: req.data.customer_id,
                                question: row['question'],
                                fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                                is_attachment: row['is_attachment'] === 'Yes',
                                extra_input: row['extra_input'] === 'Yes',
                                extra_input_type: extraInputType,
                                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                                category_id: uniqueCategories.get(key).id,
                                parent_id: parentId,
                                industry_vertical_id: 1
                            });
                        } else {
                            await deleteFile(req.files[0].path);
                            return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                        }
                    }
                }
                // Batch create child controls
                const childControls = await commonService.bulkAdd(tiaControls, childControlsData, dbTrans);
                if (!childControls) {
                    await deleteFile(req.files[0].path);
                    return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
                }

                await deleteFile(req.files[0].path);

                return response.success(req, res, { msgCode: "CONTROLS_UPLOADED" }, httpStatus.OK, dbTrans);
            });
    }
    catch (err) {
        console.log('error', err);
        await deleteFile(req.files[0].path);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


exports.getTiaData = async (req, res) => {
    try {
        const { tiaControls, tiaCustomerControls, tiaAnswers, User, CustomerAssessments, tiaCategory, Customer, Departments, Processes } = db.models;
        const tia_id = req.params.tia_id;

        const tia = await commonService.findByCondition(CustomerAssessments, { id: tia_id }, ['status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks']);
        if (!tia) {
            return response.error(req, res, { msgCode: "TIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        if (tia.status !== constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "TIA_NOT_COMPLETED" }, httpStatus.BAD_REQUEST);
        }

        const controlsAttributes = [
            [
                sequelize.literal(`"tiaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."title" ELSE "tiaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."description" ELSE "tiaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN CAST("tiaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("tiaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."is_attachment" ELSE "tiaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."question" ELSE "tiaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."fields" ELSE "tiaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."extra_input" ELSE "tiaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN CAST("tiaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("tiaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."is_custom" THEN "tiaCustomerControls"."extra_input_fields" ELSE "tiaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "tiaCustomerControls"."question_id" IS NOT NULL THEN "tiaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        const controls = await assessmentService.getControlsWithCategory(tiaCustomerControls, tiaControls, tiaAnswers, User, tiaCategory, { tia_id: tia_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'name'], [['question_id', 'ASC']]);
        for (let control of controls) {
            control.Answer = control.tiaAnswer;
            delete control.tiaAnswer;
            control.Category = control.tiaCategory;
            delete control.tiaCategory;
        }
        let parents = controls?.filter(control => control.parent_id === null);

        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });


        const excelData = transformData(parents);

        const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

        const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
        if (!customer) {
            return response.error(req, res, { msgCode: "CUSTOMER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        let deptName = '';
        let procName = '';

        if (tia.department_id) {
            const dept = await commonService.findByCondition(Departments, { id: tia.department_id }, ['name']);
            deptName = dept?.name;
        } else if (tia.process_id) {
            const proc = await commonService.getDataAssociate(Processes, Departments, { id: tia.process_id }, {}, ['name'], ['name']);
            procName = proc?.name;
            deptName =`${proc?.Department?.name} - ${procName}`;
        
        }
        
        const excelFile = await createAssessmentExcelFile(excelData, 'TIA (Transfer Impact Assessment)', date, customer?.name, deptName, procName);

        await sendMailWithAttach(
            req.data.email,
            { name: `${user?.firstName} ${user?.lastName}` },
            `TIA ${deptName}  Successfully Completed `,
            'tia_download.ejs',
            excelFile
        );

        return response.success(req, res, { msgCode: "TIA_DOWNLOADED", data: "TIA data sent via E-mail" }, httpStatus.OK);

    } catch (err) {
        console.log('assessmentDownloadError', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};