const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const constant = require('../constant/PDA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
const { sendMail } = require('../config/email');




exports.getFormCorntrols = async (req, res) => {
    try {
        const { DSRControls, DSRCustomerControls, DSRAnswers, User } = db.models;
        const pia_id = req.params.pia_id;
        const category_id = req.query.category_id;

        const pia = await commonService.findByCondition(CustomerAssessments, { id: pia_id }, ['status', 'assigned_to', 'approver']);
        if (!pia) {
            return response.error(req, res, { msgCode: "PIA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pia.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PIA_NOT_STARTED" }, httpStatus.BAD_REQUEST);
        }

        if (pia.assigned_to !== req.data.userId && pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(piaCollaborator, { pia_id: pia_id, user_id: req.data.userId, category_id: category_id }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PIA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
        }

        let controls;
        
        const controlsAttributes = [
            [
                sequelize.literal(`"piaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."title" ELSE "piaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."description" ELSE "piaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN CAST("piaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("piaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."is_attachment" ELSE "piaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."question" ELSE "piaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."fields" ELSE "piaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."extra_input" ELSE "piaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN CAST("piaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("piaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."is_custom" THEN "piaCustomerControls"."extra_input_fields" ELSE "piaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "piaCustomerControls"."question_id" IS NOT NULL THEN "piaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        
        if (pia.status === constant.status.UNDER_REVIEW || pia.status === constant.status.CHANGES_REQUESTED || pia.status === constant.status.COMPLETED) {
            if (pia.status === constant.status.UNDER_REVIEW && (pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2])) {
                return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
            }
            controls = await piaService.getControlsWithReview(piaCustomerControls, piaControls, piaAnswers, User, ReviewPIA, { pia_id: pia_id, category_id: category_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'accurate_information', 'comments'], [['question_id', 'ASC']]);
        } else {
            controls = await piaService.getControls(piaCustomerControls, piaControls, piaAnswers, User, { pia_id: pia_id, category_id: category_id }, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], [['question_id', 'ASC']]);
        }
        
        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        for (let control of controls) {
            control.Answer = control.piaAnswer;
            delete control.piaAnswer;
            control.Review = control.ReviewPIA;
            delete control.ReviewPIA;
            if (control.Answer) {
                control.answered = true;
                if (control.Answer.extra_answer) {
                    control.Answer.extra_answered = true;
                } else {
                    control.Answer.extra_answered = false;
                }
            } else {
                control.answered = false;
            }

            if (control.Review) {
                control.reviewed = true;
            } else {
                control.reviewed = false;
            }

            if (pia.assigned_to !== req.data.userId && pia.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                control.is_collaborator = true;
            } else {
                control.is_collaborator = false;
            }
        }

        let parents = controls?.filter(control => control.parent_id === null);
        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        if (pia.status === constant.status.CHANGES_REQUESTED) {
            parents = parents?.filter(parent => parent.Review?.accurate_information === 0);
        }

        return response.success(req, res, { msgCode: "CONTROLS_FETCHED", data: { status: pia.status, controls: parents } }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};