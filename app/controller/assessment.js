const commonService = require('../services/common');
const assessmentService = require('../services/assessment');
const csv = require('csv-parser');
const fs = require('fs');
const moment = require('moment');
const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const constant = require('../constant/vendor');
const asssessmentService = require('../services/assessment');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const ropaService = require('../services/ropa');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createAssessmentExcelFile, generateExcelForAuditData } = require('../utils/helper');
const auditLogger = require('../utils/audit-logger');
const { generateEmbedding } = require('../utils/embeddings');
const parsingService = require('../utils/parsing-file');
const { cosineSimilarity } = require('../utils/cosineSimilarity');

exports.uploadTemplate = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentTemplate } = db.models;

    const data = {
      name: req.body.name,
      url: req.body.url,
      customer_id: req.data.customer_id,
      key: req.body.key,
      assessment_id: req.body.assessment
    };

    const Templates = await commonService.addDetail(AssessmentTemplate, data, dbTrans);
    if (!Templates) {
      return response.error(req, res, { msgCode: 'TEMPLATE_NOT_ADDED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'TEMPLATE_ADDED', data: Templates }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.publishedTemaplate = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentTemplate } = db.models;
    const data = {
      id: req.body.id,
      published: req.body.published
    };

    const publishedTemp = await commonService.updateData(AssessmentTemplate, { id: data.id }, { published: data.published }, dbTrans);

    if (!publishedTemp[1]) {
      return response.error(req, res, { msgCode: 'NOT_PUBLISHED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'TEMPLATE_PUBLISHED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.addCustomeAssessmentType = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Assessments, AssessmentCategory, AssessmentControls } = db.models;
    const controls = [];
    let parentId = null;
    const childControlsData = [];
    const uniqueCategories = new Map();
    const artifactTypeMapping = {
      radio: 'radio',
      dropdown: 'select',
      table: 'table',
      'text box': 'textarea',
      'upload attachment': 'attachment'
    };
    const data = {
      assessment_name: req.body.name,
      customer_id: req.data.customer_id,
      status: true,
      type: req.body.type
    };
    data.key = data.assessment_name
      .split(' ')
      ?.map(word => word[0])
      .join('')
      .toUpperCase();
    const findAssessment = await commonService.findByCondition(Assessments, { assessment_name: data.assessment_name, customer_id: req.data.customer_id }, {});
    if (findAssessment) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ASSESSMENT_ALREADY_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const addAssessment = await commonService.addDetail(Assessments, data, dbTrans);
    if (!addAssessment) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_ADDED' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const requiredHeaders = ['ASSESSMENT Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
    const { isValid, missingHeader } = await assessmentService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const fileStream = fs.createReadStream(req.files[0].path);
    const csvParser = csv();

    fileStream
      .pipe(csvParser)
      .on('data', async row => {
        const controlData = {
          Level: row['ASSESSMENT Level'],
          Category: row['Category'],
          title: row['Title'],
          description: row['Explanation'],
          artifact_type: row['Artifact Type'],
          question: row['Question'],
          fields: row['Fields'],
          is_attachment: row['Has Attachment'],
          extra_input: row['Extra Input Required'],
          customer_id: req.data.customer_id,
          extra_input_type: row['Extra Input Type'],
          extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields']
        };

        if (!Object.values(controlData).every(x => x === '')) {
          if (controlData.title === '' && controlData.question === '') {
            fileStream.destroy();
            csvParser.end();
            csvParser.removeAllListeners();
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          controls.push(controlData);
        }
      })
      .on('end', async () => {
        const categoryTitleQuestionSet = new Set();
        for (let row of controls) {
          if (!row['Level'] || !row['Category']) {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'ASSESSMENT Level or Category is missing.' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          const key = `${row['Level']}_${row['Category']}`;

          if (row['title']) {
            const titleText = row['title']?.trim();
            const duplicateKey = `${key}_${titleText}`;

            if (categoryTitleQuestionSet.has(duplicateKey)) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'DUPLICATE_QUESTION_TITLE', data: `Duplicate control found in category [${key}] with title [${titleText}]` }, httpStatus.BAD_REQUEST, dbTrans);
            }

            categoryTitleQuestionSet.add(duplicateKey);
            if (!uniqueCategories.has(key)) {
              const [level, name] = key.split('_');
              let category = await commonService.findByCondition(AssessmentCategory, { level, name, template_id: null, customer_id: req.data.customer_id, assessment_id: addAssessment.id, key: addAssessment.key }, {});
              if (!category) {
                const data = { level, name, template_id: null, customer_id: req.data.customer_id, assessment_id: addAssessment.id, key: addAssessment.key };

                category = await AssessmentCategory.create(data, { transaction: dbTrans });
              }
              uniqueCategories.set(key, category);
            }

            let artifactType = null;
            if (row['artifact_type'] !== '') {
              artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
              if (!artifactType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE', data: `Invalid artifact type: ${row['artifact_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            let extraInputType = null;
            if (row['extra_input_type'] !== '') {
              extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
              if (!extraInputType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE', data: `Invalid extra input type: ${row['extra_input_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }
            const str = row['title'] + ' and ' + row['description'] + ' and ' + row['question'];
            const embedding = await generateEmbedding(str);

            const control = await commonService.addDetail(
              AssessmentControls,
              {
                title: row['title'],
                description: row['description'],
                artifact_type: artifactType,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: null,
                template_id: null,
                customer_id: req.data.customer_id,
                industry_vertical_id: req.body.industry_vertical_id,
                key: addAssessment.key,
                assessment_id: addAssessment.id,
                embedding: embedding
              },
              dbTrans
            );

            if (!control) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: 'Error creating parent control.' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            parentId = control.id;
          } else {
            if (parentId) {
              let artifactType = null;
              if (row['artifact_type'] !== '') {
                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                if (!artifactType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE', data: `Invalid artifact type for child control: ${row['artifact_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              let extraInputType = null;
              if (row['extra_input_type'] !== '') {
                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                if (!extraInputType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE', data: `Invalid extra input type for child control: ${row['extra_input_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }
              const str = row['title'] + ' and ' + row['description'] + ' and ' + row['question'];
              const embedding = await generateEmbedding(str);

              childControlsData.push({
                title: null,
                description: null,
                artifact_type: artifactType,
                customer_id: req.data.customer_id,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: parentId,
                template_id: null,
                industry_vertical_id: req.body.industry_vertical_id,
                embedding: embedding
              });
            } else {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'Child control found without a parent control.' }, httpStatus.BAD_REQUEST, dbTrans);
            }
          }
        }
        const childControls = await commonService.bulkAdd(AssessmentControls, childControlsData, dbTrans);
        if (!childControls) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: 'Error creating child controls.' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        await deleteFile(req.files[0].path);
        const auditLog = await auditLogger.createGeneralAuditLog(
          {
            type: 'ASSESSMENT',
            type_id: null,
            customer_id: req.data.customer_id,
            actorId: req.data.userId,
            actionType: 'CREATE_ASSESSMENT_TYPE',
            metadata: {
              assessmentName: data.assessment_name,
              key: data.key,
              type: data.type
            }
          },
          req,
          dbTrans
        );
        console.log(auditLog);
        if (!auditLog) {
          return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (error) {
    console.log('error', error);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.assessmentList = async (req, res) => {
  try {
    const { Assessments } = db.models;
    const condition = {
      [Op.or]: [{ customer_id: req.data.customer_id }, { customer_id: { [Op.is]: null } }]
    };

    const list = await commonService.getList(Assessments, condition, {});
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }
    return response.success(req, res, { msgCode: 'LIST_FETCHED', data: list }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.listTemplates = async (req, res) => {
  try {
    const { Assessments, AssessmentTemplate } = db.models;
    const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    let order = [[sort_by, sort_order]];
    const customerCondition = {
      [Op.or]: [{ customer_id: req.data.customer_id }, { customer_id: null }]
    };
    const templateCondition = { customer_id: req.data.customer_id };
    if (search) {
      templateCondition.name = {
        [Op.iLike]: `%${search}%`
      };
    }
    const fetchList = await commonService.getList2(Assessments, AssessmentTemplate, customerCondition, templateCondition, {}, {}, limit, offset, order);
    if (!fetchList) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }
    if (search) {
      // Return all assessments that match the search criteria, regardless of whether they have templates
      const filteredRows = fetchList?.rows?.filter(item => item.AssessmentTemplates && item.AssessmentTemplates.length > 0);
      return response.success(req, res, { msgCode: 'LIST_FETCHED', data: { count: filteredRows.length, rows: filteredRows } }, httpStatus.OK);
    }

    return response.success(req, res, { msgCode: 'LIST_FETCHED', data: fetchList }, httpStatus.OK);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.uploadControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Assessments, AssessmentTemplate, AssessmentCategory, AssessmentControls } = db.models;
    const controls = [];
    let parentId = null;
    const childControlsData = [];
    const uniqueCategories = new Map();
    const artifactTypeMapping = {
      radio: 'radio',
      dropdown: 'select',
      table: 'table',
      'text box': 'textarea',
      'upload attachment': 'attachment'
    };
    const templateData = {
      customer_id: req.data.customer_id,
      key: req.body.key,
      assessment_id: req.body.assessment_id,
      name: req.body.name
    };
    const checkAssessment = await commonService.findByCondition(Assessments, { id: req.body.assessment_id }, {});
    if (!checkAssessment) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const requiredHeaders = ['ASSESSMENT Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
    const { isValid, missingHeader } = await assessmentService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // console.log("------------------------------------>>>>>>>>>>>>>>>>>>", isValid)

    const fileStream = fs.createReadStream(req.files[0].path);
    const csvParser = csv();

    fileStream
      .pipe(csvParser)
      .on('data', async row => {
        const controlData = {
          Level: row['ASSESSMENT Level'],
          Category: row['Category'],
          title: row['Title'],
          description: row['Explanation'],
          artifact_type: row['Artifact Type'],
          question: row['Question'],
          fields: row['Fields'],
          is_attachment: row['Has Attachment'],
          extra_input: row['Extra Input Required'],
          customer_id: req.data.customer_id,
          extra_input_type: row['Extra Input Type'],
          extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields']
        };

        if (!Object.values(controlData).every(x => x === '')) {
          if (controlData.title === '' && controlData.question === '') {
            // console.log("jiiii",controlData);
            fileStream.destroy();
            csvParser.end();
            csvParser.removeAllListeners();
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          controls.push(controlData);
          // console.log("-------->>>>>",controls[0])
        }
      })
      .on('end', async () => {
        // console.log("-------->>>>>",controls[0])
        // // console.log(templateData)
        // console.log("------>>>>>", dbTrans[0])
        const categoryTitleQuestionSet = new Set();
        const template = await commonService.addDetail(AssessmentTemplate, templateData, dbTrans);
        if (!template) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'TEMPLATE_NOT_ADDED' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        for (let row of controls) {
          if (!row['Level'] || !row['Category']) {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'ASSESSMENT Level or Category is missing.' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          const key = `${row['Level']}_${row['Category']}`;

          if (row['title']) {
            const titleText = row['title']?.trim();
            const duplicateKey = `${key}_${titleText}`;

            if (categoryTitleQuestionSet.has(duplicateKey)) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'DUPLICATE_QUESTION_TITLE', data: `Duplicate control found in category [${key}] with title [${titleText}]` }, httpStatus.BAD_REQUEST, dbTrans);
            }

            categoryTitleQuestionSet.add(duplicateKey);

            if (!uniqueCategories.has(key)) {
              const [level, name] = key.split('_');
              let category = await commonService.findByCondition(AssessmentCategory, { level, name, template_id: template.id, customer_id: req.data.customer_id, assessment_id: template.assessment_id, key: template.key }, {});
              if (!category) {
                category = await AssessmentCategory.create({ level, name, template_id: template.id, customer_id: req.data.customer_id, assessment_id: template.assessment_id, key: template.key }, { transaction: dbTrans });
              }
              uniqueCategories.set(key, category);
            }

            let artifactType = null;
            if (row['artifact_type'] !== '') {
              artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
              if (!artifactType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE', data: `Invalid artifact type: ${row['artifact_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            let extraInputType = null;
            if (row['extra_input_type'] !== '') {
              extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
              if (!extraInputType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE', data: `Invalid extra input type: ${row['extra_input_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }
            //creating embedding
            const str = row['title'] + ' and ' + row['description'] + ' and ' + row['question'];
            const embedding = await generateEmbedding(str);
            // console.log('embedding generated', embedding);

            const control = await commonService.addDetail(
              AssessmentControls,
              {
                title: row['title'],
                description: row['description'],
                artifact_type: artifactType,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: null,
                template_id: template.id,
                customer_id: req.data.customer_id,
                industry_vertical_id: req.body.industry_vertical_id,
                key: template.key,
                assessment_id: template.assessment_id,
                embedding: embedding
              },
              dbTrans
            );

            if (!control) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: 'Error creating parent control.' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            parentId = control.id;
          } else {
            if (parentId) {
              let artifactType = null;
              if (row['artifact_type'] !== '') {
                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                if (!artifactType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE', data: `Invalid artifact type for child control: ${row['artifact_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              let extraInputType = null;
              if (row['extra_input_type'] !== '') {
                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                if (!extraInputType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE', data: `Invalid extra input type for child control: ${row['extra_input_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              //creating embedding
              const str = row['title'] + ' and ' + row['description'] + ' and ' + row['question'];
              const embedding = await generateEmbedding(str);
              // console.log('embedding generated', embedding);

              childControlsData.push({
                title: null,
                description: null,
                artifact_type: artifactType,
                customer_id: req.data.customer_id,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: parentId,
                template_id: template.id,
                industry_vertical_id: req.body.industry_vertical_id,
                assessment_id: template.assessment_id,
                key: template.key,
                embedding: embedding
              });
            } else {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'Child control found without a parent control.' }, httpStatus.BAD_REQUEST, dbTrans);
            }
          }
        }
        const childControls = await commonService.bulkAdd(AssessmentControls, childControlsData, dbTrans);
        if (!childControls) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: 'Error creating child controls.' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        await deleteFile(req.files[0].path);
        const auditLog = await auditLogger.createGeneralAuditLog(
          {
            type: 'ASSESSMENT',
            type_id: null,
            customer_id: req.data.customer_id,
            actorId: req.data.userId,
            actionType: 'UPLOAD_CONTROLS',
            metadata: {
              assessmentName: checkAssessment.assessment_name,
              key: checkAssessment.key,
              templateName: req.body.name
            }
          },
          req,
          dbTrans
        );
        console.log(auditLog);
        if (!auditLog) {
          return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.taskOverview = async (req, res) => {
  try {
    const { User, Group, Departments, Processes, Assessments, CustomerAssessment, AssessmentTemplate } = db.models;
    const { AssessmentCollaborator } = db.models;
    const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    let userType = null;
    let customerFilter = { customer_id: req.data.customer_id };
    let userFilter = {};
    let searchCondition = {};
    const groupIds = [req.params.entity_id];

    if (search && !search_key) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Assessment.assessment_name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssessmentTemplate.name'), { [Op.iLike]: `%${search}%` })
          // sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        ]
      };
    }

    if (search && search_key) {
      if (search_key === 'Department') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Process') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Assessment') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Assessment.assessment_name'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'AssignedTo') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Approver') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Owner') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Status') {
        searchCondition = {
          status: sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'Risks') {
        searchCondition = {
          risks: sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'is_assigned') {
        searchCondition = {
          is_Assigned: true
        };
      }
      // } else if (search_key === 'Status') {
      //     searchCondition = {
      //         status: sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
      //     };
      // } else if (search_key === 'Risks') {
      //     searchCondition = {
      //         risks: sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
      //     };
      // }
    }

    const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
    const departmentIds = departments?.rows?.map(department => department.id);
    const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
    const processIds = processes?.rows?.map(process => process.id);

    if (req.data.roleName === authConstant.USER_ROLE[2]) {
      userType = 'DPO';
    } else if (departmentIds.length > 0) {
      userType = 'Department Head';
    } else {
      userType = 'Employee';
    }

    const collaborator = await commonService.getList(AssessmentCollaborator, { user_id: req.data.userId }, ['assessment_id']);
    const collaboratorId = collaborator?.rows?.map(collaborator => collaborator.assessment_id);

    const collaboratorIds = [...collaboratorId];

    let assessmentFilter = { entity_id: req.params.entity_id };

    if (userType === 'DPO') {
      assessmentFilter = { ...assessmentFilter };
    } else if (userType === 'Department Head') {
      assessmentFilter = {
        ...assessmentFilter,
        [Op.or]: [{ assigned_to: req.data.userId }, { approver: req.data.userId }, { id: { [Op.in]: collaboratorIds } }, { department_id: { [Op.in]: departmentIds } }, { process_id: { [Op.in]: processIds } }]
      };
    } else {
      assessmentFilter = {
        ...assessmentFilter,
        [Op.or]: [{ assigned_to: req.data.userId }, { approver: req.data.userId }, { id: { [Op.in]: collaboratorIds } }]
      };
    }

    const list = await assessmentService.getListWithMultipleAssociates(
      CustomerAssessment,
      Assessments,
      AssessmentTemplate,
      Departments,
      Processes,
      User,
      'AssignedTo',
      User,
      'Approver',
      User,
      'Owner',
      Group,
      {
        [Op.and]: [
          assessmentFilter,
          {
            [Op.or]: [sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('CustomerAssessment.entity_id'), { [Op.ne]: null })]
          },
          is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
          searchCondition
        ]
      },
      {},
      {},
      {},
      customerFilter,
      userFilter,
      {},
      {},
      {},
      ['id', 'assessment_id', 'risks', 'progress', 'start_date', 'end_date', 'tentative_date', 'status'],
      ['id', 'type', 'assessment_name', 'key'],
      ['id', 'name', 'key'],
      ['id', 'name'],
      ['id', 'name'],
      ['id', 'firstName', 'lastName'],
      ['id', 'firstName', 'lastName'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      limit,
      offset,
      order
    );

    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    const taskOverview = {
      user_type: userType,
      rows: list?.rows.map(listItem => {
        // if (listItem.Process) {
        //     listItem.Department = listItem.Process.Department;
        //     delete listItem.Process.Department;
        //     delete listItem.Process.User;
        // }
        if (listItem.Assessment) {
          listItem.Assessment.AssessmentTemplates = listItem.AssessmentTemplate ? [{ ...listItem.AssessmentTemplate }] : null;
          delete listItem.AssessmentTemplate;
        }

        if (listItem.AssignedTo) {
          if (listItem?.AssignedTo?.id === req.data.userId) {
            listItem.isAssigned = true;
          } else {
            listItem.isAssigned = false;
          }
          listItem.AssignedTo.name = `${listItem?.AssignedTo?.firstName} ${listItem?.AssignedTo?.lastName}`;
          delete listItem.AssignedTo.firstName;
          delete listItem.AssignedTo.lastName;
        }

        if (listItem?.Approver) {
          listItem.Approver.name = `${listItem?.Approver?.firstName} ${listItem?.Approver?.lastName}`;
          delete listItem.Approver.firstName;
          delete listItem.Approver.lastName;
        }

        if (listItem.Owner) {
          listItem.Owner.name = `${listItem?.Owner?.firstName} ${listItem?.Owner?.lastName}`;
          delete listItem.Owner.firstName;
          delete listItem.Owner.lastName;
        }

        // listItem.SPOC = { id: listItem.Department.User.id, name: `${tiaItem.Department.User.firstName} ${tiaItem.Department.User.lastName}` },
        listItem.isCollaborator = collaboratorIds.includes(listItem.id);
        // delete tiaItem.Department.User;

        return listItem;
      }),
      count: list.count
    };

    return response.success(req, res, { msgCode: 'TASK_OVERVIEW_FETCHED', data: taskOverview }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST);
  }
};
exports.createAssessments = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const { CustomerAssessment, Assessments, Departments, Processes, User } = db.models;
    req.body.owner_id = req.data.userId;
    req.body.customer_id = req.data.customer_id;
    req.body.approver = req.body.reviewer_id;
    delete req.body.reviewer_id;
    let assessmentName = null;

    const assessment = await commonService.addDetail(CustomerAssessment, req.body, dbTrans);
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_ASSESSMENT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // await dbTrans.commit();
    // dbTrans=undefined;

    const data = await assessmentService.getAssessmentData(
      CustomerAssessment,
      Assessments,
      Departments,
      Processes,
      User,
      'AssignedTo',
      User,
      'Approver',
      User,
      'Owner',
      { id: assessment.id },
      {},
      {},
      {},
      {},
      {},
      {},
      ['id', 'tentative_date', 'status'],
      ['id', 'key', 'assessment_name'],
      ['id', 'name'],
      ['id', 'name'],
      ['id', 'email', 'firstName', 'lastName'],
      ['id', 'email', 'firstName', 'lastName'],
      ['id', 'email', 'firstName', 'lastName'],
      dbTrans
    );

    if (!data) {
      return response.success(req, res, { msgCode: 'ASSESSMENT_MAIL_NOT_TRIGGRED', data: assessment }, httpStatus.OK, dbTrans);
    }
    if (data?.Department) {
      assessmentName = data?.Department?.name;
    } else if (data?.Process) {
      assessmentName = data?.Process?.name;
    }
    let textTemplate = '';
    let textTemplate1 = '';
    let sendData = {};
    let sendData1 = {};
    const currentDate = moment().tz('Asia/Kolkata');
    const completionDate = moment(data?.tentative_date);
    const daysUntilCompletion = completionDate.diff(currentDate, 'days');
    const subject = `${data.Assessment.assessment_name} Assigned – ${assessmentName} Task Due in ${daysUntilCompletion} Days`;
    const subject1 = ` Upcoming Review Task: ${data.Assessment.assessment_name} for ${assessmentName}`;
    const baseUrl = req.protocol + '://' + req.get('host');
    const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'https://devapi.gotrust.tech';
    textTemplate = 'assessment_assigned.ejs';
    sendData = {
      assignee: `${data?.AssignedTo?.firstName} ${data?.AssignedTo?.lastName}`,
      assessmentName: assessmentName,
      masterAssessment: data.Assessment.assessment_name,
      url: `${frontEndUrl}/assessment-management/task-overview/`,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`,
      daysLeft: daysUntilCompletion
    };

    textTemplate1 = 'assessment_reviewer.ejs';
    sendData1 = {
      reviewer: `${data?.Approver?.firstName} ${data?.Approver?.lastName}`,
      assessmentName: assessmentName,
      masterAssessment: data.Assessment.assessment_name,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`,
      // assigner: `${assigner.firstName} ${assigner.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/${data.Assessment.key}/`
    };
    // console.log('assessment list', data );

    sendMail(data?.AssignedTo?.email, sendData, subject, textTemplate);

    sendMail(data?.Approver?.email, sendData1, subject1, textTemplate1);
    const key = data.Assessment.key;
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: data.id,
        dept_id: req.body.department_id || null,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'CREATE_ASSESSMENT',
        metadata: {
          assessmentName,
          key,
          assessmentType: data.Assessment.assessment_name || 'Standard'
        }
      },
      req,
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'ASSESSMENT_CREATED', data: assessment }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
  }
};
exports.startAssesment = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Assessments, CustomerAssessment, AssessmentControls, AssessmentCustomerControls, AssessmentCollaborator, Departments, Processes, User } = db.models;

    const checkASSESSMENT = await asssessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: req.params.assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkASSESSMENT) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkASSESSMENT.status === constant.status.STARTED || checkASSESSMENT.status === constant.status.CHANGES_REQUESTED) {
      if (checkASSESSMENT.assigned_to && checkASSESSMENT.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        const collaborator = await commonService.findByCondition(AssessmentCollaborator, { assessment_id: req.params.assessment_id, user_id: req.data.userId }, ['id']);
        if (!collaborator) {
          return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }
      }
      return response.success(req, res, { msgCode: 'ASSESSMENT_STARTED' }, httpStatus.OK, dbTrans);
    } else if (checkASSESSMENT.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkASSESSMENT.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let assessmentName = null;
    let dept_id = null;
    // let approverId = null;
    const keyName = await commonService.findByCondition(Assessments, { id: checkASSESSMENT.assessment_id }, ['key']);
    const key = keyName.key.toUpperCase();
    if (checkASSESSMENT.Department) {
      assessmentName = checkASSESSMENT.Department.name;
      dept_id = checkASSESSMENT.Department.id;
      // approverId = checkPIA.Department.spoc_id;
    } else if (checkASSESSMENT.Process) {
      assessmentName = checkASSESSMENT.Process.name;
      dept_id = checkASSESSMENT.Process.Department.id;
      // approverId = checkPIA.Process.Department.spoc_id;
    }

    if (checkASSESSMENT.assigned_to && checkASSESSMENT.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(AssessmentCollaborator, { assessment_id: req.params.assessment_id, user_id: req.data.userId }, ['id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }
    }

    const assessment = await commonService.updateData(CustomerAssessment, { status: constant.status.STARTED, start_date: Date() }, { id: req.params.assessment_id }, dbTrans);
    if (!assessment[1]) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let controls = await commonService.getList(AssessmentControls, { customer_id: req.data.customer_id, template_id: assessment[1].template_id, assessment_id: assessment[1].assessment_id }, ['id', 'category_id', 'parent_id', 'customer_id']);
    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (controls.count == 0) {
      controls = await commonService.getList(AssessmentControls, { assessment_id: assessment[1].assessment_id, template_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
    }

    const parentControls = controls?.rows?.filter(control => control.parent_id === null);
    const childControls = controls?.rows?.filter(control => control.parent_id !== null);

    const customerControlsParents = parentControls?.map(control => {
      return {
        question_id: control.id,
        customer_id: control.customer_id,
        category_id: control.category_id,
        key: control.key,
        assessment_id: assessment[1].id,
        template_id: assessment[1].template_id,
        is_custom: false
      };
    });

    const newCustomerControlsParents = await commonService.bulkAdd(AssessmentCustomerControls, customerControlsParents, dbTrans);
    if (!newCustomerControlsParents) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
      map[parentControls[index].id] = control.id;
      return map;
    }, {});

    const customerControlsChildren = childControls?.map(control => {
      return {
        question_id: control.id,
        category_id: control.category_id,
        customer_id: control.customer_id,
        assessment_id: assessment[1].id,
        parent_id: parentIdMap[control.parent_id],
        is_custom: false
      };
    });

    const newCustomerControlsChildren = await commonService.bulkAdd(AssessmentCustomerControls, customerControlsChildren, dbTrans);
    if (!newCustomerControlsChildren) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Create detailed audit log
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: req.params.assessment_id,
        dept_id: dept_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'START',
        metadata: {
          assessmentName,
          key
        }
      },
      req,
      dbTrans
    );

    // const auditAction = `${user?.firstName} ${user?.lastName} started ${assessmentName} ${key}`;

    // const auditLog = await commonService.addDetail(AuditLog, { type:'ASSESSMENT', type_id: req.params.assessment_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ASSESSMENT_STARTED', data: assessment[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.getCategories = async (req, res) => {
  try {
    const { AssessmentCategory, AssessmentCollaborator, CustomerAssessment, AssessmentCustomerControls, ReviewAssessment } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    let Level = req.params.level;
    const assessment_id = req.query.assessment_id;
    Level = Level.charAt(0).toUpperCase() + Level.slice(1);

    let categoryCondition = { level: Level };
    let conditions = [];

    const assessment = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, {});
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (assessment.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && assessment.approver !== req.data.userId) {
      const collaborators = await commonService.getList(AssessmentCollaborator, { assessment_id: assessment_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborators) {
        return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED);
      }
      // categoryCondition.id = { [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) };
      conditions.push({ [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) });
    }

    if (assessment.status === constant.status.CHANGES_REQUESTED) {
      const changeReqCategories = await commonService.getListAssociateWithAlias(AssessmentCustomerControls, ReviewAssessment, 'ReviewAssessment', { assessment_id: assessment_id }, { accurate_information: 0 }, ['category_id']);
      if (!changeReqCategories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
      // categoryCondition.id = { [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) };
      conditions.push({ [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) });
    } else {
      categoryCondition.customer_id = req.data.customer_id;
    }

    if (conditions.length > 0) {
      categoryCondition.id = {
        [Op.and]: conditions
      };
    }

    categoryCondition.template_id = assessment.template_id;
    categoryCondition.assessment_id = assessment.assessment_id;

    let categories = await commonService.getList(AssessmentCategory, categoryCondition, ['id', 'name'], limit, offset, order);
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (categories.count == 0) {
      categoryCondition.customer_id = null;
      categoryCondition.template_id = null;

      categories = await commonService.getList(AssessmentCategory, categoryCondition, ['id', 'name'], limit, offset, order);
      if (!categories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }

    //for uncategorized
    if (assessment.is_automated === true) {
      categories.rows.push({
        id: categories.rows[categories.count - 1].id + 1,
        name: 'Uncategorized'
      });
      categories.count = categories.count + 1;
    }

    // if (!categories) {
    //     return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
    // }
    return response.success(req, res, { msgCode: 'CATEGORIES_FETCHED', data: categories }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.getControls = async (req, res) => {
  try {
    const { AssessmentControls, AssessmentCustomerControls, AssessmentAnswers, User, CustomerAssessment, AssessmentCollaborator, ReviewAssessment, AssessmentCategory } = db.models;
    const assessment_id = req.params.assessment_id;
    let category_id = req.query.category_id;

    const assessment = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, ['status', 'assigned_to', 'approver', 'is_already_performed', 'template_id','assessment_id','is_automated']);
    console.log('assessment----------------', assessment);
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (assessment.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_STARTED' }, httpStatus.BAD_REQUEST);
    }

    if (assessment.assigned_to !== req.data.userId && assessment.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(AssessmentCollaborator, { assessment_id: assessment_id, user_id: req.data.userId, category_id: category_id }, ['id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED);
      }
    }

    //get uncategorized controls
    let categoryCondition = {};
    categoryCondition.customer_id = req.data.customer_id;
    categoryCondition.assessment_id = assessment.assessment_id;

    if (assessment.template_id) {
      categoryCondition.template_id = assessment.template_id;
    } else {
      categoryCondition.template_id = null;
    }
    let categories = await commonService.getList(AssessmentCategory, categoryCondition, ['id', 'name'], null, null, [['id', 'ASC']]);
    // console.log('categories----------------', categories);
    if (categories.count === 0) {
      categoryCondition.customer_id = null;
      categories = await commonService.getList(AssessmentCategory, categoryCondition, ['id', 'name'], null, null, [['id', 'ASC']]);
      if (!categories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }
    if (category_id == categories.rows[categories.count - 1].id + 1) {
      category_id = null;
    }

    let controls;
    const controlsAttributes = [
      [sequelize.literal(`"AssessmentCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'assessment_id',
      'template_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."title" ELSE "AssessmentControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."description" ELSE "AssessmentControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN CAST("AssessmentCustomerControls"."artifact_type" AS TEXT) ELSE CAST("AssessmentControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."is_attachment" ELSE "AssessmentControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."question" ELSE "AssessmentControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."fields" ELSE "AssessmentControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."extra_input" ELSE "AssessmentControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN CAST("AssessmentCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("AssessmentControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."extra_input_fields" ELSE "AssessmentControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."question_id" IS NOT NULL THEN "AssessmentControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    if (assessment.status === constant.status.UNDER_REVIEW || assessment.status === constant.status.CHANGES_REQUESTED || assessment.status === constant.status.COMPLETED) {
      if (assessment.status === constant.status.UNDER_REVIEW && assessment.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }
      controls = await asssessmentService.getControlsWithReview2(
        AssessmentCustomerControls,
        AssessmentControls,
        AssessmentAnswers,
        User,
        ReviewAssessment,
        { assessment_id: assessment_id, category_id: category_id },
        {},
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'comments'],
        [['question_id', 'ASC']]
      );
    } else {
      // console.log('assessment_id', assessment_id);
    console.log('category_id', category_id);
      controls = await asssessmentService.getControls(
        AssessmentCustomerControls,
        AssessmentControls,
        AssessmentAnswers,
        User,
        { assessment_id: assessment_id, category_id: category_id },
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        [['question_id', 'ASC']]
      );
      // console.log('controls----------------', controls);
    }

    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    for (let control of controls) {
      control.Answer = control.AssessmentAnswer;
      delete control.AssessmentAnswers;
      control.Review = control.ReviewAssessment;
      delete control.ReviewAssessment;
      if (control.Answer) {
        control.answered = true;
        if (control.Answer.extra_answer) {
          control.Answer.extra_answered = true;
        } else {
          control.Answer.extra_answered = false;
        }
      } else {
        control.answered = false;
      }

      if (control.Review) {
        control.reviewed = true;
      } else {
        control.reviewed = false;
      }

      if (assessment.assigned_to !== req.data.userId && assessment.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        control.is_collaborator = true;
      } else {
        control.is_collaborator = false;
      }
    }

    let parents = controls?.filter(control => control.parent_id === null);
    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    if (assessment.status === constant.status.CHANGES_REQUESTED) {
      parents = parents?.filter(parent => parent.Review?.accurate_information === 0);
    }

    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: { status: assessment.status, controls: parents } }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getArtifactTypes = async (req, res) => {
  try {
    const { AssessmentControls } = db.models;
    const artifactTypes = AssessmentControls.rawAttributes.artifact_type.values;
    if (!artifactTypes) {
      return response.error(req, res, { msgCode: 'ARTIFACT_TYPES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'ARTIFACT_TYPES_FETCHED', data: artifactTypes }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentCustomerControls, CustomerAssessment, Assessments, Departments, Processes } = db.models;
    req.body.is_custom = true;
    const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
      CustomerAssessment,
      Assessments,
      Departments,
      Processes,
      { id: req.body.assessment_id },
      {},
      {},
      {},
      ['id', 'tentative_date', 'status'],
      ['id', 'key', 'assessment_name'],
      ['id', 'name'],
      ['id', 'name']
    );
    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.OK, dbTrans);
    }
    const addedControls = await commonService.addDetail(AssessmentCustomerControls, req.body, dbTrans);
    if (!addedControls) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    let assessmentName;
    if (assessmentDetails?.Department) {
      assessmentName = assessmentDetails?.Department?.name;
    } else if (assessmentDetails?.Process) {
      assessmentName = assessmentDetails?.Process?.name;
    }
    const key = assessmentDetails?.Assessment?.key || '';
    const controlTitle = req.body.title || 'Custom Control';

    // Create audit log for adding custom control
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: req.body.assessment_id,
        dept_id: req.body.dept_id || null,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'ADD_CUSTOM_CONTROL',
        metadata: {
          assessmentName,
          key,
          controlTitle,
          controlId: addedControls.id
        }
      },
      req,
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_CREATED', data: addedControls }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

///////////new

exports.updateControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentControls, AssessmentCustomerControls, CustomerAssessment, Assessments, Departments, Processes } = db.models;
    const { title, description, artifact_type, question, fields, is_attachment } = req.body;
    const { master } = req.query;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    let raw_question = null;
    const originalQuestion = await commonService.getDataAssociate(AssessmentControls, AssessmentCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    // console.log('originalQuestion----------------', originalQuestion);

    if (master === 'true' && originalQuestion) {  
      const updateMasterData = {
        title: title,
        description: description
      };
      const updateMasterControl = await commonService.updateData(AssessmentControls, updateMasterData, { id: originalQuestion?.id }, dbTrans);
      if (!updateMasterControl[1]) {
        return response.error(req, res, { msgCode: 'ERROR_UPDATING_MASTER_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      // console.log('updateMasterControl----------------', updateMasterControl);
      const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
        CustomerAssessment,
        Assessments,
        Departments,
        Processes,
        { id: originalQuestion?.AssessmentCustomerControls[0]?.assessment_id },
        {},
        {},
        {},
        ['id', 'tentative_date', 'status'],
        ['id', 'key', 'assessment_name'],
        ['id', 'name'],
        ['id', 'name']
      );
      let assessmentName;
      if (assessmentDetails?.Department) {
        assessmentName = assessmentDetails?.Department?.name;
      } else if (assessmentDetails?.Process) {
        assessmentName = assessmentDetails?.Process?.name;
      }
      const key = originalQuestion?.key;
      const controlTitle = originalQuestion.title || 'Control';
      const auditLog = await auditLogger.createGeneralAuditLog(
        {
          type: 'ASSESSMENT',
          type_id: originalQuestion?.AssessmentCustomerControls[0]?.assessment_id,
          customer_id: req.data.customer_id,
          actorId: req.data.userId,
          actionType: 'UPDATE_MASTER_CONTROL',
          metadata: {
            assessmentName,
            key,
            controlTitle,
            controlId: req.params.customer_control_id,
            changes: {
              title: title !== originalQuestion.title,
              description: description !== originalQuestion.description
            }
          }
        },
        req,
        dbTrans
      );
      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updateMasterControl[1] }, httpStatus.OK, dbTrans);
    } else if (originalQuestion && master !== 'true') {
      raw_question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(AssessmentCustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      raw_question = customQuestion;
    }
    const updatedValues = {
      title: title || raw_question.title,
      description: description || raw_question.description,
      artifact_type: artifact_type || raw_question.artifact_type,
      is_attachment: is_attachment || raw_question.is_attachment,
      question: question || raw_question.question,
      fields: fields || raw_question.fields,
      is_custom: true,
      extra_input: raw_question.extra_input,
      extra_input_type: raw_question.extra_input_type,
      extra_input_fields: raw_question.extra_input_fields
    };

    const updatedControls = await commonService.updateData(AssessmentCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
      CustomerAssessment,
      Assessments,
      Departments,
      Processes,
      { id: updatedControls[1]?.assessment_id },
      {},
      {},
      {},
      ['id', 'tentative_date', 'status'],
      ['id', 'key', 'assessment_name'],
      ['id', 'name'],
      ['id', 'name']
    );
    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.OK, dbTrans);
    }
    let assessmentName;
    if (assessmentDetails?.Department) {
      assessmentName = assessmentDetails?.Department?.name;
    } else if (assessmentDetails?.Process) {
      assessmentName = assessmentDetails?.Process?.name;
    }
    const key = assessmentDetails?.Assessment?.key || '';
    const controlTitle = raw_question.title || 'Control';

    // Create audit log for updating control
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: updatedControls[1]?.assessment_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'UPDATE_CONTROL',
        metadata: {
          assessmentName,
          key,
          controlTitle,
          controlId: req.params.customer_control_id,
          changes: {
            title: title !== raw_question.title,
            description: description !== raw_question.description,
            artifact_type: artifact_type !== raw_question.artifact_type,
            question: question !== raw_question.question,
            fields: fields !== raw_question.fields
          }
        }
      },
      req,
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateFields = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentControls, AssessmentCustomerControls, CustomerAssessment, Assessments, Departments, Processes } = db.models;
    const { fields } = req.body;
    let question = null;

    const originalQuestion = await commonService.getDataAssociate(AssessmentControls, AssessmentCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    if (originalQuestion) {
      question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(AssessmentCustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      question = customQuestion;
    }

    const updatedValues = {
      title: question.title,
      description: question.description,
      artifact_type: question.artifact_type,
      is_attachment: question.is_attachment,
      question: question.question,
      fields: fields || question.fields,
      is_custom: true
    };

    const updatedControls = await commonService.updateData(AssessmentCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // Get assessment details for audit log
    const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
      CustomerAssessment,
      Assessments,
      Departments,
      Processes,
      { id: question?.AssessmentCustomerControls[0]?.assessment_id },
      {},
      {},
      {},
      ['id', 'tentative_date', 'status'],
      ['id', 'key', 'assessment_name'],
      ['id', 'name'],
      ['id', 'name']
    );
    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.OK, dbTrans);
    }
    let assessmentName;
    if (assessmentDetails?.Department) {
      assessmentName = assessmentDetails?.Department?.name;
    } else if (assessmentDetails?.Process) {
      assessmentName = assessmentDetails?.Process?.name;
    }
    const key = assessmentDetails?.Assessment?.key || '';
    const controlTitle = question.title || 'Control';

    // Create audit log for updating fields
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: question?.AssessmentCustomerControls[0]?.assessment_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'UPDATE_CONTROL_FIELDS',
        metadata: {
          assessmentName,
          key,
          controlTitle,
          controlId: req.params.customer_control_id
        }
      },
      req,
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.deleteCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentCustomerControls, Assessments, CustomerAssessment, Departments, Processes } = db.models;
    const check = await commonService.findByCondition(AssessmentCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, {});
    if (!check) {
      return response.error(req, res, { msgCode: 'CONTROL_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
      CustomerAssessment,
      Assessments,
      Departments,
      Processes,
      { id: check?.assessment_id },
      {},
      {},
      {},
      ['id', 'tentative_date', 'status'],
      ['id', 'key', 'assessment_name'],
      ['id', 'name'],
      ['id', 'name']
    );

    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.OK, dbTrans);
    }
    const deletedControls = await commonService.deleteQuery(AssessmentCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, dbTrans);
    if (!deletedControls) {
      return response.error(req, res, { msgCode: 'ERROR_DELETING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    let assessmentName;
    if (assessmentDetails?.Department) {
      assessmentName = assessmentDetails?.Department?.name;
    } else if (assessmentDetails?.Process) {
      assessmentName = assessmentDetails?.Process?.name;
    }
    const key = assessmentDetails?.Assessment?.key || '';
    const controlTitle = check.title || 'Custom Control';

    // Create audit log for deleting custom control
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: check?.assessment_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'DELETE_CUSTOM_CONTROL',
        metadata: {
          assessmentName,
          key,
          controlTitle,
          controlId: req.params.customer_control_id
        }
      },
      req,
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'CONTROL_DELETED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getProgress = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentCustomerControls, AssessmentAnswers, CustomerAssessment, ReviewAssessment, AssessmentCollaborator } = db.models;
    const assessment_id = req.params.assessment_id;

    const assessment = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, ['status', 'approver', 'assigned_to']);
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const status = assessment.status;
    let controls = null;
    let totalControls = 0;
    let answeredControls = 0;
    let childControls = [];
    let progress = 0;

    if (assessment.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && assessment.approver !== req.data.userId) {
      const collaborator = await commonService.getList(AssessmentCollaborator, { assessment_id: assessment_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }

      const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

      controls = await commonService.getListAssociateWithoutCount(AssessmentCustomerControls, AssessmentAnswers, { assessment_id: assessment_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.piaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.AssessmentAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.AssessmentAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });

      progress = (answeredControls / totalControls) * 100;
      progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

      return response.success(req, res, { msgCode: 'PROGRESS_FETCHED', data: { totalControls, answeredControls, progress } }, httpStatus.OK, dbTrans);
    }

    if (status === constant.status.STARTED) {
      controls = await commonService.getListAssociateWithoutCount(AssessmentCustomerControls, AssessmentAnswers, { assessment_id: assessment_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.piaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.AssessmentAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.AssessmentAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.UNDER_REVIEW) {
      controls = await commonService.getListAssociateWithoutCountWithAlias(AssessmentCustomerControls, ReviewAssessment, 'ReviewAssessment', { assessment_id: assessment_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      // totalControls = controls.filter(control => control.parent_id === null).length;
      // answeredControls = controls.filter(control => control.ReviewPIA).length;
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
        }
        if (control.ReviewAssessment) {
          answeredControls++;
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.CHANGES_REQUESTED) {
      controls = await asssessmentService.getControlsWithAnswersAndReviews(
        AssessmentCustomerControls,
        AssessmentAnswers,
        ReviewAssessment,
        { assessment_id: assessment_id },
        {},
        {},
        ['id', 'parent_id'],
        ['updatedAt'],
        ['accurate_information', 'updatedAt']
      );
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      // totalControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewPIA && control.ReviewPIA.accurate_information === 0).length;
      // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.piaAnswer?.updatedAt > control.ReviewPIA?.updatedAt).length;
      // childControls = controls.rows?.filter(control => control.parent_id !== null);
      controls.rows?.forEach(control => {
        if (control.parent_id === null) {
          if (control.ReviewAssessment && control.ReviewAssessment.accurate_information === 0) {
            totalControls++;
          }
          if (control.AssessmentAnswer?.updatedAt > control.ReviewAssessment?.updatedAt && control.ReviewAssessment.accurate_information === 0) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});
      Object.entries(childControlsByParent)?.forEach(([parentId, childControls]) => {
        const parentControl = controls.rows.find(control => control.id == parentId);
        if (parentControl && childControls.every(control => control.AssessmentAnswer.updatedAt > parentControl.ReviewAssessment.updatedAt)) {
          answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewPIA
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.COMPLETED) {
      controls = await commonService.getListAssociateWithoutCount(AssessmentCustomerControls, AssessmentAnswers, { assessment_id: assessment_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls.filter(control => control.parent_id === null).length;
      // answeredControls = controls.filter(control => control.parent_id === null && control.piaAnswer).length;
      // childControls = controls.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.AssessmentAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.AssessmentAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    }

    progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

    const updateProgress = await commonService.updateData(CustomerAssessment, { progress: progress }, { id: assessment_id }, dbTrans);
    if (!updateProgress[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

    return response.success(req, res, { msgCode: 'PROGRESS_FETCHED', data: { totalControls, answeredControls, progress } }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getCollaborators = async (req, res) => {
  try {
    const { AssessmentCollaborator, User, CustomerAssessment, AssessmentCategory } = db.models;
    const assessment_id = req.params.assessment_id;

    const assessment = await asssessmentService.getAssessmentWithAssignee(CustomerAssessment, User, { id: assessment_id }, {}, {}, ['firstName', 'lastName']);
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (assessment.assigned_to !== req.data.userId && assessment.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }
    // const categoryCondition = {
    //     assessment_id: assessment.assessment_id,
    //     [Op.or]: [
    //         {
    //             customer_id: req.data.customer_id,
    //             template_id: assessment.template_id
    //         },
    //         {
    //             customer_id: null,
    //             template_id: null
    //         }
    //     ]
    // }

    let categoryCondition = { customer_id: req.data.customer_id, assessment_id: assessment.assessment_id, template_id: assessment.template_id };

    let collaborators = await asssessmentService.getAssessmentCollaborators(
      AssessmentCategory,
      AssessmentCollaborator,
      User,
      categoryCondition,
      { assessment_id: assessment_id },
      {},
      ['id', 'name'],
      ['id', 'user_id'],
      ['id', 'firstName', 'lastName', 'email']
    );
    if (!collaborators) {
      return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (collaborators.length === 0) {
      categoryCondition.customer_id = null;
      categoryCondition.template_id = null;

      collaborators = await ropaService.getCollaborators(AssessmentCategory, AssessmentCollaborator, User, categoryCondition, { assessment_id: assessment_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
      if (!collaborators) {
        return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }

    collaborators?.forEach(collaborator => {
      collaborator.Collaborators = collaborator.AssessmentCollaborator;
      delete collaborator.AssessmentCollaborator;
    });

    const assignee = `${assessment.AssignedTo?.firstName} ${assessment.AssignedTo?.lastName}`;

    return response.success(req, res, { msgCode: 'COLLABORATORS_FETCHED', data: { assignee: assignee, collaborators } }, httpStatus.OK);
  } catch (err) {
    console.log('Error--->>>>', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCollaborator = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Assessments, AssessmentCollaborator, CustomerAssessment, Departments, Processes, User, AssessmentCategory } = db.models;

    const checkASSESSMENT = await asssessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: req.body.assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
    if (!checkASSESSMENT) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkASSESSMENT.assigned_to !== req.data.userId && checkASSESSMENT.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const userList = req.body.collaborators?.flatMap(collaborator => collaborator?.users?.map(user => user.id));

    const users = await commonService.getListWithoutCount(
      User,
      {
        id: { [Op.in]: userList }
      },
      ['id', 'firstName', 'lastName', 'email']
    );
    if (!users) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const categories = await commonService.getListWithoutCount(
      AssessmentCategory,
      {
        id: { [Op.in]: req.body.collaborators.map(collaborator => collaborator.category_id) }
      },
      ['id', 'name']
    );
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!invitee) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let collaboratorData = [];
    const auditData = [];
    let assessmentName = null;
    let dept_id = null;
    const keyName = await commonService.findByCondition(Assessments, { id: checkASSESSMENT.assessment_id }, ['key', 'assessment_name']);
    const key = keyName.key.toUpperCase();
    if (checkASSESSMENT.Department) {
      assessmentName = checkASSESSMENT.Department.name;
      dept_id = checkASSESSMENT.Department.id;
    } else if (checkASSESSMENT.Process) {
      assessmentName = checkASSESSMENT.Process.name;
      dept_id = checkASSESSMENT.Process.Department.id;
    }

    // const names = [];
    for (let collaborator of req.body.collaborators) {
      const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

      for (let collaboratingUser of collaborator.users) {
        const user = users?.find(user => user.id === collaboratingUser.id);
        let userName = `${user?.firstName} ${user?.lastName}`;
        // names.push(userName);

        if (collaboratingUser.action === 'add') {
          collaboratorData.push({
            assessment_id: req.body.assessment_id,
            user_id: collaboratingUser.id,
            category_id: collaborator.category_id
          });

          auditData.push({
            type: 'ASSESSMENT',
            type_id: req.body.assessment_id,
            actorId: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id,
            actionType: 'ADD_COLLABORATOR',
            metadata: {
              userName,
              assessmentName,
              categoryName
            }
          });

          const currentDate = moment().tz('Asia/Kolkata');
          const completionDate = moment(checkASSESSMENT?.tentative_date);
          const daysUntilCompletion = completionDate.diff(currentDate, 'days');

          //send mail
          const subject = `Collaboration Request: ${keyName.assessment_name} for ${assessmentName} - ${daysUntilCompletion} Days Remaining`;
          const textTemplate = 'Assessment_collaborator.ejs';
          const baseUrl = req.protocol + '://' + req.get('host');
          const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
          const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'https://devapi.gotrust.tech';

          const sendData = {
            collaboratorName: userName,
            assessmentName: assessmentName,
            masterAssessment: keyName.assessment_name,
            categoryName: categoryName,
            url: `${frontEndUrl}/assessment-management/task-overview/`,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
            daysLeft: daysUntilCompletion
          };

          sendMail(user.email, sendData, subject, textTemplate);
        } else if (collaboratingUser.action === 'remove') {
          const oldassessmentCollaborator = await commonService.deleteQuery(AssessmentCollaborator, { assessment_id: req.body.assessment_id, user_id: collaboratingUser.id, category_id: collaborator.category_id }, dbTrans, true);
          if (!oldassessmentCollaborator) {
            return response.error(req, res, { msgCode: 'ERROR_DELETING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
          }

          auditData.push({
            type: 'ASSESSMENT',
            type_id: req.body.assessment_id,
            actorId: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id,
            actionType: 'REMOVE_COLLABORATOR',
            metadata: {
              userName,
              assessmentName,
              categoryName
            }
          });
        }
      }
    }

    const newassessmentCollaborators = await commonService.bulkAdd(AssessmentCollaborator, collaboratorData, dbTrans);
    if (!newassessmentCollaborators) {
      return response.error(req, res, { msgCode: 'ERROR_ADDING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
    const auditLog = await auditLogger.createGeneralAuditLog(auditData, req, dbTrans);

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'COLLABORATOR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createOrUpdateAnswers = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentAnswers, CustomerAssessment, Assessments, Departments, Processes } = db.models;
    const answers = req.body.answers;
    const assessment_id = req.body.assessment_id;

    const checkASSESSMENT = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, ['status']);
    if (!checkASSESSMENT) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
      CustomerAssessment,
      Assessments,
      Departments,
      Processes,
      { id: assessment_id },
      {},
      {},
      {},
      ['id', 'tentative_date', 'status'],
      ['id', 'key', 'assessment_name'],
      ['id', 'name'],
      ['id', 'name']
    );
    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.OK, dbTrans);
    }
    if (checkASSESSMENT.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkASSESSMENT.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkASSESSMENT.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // // Separate answers into two arrays based on the type
    // const addAnswers = answers?.filter(answer => answer.type === 'add');
    // const updateAnswers = answers?.filter(answer => answer.type === 'update');

    // // Add 'answered_by' field to all answers
    // addAnswers?.forEach(answer => answer.answered_by = req.data.userId);
    // updateAnswers?.forEach(answer => answer.answered_by = req.data.userId);

    let addAnswers = [],
      updateAnswers = [];
    answers?.forEach(items => {
      items.answered_by = req.data.userId;
      if (items.type === 'add') {
        addAnswers.push(items);
      } else if (items.type === 'update') {
        updateAnswers.push(items);
      }
    });

    // Bulk add or update answers
    if (addAnswers.length > 0) {
      const addNewAnswers = await commonService.bulkAdd(AssessmentAnswers, addAnswers, dbTrans);
      if (!addNewAnswers) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateAnswers.length > 0) {
      for (let answer of updateAnswers) {
        const updateAnswers = await commonService.updateData(AssessmentAnswers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
        if (!updateAnswers[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }
    if (answers.length > 0 || updateAnswers.length > 0) {
      let assessmentName;
      if (assessmentDetails?.Department) {
        assessmentName = assessmentDetails?.Department?.name;
      } else if (assessmentDetails?.Process) {
        assessmentName = assessmentDetails?.Process?.name;
      }
      const key = assessmentDetails?.Assessment?.key || '';
      // Create audit log for saving answers
      const auditLog = await auditLogger.createGeneralAuditLog(
        {
          type: 'ASSESSMENT',
          type_id: assessment_id,
          customer_id: req.data.customer_id,
          actorId: req.data.userId,
          actionType: 'SAVE_ANSWERS',
          metadata: {
            assessmentName,
            key,
            answerCount: answers.length,
            addedCount: addAnswers?.length || 0,
            updatedCount: updateAnswers?.length || 0
          }
        },
        req,
        dbTrans
      );

      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    return response.success(req, res, { msgCode: 'ANSWER_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitASSESSMENT = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Assessments, CustomerAssessment, Departments, Processes, User, AssessmentAnswers, AssessmentCustomerControls } = db.models;
    const checkASSESSMENT = await asssessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: req.params.assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
    if (!checkASSESSMENT) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkASSESSMENT.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkASSESSMENT.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkASSESSMENT.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const checkAnswerStatus = await commonService.getListAssociateWithoutCount(
      AssessmentCustomerControls,
      AssessmentAnswers,
      { assessment_id: req.params.assessment_id },
      {},
      [[sequelize.literal(`"AssessmentCustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      {}
    );

    const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
    const childrenMap = checkAnswerStatus?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const unansweredQuestions = parents?.reduce((acc, parent) => {
      if (parent.children.length > 0) {
        parent.children.forEach(child => {
          if (child.AssessmentAnswer === null) {
            acc.push({ customer_question_id: child.customer_question_id });
          }
        });
      } else if (parent.AssessmentAnswer === null) {
        acc.push({ customer_question_id: parent.customer_question_id });
      }
      return acc;
    }, []);

    if (unansweredQuestions.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_ANSWERED', data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const assessment = await commonService.updateData(CustomerAssessment, { status: constant.status.UNDER_REVIEW }, { id: req.params.assessment_id }, dbTrans);
    if (!assessment[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let assessmentName = null;
    let dept_id = null;
    const keyName = await commonService.findByCondition(Assessments, { id: checkASSESSMENT.assessment_id }, ['key', 'assessment_name']);
    const key = keyName.key.toUpperCase();
    if (checkASSESSMENT.Department) {
      assessmentName = checkASSESSMENT.Department.name;
      dept_id = checkASSESSMENT.Department.id;
    } else if (checkASSESSMENT.Process) {
      assessmentName = checkASSESSMENT.Process.name;
      dept_id = checkASSESSMENT.Process.Department.id;
    }

    const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!submitter) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const submitterName = `${submitter.firstName} ${submitter.lastName}`;
    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'https://devapi.gotrust.tech';

    const subject = `${keyName.assessment_name} ${assessmentName} completed - Ready for Review`;
    const textTemplate = 'assessment_submit.ejs';
    const sendData = {
      masterAssessment: keyName.assessment_name,
      assessmentName: assessmentName,
      reviewer: `${checkASSESSMENT.Approver?.firstName} ${checkASSESSMENT.Approver?.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/${keyName.key}/`,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`
    };

    sendMail(checkASSESSMENT.Approver.email, sendData, subject, textTemplate);

    // Create detailed audit log
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: req.params.assessment_id,
        dept_id: dept_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'SUBMIT_ASSESSMENT',
        metadata: {
          assessmentName,
          assessment_name: keyName.assessment_name
        }
      },
      req,
      dbTrans
    );

    // const auditAction = `Submitted ${assessmentName} ${keyName.assessment_name} for review`;

    // const auditLog = await commonService.addDetail(AuditLog, { type: 'ASSESSMENT', type_id: req.params.assessment_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ASSESSMENT_SUBMITTED', data: assessment[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.reviewASSESSMENT = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ReviewAssessment, CustomerAssessment, Assessments, AssessmentCustomerControls, Departments, Processes } = db.models;
    const reviews = req.body.reviews;

    // // Separate answers into two arrays based on the type
    // const addReviews = reviews?.filter(answer => answer.type === 'add');
    // const updateReviews = reviews?.filter(answer => answer.type === 'update');

    // // Add 'areviewer_id' field to all answers
    // addReviews?.forEach(review => review.reviewer_id = req.data.userId);
    // updateReviews?.forEach(review => review.reviewer_id = req.data.userId);

    let addReviews = [],
      updateReviews = [];

    reviews?.forEach(review => {
      review.reviewer_id = req.data.userId;
      if (review.type === 'add') {
        addReviews.push(review);
      } else if (review.type === 'update') {
        updateReviews.push(review);
      }
    });

    // Bulk add or update reviews
    if (addReviews.length > 0) {
      const addNewReviews = await commonService.bulkAdd(ReviewAssessment, addReviews, dbTrans);
      if (!addNewReviews) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateReviews.length > 0) {
      for (let review of updateReviews) {
        const updateReviews = await commonService.updateData(ReviewAssessment, review, { customer_question_id: review.customer_question_id }, dbTrans);
        if (!updateReviews[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }
    if (addReviews.length > 0 || updateReviews.length > 0) {
      const check = await commonService.findByCondition(AssessmentCustomerControls, { id: reviews[0]?.customer_question_id }, {});
      if (!check) {
        return response.error(req, res, { msgCode: 'CONTROL_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      const assessmentId = check.assessment_id;
      const assessmentDetails = await assessmentService.getDataWithMultipleAssociates2(
        CustomerAssessment,
        Assessments,
        Departments,
        Processes,
        { id: assessmentId },
        {},
        {},
        {},
        ['id', 'tentative_date', 'status'],
        ['id', 'key', 'assessment_name'],
        ['id', 'name'],
        ['id', 'name']
      );
      if (!assessmentDetails) {
        return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.OK, dbTrans);
      }
      let assessmentName;
      if (assessmentDetails?.Department) {
        assessmentName = assessmentDetails?.Department?.name;
      } else if (assessmentDetails?.Process) {
        assessmentName = assessmentDetails?.Process?.name;
      }
      const key = assessmentDetails?.Assessment?.key || '';
      // Create audit log for reviewing assessment
      const auditLog = await auditLogger.createGeneralAuditLog(
        {
          type: 'ASSESSMENT',
          type_id: assessmentId,
          customer_id: req.data.customer_id,
          actorId: req.data.userId,
          actionType: 'ADD_REVIEW_COMMENTS',
          metadata: {
            assessmentName,
            key,
            reviewCount: reviews.length,
            addedCount: addReviews?.length || 0,
            updatedCount: updateReviews?.length || 0
          }
        },
        req,
        dbTrans
      );

      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    return response.success(req, res, { msgCode: 'REVIEW_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
//done
exports.submitReview = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Assessments, CustomerAssessment, AssessmentCustomerControls, Departments, Processes, User, ReviewAssessment } = db.models;
    const checkASSESSMENT = await asssessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: req.params.assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
    if (!checkASSESSMENT) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkASSESSMENT.status !== constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_SUBMITTED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let assessmentName = null;
    let dept_id = null;
    const keyName = await commonService.findByCondition(Assessments, { id: checkASSESSMENT.assessment_id }, ['key', 'assessment_name']);
    const key = keyName.key.toUpperCase();

    if (checkASSESSMENT.Department) {
      assessmentName = checkASSESSMENT.Department.name;
      dept_id = checkASSESSMENT.Department.id;
    } else if (checkASSESSMENT.Process) {
      assessmentName = checkASSESSMENT.Process.name;
      dept_id = checkASSESSMENT.Process.Department.id;
    }
    const reviewer = await commonService.findByCondition(User, { id: req.data.userId }, {});
    let status = constant.status.COMPLETED;
    let end_date = Date();
    const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(
      AssessmentCustomerControls,
      ReviewAssessment,
      'ReviewAssessment',
      { assessment_id: req.params.assessment_id },
      {},
      [[sequelize.literal(`"AssessmentCustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      ['accurate_information']
    );

    const unreviewedControls = [];
    const unapprovedControls = [];
    checkReviewStatus?.forEach(review => {
      if (review.ReviewAssessment === null && review.parent_id === null) {
        unreviewedControls.push(review);
      } else if (review.ReviewAssessment?.accurate_information === 0) {
        unapprovedControls.push(review);
      }
    });

    // Now check unreviewed first
    if (unreviewedControls.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_REVIEWED', data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Then check unapproved
    if (unapprovedControls.length > 0) {
      status = constant.status.CHANGES_REQUESTED;
      end_date = null;
    }

    let riskLevel = null;

    if (status === constant.status.COMPLETED) {
      // risk assessment logic
      riskLevel = 'Low';

      // const question_id = [constant.question.CHILDREN_DATA_COLLECTION, constant.question.CROSS_BORDER_DATA_TRANSFER, constant.question.DATA_SUBJECTS_CATEGORIES, constant.question.PERSONAL_DATA, constant.question.SENSITIVE_DATA, constant.question.THIRD_PARTY_VENDORS];

      // const answers = await commonService.getListAssociate(piaAnswers, piaCustomerControls, {}, { pia_id: req.params.pia_id, question_id: { [Op.in]: question_id } }, {}, {});

      // //create a map with question_id as key and answer as value
      // const answerMap = answers?.reduce((map, answer) => {
      //     map[answer.CustomerControl.question_id] = answer.answer;
      //     return map;
      // }, {});

      // const riskAttributes = {
      //     PERSONAL_DATA: 0,
      //     SENSITIVE_DATA: 0,
      //     DATA_SUBJECTS_CATEGORIES: 0,
      //     CHILDREN_DATA_COLLECTION: 0,
      //     THIRD_PARTY_VENDORS: 0,
      //     CROSS_BORDER_DATA_TRANSFER: 0
      // }

      // for (let key in answerMap) {
      //     switch (parseInt(key)) {
      //         case constant.question.PERSONAL_DATA:
      //             riskAttributes.PERSONAL_DATA = answerMap[key].length;
      //             break;
      //         case constant.question.SENSITIVE_DATA:
      //             riskAttributes.SENSITIVE_DATA = answerMap[key].length;
      //             break;
      //         case constant.question.DATA_SUBJECTS_CATEGORIES:
      //             riskAttributes.DATA_SUBJECTS_CATEGORIES = answerMap[key].length;
      //             break;
      //         case constant.question.CHILDREN_DATA_COLLECTION:
      //             riskAttributes.CHILDREN_DATA_COLLECTION = answerMap[key];
      //             break;
      //         case constant.question.THIRD_PARTY_VENDORS:
      //             riskAttributes.THIRD_PARTY_VENDORS = answerMap[key].length;
      //             break;
      //         case constant.question.CROSS_BORDER_DATA_TRANSFER:
      //             riskAttributes.CROSS_BORDER_DATA_TRANSFER = answerMap[key].length;
      //             break;
      //     }
      // }

      // if (riskAttributes.PERSONAL_DATA > 7 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 7 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 5) {
      //     riskLevel = 'High';
      // } else if (riskAttributes.PERSONAL_DATA > 3 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 3 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 2) {
      //     riskLevel = 'Medium';
      // }

      // if (riskAttributes.CHILDREN_DATA_COLLECTION[0] === '0') {
      //     riskLevel = 'High';
      // }

      // if (riskAttributes.SENSITIVE_DATA > 1 || riskAttributes.THIRD_PARTY_VENDORS > 1) {
      //     riskLevel = 'High';
      // }

      const subject = `${keyName.assessment_name} Completed: Review Suggested Risks for Compliance Enhancement`;
      const textTemplate = 'assessment_review_submit.ejs';
      const sendDataAssignedTo = {
        assignee: `${checkASSESSMENT.AssignedTo.firstName} ${checkASSESSMENT.AssignedTo.lastName}`,
        assessmentName: assessmentName,
        reviewer: `${reviewer.firstName} ${reviewer.lastName}`,
        status: status,
        url: `${process.env.SERVER_IP}/privacy/${keyName.key}/`
      };

      sendMail(checkASSESSMENT.AssignedTo.email, sendDataAssignedTo, subject, textTemplate);

      const sendDataApprover = {
        assignee: `${checkASSESSMENT.Approver.firstName} ${checkASSESSMENT.Approver.lastName}`,
        assessmentName: assessmentName,
        reviewer: `${reviewer.firstName} ${reviewer.lastName}`,
        status: status,
        key: key,
        url: `${process.env.SERVER_IP}/privacy/${keyName.key}/`
      };

      sendMail(checkASSESSMENT.AssignedTo.email, sendDataApprover, subject, textTemplate);
    }

    const assessment = await commonService.updateData(CustomerAssessment, { status: status, end_date: end_date, risks: riskLevel }, { id: req.params.assessment_id }, dbTrans);
    if (!assessment[1]) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Create detailed audit log
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: req.params.assessment_id,
        dept_id: dept_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'SUBMIT_REVIEW',
        metadata: {
          assessmentName,
          key,
          status
        }
      },
      req,
      dbTrans
    );

    // const auditAction = `Submitted review for ${assessmentName} ${key} with status '${status}'`;

    // const auditLog = await commonService.addDetail(AuditLog, { type: 'ASSESSMENT', type_id: req.params.assessment_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ASSESSMENT_REVIEWED', data: assessment[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getAuditLog = async (req, res) => {
  try {
    const { AuditLog, User, Departments } = db.models;

    const { assessment_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    const auditCondition = { type: 'ASSESSMENT', customer_id: req.data.customer_id };
    const userCondition = {};

    if (search) {
      userCondition[Op.or] = [{ firstName: { [Op.iLike]: `%${search}%` } }, { lastName: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }];
    }

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
      if (!deptHead) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }

      const deptIds = deptHead.rows?.map(dept => dept.id);
      auditCondition.dept_id = { [Op.in]: deptIds };
    }

    if (assessment_id) {
      auditCondition.type_id = assessment_id;
    }

    const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

    if (!auditData) {
      return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    // getting name initials and added to the audit data
    auditData.rows?.map(row => {
      const name = row?.User?.firstName + ' ' + row?.User?.lastName;
      row.name = name;
      const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
      row.initials = initials;
      delete row.User;
    });

    return response.success(req, res, { msgCode: 'AUDIT_LOG_FETCHED', data: auditData }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.downloadAuditLog = async (req, res) => {
  try {
    const { AuditLog, User, Departments, Customer, Assessments, CustomerAssessment } = db.models;

    const assessment_id = req.params.assessment_id;
    const sort_by = 'createdAt',
      sort_order = 'DESC';

    const auditCondition = { type: 'ASSESSMENT', customer_id: req.data.customer_id };
    const userCondition = {};
    const order = [[sort_by, sort_order]];

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
      if (!deptHead) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }

      const deptIds = deptHead.rows?.map(dept => dept.id);
      auditCondition.dept_id = { [Op.in]: deptIds };
    }

    if (assessment_id) {
      auditCondition.type_id = assessment_id;
    }

    const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt', 'type_id'], ['firstName', 'lastName'], null, null, order);

    if (!auditData) {
      return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    // 1️⃣ Get all type_ids
    let typeIds = auditData?.rows?.map(row => row.type_id);
    typeIds = [...new Set(typeIds)];
    // 2️⃣ Get all assessment_ids
    const customerAssessments = await commonService.getListWithoutCount(CustomerAssessment, { id: { [Op.in]: typeIds } }, ['id', 'assessment_id']);
    if (customerAssessments && customerAssessments.length !== typeIds.length) {
      return response.error(req, res, { msgCode: 'CUSTOMER_ASSESSMENTS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const customerAssessmentMap = new Map(customerAssessments.map(item => [item.id, item.assessment_id]));
    // 3️⃣ Get all assessment_ uds
    let assessmentIds = customerAssessments?.map(item => item.assessment_id);
    assessmentIds = [...new Set(assessmentIds)];
    // 4️⃣ Get all assessment_names
    const assessments = await commonService.getListWithoutCount(Assessments, { id: { [Op.in]: assessmentIds } }, ['id', 'assessment_name']);
    if (assessmentIds && assessments.length !== assessmentIds.length) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const assessmentMap = new Map(assessments.map(item => [item.id, item.assessment_name]));

    auditData?.rows?.forEach(row => {
      const name = row?.User?.firstName + ' ' + row?.User?.lastName;
      row.name = name;
      const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
      row.initials = initials;
      const assessment_id = customerAssessmentMap.get(row.type_id);
      const assessment_name = assessmentMap.get(assessment_id);
      row.typeName = assessment_name || 'N/A';
      delete row.User;
    });

    // getting name initials and added to the audit data
    // await Promise.all(auditData.rows.map(async (row) => {
    //     const name = row?.User?.firstName + ' ' + row?.User?.lastName;
    //     row.name = name;
    //     const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
    //     row.initials = initials;

    //     const customerAssessment = await commonService.findByCondition(
    //         CustomerAssessment,
    //         { id: row.type_id },
    //         ['assessment_id']
    //     );

    //     if (!customerAssessment) {
    //         return response.error(req, res, { msgCode: "CUSTOMER_ASSESSMENTS_NOT_FOUND" }, httpStatus.NOT_FOUND);
    //     }

    //     const assessments = await commonService.findByCondition(
    //         Assessments,
    //         { id: customerAssessment.assessment_id },
    //         ['assessment_name']
    //     );

    //     if (!assessments) {
    //         return response.error(req, res, { msgCode: "ASSESSMENT_NOT_FOUND" }, httpStatus.NOT_FOUND);
    //     }

    //     row.typeName = assessments.assessment_name;
    //     delete row.User;
    // }));

    const excelData = auditData?.rows;

    const typeName = auditData?.rows[0]?.typeName ? auditData.rows[0].typeName : 'Unknown Type'; // get the assessment name from the first row

    // remove the unnecessary fields from the excel data using map
    excelData?.forEach(row => {
      const istTime = moment(row.createdAt).tz('Asia/Kolkata');
      const date = istTime.format('YYYY-MM-DD');
      const time = istTime.format('HH:mm:ss.SSSZ');
      row.date = date;
      row.time = time;
      delete row.createdAt;
      delete row.id;
      delete row.initials;
      delete row.action_by_id;
      delete row.typeName;
    });

    const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

    const excelFile = await generateExcelForAuditData(excelData, date, customer?.name, assessment_id, 'ASSESSMENT', typeName);

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const mailData = {
      name: `${user?.firstName} ${user?.lastName}`,
      type: 'ASSESSMENT'
    };

    sendMailWithAttach(req.data.email, mailData, 'Your Copy of ASSESSMENT Audit Log file made on GoTrust', 'audit_log_download.ejs', excelFile);

    return response.success(req, res, { msgCode: 'AUDIT_DOWNLOADED' }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getAssessmentData = async (req, res) => {
  try {
    const { Assessments, AssessmentControls, AssessmentCustomerControls, AssessmentAnswers, User, CustomerAssessment, AssessmentCategory, Customer, Departments, Processes } = db.models;
    const assessment_id = req.params.assessment_id;

    const assessment = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, ['status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks', 'assessment_id']);
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    let user;
    if (req.data?.firstName) {
      user = {
        firstName: req.data?.firstName,
        lastName: req.data?.lastName,
        email: req.data?.email
      };
    } else {
      user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    }
    // const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (assessment.status !== constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_COMPLETED' }, httpStatus.BAD_REQUEST);
    }

    const controlsAttributes = [
      [sequelize.literal(`"AssessmentCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."title" ELSE "AssessmentControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."description" ELSE "AssessmentControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN CAST("AssessmentCustomerControls"."artifact_type" AS TEXT) ELSE CAST("AssessmentControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."is_attachment" ELSE "AssessmentControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."question" ELSE "AssessmentControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."fields" ELSE "AssessmentControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."extra_input" ELSE "AssessmentControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN CAST("AssessmentCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("AssessmentControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."extra_input_fields" ELSE "AssessmentControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."question_id" IS NOT NULL THEN "AssessmentControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    const controls = await assessmentService.getControlsWithCategory(
      AssessmentCustomerControls,
      AssessmentControls,
      AssessmentAnswers,
      User,
      AssessmentCategory,
      { assessment_id: assessment_id },
      {},
      {},
      {},
      {},
      controlsAttributes,
      [],
      ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      [['question_id', 'ASC']]
    );

    for (let control of controls) {
      control.Answer = control.AssessmentAnswer;
      delete control.AssessmentAnswers;
      control.Category = control.AssessmentCategory;
      delete control.AssessmentCategory;
    }
    let parents = controls?.filter(control => control.parent_id === null);

    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const excelData = transformData(parents);

    const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    let customer;
    if (req.data?.customerName) {
      customer = {
        name: req.data.customerName
      };
    } else {
      customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    }
    // const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const keyName = await commonService.findByCondition(Assessments, { id: assessment.assessment_id }, ['key', 'assessment_name']);
    const key = keyName.key.toUpperCase();

    let deptName = '';
    let procName = '';

    if (assessment.department_id) {
      const dept = await commonService.findByCondition(Departments, { id: assessment.department_id }, ['name']);
      deptName = dept?.name;
    } else if (assessment.process_id) {
      const proc = await commonService.getDataAssociate(Processes, Departments, { id: assessment.process_id }, {}, ['name'], ['name']);
      procName = proc?.name;
      deptName = `${proc?.Department?.name} - ${procName}`;
    }

    const excelFile = await createAssessmentExcelFile(excelData, `${keyName.assessment_name}`, date, customer?.name, deptName, procName);

    sendMailWithAttach(
      req.data.email,
      {
        name: `${user?.firstName} ${user?.lastName}`,
        masterAssessment: keyName.assessment_name
      },
      `${keyName.assessment_name} ${deptName}  Successfully Completed `,
      'assessment_download.ejs',
      excelFile
    );

    return response.success(req, res, { msgCode: 'ASSESSMENT_DOWNLOADED', data: `${keyName.assessment_name} data sent via E-mail` }, httpStatus.OK);
  } catch (err) {
    console.log('assessmentDownloadError', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getAssessmentCategories = async (req, res) => {
  try {
    const { Assessments } = db.models;
    const types = Assessments.rawAttributes.type.values;
    if (!types) {
      return response.error(req, res, { msgCode: 'TYPES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const typesWithIndex = types?.map((type, index) => ({ id: index + 1, type }));
    return response.success(req, res, { msgCode: 'TYPES_FETCHED', data: typesWithIndex }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.viewtemplate = async (req, res) => {
  try {
    const { AssessmentControls, AssessmentCategory } = db.models;
    let { assessment_id, template_id } = req.query;
    if (!template_id) {
      template_id = null;
    }

    let controls = await assessmentService.getCategorieswiseControls(
      AssessmentCategory,
      AssessmentControls,
      { customer_id: req.data.customer_id, template_id: template_id, assessment_id: assessment_id },
      {},
      ['id', 'name'],
      ['id', 'title', 'description', 'fields', 'artifact_type', 'is_attachment', 'question', 'fields', 'extra_input', 'extra_input_type', 'extra_input_fields']
    );
    if (controls.length === 0) {
      controls = await assessmentService.getCategorieswiseControls(
        AssessmentCategory,
        AssessmentControls,
        { template_id: null, assessment_id: assessment_id },
        {},
        ['id', 'name'],
        ['id', 'title', 'description', 'fields', 'artifact_type', 'is_attachment', 'question', 'fields', 'extra_input', 'extra_input_type', 'extra_input_fields']
      );
    }
    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    controls = controls?.map(category => ({
      ...category,
      AssessmentControls: category.AssessmentControls.filter(control => control.title !== null)
    }));
    return response.success(req, res, { msgCode: 'TYPES_FETCHED', data: controls }, httpStatus.OK);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.assignAssessment = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerAssessment, Departments, Processes, User, Assessments } = db.models;
    let assessmentName = null;
    // let approverId = null;
    let assignedToName = null;
    let dept_id = null;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const checkAssessment = await assessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: req.body.assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkAssessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkAssessment.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkAssessment.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const keyName = await commonService.findByCondition(Assessments, { id: checkAssessment.assessment_id }, ['key', 'assessment_name']);
    if (checkAssessment.Department) {
      // approverId = checkPIA.Department.spoc_id;
      assessmentName = checkAssessment.Department.name;
      dept_id = checkAssessment.Department.id;
    } else if (checkAssessment.Process) {
      // approverId = checkPIA.Process.Department.spoc_id;
      assessmentName = checkAssessment.Process.name;
      dept_id = checkAssessment.Process.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    assignedToName = `${user.firstName} ${user.lastName}`;

    // const assigner = await commonService.findByCondition(User, {
    //     id: req.data.userId
    // }, ['firstName', 'lastName']);
    // if (!assigner) {
    //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }
    const assessment = await commonService.updateData(CustomerAssessment, { assigned_to: req.body.user_id }, { id: req.body.assessment_id }, dbTrans);
    if (!assessment[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const currentDate = moment().tz('Asia/Kolkata');
    const completionDate = moment(checkAssessment?.tentative_date);
    const daysUntilCompletion = completionDate.diff(currentDate, 'days');

    const subject = `${keyName.assessment_name} Assigned – ${assessmentName} Task Due in ${daysUntilCompletion} Days`;
    const textTemplate = 'assessment_assigned.ejs';
    const baseUrl = req.protocol + '://' + req.get('host');
    const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'https://devapi.gotrust.tech';

    const sendData = {
      assignee: `${user.firstName} ${user.lastName}`,
      assessmentName: assessmentName,
      masterAssessment: keyName.assessment_name,
      url: `${frontEndUrl}/assessment-management/task-overview/`,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`,
      daysLeft: daysUntilCompletion
    };

    sendMail(user.email, sendData, subject, textTemplate);

    // Create detailed audit log
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: req.body.assessment_id,
        dept_id: dept_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'ASSIGN',
        metadata: {
          assessmentName,
          assignedToName
        }
      },
      req,
      dbTrans
    );

    // const auditAction = `Assigned ${assessmentName} to ${assignedToName}`;

    // const auditLog = await commonService.addDetail(AuditLog, { type: 'ASSESSMENT', type_id: req.body.assessment_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ASSESSMENT_ASSIGNED', data: assessment[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.reviewerAssessment = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerAssessment, Departments, Processes, User, Assessments } = db.models;
    let assessmentName = null;
    // let approver = null;
    let reviewerName = null;
    let dept_id = null;

    const checkAssessment = await assessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: req.body.assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkAssessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkAssessment.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkAssessment.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const keyName = await commonService.findByCondition(Assessments, { id: checkAssessment.assessment_id }, ['key', 'assessment_name']);
    const key = keyName.key.toUpperCase();
    if (checkAssessment.Department) {
      // approverId = checkTIA.Department.spoc_id;
      assessmentName = checkAssessment.Department.name;
      dept_id = checkAssessment.Department.id;
    } else if (checkAssessment.Process) {
      // approverId = checkTIA.Process.Department.spoc_id;
      assessmentName = checkPIA.Process.name;
      dept_id = checkPIA.Process.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    reviewerName = `${user.firstName} ${user.lastName}`;

    // const reviewer = await commonService.findByCondition(User, {
    //     id: req.data.userId
    // }, ['firstName', 'lastName']);
    // if (!reviewer) {
    //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }
    const assessment = await commonService.updateData(CustomerAssessment, { approver: req.body.user_id }, { id: req.body.assessment_id }, dbTrans);
    if (!assessment[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const subject = `You have been assigned as Reviewer for ${assessmentName}: We Need Your Input!`;
    const textTemplate = 'assessment_reviewer.ejs';
    const sendData = {
      reviewer: `${reviewerName}`,
      assessmentName: assessmentName,
      masterAssessment: keyName.assessment_name,
      // assigner: `${assigner.firstName} ${assigner.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/${key}/`
    };

    sendMail(user.email, sendData, subject, textTemplate);

    // Create detailed audit log
    const auditLog = await auditLogger.createGeneralAuditLog(
      {
        type: 'ASSESSMENT',
        type_id: req.body.assessment_id,
        dept_id: dept_id,
        customer_id: req.data.customer_id,
        actorId: req.data.userId,
        actionType: 'ADD_REVIEWER',
        metadata: {
          assessmentName,
          reviewerName
        }
      },
      req,
      dbTrans
    );

    // const auditAction = `Added Reviewer to  ${assessmentName}  to ${reviewerName}`;

    // const auditLog = await commonService.addDetail(AuditLog, { type: 'ASSESSMENT', type_id: req.body.assessment_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ASSESSMENT_REVIEWER_ADDED', data: assessment[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.collaboratorProgress = async (req, res) => {
  try {
    const { AssessmentCategory, CustomerAssessment, AssessmentCustomerControls, AssessmentAnswers, ReviewAssessment, AssessmentCollaborator, User } = db.models;
    const assessmentId = req.params.assessment_id;

    const assessment = await commonService.findByCondition(CustomerAssessment, { id: assessmentId }, ['status']);
    if (!assessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const findCategoryAndDetails = await commonService.getDistinct(AssessmentCustomerControls, { assessment_id: assessmentId }, ['category_id'], ['category_id']);
    // console.log('------------------------------', findCategoryAndDetails);
    findCategoryAndDetails.sort((a, b) => a.category_id - b.category_id);
    if (!findCategoryAndDetails) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    let categoryProgress = [];

    for (let category of findCategoryAndDetails) {
      const category_id = category.category_id;

      let totalControls = 0;
      let answeredControls = 0;
      let childControls = [];
      let progress = 0;

      let controls = null;
      const condition = { assessment_id: assessmentId, category_id };

      if (assessment.status === constant.status.STARTED || assessment.status === constant.status.COMPLETED || assessment.status === constant.status.UNDER_REVIEW) {
        controls = await commonService.getListAssociateWithoutCount(AssessmentCustomerControls, AssessmentAnswers, condition, {}, ['id', 'parent_id'], ['id']);
        controls?.forEach(control => {
          if (control.parent_id === null) {
            totalControls++;
            if (control.AssessmentAnswer) answeredControls++;
          } else {
            childControls.push(control);
          }
        });
      } else if (assessment.status === constant.status.CHANGES_REQUESTED) {
        controls = await asssessmentService.getControlsWithAnswersAndReviews(AssessmentCustomerControls, AssessmentAnswers, ReviewAssessment, condition, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);

        const parentControls = controls.rows?.filter(
          control => control?.parent_id === null && control?.ReviewAssessment // only where review marked answer as accurate
        );
        parentControls?.forEach(control => {
          totalControls++;
          if (control.AssessmentAnswer?.updatedAt > control.ReviewAssessment?.updatedAt || (control.AssessmentAnswer && control?.ReviewAssessment?.accurate_information === 1)) {
            answeredControls++;
          }
        });

        childControls = controls.rows?.filter(
          ctrl => ctrl.parent_id !== null && ctrl.AssessmentAnswer // only include reviewed-accurate child controls
        );
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.entries(childControlsByParent)?.forEach(([parentId, childList]) => {
          const parentControl = parentControls?.find(control => control.id == parentId);
          if (parentControl && childList.every(ctrl => ctrl.AssessmentAnswer?.updatedAt > parentControl?.ReviewAssessment?.updatedAt || (ctrl?.AssessmentAnswer && ctrl?.ReviewAssessment?.accurate_information === 1))) {
            answeredControls += 1;
          }
        });
      }

      if (assessment.status !== constant.status.CHANGES_REQUESTED) {
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.values(childControlsByParent)?.forEach(childList => {
          if (childList.every(control => control.AssessmentAnswer)) {
            answeredControls += 1;
          }
        });
      }

      progress = totalControls ? parseFloat(((answeredControls / totalControls) * 100).toFixed(2)) : 0;

      const details = await commonService.getListWith3Models(AssessmentCategory, AssessmentCollaborator, User, { id: category_id }, { assessment_id: assessmentId, category_id }, {}, ['id', 'name'], {}, ['id', 'firstName', 'lastName', 'email']);
      const categoryDetails = details[0] || {};
      const collaborators =
        categoryDetails?.AssessmentCollaborators?.map(collab => ({
          id: collab.User?.id,
          name: `${collab.User?.firstName} ${collab.User?.lastName}`
        })) || [];

      categoryProgress.push({
        category_id,
        category_name: categoryDetails?.name || 'Unknown',
        progress,
        collaborators: collaborators.length ? collaborators : null
      });
    }

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: categoryProgress }, httpStatus.OK);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.automateFilledAssessment = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentControls, CustomerAssessment, AssessmentCustomerControls, AssessmentAnswers, User, AuditLog, Departments, Processes, Group } = db.models;
    const assessment_id = req.params.assessment_id;
    const filePath = req.files[0]?.path;
    const mimeType = req.files[0]?.mimetype;
    if (!filePath || !mimeType) {
      return response.error(req, res, { msgCode: 'FILE_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const assessment = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, ['status', 'assigned_to', 'approver', 'is_already_performed', 'template_id', 'department_id', 'process_id']);
    console.log('assessment-------------', assessment);
    if (!assessment) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    if (assessment.status === constant.status.YET_TO_START) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (assessment.status === constant.status.UNDER_REVIEW && assessment.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const updateAssessment = await commonService.updateData(CustomerAssessment, { is_automated: true }, { id: assessment_id }, dbTrans);
    console.log('updateAssessment-------------', updateAssessment);
    if (!updateAssessment[1]) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'FAILED_TO_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const controlsAttributes = [
      [sequelize.literal(`"AssessmentCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'assessment_id',
      'template_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."title" ELSE "AssessmentControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."description" ELSE "AssessmentControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN CAST("AssessmentCustomerControls"."artifact_type" AS TEXT) ELSE CAST("AssessmentControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."is_attachment" ELSE "AssessmentControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."question" ELSE "AssessmentControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."fields" ELSE "AssessmentControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."extra_input" ELSE "AssessmentControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN CAST("AssessmentCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("AssessmentControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN "AssessmentCustomerControls"."extra_input_fields" ELSE "AssessmentControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."is_custom" THEN null ELSE "AssessmentControl"."embedding" END`), 'embedding'],
      [sequelize.literal(`CASE WHEN "AssessmentCustomerControls"."question_id" IS NOT NULL THEN "AssessmentControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];
    const controls = await assessmentService.getControls(
      AssessmentCustomerControls,
      AssessmentControls,
      AssessmentAnswers,
      User,
      { assessment_id: assessment_id },
      {},
      {},
      {},
      controlsAttributes,
      [],
      ['id', 'answer', 'extra_answer'],
      ['id', 'firstName', 'lastName'],
      [['question_id', 'ASC']]
    );
    // console.log('controls----------------', controls);

    if (!controls) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const extractedData = await parsingService.parseFile(filePath, mimeType);
    // console.log('extractedData----------------', extractedData);

    const matchedResults = [];
    const unmatchedResults = [];
    for (const fileRow of extractedData) {
      if (fileRow.question === undefined || fileRow.question === '') continue;
      let unmatch = true;
      for (const control of controls) {
        if (control.embedding) {
          const score = cosineSimilarity(control.embedding, fileRow.embedding);
          console.log(score)
          if (score >= 0.85 && matchedResults.filter(r => r.customer_question_id === control.customer_question_id).length === 0) {
            unmatch = false;
            matchedResults.push({
              customer_question_id: control.customer_question_id,
              answer: [fileRow.answer],
              answered_by: req.data.userId,
              is_automated: true
            });

            //updating question
            const data = {
              title: control.title,
              description: control.description,
              artifact_type: 'textarea',
              is_attachment: control.is_attachment,
              question: control.question,
              fields: control.fields,
              extra_input: control.extra_input,
              extra_input_type: control.extra_input_type,
              extra_input_fields: control.extra_input_fields,
              endpoint: control.endpoint,
              is_custom: true,
              is_automated: true
            };
            const updateQuestion = await commonService.updateData(AssessmentCustomerControls, data, { id: control.customer_question_id }, dbTrans);
            console.log('updateQuestion----------------', updateQuestion);

            if (!updateQuestion[1]) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'FAILED_TO_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
            }
          }
        }
      }
      if (unmatch) {
        const data = {
          title: fileRow.question,
          category_id: null,
          customer_id: req.data.customer_id,
          artifact_type: 'textarea',
          assessment_id: assessment_id,
          is_custom: true,
          is_automated: true
        };

        const addUnmatchedControl = await commonService.addDetail(AssessmentCustomerControls, data, dbTrans);
        console.log('addUnmatchedControl----------------', addUnmatchedControl);
        if (!addUnmatchedControl) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        unmatchedResults.push({
          customer_question_id: addUnmatchedControl.id,
          answer: [fileRow.answer],
          answered_by: req.data.userId,
          is_automated: true
        });
      }
    }

    const addMatchedControls = await commonService.bulkAdd(AssessmentAnswers, matchedResults, dbTrans);
    if (!addMatchedControls) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const addUnmatchedControls = await commonService.bulkAdd(AssessmentAnswers, unmatchedResults, dbTrans);
    if (!addUnmatchedControls) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    await deleteFile(req.files[0].path);

    const assessmentDetails = await assessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let assessmentName = null;
    let dept_id = null;
    if (assessmentDetails?.Department) {
      assessmentName = assessmentDetails.Department?.name;
      dept_id = assessmentDetails.Department?.id;
    } else if (assessmentDetails?.Process) {
      assessmentName = assessmentDetails.Process?.name;
      dept_id = assessmentDetails.Process?.Department?.id;
    } else {
      const businessUnit = await commonService.findByCondition(Group, { id: assessmentDetails?.entity_id }, ['name']);
      assessmentName = businessUnit?.name;
    }

    // Get user details for audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let auditAction;
    if (assessmentDetails?.Department || assessmentDetails?.Process) {
      auditAction = `${user.firstName} ${user.lastName} automated answers upload for ${assessmentName} Assessment`;
    } else {
      auditAction = `${user.firstName} ${user.lastName} automated answers upload for Business Unit: ${assessmentName} Assessment`;
    }

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ASSESSMENT',
        type_id: assessment_id,
        action: auditAction,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id,
        dept_id
      },
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createEmbeddings = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentControls } = db.models;
    const controls = await commonService.getList(AssessmentControls, { customer_id: null, template_id: null }, ['id', 'description', 'question', 'title']);
    if (!controls) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    for (const control of controls.rows) {
      const str = control.title + ' and ' + control.description + ' and ' + control.question;
      const embedding = await generateEmbedding(str);
      if (embedding) {
        await commonService.updateData(AssessmentControls, { embedding }, { id: control.id });
      }
    }
    return response.success(req, res, { msgCode: 'EMBEDDINGS_CREATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.categorizationOfControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { AssessmentCustomerControls, AssessmentControls, CustomerAssessment, AuditLog, User, Departments, Processes, AssessmentCategory } = db.models;
    const { category, assessment_id, global, customer_question_id } = req.body;
    const checkAssessment = await commonService.findByCondition(CustomerAssessment, { id: assessment_id }, {});
    console.log('checkAssessment', checkAssessment);
    if (!checkAssessment) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const checkControl = await commonService.findByCondition(AssessmentCustomerControls, { id: customer_question_id }, {});
    console.log('checkControl', checkControl);
    if (!checkControl) {
      return response.error(req, res, { msgCode: 'CONTROL_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    let customer_id = null;

    //auditlog
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    const assessmentDetails = await assessmentService.getAssessment(CustomerAssessment, Departments, Processes, User, { id: assessment_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    console.log('assessmentDetails', assessmentDetails);
    if (!assessmentDetails) {
      return response.error(req, res, { msgCode: 'ASSESSMENT_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    let assessmentName = null;
    let dept_id = null;
    if (assessmentDetails?.Department) {
      assessmentName = assessmentDetails.Department?.name;
      dept_id = assessmentDetails.Department?.id;
    } else if (assessmentDetails?.Process) {
      assessmentName = assessmentDetails.Process?.name;
      dept_id = assessmentDetails.Process?.Department?.id;
    }

    if (global === true) {
      const str = checkControl.title + ' and ' + checkControl.description + ' and ' + checkControl.question;
      const embedding = await generateEmbedding(str);
      if (checkAssessment.template_id) {
        customer_id = checkAssessment.customer_id;
      }
      const data = {
        title: checkControl.title,
        description: checkControl.description,
        customer_id: customer_id, //it is null in master control as in ropa
        assessment_id: checkAssessment.assessment_id,
        category_id: category,
        industry_vertical_id: 1,
        artifact_type: checkControl.artifact_type,
        is_attachment: checkControl.is_attachment,
        question: checkControl.question,
        fields: checkControl.fields,
        extra_input: checkControl.extra_input,
        extra_input_type: checkControl.extra_input_type,
        extra_input_fields: checkControl.extra_input_fields,
        endpoint: checkControl.endpoint,
        template_id: checkAssessment.template_id,
        embedding: embedding
      };
      const addMasterControl = await commonService.addDetail(AssessmentControls, data, dbTrans);
      // console.log('addMasterControl----------------', addMasterControl);
      if (!addMasterControl) {
        return response.error(req, res, { msgCode: 'CONTROL_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      const updateCustomerControl = await commonService.updateData(AssessmentCustomerControls, { category_id: category }, { id: customer_question_id }, dbTrans);
      if (!updateCustomerControl[1]) {
        return response.error(req, res, { msgCode: 'CONTROL_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      //find category name
      const categoryObj = await commonService.findByCondition(AssessmentCategory, { id: category }, ['name']);
      if (!categoryObj) {
        return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      const controlTitle = checkControl?.title;
      const auditAction = `${user?.firstName} ${user?.lastName} moved the control ${controlTitle} to the ${categoryObj?.name} category as a master control in ${assessmentName} Assessment`;
      const auditLog = await commonService.addDetail(
        AuditLog,
        {
          type: 'ASSESSMENT',
          type_id: assessment_id,
          action: auditAction,
          action_by_id: req.data.userId,
          customer_id: req.data.customer_id,
          dept_id
        },
        dbTrans
      );
      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updateCustomerControl[1] }, httpStatus.OK, dbTrans);
    }
    const updateControl = await commonService.updateData(AssessmentCustomerControls, { category_id: category }, { id: customer_question_id }, dbTrans);
    console.log('updateControl----------------', updateControl);
    if (!updateControl[1]) {
      return response.error(req, res, { msgCode: 'CONTROL_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    //audit
    const categoryObj = await commonService.findByCondition(AssessmentCategory, { id: category }, ['name']);
    if (!categoryObj) {
      return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const controlTitle = checkControl?.title;
    const auditAction = `${user?.firstName} ${user?.lastName} moved the control "${controlTitle}" to the "${categoryObj?.name}" category in ${assessmentName} Assessment`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ASSESSMENT',
        type_id: assessment_id,
        action: auditAction,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id,
        dept_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_FOUND', data: checkControl }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
