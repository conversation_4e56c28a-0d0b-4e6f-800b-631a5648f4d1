const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const pdaService = require('../services/pda');
const assessmentService = require('../services/assessment');
const constant = require('../constant/PDA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createAssessmentExcelFile } = require('../utils/helper');
const moment = require('moment');



exports.getPDAList = async (req, res) => {
    try {
        const { Departments, Processes, PDA, User, pdaCollaborator } = db.models;
        const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let userType = null;
        let customerFilter = { customer_id: req.data.customer_id };
        let userFilter = {};

        let searchCondition = {};

        if (search && !search_key) {
            searchCondition = {
                [Op.or]: [
                    sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('PDA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
                    sequelize.where(sequelize.cast(sequelize.col('PDA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                ]
            };
        };

        if (search && search_key) {
            if (search_key === 'Department') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Process') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Process.name'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'AssignedTo') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Reviewer') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'SPOC') {
                searchCondition = {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.User.firstName'), { [Op.iLike]: `%${search}%` }),
                        sequelize.where(sequelize.col('Department.User.lastName'), { [Op.iLike]: `%${search}%` })
                    ]
                };
            } else if (search_key === 'Status') {
                searchCondition = {
                    status: sequelize.where(sequelize.cast(sequelize.col('PDA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            } else if (search_key === 'Risks') {
                searchCondition = {
                    risks: sequelize.where(sequelize.cast(sequelize.col('PDA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
                };
            }
        }

        const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
        const departmentIds = departments?.rows?.map(department => department.id);
        const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
        const processIds = processes?.rows?.map(process => process.id);

        if (req.data.roleName === authConstant.USER_ROLE[2]) {
            userType = 'DPO';
        } else if (departmentIds.length > 0) {
            userType = 'Department Head';
        } else {
            userType = 'Employee';
        }

        const collaboratorPda = await commonService.getList(pdaCollaborator, { user_id: req.data.userId }, ['pda_id']);
        const collaboratorPdaIds = collaboratorPda?.rows?.map(collaborator => collaborator.pda_id);

        let pdaFilter = {}

        if (userType === 'DPO') {
            pdaFilter = {};
        } else if (userType === 'Department Head') {
            pdaFilter = {
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { id: { [Op.in]: collaboratorPdaIds } },
                    { department_id: { [Op.in]: departmentIds } },
                    { process_id: { [Op.in]: processIds } }
                ]
            }
        } else {
            pdaFilter = {
                [Op.or]: [
                    { assigned_to: req.data.userId },
                    { id: { [Op.in]: collaboratorPdaIds } }
                ]
            }
        }

        const pda = await pdaService.getEmployeePDA(PDA, Departments, Processes, User, {
            [Op.and]: [
                pdaFilter,
                {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                        sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                    ]
                },
                is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
                searchCondition
            ]
        }, { ...customerFilter, group_id: req.params.entity_id }, customerFilter, userFilter, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }

        const pdaStr = {
            user_type: userType,
            rows: pda.rows.map(pdaItem => {
                if (pdaItem.Process) {
                    pdaItem.Department = pdaItem.Process.Department;
                    delete pdaItem.Process.Department;
                    delete pdaItem.Process.User;
                }

                if (pdaItem.AssignedTo) {
                    if (pdaItem.AssignedTo.id === req.data.userId) {
                        pdaItem.isAssigned = true;
                    } else {
                        pdaItem.isAssigned = false;
                    }
                    pdaItem.AssignedTo.name = `${pdaItem.AssignedTo.firstName} ${pdaItem.AssignedTo.lastName}`;
                    delete pdaItem.AssignedTo.firstName;
                    delete pdaItem.AssignedTo.lastName;
                }

                if (pdaItem.Approver) {
                    pdaItem.Approver.name = `${pdaItem.Approver.firstName} ${pdaItem.Approver.lastName}`;
                    delete pdaItem.Approver.firstName;
                    delete pdaItem.Approver.lastName;
                }

                pdaItem.SPOC = { id: pdaItem.Department.User.id, name: `${pdaItem.Department.User.firstName} ${pdaItem.Department.User.lastName}` },
                    pdaItem.isCollaborator = collaboratorPdaIds.includes(pdaItem.id);
                delete pdaItem.Department.User;

                return pdaItem;
            }),
            count: pda.count
        };

        return response.success(req, res, { msgCode: "PDA_FETCHED", data: pdaStr }, httpStatus.OK);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getDepartmentsPDA = async (req, res) => {
    try {
        const { Departments, Processes, CustomerAssessments, User, pdaCollaborator } = db.models;
        const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let pdaCondition = { [Op.not]: { department_id: null } };
        let empCheck = false;
        let roleBasedData = [];
        let filter = { customer_id: req.data.customer_id };

        if (search) {
            filter[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
            ];
        }

        // DPO can see all data, so no changes to pdaCondition
        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            // Fetch department details
            const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);

            if (departments && departments.rows.length > 0) {
                // Department head can only see their own departments
                const departmentIds = departments?.rows?.map(department => department.id);
                pdaCondition.department_id = { [Op.in]: departmentIds };
            } else {
                // Employee can only see their own PDA
                empCheck = true;
            }
        }

        if (empCheck) {
            const collaboratorPda = await commonService.getList(pdaCollaborator, { user_id: req.data.userId }, ['pda_id']);
            const collaboratorPdaIds = collaboratorPda?.rows?.map(collaborator => collaborator.pda_id);
            const pda = await pdaService.getEmployeePDA(PDA, Departments, Processes, User, {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { assigned_to: req.data.userId },
                            { id: { [Op.in]: collaboratorPdaIds } }
                        ]
                    },
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                        ]
                    }
                ]
            }, { ...filter, group_id: req.params.entity_id }, filter, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (!pda) {
                return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
            }

            roleBasedData = pda;
            roleBasedData.rows = roleBasedData.rows.map(pdaItem => {
                if (pdaItem.Process) {
                    pdaItem.department_name = pdaItem.Process.Department.name;
                    delete pdaItem.Process.Department;
                } else {
                    pdaItem.department_name = pdaItem.Department.name;
                }

                return {
                    ...pdaItem,
                    isCollaborator: collaboratorPdaIds.includes(pdaItem.id)
                };
            });
        } else {
            const pda = await pdaService.getDepartmentsPDA(CustomerAssessments, Departments, User, pdaCondition, { ...filter, group_id: req.params.entity_id }, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (!pda) {
                return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
            }
            roleBasedData = pda;

            // Add assigned PDAs
            const collaboratorPda = await commonService.getList(pdaCollaborator, { user_id: req.data.userId }, ['pda_id']);
            const collaboratorPdaIds = collaboratorPda?.rows?.map(collaborator => collaborator.pda_id);
            const assignedPda = await pdaService.getEmployeePDA(CustomerAssessments, Departments, Processes, User, {
                [Op.and]: [
                    {
                        [Op.or]: [
                            { assigned_to: req.data.userId },
                            { id: { [Op.in]: collaboratorPdaIds } }
                        ]
                    },
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                        ]
                    },
                ], status: { [Op.ne]: constant.status.COMPLETED }
            }, { ...filter, group_id: req.params.entity_id }, filter, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName'], limit, offset, order);
            if (assignedPda) {
                roleBasedData.assigned = assignedPda.rows?.map(pdaItem => {
                    if (pdaItem.Process) {
                        pdaItem.department_name = pdaItem.Process.Department.name;
                        delete pdaItem.Process.Department;
                    } else {
                        pdaItem.department_name = pdaItem.Department.name;
                    }

                    return {
                        ...pdaItem,
                        isCollaborator: collaboratorPdaIds.includes(pdaItem.id)
                    };
                });
            }
        }
        return response.success(req, res, { msgCode: "PDA_FETCHED", data: roleBasedData }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getProcessesPDA = async (req, res) => {
    try {
        const { Processes, CustomerAssessments, User } = db.models;
        const department_id = req.params.department_id;
        const pda = await pdaService.getProcessesPDA(CustomerAssessments, Processes, User, { [Op.not]: { process_id: null } }, { customer_id: req.data.customer_id, department_id: department_id }, {}, ['id', 'start_date', 'end_date', 'status', 'risks'], ['id', 'name'], ['id', 'firstName', 'lastName'], [['id', 'ASC']]);
        // const pda = await privacyService.getProcessesPDA(req.data.customer_id, department_id);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.NOT_FOUND);
        }
        return response.success(req, res, { msgCode: "PDA_FETCHED", data: pda }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.assignPDA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, Departments, Processes, AuditLog, User } = db.models;
        let pdaName = null;
        // let approverId = null;
        let assignedToName = null;
        let dept_id = null;

        if(req.data.roleName !== authConstant.USER_ROLE[2]){
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        const checkPDA = await pdaService.getPDA(CustomerAssessments, Departments, Processes, User, { id: req.body.pda_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PDA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PDA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (checkPDA.Department) {
            // approverId = checkPDA.Department.spoc_id;
            pdaName = checkPDA.Department.name;
            dept_id = checkPDA.Department.id;
        } else if (checkPDA.Process) {
            // approverId = checkPDA.Process.Department.spoc_id;
            pdaName = checkPDA.Process.name;
            dept_id = checkPDA.Process.Department.id;
        }

        const user = await commonService.findByCondition(User, {
            id: req.body.user_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        assignedToName = `${user.firstName} ${user.lastName}`;

        const assigner = await commonService.findByCondition(User, {
            id: req.data.userId
        }, ['firstName', 'lastName']);
        if (!assigner) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }
        const pda = await commonService.updateData(CustomerAssessments, { assigned_to: req.body.user_id }, { id: req.body.pda_id }, dbTrans);
        if (!pda[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const currentDate = moment().tz('Asia/Kolkata');
        const completionDate = moment(checkPDA?.tentative_date);
        const daysUntilCompletion = completionDate.diff(currentDate, 'days');

        const subject = `You have been assigned ${pdaName} assessment: We Need Your Input!`;
        const textTemplate = "pda_assigned.ejs";
        const sendData = {
            assignee: `${user.firstName} ${user.lastName}`,
            pdaName: pdaName,
            assigner: `${assigner.firstName} ${assigner.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/pda/`,
            daysLeft: daysUntilCompletion
        };

        sendMail(
            user.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Assigned ${pdaName} PDA to ${assignedToName}`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PDA', type_id: req.body.pda_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PDA_ASSIGNED", data: pda[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


exports.reviewerPDA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, Departments, Processes, AuditLog, User } = db.models;
        let pdaName = null;
        // let approver = null;
        let reviewerName = null;
        let dept_id = null;

        const checkPDA = await pdaService.getPDA(CustomerAssessments, Departments, Processes, User, { id: req.body.pda_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PDA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PDA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (checkPDA.Department) {
            // approverId = checkTIA.Department.spoc_id;
            pdaName = checkPDA.Department.name;
            dept_id = checkPDA.Department.id;
        } else if (checkPDA.Process) {
            // approverId = checkTIA.Process.Department.spoc_id;
            pdaName = checkPDA.Process.name;
            dept_id = checkPDA.Process.Department.id;
        }

        const user = await commonService.findByCondition(User, {
            id: req.body.user_id
        }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        reviewerName = `${user.firstName} ${user.lastName}`;

        // const reviewer = await commonService.findByCondition(User, {
        //     id: req.data.userId
        // }, ['firstName', 'lastName']);
        // if (!reviewer) {
        //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        // }
        const pda = await commonService.updateData(CustomerAssessments, { approver: req.body.user_id }, { id: req.body.pda_id }, dbTrans);
        if (!pda[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const subject = `You have been assigned as Reviewer for ${pdaName} - PDA : We Need Your Input!`;
        const textTemplate = "pda_reviewer.ejs";
        const sendData = {
            reviewer: `${reviewerName}`,
            pdaName: pdaName,
            // assigner: `${assigner.firstName} ${assigner.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/pda/`,
        };

        sendMail(
            user.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Added Reviewer to  ${pdaName} PIA to ${reviewerName}`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PDA', type_id: req.body.pda_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PIA_REVIEWER_ADDED", data: pda[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}




exports.startPDA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, pdaControls, pdaCustomerControls, pdaCollaborator, AuditLog, Departments, Processes, User } = db.models;

        const checkPDA = await pdaService.getPDA(CustomerAssessments, Departments, Processes, User, { id: req.params.pda_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.status === constant.status.STARTED || checkPDA.status === constant.status.CHANGES_REQUESTED) {
            if (checkPDA.assigned_to && checkPDA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                const collaborator = await commonService.findByCondition(pdaCollaborator, { pda_id: req.params.pda_id, user_id: req.data.userId }, ['id']);
                if (!collaborator) {
                    return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED, dbTrans);
                }
            }
            return response.success(req, res, { msgCode: "PDA_STARTED" }, httpStatus.OK, dbTrans);
        } else if (checkPDA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PDA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PDA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let pdaName = null;
        let dept_id = null;
        let approverId = null;

        if (checkPDA.Department) {
            pdaName = checkPDA.Department.name;
            dept_id = checkPDA.Department.id;
            approverId = checkPDA.Department.spoc_id;
        } else if (checkPDA.Process) {
            pdaName = checkPDA.Process.name;
            dept_id = checkPDA.Process.Department.id;
            approverId = checkPDA.Process.Department.spoc_id;
        }

        if (checkPDA.assigned_to && checkPDA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(pdaCollaborator, { pda_id: req.params.pda_id, user_id: req.data.userId }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED, dbTrans);
            }
        }

        const pda = await commonService.updateData(CustomerAssessments, { status: constant.status.STARTED, start_date: Date() }, { id: req.params.pda_id }, dbTrans);
        if (!pda[1]) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let controls = await commonService.getList(pdaControls, { industry_vertical_id: 1, customer_id: req.data.customer_id, }, ['id', 'category_id', 'parent_id', 'customer_id']);
        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (controls.count == 0) {
            controls = await commonService.getList(pdaControls, { industry_vertical_id: 1, customer_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
        }


        const parentControls = controls?.rows?.filter(control => control.parent_id === null);
        const childControls = controls?.rows?.filter(control => control.parent_id !== null);

        const customerControlsParents = parentControls?.map(control => {
            return {
                question_id: control.id,
                customer_id: control.customer_id,
                category_id: control.category_id,
                pda_id: Number(req.params.pda_id),
                is_custom: false
            }
        });

        const newCustomerControlsParents = await commonService.bulkAdd(pdaCustomerControls, customerControlsParents, dbTrans);
        if (!newCustomerControlsParents) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
            map[parentControls[index].id] = control.id;
            return map;
        }, {});

        const customerControlsChildren = childControls?.map(control => {
            return {
                question_id: control.id,
                customer_id: control.customer_id,
                category_id: control.category_id,
                pda_id: req.params.pda_id,
                parent_id: parentIdMap[control.parent_id],
                is_custom: false
            }
        });

        const newCustomerControlsChildren = await commonService.bulkAdd(pdaCustomerControls, customerControlsChildren, dbTrans);
        if (!newCustomerControlsChildren) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditAction = `${user?.firstName} ${user?.lastName} started ${pdaName} PDA`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PDA', type_id: req.params.pda_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PDA_STARTED", data: pda[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getProgress = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaCustomerControls, pdaAnswers, CustomerAssessments, ReviewPDA, pdaCollaborator } = db.models;
        const pda_id = req.params.pda_id;

        const pda = await commonService.findByCondition(CustomerAssessments, { id: pda_id }, ['status']);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
        }

        const status = pda.status;
        let controls = null;
        let totalControls = 0;
        let answeredControls = 0;
        let childControls = [];
        let progress = 0;

        if (pda.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && pda.approver !== req.data.userId) {
            const collaborator = await commonService.getList(pdaCollaborator, { pda_id: pda_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED ,dbTrans);
            }

            const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

            controls = await commonService.getListAssociateWithoutCount(pdaCustomerControls, pdaAnswers, { pda_id: pda_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
            }

            // totalControls = controls.filter(control => control.parent_id === null).length;
            // answeredControls = controls.filter(control => control.parent_id === null && control.pdaAnswer).length;
            // childControls = controls.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.pdaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });

            const childControlsByParent = childControls.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.pdaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });

            progress = (answeredControls / totalControls) * 100;
            progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

            return response.success(req, res, { msgCode: "PROGRESS_FETCHED", data: { totalControls, answeredControls, progress } }, httpStatus.OK ,dbTrans);
        }

        if (status === constant.status.STARTED) {
            controls = await commonService.getListAssociateWithoutCount(pdaCustomerControls, pdaAnswers, { pda_id: pda_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.parent_id === null && control.pdaAnswer).length;
            // childControls = controls?.filter(control => control.parent_id !== null);
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.pdaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.pdaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.UNDER_REVIEW) {
            controls = await commonService.getListAssociateWithoutCountWithAlias(pdaCustomerControls, ReviewPDA, 'ReviewPDA', { pda_id: pda_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
            }

            // totalControls = controls?.filter(control => control.parent_id === null).length;
            // answeredControls = controls?.filter(control => control.ReviewPDA).length;
            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                }
                if (control.ReviewPDA) {
                    answeredControls++;
                }
            });
            progress = (answeredControls / totalControls) * 100;
        } else if (status === constant.status.CHANGES_REQUESTED) {
            controls = await pdaService.getControlsWithAnswersAndReviews(pdaCustomerControls, pdaAnswers, ReviewPDA, { pda_id: pda_id }, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND ,dbTrans);
            }
            // totalControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewPDA && control.ReviewPDA.accurate_information === 0).length;
            // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.pdaAnswer?.updatedAt > control.ReviewPDA?.updatedAt).length;
            // childControls = controls.rows?.filter(control => control.parent_id !== null);
            controls.rows?.forEach(control => {
                if (control.parent_id === null) {
                    if (control.ReviewPDA && control.ReviewPDA.accurate_information === 0) {
                        totalControls++;
                    }
                    if (control.pdaAnswer?.updatedAt > control.ReviewPDA?.updatedAt && control.ReviewPDA.accurate_information === 0) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls?.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});
            Object.entries(childControlsByParent)?.forEach(([parentId, childControls]) => {
                const parentControl = controls.rows.find(control => control.id == parentId);
                if (parentControl && childControls.every(control => control.pdaAnswer.updatedAt > parentControl.ReviewPDA.updatedAt)) {
                    answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewPDA
                }
            });
            progress = (answeredControls / totalControls) * 100;
        }else if (status === constant.status.COMPLETED) {
            controls = await commonService.getListAssociateWithoutCount(pdaCustomerControls, pdaAnswers, { pda_id: pda_id }, {}, ['id', 'parent_id'], ['id']);
            if (!controls) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND , dbTrans);
            }

            // totalControls = controls.filter(control => control.parent_id === null).length;
            // answeredControls = controls.filter(control => control.parent_id === null && control.pdaAnswer).length;
            // childControls = controls.filter(control => control.parent_id !== null);

            controls?.forEach(control => {
                if (control.parent_id === null) {
                    totalControls++;
                    if (control.pdaAnswer) {
                        answeredControls++;
                    }
                } else {
                    childControls.push(control);
                }
            });
            const childControlsByParent = childControls.reduce((acc, control) => {
                if (!acc[control.parent_id]) {
                    acc[control.parent_id] = [];
                }
                acc[control.parent_id].push(control);
                return acc;
            }, {});

            Object.values(childControlsByParent)?.forEach(childControls => {
                if (childControls.every(control => control.pdaAnswer)) {
                    answeredControls += 1; // Increment if all child controls of this parent are answered
                }
            });
            progress = (answeredControls / totalControls) * 100;
            
        }

        progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

        const updateProgress = await commonService.updateData(CustomerAssessments ,{ progress : progress} , {id: pda_id} , dbTrans);
        if (!updateProgress) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans );
        }

        progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

        return response.success(req, res, { msgCode: "PROGRESS_FETCHED", data: { totalControls, answeredControls, progress } }, httpStatus.OK , dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


exports.getCategories = async (req, res) => {
    try {
        const { pdaCategory, pdaCollaborator, CustomerAssessments, pdaCustomerControls, ReviewPDA } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let pdaLevel = req.params.pda_level;
        const pda_id = req.query.pda_id;
        pdaLevel = pdaLevel.charAt(0).toUpperCase() + pdaLevel.slice(1);

        let categoryCondition = { pda_level: pdaLevel };
        let conditions = [];

        const pda = await commonService.findByCondition(CustomerAssessments, { id: pda_id }, ['status', 'assigned_to', 'approver']);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pda.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && pda.approver !== req.data.userId) {
            const collaborators = await commonService.getList(pdaCollaborator, { pda_id: pda_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborators) {
                return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
            // categoryCondition.id = { [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) };
            conditions.push({ [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) });
        }

        if (pda.status === constant.status.CHANGES_REQUESTED) {
            const changeReqCategories = await commonService.getListAssociateWithAlias(pdaCustomerControls, ReviewPDA, 'ReviewPDA', { pda_id: pda_id }, { accurate_information: 0 }, ['category_id']);
            if (!changeReqCategories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
            // categoryCondition.id = { [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) };
            conditions.push({ [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) });
        }else {
            categoryCondition.customer_id = req.data.customer_id;
        }
        
        if (conditions.length > 0) {
            categoryCondition.id = {
                [Op.and]: conditions
            };
        }


        let categories = await commonService.getList(pdaCategory, categoryCondition, ['id', 'name'], limit , offset ,order);
        if (!categories) {
            return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if(categories.count==0){
            categoryCondition.customer_id = null;
            categories = await commonService.getList(pdaCategory, categoryCondition, ['id', 'name'] , limit , offset ,order);
            if (!categories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
        }

        return response.success(req, res, { msgCode: "CATEGORIES_FETCHED", data: categories }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getControls = async (req, res) => {
    try {
        const { pdaControls, pdaCustomerControls, pdaAnswers, User, CustomerAssessments, pdaCollaborator, ReviewPDA } = db.models;
        const pda_id = req.params.pda_id;
        const category_id = req.query.category_id;

        const pda = await commonService.findByCondition(CustomerAssessments, { id: pda_id }, ['status', 'assigned_to', 'approver']);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pda.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PDA_NOT_STARTED" }, httpStatus.BAD_REQUEST);
        }

        if (pda.assigned_to !== req.data.userId && pda.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(pdaCollaborator, { pda_id: pda_id, user_id: req.data.userId, category_id: category_id }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "PDA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
        }

        let controls;
        const controlsAttributes = [
            [
                sequelize.literal(`"pdaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."title" ELSE "pdaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."description" ELSE "pdaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN CAST("pdaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("pdaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."is_attachment" ELSE "pdaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."question" ELSE "pdaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."fields" ELSE "pdaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."extra_input" ELSE "pdaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN CAST("pdaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("pdaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."extra_input_fields" ELSE "pdaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."question_id" IS NOT NULL THEN "pdaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        if (pda.status === constant.status.UNDER_REVIEW || pda.status === constant.status.CHANGES_REQUESTED || pda.status === constant.status.COMPLETED) {
            if (pda.status === constant.status.UNDER_REVIEW && (pda.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2])) {
                return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
            }
            controls = await pdaService.getControlsWithReview(pdaCustomerControls, pdaControls, pdaAnswers, User, ReviewPDA, { pda_id: pda_id, category_id: category_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'accurate_information', 'comments'], [['question_id', 'ASC']]);
        } else {
            controls = await pdaService.getControls(pdaCustomerControls, pdaControls, pdaAnswers, User, { pda_id: pda_id, category_id: category_id }, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], [['question_id', 'ASC']]);
        }

        if (!controls) {
            return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        for (let control of controls) {
            control.Answer = control.pdaAnswer;
            delete control.pdaAnswer;
            control.Review = control.ReviewPDA;
            delete control.ReviewPDA;
            if (control.Answer) {
                control.answered = true;
                if (control.Answer.extra_answer) {
                    control.Answer.extra_answered = true;
                } else {
                    control.Answer.extra_answered = false;
                }
            } else {
                control.answered = false;
            }

            if (control.Review) {
                control.reviewed = true;
            } else {
                control.reviewed = false;
            }

            if (pda.assigned_to !== req.data.userId && pda.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
                control.is_collaborator = true;
            } else {
                control.is_collaborator = false;
            }
        }

        let parents = controls?.filter(control => control.parent_id === null);
        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        if (pda.status === constant.status.CHANGES_REQUESTED) {
            parents = parents?.filter(parent => parent.Review?.accurate_information === 0);
        }

        return response.success(req, res, { msgCode: "CONTROLS_FETCHED", data: { status: pda.status, controls: parents } }, httpStatus.OK);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.getArtifactTypes = async (req, res) => {
    try {
        const { pdaControls } = db.models;
        const artifactTypes = pdaControls.rawAttributes.artifact_type.values;
        if (!artifactTypes) {
            return response.error(req, res, { msgCode: "ARTIFACT_TYPES_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        return response.success(req, res, { msgCode: "ARTIFACT_TYPES_FETCHED", data: artifactTypes }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.addCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaCustomerControls } = db.models;
        req.body.is_custom = true;

        const addedControls = await commonService.addDetail(pdaCustomerControls, req.body, dbTrans);
        if (!addedControls) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_CREATED", data: addedControls }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.updateControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaControls, pdaCustomerControls } = db.models;
        const { title, description, artifact_type, question, fields, is_attachment } = req.body;

        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        let raw_question = null;
        const originalQuestion = await commonService.getDataAssociate(pdaControls, pdaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (originalQuestion) {
            raw_question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(pdaCustomerControls, { id: req.params.customer_control_id }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            raw_question = customQuestion;
        }

        const updatedValues = {
            title: title || raw_question.title,
            description: description || raw_question.description,
            artifact_type: artifact_type || raw_question.artifact_type,
            is_attachment: is_attachment || raw_question.is_attachment,
            question: question || raw_question.question,
            fields: fields || raw_question.fields,
            is_custom: true
        }

        const updatedControls = await commonService.updateData(pdaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.updateFields = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaControls, pdaCustomerControls } = db.models;
        const { fields } = req.body;
        let question = null;

        const originalQuestion = await commonService.getDataAssociate(pdaControls, pdaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (originalQuestion) {
            question = originalQuestion;
        } else {
            const customQuestion = await commonService.findByCondition(pdaCustomerControls, { id: req.params.customer_control_id }, {});
            if (!customQuestion) {
                return response.error(req, res, { msgCode: "CONTROLS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            question = customQuestion;
        }

        const updatedValues = {
            title: question.title,
            description: question.description,
            artifact_type: question.artifact_type,
            is_attachment: question.is_attachment,
            question: question.question,
            fields: fields || question.fields,
            is_custom: true
        }

        const updatedControls = await commonService.updateData(pdaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
        if (!updatedControls[1]) {
            return response.error(req, res, { msgCode: "ERROR_UPDATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_UPDATED", data: updatedControls[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.deleteCustomControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaCustomerControls } = db.models;

        const deletedControls = await commonService.deleteQuery(pdaCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, dbTrans);
        if (!deletedControls) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CONTROL_DELETED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getpdaCollaborators = async (req, res) => {
    try {
        const { pdaCollaborator, User, CustomerAssessments, pdaCategory } = db.models;
        const pda_id = req.params.pda_id;

        const pda = await pdaService.getPDAWithAssignee(CustomerAssessments, User, { id: pda_id }, {}, ['status', 'assigned_to', 'approver'], ['firstName', 'lastName']);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (pda.assigned_to !== req.data.userId && pda.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED);
        }

        const collaborators = await pdaService.getpdaCollaborators(pdaCategory, pdaCollaborator, User, {}, { pda_id: pda_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
        if (!collaborators) {
            return response.error(req, res, { msgCode: "COLLABORATORS_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        collaborators?.forEach(collaborator => {
            collaborator.Collaborators = collaborator.pdaCollaborators;
            delete collaborator.pdaCollaborators;
        });

        const assignee = `${pda.AssignedTo?.firstName} ${pda.AssignedTo?.lastName}`;

        return response.success(req, res, { msgCode: "COLLABORATORS_FETCHED", data: { assignee: assignee, collaborators } }, httpStatus.OK);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.addpdaCollaborator = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaCollaborator, AuditLog, CustomerAssessments, Departments, Processes, User, pdaCategory } = db.models;

        const checkPDA = await pdaService.getPDA(CustomerAssessments, Departments, Processes, User, { id: req.body.pda_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.assigned_to !== req.data.userId && checkPDA.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            return response.error(req, res, { msgCode: "UNAUTHORIZED" }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        const userList = req.body.collaborators?.flatMap(collaborator => collaborator.users.map(user => user.id));

        const users = await commonService.getListWithoutCount(User, {
            id: { [Op.in]: userList }
        }, ['id', 'firstName', 'lastName', 'email']);
        if (!users) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const categories = await commonService.getListWithoutCount(pdaCategory, {
            id: { [Op.in]: req.body.collaborators?.map(collaborator => collaborator.category_id) }
        }, ['id', 'name']);
        if (!categories) {
            return response.error(req, res, { msgCode: "CATEGORY_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!invitee) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        let collaboratorData = [];
        const auditData = [];
        let pdaName = null;
        let dept_id = null;
        if (checkPDA.Department) {
            pdaName = checkPDA.Department.name;
            dept_id = checkPDA.Department.id;
        } else if (checkPDA.Process) {
            pdaName = checkPDA.Process.name;
            dept_id = checkPDA.Process.Department.id;
        }

        // const names = [];
        for (let collaborator of req.body.collaborators) {
            const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

            for (let collaboratingUser of collaborator.users) {
                const user = users?.find(user => user.id === collaboratingUser.id);
                let userName = `${user?.firstName} ${user?.lastName}`;
                // names.push(userName);

                if (collaboratingUser.action === 'add') {
                    collaboratorData.push({
                        pda_id: req.body.pda_id,
                        user_id: collaboratingUser.id,
                        category_id: collaborator.category_id
                    });

                    auditData.push({
                        type: 'PDA',
                        type_id: req.body.pda_id,
                        action: `Added ${userName} as a collaborator for ${pdaName} PDA under ${categoryName} category`,
                        action_by_id: req.data.userId,
                        dept_id: dept_id,
                        customer_id: req.data.customer_id
                    });

                    const currentDate = moment().tz('Asia/Kolkata');
                    const completionDate = moment(checkPDA?.tentative_date);
                    const daysUntilCompletion = completionDate.diff(currentDate, 'days');

                    //send mail
                    const subject = `Collaboration Request: Assistance Needed with PDA in ${categoryName}`;
                    const textTemplate = "pda_collaborator.ejs";
                    const sendData = {
                        collaboratorName: userName,
                        inviteeName: `${invitee.firstName} ${invitee.lastName}`,
                        pdaName: pdaName,
                        categoryName: categoryName,
                        url: `${process.env.SERVER_IP}/privacy/pda/`,
                        daysLeft: daysUntilCompletion
                    };

                    sendMail(
                        user.email,
                        sendData,
                        subject,
                        textTemplate,
                    );
                } else if (collaboratingUser.action === 'remove') {
                    const oldpdaCollaborator = await commonService.deleteQuery(pdaCollaborator, { pda_id: req.body.pda_id, user_id: collaboratingUser.id, category_id: collaborator.category_id }, dbTrans, true);
                    if (!oldpdaCollaborator) {
                        return response.error(req, res, { msgCode: "ERROR_DELETING_COLLABORATOR" }, httpStatus.BAD_REQUEST, dbTrans);
                    }

                    auditData.push({
                        type: 'PDA',
                        type_id: req.body.pda_id,
                        action: `Removed ${userName} as a collaborator for ${pdaName} PDA under ${categoryName} category`,
                        action_by_id: req.data.userId,
                        dept_id: dept_id,
                        customer_id: req.data.customer_id
                    });
                }
            }
        }

        const newpdaCollaborators = await commonService.bulkAdd(pdaCollaborator, collaboratorData, dbTrans);
        if (!newpdaCollaborators) {
            return response.error(req, res, { msgCode: "ERROR_ADDING_COLLABORATOR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "COLLABORATOR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.createOrUpdateAnswers = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaAnswers, CustomerAssessments } = db.models;
        const answers = req.body.answers;
        const pda_id = req.body.pda_id;

        const checkPDA = await commonService.findByCondition(CustomerAssessments, { id: pda_id }, ['status']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PDA_NOT_STARTED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PDA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PDA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Separate answers into two arrays based on the type
        const addAnswers = answers?.filter(answer => answer.type === 'add');
        const updateAnswers = answers?.filter(answer => answer.type === 'update');

        // Add 'answered_by' field to all answers
        addAnswers?.forEach(answer => answer.answered_by = req.data.userId);
        updateAnswers?.forEach(answer => answer.answered_by = req.data.userId);

        // Bulk add or update answers
        if (addAnswers.length > 0) {
            const addNewAnswers = await commonService.bulkAdd(pdaAnswers, addAnswers, dbTrans);
            if (!addNewAnswers) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        if (updateAnswers.length > 0) {
            for (let answer of updateAnswers) {
                const updateAnswers = await commonService.updateData(pdaAnswers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
                if (!updateAnswers[1]) {
                    return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

        return response.success(req, res, { msgCode: "ANSWER_CREATED_OR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
};

exports.submitPDA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, AuditLog, Departments, Processes, User, pdaAnswers, pdaCustomerControls } = db.models;
        const checkPDA = await pdaService.getPDA(CustomerAssessments, Departments, Processes, User, { id: req.params.pda_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "PDA_NOT_STARTED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PDA_COMPLETED" }, httpStatus.BAD_REQUEST, dbTrans);
        } else if (checkPDA.status === constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PDA_UNDER_REVIEW" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const checkAnswerStatus = await commonService.getListAssociateWithoutCount(pdaCustomerControls, pdaAnswers, { pda_id: req.params.pda_id }, {}, [[sequelize.literal(`"pdaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], {});

        const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
        const childrenMap = checkAnswerStatus?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });

        const unansweredQuestions = parents?.reduce((acc, parent) => {
            if (parent.children.length > 0) {
                parent.children?.forEach(child => {
                    if (child.pdaAnswer === null) {
                        acc.push({ customer_question_id: child.customer_question_id });
                    }
                });
            } else if (parent.pdaAnswer === null) {
                acc.push({ customer_question_id: parent.customer_question_id });
            }
            return acc;
        }, []);

        if (unansweredQuestions.length > 0) {
            return response.error(req, res, { msgCode: "ALL_NOT_ANSWERED", data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const pda = await commonService.updateData(CustomerAssessments, { status: constant.status.UNDER_REVIEW }, { id: req.params.pda_id }, dbTrans);
        if (!pda[1]) {
            return response.error(req, res, { msgCode: "UPDATE_ERROR" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let pdaName = null;
        let dept_id = null;

        if (checkPDA.Department) {
            pdaName = checkPDA.Department.name;
            dept_id = checkPDA.Department.id;
        } else if (checkPDA.Process) {
            pdaName = checkPDA.Process.name;
            dept_id = checkPDA.Process.Department.id;
        }


        const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
        if (!submitter) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const submitterName = `${submitter.firstName} ${submitter.lastName}`;

        const subject = `Request for Review: Legitimate Interests Assessment (PDA) - ${pdaName} Submission by ${submitterName}`;
        const textTemplate = "pda_submit.ejs";
        const sendData = {
            assignee: submitterName,
            pdaName: pdaName,
            reviewer: `${checkPDA.Approver?.firstName} ${checkPDA.Approver?.lastName}`,
            url: `${process.env.SERVER_IP}/privacy/pda/`,
        };

        sendMail(
            checkPDA.Approver.email,
            sendData,
            subject,
            textTemplate,
        );

        const auditAction = `Submitted ${pdaName} PDA for review`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PDA', type_id: req.params.pda_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PDA_SUBMITTED", data: pda[1] }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.reviewPDA = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { ReviewPDA } = db.models;
        const reviews = req.body.reviews;

        // Separate answers into two arrays based on the type
        const addReviews = reviews?.filter(answer => answer.type === 'add');
        const updateReviews = reviews?.filter(answer => answer.type === 'update');

        // Add 'areviewer_id' field to all answers
        addReviews?.forEach(review => review.reviewer_id = req.data.userId);
        updateReviews?.forEach(review => review.reviewer_id = req.data.userId);

        // Bulk add or update reviews
        if (addReviews.length > 0) {
            const addNewReviews = await commonService.bulkAdd(ReviewPDA, addReviews, dbTrans);
            if (!addNewReviews) {
                return response.error(req, res, { msgCode: "ERROR_CREATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
            }
        }
        if (updateReviews.length > 0) {
            for (let review of updateReviews) {
                const updateReviews = await commonService.updateData(ReviewPDA, review, { customer_question_id: review.customer_question_id }, dbTrans);
                if (!updateReviews[1]) {
                    return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }
        return response.success(req, res, { msgCode: "REVIEW_CREATED_OR_UPDATED" }, httpStatus.OK, dbTrans);
    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.submitReview = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { CustomerAssessments, pdaCustomerControls, pdaAnswers, AuditLog, Departments, Processes, User, ReviewPDA } = db.models;
        const checkPDA = await pdaService.getPDA(CustomerAssessments, Departments, Processes, User, { id: req.params.pda_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
        if (!checkPDA) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        if (checkPDA.status !== constant.status.UNDER_REVIEW) {
            return response.error(req, res, { msgCode: "PDA_NOT_SUBMITTED" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let pdaName = null;
        let dept_id = null;

        if (checkPDA.Department) {
            pdaName = checkPDA.Department.name;
            dept_id = checkPDA.Department.id;
        } else if (checkPDA.Process) {
            pdaName = checkPDA.Process.name;
            dept_id = checkPDA.Process.Department.id;
        }

        let status = constant.status.COMPLETED;
        let end_date = Date();
        const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(pdaCustomerControls, ReviewPDA, 'ReviewPDA', { pda_id: req.params.pda_id }, {}, [[sequelize.literal(`"pdaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], ['accurate_information']);

        const unreviewedControls = checkReviewStatus?.filter(review => review.ReviewPDA === null && review.parent_id === null);
        if (unreviewedControls.length > 0) {
            return response.error(req, res, { msgCode: "ALL_NOT_REVIEWED", data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const unapprovedControls = checkReviewStatus?.filter(review => review.ReviewPDA?.accurate_information === 0);
        if (unapprovedControls.length > 0) {
            status = constant.status.CHANGES_REQUESTED;
            end_date = null;
        }

        let riskLevel = null;

        if (status === constant.status.COMPLETED) {
            // risk assessment logic
            riskLevel = 'Low';

            // const question_id = [constant.question.CHILDREN_DATA_COLLECTION, constant.question.CROSS_BORDER_DATA_TRANSFER, constant.question.DATA_SUBJECTS_CATEGORIES, constant.question.PERSONAL_DATA, constant.question.SENSITIVE_DATA, constant.question.THIRD_PARTY_VENDORS];

            // const answers = await commonService.getListAssociate(pdaAnswers, pdaCustomerControls, {}, { pda_id: req.params.pda_id, question_id: { [Op.in]: question_id } }, {}, {});

            // //create a map with question_id as key and answer as value
            // const answerMap = answers?.reduce((map, answer) => {
            //     map[answer.CustomerControl.question_id] = answer.answer;
            //     return map;
            // }, {});

            // const riskAttributes = {
            //     PERSONAL_DATA: 0,
            //     SENSITIVE_DATA: 0,
            //     DATA_SUBJECTS_CATEGORIES: 0,
            //     CHILDREN_DATA_COLLECTION: 0,
            //     THIRD_PARTY_VENDORS: 0,
            //     CROSS_BORDER_DATA_TRANSFER: 0
            // }

            // for (let key in answerMap) {
            //     switch (parseInt(key)) {
            //         case constant.question.PERSONAL_DATA:
            //             riskAttributes.PERSONAL_DATA = answerMap[key].length;
            //             break;
            //         case constant.question.SENSITIVE_DATA:
            //             riskAttributes.SENSITIVE_DATA = answerMap[key].length;
            //             break;
            //         case constant.question.DATA_SUBJECTS_CATEGORIES:
            //             riskAttributes.DATA_SUBJECTS_CATEGORIES = answerMap[key].length;
            //             break;
            //         case constant.question.CHILDREN_DATA_COLLECTION:
            //             riskAttributes.CHILDREN_DATA_COLLECTION = answerMap[key];
            //             break;
            //         case constant.question.THIRD_PARTY_VENDORS:
            //             riskAttributes.THIRD_PARTY_VENDORS = answerMap[key].length;
            //             break;
            //         case constant.question.CROSS_BORDER_DATA_TRANSFER:
            //             riskAttributes.CROSS_BORDER_DATA_TRANSFER = answerMap[key].length;
            //             break;
            //     }
            // }

            // if (riskAttributes.PERSONAL_DATA > 7 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 7 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 5) {
            //     riskLevel = 'High';
            // } else if (riskAttributes.PERSONAL_DATA > 3 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 3 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 2) {
            //     riskLevel = 'Medium';
            // }

            // if (riskAttributes.CHILDREN_DATA_COLLECTION[0] === '0') {
            //     riskLevel = 'High';
            // }

            // if (riskAttributes.SENSITIVE_DATA > 1 || riskAttributes.THIRD_PARTY_VENDORS > 1) {
            //     riskLevel = 'High';
            // }

            const subject = `PDA Completed: Review Suggested Risks for Comppdance Enhancement`;
            const textTemplate = "pda_review_submit.ejs";
            const sendDataAssignedTo = {
                recipient: `${checkPDA.AssignedTo.firstName} ${checkPDA.AssignedTo.lastName}`,
                pdaName: pdaName,
                url: `${process.env.SERVER_IP}/privacy/pda/`
            };

            sendMail(
                checkPDA.AssignedTo.email,
                sendDataAssignedTo,
                subject,
                textTemplate,
            );

            const sendDataApprover = {
                assignee: `${checkPDA.Approver.firstName} ${checkPDA.Approver.lastName}`,
                pdaName: pdaName,
                url: `${process.env.SERVER_IP}/privacy/pda/`
            };

            sendMail(
                checkPDA.AssignedTo.email,
                sendDataApprover,
                subject,
                textTemplate,
            );
        }

        const pda = await commonService.updateData(CustomerAssessments, { status: status, end_date: end_date, risks: riskLevel }, { id: req.params.pda_id }, dbTrans);
        if (!pda[1]) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
        }

        const auditAction = `Submitted review for ${pdaName} PDA with status '${status}'`;

        const auditLog = await commonService.addDetail(AuditLog, { type: 'PDA', type_id: req.params.pda_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
        if (!auditLog) {
            return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "PDA_REVIEWED", data: pda[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        console.log('error', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}

exports.getAuditLog = async (req, res) => {
    try {
        const { AuditLog, User, Departments } = db.models;

        const { pda_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        const auditCondition = { type: 'PDA', customer_id: req.data.customer_id };
        const userCondition = {};

        if (search) {
            userCondition[Op.or] = [
                { firstName: { [Op.iLike]: `%${search}%` } },
                { lastName: { [Op.iLike]: `%${search}%` } },
                { email: { [Op.iLike]: `%${search}%` } }
            ];
        }

        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
            const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
            if (!deptHead) {
                return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
            }

            const deptIds = deptHead.rows?.map(dept => dept.id);
            auditCondition.dept_id = { [Op.in]: deptIds };
        }

        if (pda_id) {
            auditCondition.type_id = pda_id;
        }

        const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // getting name initials and added to the audit data
        auditData.rows?.map(row => {
            const name = row?.User?.firstName + ' ' + row?.User?.lastName;
            row.name = name;
            const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
            row.initials = initials;
            delete row.User;
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.uploadControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { pdaControls, pdaCategory ,Assessments, CustomerAssessments, pdaCustomerControls} = db.models;
        const controls = [];
        let parentId = null;
        const childControlsData = [];
        const uniqueCategories = new Map(); // Map to store unique categories
        const artifactTypeMapping = {
            'radio': 'radio',
            'dropdown': 'select',
            'table': 'table',
            'text box': 'textarea',
            'upload attachment': 'attachment'
        };

        // const checkPda =  await commonService.getList(pdaControls, { customer_id: req.data.customer_id }, {} );
        // if(checkPda.rows[0]){
        //     deleteFile(req.files[0].path);
        //     return response.error(req, res, { msgCode: "ALREADY_EXIST_TEMPLATE" }, httpStatus.BAD_REQUEST, dbTrans);
        // }


        const transaction = await db.transaction();
        try{
            const assessmentTypeId = await commonService.getList(Assessments,{key:'pda'});
            const listCustomerAssessment = await commonService.getList(CustomerAssessments,{customer_id:req.data.customer_id, assessment_id:assessmentTypeId.rows[0].id},['id','status']);
            const pdaIds =  listCustomerAssessment?.rows?.map(item => item.id);

            const checkPdaControls = await commonService.getList(pdaCustomerControls,{ pda_id: { [Op.in]: pdaIds }});
            if (checkPdaControls?.rows?.length > 0) {
                await commonService.deleteQuery(pdaCustomerControls, { pda_id: { [Op.in]: pdaIds }}, transaction ,true);
            }

            const checkControls = await commonService.getList(pdaControls, { customer_id: req.data.customer_id });

            if (checkControls?.rows?.length > 0) {
                await commonService.deleteQuery(pdaControls, { customer_id: req.data.customer_id }, transaction ,true);
            }

            const checkCategory = await commonService.getList(pdaCategory, { customer_id: req.data.customer_id });
            if (checkCategory?.rows?.length > 0) {
                await commonService.deleteQuery(pdaCategory, { customer_id: req.data.customer_id }, transaction ,true);
            }

            for (const { id, status } of listCustomerAssessment.rows) {
                await commonService.updateData(CustomerAssessments, {
                    start_date: null,
                    end_date:null,
                    // assigned_to: null,
                    // approver: null,
                    progress: null,
                    tentative_date: null,
                    status: 'Yet to Start'
                }, { id: id }, transaction);
            }
         
            // Commit the transaction
            await transaction.commit();
        }
        catch(error){
            await transaction.rollback();
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: "TRANSACTION_FAILED" }, httpStatus.INTERNAL_SERVER_ERROR);
        }

        const requiredHeaders = ['PDA Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
        const { isValid, missingHeader } = await pdaService.validateHeaders(req.files[0].path, requiredHeaders);
        if (!isValid) {
            deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: "INVALID_HEADER", data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
        }

        fs.createReadStream(req.files[0].path)
            .pipe(csv())
            .on('data', async (row) => {
                const controlData = {
                    PDALevel: row['PDA Level'],
                    pdaCategory: row['Category'],
                    title: row['Title'],
                    description: row['Explanation'],
                    artifact_type: row['Artifact Type'],
                    question: row['Question'],
                    fields: row['Fields'],
                    customer_id: req.data.customer_id,
                    is_attachment: row['Has Attachment'],
                    extra_input: row['Extra Input Required'],
                    extra_input_type: row['Extra Input Type'],
                    extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields'],
                };

                // Check if all properties of controlData are empty
                if (!Object.values(controlData).every(x => (x === ''))) {
                    if (controlData.title === '' && controlData.question === '') {
                        await deleteFile(req.files[0].path);
                        return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                    controls.push(controlData);
                }
            })
            .on('end', async () => {
                // Insert the data into the database
                for (let row of controls) {
                    if (!row['PDALevel'] || !row['pdaCategory']) {
                        await deleteFile(req.files[0].path);
                        return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                    }
                    const key = `${row['PDALevel']}_${row['pdaCategory']}`;
                    if (row['title']) { // Parent question
                        if (!uniqueCategories.has(key)) {
                            // Create or retrieve category and store in the map
                            const [pda_level, name] = key.split('_');
                            let category = await commonService.findByCondition(pdaCategory, { pda_level, name, customer_id: req.data.customer_id, }, {});
                            if (!category) {
                                category = await pdaCategory.create({ name, pda_level, customer_id: req.data.customer_id, }, { transaction: dbTrans });
                            }
                            uniqueCategories.set(key, category);
                        }

                        let artifactType = null;
                        if (row['artifact_type'] !== '') {
                            artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                            if (!artifactType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }

                        let extraInputType = null;
                        if (row['extra_input_type'] !== '') {
                            extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                            if (!extraInputType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }

                        // Create parent control
                        const control = await commonService.addDetail(pdaControls, {
                            title: row['title'],
                            description: row['description'],
                            artifact_type: artifactType,
                            customer_id: req.data.customer_id,
                            question: row['question'],
                            fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                            is_attachment: row['is_attachment'] === 'Yes',
                            extra_input: row['extra_input'] === 'Yes',
                            extra_input_type: extraInputType,
                            extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                            category_id: uniqueCategories.get(key).id,
                            parent_id: null,
                            industry_vertical_id: 1
                        }, dbTrans);

                        if (!control) {
                            await deleteFile(req.files[0].path);
                            return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
                        }

                        // Update parent ID for potenpdal child questions
                        parentId = control.id;
                    } else { // Child question
                        if (parentId) {
                            // Create child control
                            let artifactType = null;
                            if (row['artifact_type'] !== '') {
                                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                                if (!artifactType) {
                                    await deleteFile(req.files[0].path);
                                    return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                                }
                            }

                            let extraInputType = null;
                            if (row['extra_input_type'] !== '') {
                                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                                if (!extraInputType) {
                                    await deleteFile(req.files[0].path);
                                    return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
                                }
                            }

                            childControlsData.push({
                                title: null,
                                description: null,
                                artifact_type: artifactType,
                                customer_id: req.data.customer_id,
                                question: row['question'],
                                fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                                is_attachment: row['is_attachment'] === 'Yes',
                                extra_input: row['extra_input'] === 'Yes',
                                extra_input_type: extraInputType,
                                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                                category_id: uniqueCategories.get(key).id,
                                parent_id: parentId,
                                industry_vertical_id: 1
                            });
                        } else {
                            await deleteFile(req.files[0].path);
                            return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
                        }
                    }
                }
                // Batch create child controls
                const childControls = await commonService.bulkAdd(pdaControls, childControlsData, dbTrans);
                if (!childControls) {
                    await deleteFile(req.files[0].path);
                    return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
                }

                await deleteFile(req.files[0].path);

                return response.success(req, res, { msgCode: "CONTROLS_UPLOADED" }, httpStatus.OK, dbTrans);
            });
    }
    catch (err) {
        console.log('error', err);
        await deleteFile(req.files[0].path);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


exports.getPdaData = async (req, res) => {
    try {
        const { pdaControls, pdaCustomerControls, pdaAnswers, User, CustomerAssessments, pdaCategory, Customer, Departments, Processes } = db.models;
        const pda_id = req.params.pda_id;

        const pda = await commonService.findByCondition(CustomerAssessments, { id: pda_id }, ['status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks']);
        if (!pda) {
            return response.error(req, res, { msgCode: "PDA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
        if (!user) {
            return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        if (pda.status !== constant.status.COMPLETED) {
            return response.error(req, res, { msgCode: "PDA_NOT_COMPLETED" }, httpStatus.BAD_REQUEST);
        }

        const controlsAttributes = [
            [
                sequelize.literal(`"pdaCustomerControls"."id"`),
                'customer_question_id'
            ],
            'question_id',
            'category_id',
            'parent_id',
            'is_custom',
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."title" ELSE "pdaControl"."title" END`),
                'title'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."description" ELSE "pdaControl"."description" END`),
                'description'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN CAST("pdaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("pdaControl"."artifact_type" AS TEXT) END`),
                'artifact_type'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."is_attachment" ELSE "pdaControl"."is_attachment" END`),
                'is_attachment'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."question" ELSE "pdaControl"."question" END`),
                'question'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."fields" ELSE "pdaControl"."fields" END`),
                'fields'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."extra_input" ELSE "pdaControl"."extra_input" END`),
                'extra_input'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN CAST("pdaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("pdaControl"."extra_input_type" AS TEXT) END`),
                'extra_input_type'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."is_custom" THEN "pdaCustomerControls"."extra_input_fields" ELSE "pdaControl"."extra_input_fields" END`),
                'extra_input_fields'
            ],
            [
                sequelize.literal(`CASE WHEN "pdaCustomerControls"."question_id" IS NOT NULL THEN "pdaControl"."endpoint" ELSE NULL END`),
                'endpoint'
            ]
        ];

        const controls = await assessmentService.getControlsWithCategory(pdaCustomerControls, pdaControls, pdaAnswers, User, pdaCategory, { pda_id: pda_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'name'], [['question_id', 'ASC']]);
        for (let control of controls) {
            control.Answer = control.pdaAnswer;
            delete control.pdaAnswer;
            control.Category = control.pdaCategory;
            delete control.pdaCategory;
        }
        let parents = controls?.filter(control => control.parent_id === null);

        const childrenMap = controls?.reduce((map, control) => {
            if (control.parent_id !== null) {
                if (!map[control.parent_id]) {
                    map[control.parent_id] = [];
                }
                map[control.parent_id].push(control);
            }
            return map;
        }, {});

        parents?.forEach(parent => {
            parent.children = childrenMap[parent.customer_question_id] || [];
        });


        const excelData = transformData(parents);

        const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

        const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
        if (!customer) {
            return response.error(req, res, { msgCode: "CUSTOMER_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        let deptName = '';
        let procName = '';

        if (pda.department_id) {
            const dept = await commonService.findByCondition(Departments, { id: pda.department_id }, ['name']);
            deptName = dept?.name;
        } else if (pda.process_id) {
            const proc = await commonService.getDataAssociate(Processes, Departments, { id: pda.process_id }, {}, ['name'], ['name']);
            procName = proc?.name;
            deptName = proc?.Department?.name;
        }
        
        const excelFile = await createAssessmentExcelFile(excelData, 'PDA (Privacy by Design Assessment)', date, customer?.name, deptName, procName);

        await sendMailWithAttach(
            req.data.email,
            { name: `${user?.firstName} ${user?.lastName}` },
            'Your Copy of PDA (Privacy by Design Assessment) file made on GoTrust',
            'pda_download.ejs',
            excelFile
        );

        return response.success(req, res, { msgCode: "PDA_DOWNLOADED", data: "PDA data sent via E-mail" }, httpStatus.OK);

    } catch (err) {
        console.log('assessmentDownloadError', err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
