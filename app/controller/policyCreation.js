const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const policyCreationService = require('../services/policyCreation');
const { Op } = require('sequelize');
const uploadDocument = require('../utils/minIO');
const HTMLtoDOCX = require('html-to-docx');
const { name } = require('ejs');
const { AuditLog } = db.models;

exports.continuePolicyCreation = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, Customer, User } = db.models;
    const { policy_id, text, policy_text } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'description', 'customer_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND,dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED,dbTrans);
    }

    const customer = await commonService.findByCondition(Customer, { id: policy.customer_id }, ['id', 'name', 'industry_vertical', 'country']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND,dbTrans);
    }

    const industry_vertical = customer.industry_vertical;
    const customer_name = customer.name;
    const country = customer.country;
    const policy_name = policy.name;
    const policy_desc = policy.description;

    const msg = await policyCreationService.continueWriting(industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text);
    if (!msg) {
      return response.error(req, res, { msgCode: 'FAILED_TO_GET_AI_RESPONSE' }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }
    // const AItext = msg.content[0].text.replace(/\s+/g, ' ').trim();
    const AItext = msg?.choices[0]?.message?.content.replace(/\s+/g, ' ').trim();
    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `${user.firstName} ${user.lastName} continued creating content for policy ${policy.name}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'OK', data: AItext }, httpStatus.OK,dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
  }
};

exports.shortenText = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, Customer, User } = db.models;
    const { policy_id, text, policy_text } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'description', 'customer_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND,dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const customer = await commonService.findByCondition(Customer, { id: policy.customer_id }, ['id', 'name', 'industry_vertical', 'country']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const industry_vertical = req.data.industry_vertical;
    const customer_name = customer.name;
    const country = customer.country;
    const policy_name = policy.name;
    const policy_desc = policy.description;

    const msg = await policyCreationService.shortenText(industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text);
    if (!msg) {
      return response.error(req, res, { msgCode: 'FAILED_TO_GET_AI_RESPONSE' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    // const AItext = msg.content[0].text.replace(/\s+/g, ' ').trim();
    const AItext = msg?.choices[0]?.message?.content.replace(/\s+/g, ' ').trim();

    // Audit log
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Text shortened for policy ${policy.name} by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'OK', data: AItext }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.rewriteText = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, Customer, User } = db.models;
    const { policy_id, text, policy_text } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'description', 'customer_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const customer = await commonService.findByCondition(Customer, { id: policy.customer_id }, ['id', 'name', 'industry_vertical', 'country']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const industry_vertical = req.data.industry_vertical;
    const customer_name = customer.name;
    const country = customer.country;
    const policy_name = policy.name;
    const policy_desc = policy.description;

    const msg = await policyCreationService.rewriteText(industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text);
    if (!msg) {
      return response.error(req, res, { msgCode: 'FAILED_TO_GET_AI_RESPONSE' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    // const AItext = msg.content[0].text.replace(/\s+/g, ' ').trim();
    const AItext = msg?.choices[0]?.message?.content.replace(/\s+/g, ' ').trim();
    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Text rewritten for policy ${policy.name} by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'OK', data: AItext }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.formalizeText = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, Customer, User } = db.models;
    const { policy_id, text, policy_text } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'description', 'customer_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const customer = await commonService.findByCondition(Customer, { id: policy.customer_id }, ['id', 'name', 'industry_vertical', 'country']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const industry_vertical = req.data.industry_vertical;
    const customer_name = customer.name;
    const country = customer.country;
    const policy_name = policy.name;
    const policy_desc = policy.description;

    const msg = await policyCreationService.formalizeText(industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text);
    if (!msg) {
      return response.error(req, res, { msgCode: 'FAILED_TO_GET_AI_RESPONSE' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    // const AItext = msg.content[0].text.replace(/\s+/g, ' ').trim();
    const AItext = msg?.choices[0]?.message?.content.replace(/\s+/g, ' ').trim();

    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Text formalized for policy ${policy.name} by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'OK', data: AItext }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.customPrompt = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, Customer, User } = db.models;
    const { policy_id, text, prompt, policy_text } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'description', 'customer_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const customer = await commonService.findByCondition(Customer, { id: policy.customer_id }, ['id', 'name', 'industry_vertical', 'country']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const industry_vertical = req.data.industry_vertical;
    const customer_name = customer.name;
    const country = customer.country;
    const policy_name = policy.name;
    const policy_desc = policy.description;

    const msg = await policyCreationService.customPrompt(industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, prompt, text);
    if (!msg) {
      return response.error(req, res, { msgCode: 'FAILED_TO_GET_AI_RESPONSE' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    // const AItext = msg.content[0].text.replace(/\s+/g, ' ').trim();
    const AItext = msg?.choices[0]?.message?.content.replace(/\s+/g, ' ').trim();

    // Audit log
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Custom prompt used for policy "${policy.name}" by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'OK', data: AItext }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.fixSpelling = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Policy, Customer, User } = db.models;
    const { policy_id, text, policy_text } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'description', 'customer_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const customer = await commonService.findByCondition(Customer, { id: policy.customer_id }, ['id', 'name', 'industry_vertical', 'country']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const industry_vertical = req.data.industry_vertical;
    const customer_name = customer.name;
    const country = customer.country;
    const policy_name = policy.name;
    const policy_desc = policy.description;

    const msg = await policyCreationService.fixSpelling(industry_vertical, customer_name, country, policy_name, policy_desc, policy_text, text);
    if (!msg) {
      return response.error(req, res, { msgCode: 'FAILED_TO_GET_AI_RESPONSE' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    // const AItext = msg.content[0].text.replace(/\s+/g, ' ').trim();
    const AItext = msg?.choices[0]?.message?.content.replace(/\s+/g, ' ').trim();
    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Spelling fixed for policy ${policy.name} by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'OK', data: AItext }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getTemplates = async (req, res) => {
  try {
    const { PolicyTemplates, Templates, Policy } = db.models;
    const { search, policy_id } = req.query;
    const where = {};
    if (search) {
      where.name = {
        [Op.iLike]: `%${search}%`
      };
    }

    if (!policy_id) {
      return response.error(req, res, { msgCode: 'POLICY_ID_REQUIRED' }, httpStatus.BAD_REQUEST);
    }

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'customer_id', 'category_id']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }
    where.policy_category_id = policy.category_id;

    const templates = await commonService.getListAssociate(PolicyTemplates, Templates, where, {}, {}, ['id', 'name', 'thumbnail', 'content']);
    if (!templates) {
      return response.error(req, res, { msgCode: 'TEMPLATES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const templatesData = templates.map(template => {
      return {
        id: template.Template?.id,
        name: template.Template?.name,
        thumbnail: template.Template?.thumbnail,
        content: template.Template?.content
      };
    });

    return response.success(req, res, { msgCode: 'TEMPLATES_FETCHED', data: templatesData }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.policyCreation = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomPolicy, Policy, User } = db.models;
    const { policy_id } = req.body;

    const policy = await commonService.findByCondition(Policy, { id: policy_id }, ['id', 'name', 'customer_id', 'status']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    if (policy.status !== 'CREATION_OF_POLICY') {
      return response.error(req, res, { msgCode: 'CANNOT_CREATE_POLICY' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let customPolicy = await commonService.findByCondition(CustomPolicy, { policy_id: policy.id }, ['id', 'name', 'content']);
    if (customPolicy) {
      return response.success(req, res, { msgCode: 'CUSTOM_POLICY_FETCHED', data: customPolicy }, httpStatus.OK, dbTrans);
    }

    // create a custom policy with a default name with firmate "Policy Name - Date"
    const policyName = `${policy.name} - ${new Date().toISOString().split('T')[0]}`;

    customPolicy = await commonService.addDetail(
      CustomPolicy,
      {
        name: policyName,
        policy_id: policy.id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );

    if (!customPolicy) {
      return response.error(req, res, { msgCode: 'FAILED_TO_CREATE_CUSTOM_POLICY' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['id', 'firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Custom policy ${policyName} created by ${user.firstName} ${user.lastName}`;

    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(
      req,
      res,
      {
        msgCode: 'CUSTOM_POLICY_CREATED',
        data: {
          id: customPolicy.id,
          name: customPolicy.name,
          content: customPolicy.content
        }
      },
      httpStatus.OK,
      dbTrans
    );
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.savePolicy = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomPolicy, Policy, User } = db.models;
    const { id } = req.params;

    const customPolicy = await commonService.findByCondition(CustomPolicy, { id: id }, ['id', 'name', 'customer_id', 'policy_id']);
    if (!customPolicy) {
      return response.error(req, res, { msgCode: 'CUSTOM_POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const policy = await commonService.findByCondition(Policy, { id: customPolicy.policy_id }, ['id', 'name', 'customer_id', 'status']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    if (policy.status !== 'CREATION_OF_POLICY') {
      return response.error(req, res, { msgCode: 'CANNOT_CREATE_POLICY' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const updatedCustomPolicy = await commonService.updateData(CustomPolicy, req.body, { id: id }, dbTrans);
    if (!updatedCustomPolicy) {
      return response.error(req, res, { msgCode: 'FAILED_TO_SAVE_POLICY' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Policy ${policy.name} saved by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'POLICY_SAVED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitPolicy = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomPolicy, Policy, PolicyDocument, User } = db.models;
    const { id } = req.params;
    const html_content = req.body.html_content;

    const customPolicy = await commonService.findByCondition(CustomPolicy, { id: id }, ['id', 'name', 'customer_id', 'policy_id']);
    if (!customPolicy) {
      return response.error(req, res, { msgCode: 'CUSTOM_POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const policy = await commonService.findByCondition(Policy, { id: customPolicy.policy_id }, ['id', 'customer_id', 'status', 'name']);
    if (!policy) {
      return response.error(req, res, { msgCode: 'POLICY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (policy.customer_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    if (policy.status !== 'CREATION_OF_POLICY') {
      return response.error(req, res, { msgCode: 'CANNOT_CREATE_POLICY' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const cleanHtmlContent = html_content.replace(/\\\"/g, '"').replace(/^"|"$/g, '');

    const docBuffer = await HTMLtoDOCX(
      cleanHtmlContent,
      null,
      {
        table: { row: { cantSplit: true } },
        footer: true,
        pageNumber: true
      },
      `<p style="text-align: right;"></p>`
    );
    const uploadBufferToS3 = await uploadDocument.uploadBufferToBucket(docBuffer, `${customPolicy.name}.docx`);
    if (!uploadBufferToS3.status) {
      return response.error(req, res, { msgCode: 'FAILED_TO_UPLOAD_DOCUMENT' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }

    const url = uploadBufferToS3.data.Location;

    const updatedPolicyData = {
      content: req.body.json_content
    };

    if (req.body.name) {
      updatedPolicyData.name = req.body.name;
    }

    const updatedCustomPolicy = await commonService.updateData(CustomPolicy, updatedPolicyData, { id: id }, dbTrans);
    if (!updatedCustomPolicy) {
      return response.error(req, res, { msgCode: 'FAILED_TO_SAVE_POLICY' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }

    const policyDocument = await commonService.findByCondition(PolicyDocument, { policyId: policy.id, url: url }, ['id']);
    if (!policyDocument) {
      const savePolicyDocument = await commonService.addDetail(
        PolicyDocument,
        {
          policyId: policy.id,
          url: url,
          original_name: `${customPolicy.name}.docx`
        },
        dbTrans
      );
      if (!savePolicyDocument) {
        return response.error(req, res, { msgCode: 'FAILED_TO_SAVE_POLICY' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
      }
    }
    //audit
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const auditAction = `Policy ${policy.name} submitted by ${user.firstName} ${user.lastName}`;
    const auditData = {
      type: 'POLICY',
      type_id: policy.id,
      action: auditAction,
      action_by_id: req.data.userId,
      customer_id: req.data.customer_id
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'POLICY_SAVED', data: url }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
