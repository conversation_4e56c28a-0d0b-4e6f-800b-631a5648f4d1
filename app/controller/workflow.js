const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { getPagination } = require('../config/helper');
const { request } = require('https');
const { Op } = require('sequelize');

function generateDsrId(input, id) {
    // Trim the input and split by space to get words
    const words = input.trim().split(" ");
    
    let firstWord = words[0] || '';
    let secondWord = words[1] || '';

    // Initialize the result
    let result = '';

    // If second word is blank, pick the first 4 letters from the first word
    if (secondWord === '') {
        result = firstWord.substring(0, 4);
    } else {
        // If second word is present, take first 2 letters from both words
        result = firstWord.substring(0, 2) + secondWord.substring(0, 2);
    }
    
    return result.toLocaleUpperCase()+"-"+id;
}

exports.allWorkflowList = async (req, res) => {
    try {
        const { DsrRequestType, Customer, User , Group} = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' ,group_id} = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let WorkflowCondition = { customer_id: req.data.customer_id };
        let customerCondition =  {id : req.data.customer_id}
        let userCondition =  {id : req.data.userId}
       
       if(search){
        WorkflowCondition.flowtype = {
            [Op.iLike]: `%${search}%`
          };
       }
        if(group_id){
           WorkflowCondition.group_id=group_id
        }

        
        // get workflow
        
        
        let getWorkflow = await dsrService.getMultiLeftJoinData2(DsrRequestType, User, Group, WorkflowCondition, {}, {}, ['id' ,'flowtype', 'createdAt', 'workflow_status', 'customer_id','group_id'], ['firstName', 'lastName'],['id','name'], limit, offset , order);
        
       
        
        if(getWorkflow.rows.length > 0){
            for(let key in getWorkflow.rows) {
                let row = getWorkflow.rows[key];
                if(row.User){
                    if(row.User.firstName){
                        getWorkflow.rows[key]['firstName'] =  row.User.firstName
                        getWorkflow.rows[key]['lastName'] =  row.User.lastName
                        delete getWorkflow.rows[key].User  
                    }
                }
            };

            
        }          
              
        return response.success(req, res, { msgCode: "WORKFLOW_FETCHED", data: getWorkflow }, httpStatus.OK);

    } catch (err) {
        console.log("err", err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.getWorkflowById = async (req, res) => {
    try {
        const { User, DsrRequestType, RequestTypeStages, RequestTask } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
       // let order = [[sort_by, sort_order]];
        let order = [[RequestTypeStages, 'order', 'ASC']];
        User
        let reqCondition = { id: req.params.id, customer_id: req.data.customer_id };
        let stepCondition =  {type_id : req.params.id}
        let taskCondition =  {}

       
        const getWorkflow = await dsrService.getThreeNestedDataById(DsrRequestType, RequestTypeStages, RequestTask, reqCondition, stepCondition, taskCondition, ['id' ,'flowtype','group_id'], ['id', 'type_id', 'step_title', 'order', 'activepieces_automation_id'], '', limit, offset , order);
        if(getWorkflow?.RequestTypeStages){
            for(let stages of getWorkflow.RequestTypeStages){
                if(stages?.RequestTasks){
                    for(let key in stages.RequestTasks){
                        let taskObj = stages.RequestTasks[key];
                        let assigneeIds = taskObj?.assignee_id
                        if(assigneeIds){
                            let taskUserCond = assigneeIds
                            
                            const allUser = await dsrService.getAllRecord(User, {id : taskUserCond}, ['firstName', 'lastName']); 
                            stages.RequestTasks[key].users = allUser;
                        } else {
                            stages.RequestTasks[key].users = [];             
                        }  
                    }
                }
            }
        }

        return response.success(req, res, { msgCode: "WORKFLOW_FETCHED", data: getWorkflow }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.createWorkflow = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTask, DsrRequestType, RequestTypeStages } = db.models;
                 
        // Create workflow
        const check = await commonService.findByCondition(DsrRequestType, { flowtype: req.body.flowtype,  customer_id : req.data.customer_id, group_id : req.body.group_id });
       
        if (check) {
            return response.error(req, res, { msgCode: 'WORKFLOW_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        req.body.created_by = req.data.userId;
        req.body.customer_id = req.data.customer_id;
        
        const newWorkflow = await commonService.addDetail(DsrRequestType, req.body, dbTrans);
         
         if (!newWorkflow) {
             return response.error(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW' }, httpStatus.BAD_REQUEST, dbTrans);
         }

         //generated dsr id and then update it.
         let dsr_id = generateDsrId(req.body.flowtype, newWorkflow.id);
         let obj = {
            dsr_id : dsr_id
         }
         
         const update = await commonService.updateData(DsrRequestType, obj, { id: newWorkflow.id }, dbTrans);

         if(!update){
            return response.eupdaterror(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW' }, httpStatus.BAD_REQUEST, dbTrans);
         }

         newWorkflow.dsr_id = dsr_id;

        //new task
        //Enter default workflow steps and task
        let defaultWorkFlowOne = {
                type_id : newWorkflow.id,
                step_title : "Verify",
                guidance_text : null,
                order : 1,
                created_by : req.data.userId            
        };

        const addDefaultStepOne = await commonService.addDetail(RequestTypeStages, defaultWorkFlowOne, dbTrans);
        if(!addDefaultStepOne){
            return response.eupdaterror(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let stepOneTasks = [
            {
                workflow_id : newWorkflow.id,
                stage_id: addDefaultStepOne.id,
                title: "ID verification",
                guidance_text : "Assigned to the user responsible for verifying the requestor’s identity.",
                created_by : req.data.userId,
                customer_id : req.data.customer_id
            }
            // {
            //     workflow_id : newWorkflow.id,
            //     stage_id: addDefaultStepOne.id,
            //     title: "Log the request",
            //     guidance_text : "Once the Identity of the Data Subject is verified, the case handler will record the Access Request in the system",
            //     created_by : req.data.userId,
            //     customer_id : req.data.customer_id
            // }
        ]
        const addDefaultTaskOne = await commonService.bulkAdd(RequestTask, stepOneTasks, dbTrans);
        if(!addDefaultTaskOne){
            return response.eupdaterror(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW' }, httpStatus.BAD_REQUEST, dbTrans);
        }


        let defaultWorkFlowTwo = {
                type_id : newWorkflow.id,
                step_title : "Acknowledgement",
                guidance_text : null,
                order : 2,
                created_by : req.data.userId            
        };

        const addDefaultStepTwo = await commonService.addDetail(RequestTypeStages, defaultWorkFlowTwo, dbTrans);
        if(!addDefaultStepTwo){
            return response.eupdaterror(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let stepTwoTasks = [
            {
                workflow_id : newWorkflow.id,
                stage_id: addDefaultStepTwo.id,
                title: "Acknowledgment email",
                guidance_text : "Once the user has verified and validated the Identity of the Data subject, an acknowledgment email.",
                created_by : req.data.userId,
                customer_id : req.data.customer_id
            }
        ]
        const addDefaultTaskTwo = await commonService.bulkAdd(RequestTask, stepTwoTasks, dbTrans);
        if(!addDefaultTaskTwo){
            return response.eupdaterror(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        //end new task
        
        newWorkflow.workflow_steps = addDefaultTaskOne;

         return response.success(req, res, { msgCode: "WORKFLOW_CREATED", data: newWorkflow }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateWorkflow = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DsrRequestType } = db.models;
        // Check department
        let workflowId = req.params.workflow_id
        
        const check = await commonService.findByCondition(DsrRequestType, { id: workflowId, customer_id: req.data.customer_id});
        if (!check) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let dsr_id = generateDsrId(req.body.flowtype, workflowId);

       
        req.body['dsr_id'] = dsr_id

        const update = await commonService.updateData(DsrRequestType, req.body, { id: req.params.workflow_id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        return response.success(req, res, { msgCode: "WORKFLOW_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.allPublishedWorkflowList = async (req, res) => {
    try {
        const { DsrRequestType, User } = await db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let WorkflowCondition = { customer_id: req.data.customer_id, workflow_status : 'published' };
        
        let userCondition =  {id : req.data.userId}
          
        const getWorkflow = await commonService.getListAssociate(DsrRequestType, User, WorkflowCondition, userCondition, ['id' ,'flowtype', 'createdAt', 'workflow_status', 'customer_id'], [], limit, offset , order);
           
              
        return response.success(req, res, { msgCode: "WORKFLOW_FETCHED", data: getWorkflow }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.allPublishedWorkflowListv2 = async (req, res) => {
    try {
        const { DsrRequestType, Customer, User } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC',customer_id , group_id} = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let WorkflowCondition = { customer_id: customer_id, workflow_status : 'published' };
          if(group_id){
            WorkflowCondition.group_id=group_id
        }
        const getWorkflow = await commonService.getListWithoutCount(DsrRequestType,WorkflowCondition,['id' ,'flowtype', 'createdAt', 'workflow_status', 'customer_id'], limit, offset, order);
           
              
        return response.success(req, res, { msgCode: "WORKFLOW_FETCHED", data: getWorkflow }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};



