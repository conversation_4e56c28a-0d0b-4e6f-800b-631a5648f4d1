const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const constant = require('../constant/PDA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
const { sendMail } = require('../config/email');

function getMonthName(monthNumber) {
    const monthNames = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];
    
    // Ensure monthNumber is valid (1-12)
    if (monthNumber < 1 || monthNumber > 12) {
        throw new Error("Month number must be between 1 and 12.");
    }

    return monthNames[monthNumber - 1]; // Convert to 0-indexed
}


exports.countByTypeBackup = async (req, res) => {
    try {
        const { DsrRequest, DsrRequestType } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', filter = 'monthly' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        let dateFilter = {};
        if (filter === 'monthly') {
            // Set filter to get records for the current month
            dateFilter = {
                createdAt: {
                    [Op.gte]: sequelize.fn(
                        'DATE_TRUNC',
                        'month',
                        sequelize.fn('NOW')
                    )
                }
            };
        } else if (filter === 'annually') {
            // Set filter to get records for the current year
            dateFilter = {
                createdAt: {
                    [Op.gte]: sequelize.fn(
                        'DATE_TRUNC',
                        'year',
                        sequelize.fn('NOW')
                    )
                }
            };
        }
        const data = await commonService.getListGroupBy(DsrRequest, { customer_id: req.data.customer_id, ...dateFilter }, ['request_type', [sequelize.fn('COUNT', 'id'), 'count']], ['request_type']);
        if (!data) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        const ids = data?.map(data => data.request_type);
        // Fetch request details 
        const requestDetails = await commonService.getListWithoutCount(DsrRequestType, { id: { [Op.in]: ids } }, ['id','flowtype']);
        // Update assessment data with owner details
        const updatedData = data?.map(request => {
            const requestDetail = requestDetails?.find(detail => detail.id === request.request_type);
            if (requestDetail) {
                return {
                    ...request,
                    request: requestDetail.flowtype
                };
            } else {
                return { ...request }; // If no match found, keep the email as it is
            }
        });
        return response.success(req, res, { msgCode: "REQUEST_TYPE_COUNT", data: updatedData }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByType = async (req, res) => {
    try {
        const { DsrRequest, DsrRequestType } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', filter = 'monthly', business_unit } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let whereClause = { customer_id: req.data.customer_id };
        let dateFilter = {};

        if (filter === 'monthly') {
            // Set filter to get records for the current month
            dateFilter = {
                createdAt: {
                    [Op.gte]: sequelize.fn(
                        'DATE_TRUNC',
                        'month',
                        sequelize.fn('NOW')
                    )
                }
            };
        } else if (filter === 'annually') {
            // Set filter to get records for the current year
            dateFilter = {
                // createdAt: {
                //     [Op.gte]: sequelize.fn(
                //         'DATE_TRUNC',
                //         'year',
                //         sequelize.fn('NOW')
                //     )
                // }

                createdAt: {
                    [Op.gte]:sequelize.literal("NOW() - INTERVAL '1 year'") ,
                    [Op.lte]:sequelize.fn(
                        'NOW' // Get the current timestamp
                    ),
                }
            };
        }

        if (business_unit) whereClause.business_unit = business_unit;

        // Merge Date Filter into Where Clause
        Object.assign(whereClause, dateFilter);

        console.log("WHERE CLAUSE:", whereClause);

        const data = await commonService.getListGroupBy(DsrRequest, whereClause, ['request_type', [sequelize.fn('COUNT', 'id'), 'count']], ['request_type']);
        if (!data) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
      
        // Fetch request details 
        const requestDetails = await commonService.getListWithoutCount(DsrRequestType, { customer_id: req.data.customer_id }, ['id','flowtype']);
        // Update assessment data with owner details
        const updatedData = requestDetails?.map(request => {
            const filterData = data?.find(detail => detail.request_type === request.id);
            if (filterData) {
                return {
                    ...filterData,
                    flowtype: request.flowtype
                };
            } else {
                return {
                     ...request,
                    count : 0
                }; // If no match found, keep the email as it is
            }
        });
        const count =updatedData.reduce((sum, item) => sum + Number(item.count), 0);

        const result = {
            total_count: count,
            list: updatedData
        };
        return response.success(req, res, { msgCode: "REQUEST_TYPE_COUNT", data: result }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.countRequestByMonthOrYearBackup = async (req, res) => {
    try {
        const { DsrRequest } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', filter = 'monthly' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let data = [];

        if (filter === 'monthly') {
            // Monthly filter: Count requests for each month of the current year
            data = await DsrRequest.findAll({
                attributes: [
                    [sequelize.fn('TO_CHAR', sequelize.col('createdAt'), 'Month'), 'month'],
                    [sequelize.fn('COUNT', sequelize.col('id')), 'count']
                ],
                where: {
                    customer_id: req.data.customer_id,
                    createdAt: {
                        [Op.gte]: sequelize.fn('DATE_TRUNC', 'year', sequelize.fn('NOW'))
                    }
                },
                group: ['month'],
                order: [[sequelize.fn('TO_CHAR', sequelize.col('createdAt'), 'Month'), 'ASC']]
            });
        } else if (filter === 'annually') {
            // Annual filter: Count requests for each year
            data = await DsrRequest.findAll({
                attributes: [
                    [sequelize.fn('DATE_PART', 'year', sequelize.col('createdAt')), 'year'],
                    [sequelize.fn('COUNT', sequelize.col('id')), 'count']
                ],
                where: { customer_id: req.data.customer_id },
                group: ['year'],
                order: [[sequelize.fn('DATE_PART', 'year', sequelize.col('createdAt')), 'ASC']]
            });
        }

        if (!data) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        // let count = 0;
        // data.forEach(row => {
        //     count += Number(row.dataValues.count);
        // });

        // // Adding total count to the response data
        // data.push({ period: 'Total', count: String(count) });

        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: data }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countRequestByMonthOrYear = async (req, res) => {
    try {
        const { DsrRequest } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', filter = 'monthly', business_unit } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let data = [];

        const year = new Date().getFullYear(); 
        const yearArr = [];

        for (let i = 0; i < 6; i++) {
            yearArr.push(year - i);
        }
        
        let currentMonth = new Date().getMonth(); 
        const monthsWithYears = [];
      
        // Loop to get the last 6 months, including the current month
        for (let i = 5; i >= 0; i--) {
            // Calculate the month (wrap around if needed)
            const month = (currentMonth - i + 12) % 12; // Wrap around if needed (e.g., for January)
        
            // Calculate the corresponding year for this month
            let monthYear = year;
            if (month > currentMonth) {
            monthYear -= 1; // If the month is in the previous year, subtract 1 from the current year
            }
        
            // Push the {month, year} object into the array
            monthsWithYears.push({ month: month + 1, year: monthYear }); // Add 1 to match the 1-12 month range
        }

        //let months = [1];
        let finalData = []
        if (filter === 'monthly') {
            // Monthly filter: Count requests for each month of the current year
            //for(let month = startMonth; month <= currentMonth; month++){
            for(let monthWithYear of monthsWithYears){
                let startOfMonth = new Date(monthWithYear['year'], monthWithYear['month'] - 1, 1);
                let endOfMonth = new Date(monthWithYear['year'], monthWithYear['month'], 1);
                
                let whereClause = {
                    customer_id: req.data.customer_id,
                    createdAt: {
                        [Op.gte]: startOfMonth,
                        [Op.lt]: endOfMonth
                    }
                };

                if (business_unit) whereClause.business_unit = business_unit;

                let data = await DsrRequest.findOne({
                    attributes: [
                        [sequelize.fn('TO_CHAR', sequelize.col('createdAt'), 'Month'), 'month'],
                        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
                    ],
                    where: whereClause,
                    group: ['month']
                });
            
                const monthName = getMonthName(monthWithYear.month);
                let countValue = data ? Number(data.dataValues.count) : 0;

                finalData.push({
                    month: monthName,
                    count: countValue
                });
                
            }
        } else if (filter === 'annually') {
            // Annual filter: Count requests for each year
            for(let yearVal of yearArr){
                let startOfYear = new Date(yearVal, 0, 1); // January 1st of the specified year
                let endOfYear = new Date(yearVal + 1, 0, 1); // January 1st of the next year (exclusive)
                
                let whereClause = { 
                    customer_id: req.data.customer_id,
                    createdAt: {
                        [Op.gte]: startOfYear,
                        [Op.lt]: endOfYear
                    }
                };

                if (business_unit) whereClause.business_unit = business_unit; 

                let data = await DsrRequest.findOne({
                    attributes: [
                        [sequelize.fn('DATE_PART', 'year', sequelize.col('createdAt')), 'year'],
                        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
                    ],
                    where: whereClause,
                    group: ['year'],
                    order: [[sequelize.fn('DATE_PART', 'year', sequelize.col('createdAt')), 'ASC']]
                });
            
                let countValue = data? Number(data.dataValues.count) : 0;
                finalData.push({
                    month: String(yearVal),
                    count: countValue
                })
               
            }
        }
            const totalCount = finalData.reduce((sum, item) => sum + item.count, 0);
            return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT",
                data: {
                    total_count:totalCount,
                    list: finalData
            } }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};


exports.countRequest = async (req, res) => {
    try {
        const { DsrRequest } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', business_unit, days } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let startDate = new Date();
        let endDate = new Date();

        let whereClause = { customer_id: req.data.customer_id };


        if(business_unit) 
            whereClause.business_unit = business_unit;


        if(days){

            startDate.setDate(startDate.getDate() - days);
            endDate.setDate(endDate.getDate());

            whereClause['createdAt'] = {
                [Op.gte]: startDate,
                [Op.lt]: endDate
            }
        }
        whereClause['first_verification'] = true;

        const data = await commonService.getListGroupBy(DsrRequest, whereClause, ['status', [sequelize.fn('COUNT', 'id'), 'count']], ['status']);
        if (!data) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        const extendedReq = await commonService.getListGroupBy(DsrRequest, { ...whereClause, extended: 'YES'}, ['extended', [sequelize.fn('COUNT', 'id'), 'count']], ['extended']);


        const allStatus = {
            PENDING: 0,
            APPROVED: 0,
            REJECTED: 0,
            REJECTED_IN_PROGRESS: 0,
            COMPLETED: 0,
            ARCHIVED: 0
        };
        
        let count = 0;
        data?.forEach(row => {
            count = count + Number(row.count);
            delete allStatus[row.status];
        });

        
        // Adding missing statuses with count 0
        for(const status in allStatus){
            data?.push({
                status,
                count: String(allStatus[status])
            });
        }

        data?.push(
            {
                status : 'Extended',
                count : extendedReq[0]?.count ? String(extendedReq[0].count) : String(0)
            }
        );
        data?.push({ status: 'Total', count: String(count) });
        
        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: data }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByStatusPendingBackup = async (req, res) => {
    try {
        const { DsrRequest } = db.models;

        // Define periods for which we need to get the count of pending requests
        const periods = [7, 15, 25, 30];
        const counts = [];

        for (const days of periods) {
            // Calculate the date threshold
            const dateThreshold = new Date();
            dateThreshold.setDate(dateThreshold.getDate() - days);
            const countCondition = {
                customer_id: req.data.customer_id,
                status: 'PENDING',
                request_date: {
                    [Op.gte]: dateThreshold
                }
            };
            const count = await commonService.count(DsrRequest, {...countCondition});
            counts.push({ period: `${days} days`, count: count });
        }

        // Calculate total count of pending requests in the last 30 days
        const totalCount = counts.reduce((acc, cur) => acc + cur.count, 0);
        counts.push({ period: 'Total (last 30 days)', count: totalCount });

        return response.success(req, res, { msgCode: "PENDING_REQUEST_COUNT", data: counts }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByStatusPending = async (req, res) => {
    try {
        const { DsrRequest } = db.models;
        const { business_unit } = req.query;

        // Define periods for which we need to get the count of pending requests
        const periods = [7, 15, 25, 30];
        const counts = [];

        for (let days of periods) {
            let expDays = days + 30;
            // Calculate the date threshold
            let startDate = new Date();
            let endDate = new Date();
            startDate.setDate(startDate.getDate() - expDays);
            endDate.setDate(endDate.getDate() - 30);
            const countCondition = {
                customer_id: req.data.customer_id,
                status: 'APPROVED',
                assigned_date: {
                   [Op.gte]: startDate,
                   [Op.lt]: endDate
                    
                }
            };

            if(business_unit){
                countCondition['business_unit']= business_unit;
            }

            const count = await commonService.count(DsrRequest, countCondition);

            counts.push({ period: `${days} days`, count });
        }

        // Calculate total count of pending requests in the last 30 days
        const totalCount = counts.reduce((acc, cur) => acc + cur.count, 0);
        //counts.push({ period: 'Total (last 30 days)', count: totalCount });

        return response.success(req, res, { msgCode: "PENDING_REQUEST_COUNT", data: counts }, httpStatus.OK);

    } catch (err) {
        console.log("dashboard error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByWorkflowStageBackup = async (req, res) => {
    try {
        const { DsrRequest, RequestTypeStages } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' , request_type } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        const data = await commonService.getListGroupBy(DsrRequest, { customer_id: req.data.customer_id, request_type: request_type }, ['workflow_step_id', [sequelize.fn('COUNT', 'id'), 'count']], ['workflow_step_id']);
        if (!data) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        const ids = data?.map(data => data.workflow_step_id);
        // Fetch request details 
        const stageDetails = await commonService.getListWithoutCount(RequestTypeStages, { id: { [Op.in]: ids } }, ['id','step_title']);

        // Update assessment data with owner details
        const updatedData = data?.map(request => {
            const requestDetail = stageDetails?.find(stage => stage.id === request.workflow_step_id);
            if (requestDetail) {
                return {
                    ...request,
                    stageName: requestDetail.step_title
                };
            } else {
                return { ...request }; // If no match found, keep the email as it is
            }
        });

        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: updatedData }, httpStatus.OK);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByWorkflowStage = async (req, res) => {
    try {
        const { DsrRequest, RequestTypeStages } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' , request_type, days = 0, business_unit} = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let finalArr = []
        const workFlowStep = await dsrService.getAllRecord(RequestTypeStages, { type_id: request_type }, ['id', 'type_id', 'step_title']);
        
        let startDate = new Date();
        let endDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        endDate.setDate(endDate.getDate());

        for(let step of workFlowStep){
            const countCondition = {
                customer_id: req.data.customer_id,
                [Op.or]: [
                    {workflow_step_id: step.id}
                ]
            };
            if(days > 0){
                countCondition['createdAt'] = {
                    [Op.gte]: startDate,
                    [Op.lt]: endDate
                     
                 }
            }

            if (business_unit) {
                countCondition['business_unit'] = business_unit;
            }

            if(step.step_title == "Verify"){
                countCondition[Op.or].push({workflow_step_id: null})
            }

            if(step.step_title != "Verify"){
                countCondition['status'] = "APPROVED"
            }
            const count = await commonService.count(DsrRequest, countCondition);
            let stepObj = {
                workflow_step_id: step.id,
                count: count,
                stageName: step.step_title
            }

            finalArr.push(stepObj)
            console.log(count)
            console.log('test')
         }


        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: finalArr }, httpStatus.OK);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByResidencyBackup = async (req, res) => {
    try{
        const { DataSubject, country } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        
            const data = await commonService.getListGroupBy(DataSubject, { customer_id: req.data.customer_id }, ['country_id', [sequelize.fn('COUNT', 'id'), 'count']], ['country_id']);
            if (!data) {
                return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
            }
            const ids = data?.map(data => data.country_id);
            // Fetch request details 
            const countryDetails = await commonService.getListWithoutCount(country, { id: { [Op.in]: ids } }, ['id','country_name']);

            // Update assessment data with owner details
            const updatedData = data?.map(request => {
                const requestDetail = countryDetails?.find(country => country.id === request.country_id);
                if (requestDetail) {
                    
                    return {
                        ...request,
                        countryName: requestDetail.country_name
                    };
                } else {
                    return { ...request }; // If no match found, keep the email as it is
                }
            });
        

        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: updatedData }, httpStatus.OK);


    }catch(err){
        console.log(err);
        return response.error(req, req, { msgCode:"INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.countByResidency = async (req, res) => {
    try{
        const { DataSubject, country, DsrRequest } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', business_unit, days } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];


        let startDate = new Date();
        let endDate = new Date();

            let returnArr = []
            let dataSubCondi = { 
                customer_id: req.data.customer_id,
                createdAt: {
                    [Op.gte]: sequelize.fn('DATE_TRUNC', 'year', sequelize.fn('NOW'))
                }
            }
            if(days){

                startDate.setDate(startDate.getDate() - days);
                endDate.setDate(endDate.getDate());
    
                dataSubCondi['createdAt'] = {
                    [Op.gte]: startDate,
                    [Op.lt]: endDate
                }
            }

            let dsrRequestCondi = {}; 
            if (business_unit) {
                dsrRequestCondi['business_unit'] = business_unit;
            }

            
            const data = await dsrService.getMultiLeftJoinWithoutCount(DataSubject, country, dataSubCondi, {}, ['id'], ['country_name']);
            for(let row of data){
                let obj = {
                    countryName : row.country.country_name,
                    PENDING : 0,
                    APPROVED : 0,
                    REJECTED : 0,
                    REJECTED_IN_PROGRESS : 0
                }
                
                const dataReq = await dsrService.getOneRecord(DsrRequest,  { data_subject_id: row.id, ...dsrRequestCondi}, ['id', 'status']);

                if (dataReq) { 
                    obj[dataReq.status] = 1;
                }
                let countryName = row.country.country_name;
                let status = dataReq?.status;
                const country = returnArr.find(c => c.countryName === countryName);
  
                if (country) {
                    if (status in country) {
                    country[status] = (country[status] || 0) + 1; // Increase the specified count
                    } else {
                    //console.log(`Status "${status}" not found for country "${countryName}"`);
                    }
                } else {
                    returnArr.push(obj)
                    //console.log(`Country "${countryName}" not found`);
                }   
            }   



        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: returnArr }, httpStatus.OK);


    }catch(err){
        console.log(err);
        return response.error(req, req, { msgCode:"INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

 exports.countByOwnersBackup = async(req, res) =>{
    try{
        const { DataSubject, User } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        const data = await commonService.getListGroupBy(DataSubject, { customer_id: req.data.customer_id }, ['assignee_id', [sequelize.fn('COUNT', 'id'), 'count']], ['assignee_id']);
        if (!data) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        const ids = data?.map(data => data.assignee_id);
        // Fetch request details 
        const assigneeDetails = await commonService.getListWithoutCount(User, { id: { [Op.in]: ids } }, ['id','firstName', 'lastName']);

        // Update assessment data with owner details
        const updatedData = data?.map(request => {
            const ownerDetail = assigneeDetails?.find(assignee => assignee.id === request.assignee_id);
            if (requestDetail) {
                return {
                    ...request,
                    ownerName: ownerDetail?.firstName +" "+ ownerDetail?.lastName
                };
            } else {
                return { ...request }; // If no match found, keep the email as it is
            }
        });

        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: updatedData }, httpStatus.OK);


    }catch(err){
        console.log(err);
        return response.error(req, req, { msgCode:"INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
 };

 exports.countByOwners = async(req, res) => {
    try {
        const { DataSubject, User, DsrRequest } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC', business_unit, days=90 } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        let startDate = new Date();
        let endDate = new Date();

        const countCondition = {
            customer_id: req.data.customer_id
        };


        if(days){
            startDate.setDate(startDate.getDate() - days);
            endDate.setDate(endDate.getDate());

            countCondition['createdAt'] = {
                [Op.gte]: startDate,
                [Op.lt]: endDate
            }
        }

        if (business_unit) {
            countCondition['business_unit'] = business_unit;
        }

        const completedCondition = { ...countCondition, status: "COMPLETED" };
        const completedData = await commonService.getListGroupBy(
            DsrRequest, completedCondition,
            ['assignee_id', [sequelize.fn('COUNT', 'id'), 'completed_count']],
            ['assignee_id']
        );

        const pendingCondition = { ...countCondition, status: "PENDING" };
        const pendingData = await commonService.getListGroupBy(
            DsrRequest, pendingCondition,
            ['assignee_id', [sequelize.fn('COUNT', 'id'), 'pending_count']],
            ['assignee_id']
        );

        if (!completedData && !pendingData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }

        // Merge completed and pending data
        const ownerMap = new Map();

        completedData?.forEach(item => {
            if (item.assignee_id) { 
                ownerMap.set(item.assignee_id, {
                    ...item,
                    completed_count: Number(item.completed_count) || 0,
                    pending_count: 0
                });
            }
        });

        pendingData?.forEach(item => {
            if (item.assignee_id) { 
                if (ownerMap.has(item.assignee_id)) {
                    ownerMap.get(item.assignee_id).pending_count = Number(item.pending_count) || 0;
                } else {
                    ownerMap.set(item.assignee_id, {
                        ...item,
                        completed_count: 0,
                        pending_count: Number(item.pending_count) || 0
                    });
                }
            }
        });

        const finalData = Array.from(ownerMap.values());

        const ids = finalData.map(data => data.assignee_id);
        const assigneeDetails = await commonService.getListWithoutCount(User, { id: { [Op.in]: ids } }, ['id', 'firstName', 'lastName']);

        // Update assessment data with owner details
        const updatedData = finalData.map(request => {
            const ownerDetail = assigneeDetails?.find(assignee => assignee.id === request.assignee_id);
            return {
                ...request,
                ownerName: ownerDetail ? `${ownerDetail.firstName} ${ownerDetail.lastName}` : ""
            };
        });

        const totalCount = updatedData.reduce((sum, item) => sum + item.completed_count + item.pending_count, 0);

        const result = {
            total_count: totalCount,
            list: updatedData
        };

        return response.success(req, res, { msgCode: "REQUEST_STATUS_COUNT", data: result }, httpStatus.OK);

    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};