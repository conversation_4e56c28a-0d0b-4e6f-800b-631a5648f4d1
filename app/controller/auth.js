const response = require('../response');
const httpStatus = require('http-status');
const passwordHash = require('../utils/password');
const db = require('../models/index').sequelize;
const { env } = require('../constant/environment');
const commonService = require('../services/common');
const { user_attributes } = require('../config/user');
const authService = require('../services/auth');
const { signToken } = require('../config/helper');
const { USER_ROLE, CLIENT_ONBOARD, MESSAGE, ONBOARDING_STATUS } = require('../constant/common');
const { sendMail } = require('../config/email');
const moment = require('moment');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { onboarding } = require('./onboarding');
const salt = bcrypt.genSaltSync(10);
const randomToken = require('random-token');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const keycloakService = require('../services/keycloak');
const CryptoJS = require('crypto-js');
const axios = require('axios');
const dsrService = require('../services/dsr');
const { automationOnCreateRequest } = require('../utils/dsr-helper');
const ucfControls = require('../models/ucfControls');
const { custom } = require('joi');

exports.getLogin = async (req, res) => {
  const { logo = '', show_forgot_password = true, show_create_account = true, primary_color = '#328f8a', secondary_color = '' } = req.query;

  const backgroundColor = secondary_color ? `linear-gradient(45deg,${primary_color},${secondary_color})` : primary_color;

  const html = `
        <style>
        @import url(https://fonts.googleapis.com/css?family=Roboto:300);
        header .header{
          background-color: #fff;
          height: 45px;
        }
        header a img{
          width: 134px;
        margin-top: 4px;
        }
        .login-page {
          width: 360px;
          padding: 8% 0 0;
          margin: auto;
        }
        .login-page .form .login{
          margin-top: -31px;
        margin-bottom: 26px;
        }
        .login-header img {
          width: 150px;
          height: 150px;
        }
        .form {
          position: relative;
          z-index: 1;
          background: #FFFFFF;
          max-width: 360px;
          margin: 0 auto 100px;
          padding: 45px;
          text-align: center;
          box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
        }
        .form input {
          font-family: "Roboto", sans-serif;
          outline: 0;
          background: #f2f2f2;
          width: 100%;
          border: 0;
          margin: 0 0 15px;
          padding: 15px;
          box-sizing: border-box;
          font-size: 14px;
        }
        .form button {
          font-family: "Roboto", sans-serif;
          text-transform: uppercase;
          outline: 0;
          background-color: ${primary_color};
          background-image: ${backgroundColor};
          width: 100%;
          border: 0;
          padding: 15px;
          color: #FFFFFF;
          font-size: 14px;
          -webkit-transition: all 0.3 ease;
          transition: all 0.3 ease;
          cursor: pointer;
        }
        .form .message {
          margin: 15px 0 0;
          color: ${primary_color};
          font-size: 12px;
        }
        .form .message a {
          color: ${primary_color};
          text-decoration: none;
        }
        .container {
          position: relative;
          z-index: 1;
          max-width: 300px;
          margin: 0 auto;
        }
        </style>

        <div id="login-page-container" class="login-page">
            <div id="form-container" class="form">
                <div id="login-container class="login">
                    <div id="login-header-container" class="login-header">
                        <h3>LOGIN</h3>
                        ${logo ? `<img src="${logo}" alt="Company Logo">` : ''}
                        <p>Please enter your credentials to login.</p>
                    </div>
                </div>
                <form class="login-form" action="${env.BASE_URL}/api/v1/auth/login" method="post">
                    <input type="email" placeholder="email"/>
                    <input type="password" placeholder="password"/>
                    <button id="submit-button" type="submit">Login</button>
                    ${show_forgot_password !== 'false' ? `<p class="message"><a href="${env.BASE_URL}/api/v1/auth/forgot-password">Forgot Password?</a></p>` : ''}
                    ${show_create_account !== 'false' ? `<p class="message">Not registered? <a href="${env.BASE_URL}/api/v1/auth/sign-up">Create an account</a></p>` : ''}
                </form>
            </div>
        </div>
    `;

  res.send(html);
};

exports.getSignup = async (req, res) => {
  const { logo = '', show_login = true, primary_color = '#328f8a', secondary_color = '' } = req.query;

  const backgroundColor = secondary_color ? `linear-gradient(45deg,${primary_color},${secondary_color})` : primary_color;

  const html = `
      <style>
      @import url(https://fonts.googleapis.com/css?family=Roboto:300);
      header .header{
        background-color: #fff;
        height: 45px;
      }
      header a img{
        width: 134px;
        margin-top: 4px;
      }
      .signup-page {
        width: 360px;
        padding: 8% 0 0;
        margin: auto;
      }
      .signup-page .form .signup{
        margin-top: -31px;
        margin-bottom: 26px;
      }
      .signup-header img {
        width: 150px;
        height: 150px;
      }
      .form {
        position: relative;
        z-index: 1;
        background: #FFFFFF;
        max-width: 360px;
        margin: 0 auto 100px;
        padding: 45px;
        text-align: center;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
      }
      .form input {
        font-family: "Roboto", sans-serif;
        outline: 0;
        background: #f2f2f2;
        width: 100%;
        border: 0;
        margin: 0 0 15px;
        padding: 15px;
        box-sizing: border-box;
        font-size: 14px;
      }
      .form .phone-input {
          display: flex;
          justify-content: space-between;
      }
      .form .phone-input .country-code {
          flex: 0.2;
          margin-right: 5px;
      }
      .form .phone-input .phone-number {
          flex: 3;
      }
      .form button {
        font-family: "Roboto", sans-serif;
        text-transform: uppercase;
        outline: 0;
        background-color: ${primary_color};
        background-image: ${backgroundColor};
        width: 100%;
        border: 0;
        padding: 15px;
        color: #FFFFFF;
        font-size: 14px;
        -webkit-transition: all 0.3 ease;
        transition: all 0.3 ease;
        cursor: pointer;
      }
      .form .message {
        margin: 15px 0 0;
        color: ${primary_color};
        font-size: 12px;
      }
      .form .message a {
        color: ${primary_color};
        text-decoration: none;
      }
      .container {
        position: relative;
        z-index: 1;
        max-width: 300px;
        margin: 0 auto;
      }
      </style>

      <div id="signup-page-container" class="signup-page">
          <div id="form-container" class="form">
              <div id="signup-container" class="signup">
                  <div id="signup-header-container" class="signup-header">
                      <h3>SIGNUP</h3>
                      ${logo ? `<img src="${logo}" alt="Company Logo">` : ''}
                      <p>Please enter your details to signup.</p>
                  </div>
              </div>
              <form class="signup-form" action="${env.BASE_URL}/api/v1/auth/sign-up" method="post">
                <input type="email" placeholder="email" name="email"/>
                <div id="phone-input-container" class="phone-input">
                    <input type="text" placeholder="+91" class="country-code" name="countryCode"/>
                    <input type="text" placeholder="phone" class="phone-number" name="phone"/>
                </div>
                <input type="text" placeholder="first name" name="firstName"/>
                <input type="text" placeholder="last name" name="lastName"/>
                <button id="submit-button" type="submit">signup</button>
                ${show_login !== 'false' ? `<p class="message">Already registered? <a href=${env.BASE_URL}/api/v1/auth/login>Login</a></p>` : ''}
              </form>
          </div>
      </div>
    `;

  res.send(html);
};

exports.getVerifyOtp = async (req, res) => {
  const { logo = '', primary_color = '#328f8a', secondary_color = '' } = req.query;

  const backgroundColor = secondary_color ? `linear-gradient(45deg,${primary_color},${secondary_color})` : primary_color;

  const html = `
      <style>
      @import url(https://fonts.googleapis.com/css?family=Roboto:300);
      .verify-otp-page {
        width: 360px;
        padding: 8% 0 0;
        margin: auto;
      }
      .verify-otp-page .form .verify-otp{
        margin-top: -31px;
        margin-bottom: 26px;
      }
      .verify-otp-header img {
        width: 150px;
        height: 150px;
      }
      .form {
        position: relative;
        z-index: 1;
        background: #FFFFFF;
        max-width: 360px;
        margin: 0 auto 100px;
        padding: 45px;
        text-align: center;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
      }
      .form input {
        font-family: "Roboto", sans-serif;
        outline: 0;
        background: #f2f2f2;
        width: 100%;
        border: 0;
        margin: 0 0 15px;
        padding: 15px;
        box-sizing: border-box;
        font-size: 14px;
      }
      .form button {
        font-family: "Roboto", sans-serif;
        text-transform: uppercase;
        outline: 0;
        background-color: ${primary_color};
        background-image: ${backgroundColor};
        width: 100%;
        border: 0;
        padding: 15px;
        color: #FFFFFF;
        font-size: 14px;
        -webkit-transition: all 0.3 ease;
        transition: all 0.3 ease;
        cursor: pointer;
      }
      .form .message {
        margin: 15px 0 0;
        color: ${primary_color};
        font-size: 12px;
      }
      .form .message a {
        color: ${primary_color};
        text-decoration: none;
      }
      </style>

      <div id="verify-otp-page-container" class="verify-otp-page">
          <div id="form-container" class="form">
              <div id="verify-otp-container" class="verify-otp">
                  <div id="verify-otp-header-container" class="verify-otp-header">
                      <h3>VERIFY OTP</h3>
                      ${logo ? `<img src="${logo}" alt="Company Logo">` : ''}
                      <p>Please enter the OTP sent to your email.</p>
                  </div>
              </div>
              <form class="verify-otp-form" action="${env.BASE_URL}/api/v1/auth/verify-otp" method="post">
                <input type="text" placeholder="OTP" name="otp"/>
                <button id="submit-button" type="submit">Verify</button>
              </form>
          </div>
      </div>
    `;

  res.send(html);
};

exports.getForgotPassword = async (req, res) => {
  const { logo = '', primary_color = '#328f8a', secondary_color = '' } = req.query;

  const backgroundColor = secondary_color ? `linear-gradient(45deg,${primary_color},${secondary_color})` : primary_color;

  const html = `
      <style>
      @import url(https://fonts.googleapis.com/css?family=Roboto:300);
      .forgot-password-page {
        width: 360px;
        padding: 8% 0 0;
        margin: auto;
      }
      .forgot-password-page .form .forgot-password{
        margin-top: -31px;
        margin-bottom: 26px;
      }
      .forgot-password-header img {
        width: 150px;
        height: 150px;
      }
      .form {
        position: relative;
        z-index: 1;
        background: #FFFFFF;
        max-width: 360px;
        margin: 0 auto 100px;
        padding: 45px;
        text-align: center;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
      }
      .form input {
        font-family: "Roboto", sans-serif;
        outline: 0;
        background: #f2f2f2;
        width: 100%;
        border: 0;
        margin: 0 0 15px;
        padding: 15px;
        box-sizing: border-box;
        font-size: 14px;
      }
      .form button {
        font-family: "Roboto", sans-serif;
        text-transform: uppercase;
        outline: 0;
        background-color: ${primary_color};
        background-image: ${backgroundColor};
        width: 100%;
        border: 0;
        padding: 15px;
        color: #FFFFFF;
        font-size: 14px;
        -webkit-transition: all 0.3 ease;
        transition: all 0.3 ease;
        cursor: pointer;
      }
      .form .message {
        margin: 15px 0 0;
        color: ${primary_color};
        font-size: 12px;
      }
      .form .message a {
        color: ${primary_color};
        text-decoration: none;
      }
      </style>

      <div id="forgot-password-page-container" class="forgot-password-page">
          <div id="form-container" class="form">
              <div id="forgot-password-container" class="forgot-password">
                  <div id="forgot-password-header-container" class="forgot-password-header">
                      <h3>Can't Login?</h3>
                      ${logo ? `<img src="${logo}" alt="Company Logo">` : ''}
                      <p>Please enter your email to reset your password.</p>
                  </div>
              </div>
              <form class="forgot-password-form" action="${env.BASE_URL}/api/v1/auth/forgot-password" method="post">
                <input type="email" placeholder="Email" name="email"/>
                <button id="submit-button" type="submit">Submit</button>
                <p class="message">Back to <a href="${env.BASE_URL}/api/v1/auth/login">login</a>
              </form>
          </div>
      </div>
    `;

  res.send(html);
};

exports.getResetPassword = async (req, res) => {
  const { logo = '', primary_color = '#328f8a', secondary_color = '' } = req.query;

  const backgroundColor = secondary_color ? `linear-gradient(45deg,${primary_color},${secondary_color})` : primary_color;

  const html = `
      <style>
      @import url(https://fonts.googleapis.com/css?family=Roboto:300);
      .reset-password-page {
        width: 360px;
        padding: 8% 0 0;
        margin: auto;
      }
      .reset-password-page .form .reset-password{
        margin-top: -31px;
        margin-bottom: 26px;
      }
      .reset-password-header img {
        width: 150px;
        height: 150px;
      }
      .form {
        position: relative;
        z-index: 1;
        background: #FFFFFF;
        max-width: 360px;
        margin: 0 auto 100px;
        padding: 45px;
        text-align: center;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
      }
      .form input {
        font-family: "Roboto", sans-serif;
        outline: 0;
        background: #f2f2f2;
        width: 100%;
        border: 0;
        margin: 0 0 15px;
        padding: 15px;
        box-sizing: border-box;
        font-size: 14px;
      }
      .form button {
        font-family: "Roboto", sans-serif;
        text-transform: uppercase;
        outline: 0;
        background-color: ${primary_color};
        background-image: ${backgroundColor};
        width: 100%;
        border: 0;
        padding: 15px;
        color: #FFFFFF;
        font-size: 14px;
        -webkit-transition: all 0.3 ease;
        transition: all 0.3 ease;
        cursor: pointer;
      }
      .form .message {
        margin: 15px 0 0;
        color: ${primary_color};
        font-size: 12px;
      }
      .form .message a {
        color: ${primary_color};
        text-decoration: none;
      }
      </style>

      <div id="reset-password-page-container" class="reset-password-page">
          <div id="form-container" class="form">
              <div id="reset-password-container" class="reset-password">
                  <div id="reset-password-header-container" class="reset-password-header">
                      <h3>RESET PASSWORD</h3>
                      ${logo ? `<img src="${logo}" alt="Company Logo">` : ''}
                      <p>Please enter your email and new password.</p>
                  </div>
              </div>
              <form class="reset-password-form" action="${env.BASE_URL}/api/v1/auth/reset-password" method="post">
                <input type="email" placeholder="Email" name="email"/>
                <input type="password" placeholder="New Password" name="password"/>
                <button id="submit-button" type="submit">Reset Password</button>
              </form>
          </div>
      </div>
    `;

  res.send(html);
};

exports.getChangePassword = async (req, res) => {
  const { logo = '', primary_color = '#328f8a', secondary_color = '' } = req.query;

  const backgroundColor = secondary_color ? `linear-gradient(45deg,${primary_color},${secondary_color})` : primary_color;

  const html = `
      <style>
      @import url(https://fonts.googleapis.com/css?family=Roboto:300);
      .change-password-page {
        width: 360px;
        padding: 8% 0 0;
        margin: auto;
      }
      .change-password-page .form .change-password{
        margin-top: -31px;
        margin-bottom: 26px;
      }
      .change-password-header img {
        width: 150px;
        height: 150px;
      }
      .form {
        position: relative;
        z-index: 1;
        background: #FFFFFF;
        max-width: 360px;
        margin: 0 auto 100px;
        padding: 45px;
        text-align: center;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
      }
      .form input {
        font-family: "Roboto", sans-serif;
        outline: 0;
        background: #f2f2f2;
        width: 100%;
        border: 0;
        margin: 0 0 15px;
        padding: 15px;
        box-sizing: border-box;
        font-size: 14px;
      }
      .form button {
        font-family: "Roboto", sans-serif;
        text-transform: uppercase;
        outline: 0;
        background-color: ${primary_color};
        background-image: ${backgroundColor};
        width: 100%;
        border: 0;
        padding: 15px;
        color: #FFFFFF;
        font-size: 14px;
        -webkit-transition: all 0.3 ease;
        transition: all 0.3 ease;
        cursor: pointer;
      }
      .form .message {
        margin: 15px 0 0;
        color: ${primary_color};
        font-size: 12px;
      }
      .form .message a {
        color: ${primary_color};
        text-decoration: none;
      }
      </style>

      <div id="change-password-page-container" class="change-password-page">
          <div id="form-container" class="form">
              <div id="change-password-container" class="change-password">
                  <div id="change-password-header-container" class="change-password-header">
                      <h3>Change Password</h3>
                      ${logo ? `<img src="${logo}" alt="Company Logo">` : ''}
                      <p>Please enter your old password and new password.</p>
                  </div>
              </div>
              <form class="change-password-form" action="${env.BASE_URL}/api/v1/auth/change-password" method="post">
                <input type="password" placeholder="Old Password" name="oldPassword"/>
                <input type="password" placeholder="New Password" name="newPassword"/>
                <input type="password" placeholder="Confirm New Password" name="confirmPassword"/>
                <button id="submit-button" type="submit">Change Password</button>
              </form>
          </div>
      </div>
    `;

  res.send(html);
};

exports.loginV2 = async (req, res, next) => {
  const dbTrans = await db.transaction();
  try {
    const { User, Customer, Role, OnboardingFlow } = db.models;
    req.body.email = req.body.email.toLowerCase();
    // user data
    const attributes = user_attributes.USER;
    const userData = await authService.GetUserDetail(User, { email: req.body.email }, attributes);
    if (!userData) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      //   return response.customRes(req, false, { msgCode: 'USER_NOT_FOUND' }, httpStatus.UNAUTHORIZED);
    }

    const { id, email, password, status, firstName, lastName, phone, profile_image, role_id, tnc_accepted, is_notifies, marketing_email_accepted, mpin, group_access, customer_id } = userData;

    // validate credential from keyclock
    const keyClockData = await keycloakService.checkUserCreds({ email: req.body.email, password: req.body.password, otp: req.body.otp });
    if (!keyClockData.status && keyClockData.statusCode != 200) {
      return response.error(req, res, { msgCode: keyClockData.message.error_description }, keyClockData.statusCode, dbTrans);
    }

    // if (req.body.email) {
    //   if (!userData.password) {
    //     return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    //   }
    //   // compare passowrd
    //   const isLogin = passwordHash.comparePassword(req.body.password, password);
    //   if (!isLogin) {
    //     return response.error(req, res, { msgCode: 'INVALID_CREDENTIALS' }, httpStatus.UNAUTHORIZED, dbTrans);
    //   }
    // }

    // generate token
    // const token = await signToken({
    //   email: userData.email,
    //   userId: userData.id,
    //   roleId: userData.role_id,
    //   customer_id: userData.customer_id
    // });
    // // save token
    // const saveDeviceToken = await commonService.addDetail(DeviceToken, { user_id: userData.id, auth_token: token, role: userData.role_id, device_id: req.body.device_id, device_token: req.body.device_token, device_type: req.body.device_type }, dbTrans);
    // if (!saveDeviceToken) {
    //   return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    // //creating refresh token of length 20
    // const salt = token + Date.now();
    // const refreshToken = randomToken.create(salt)(100);
    // const updateRefreshToken = await commonService.updateData(User, { refresh_token: refreshToken }, { id }, dbTrans);
    // if (!updateRefreshToken[1]) {
    //   return response.error(req, res, { msgCode: 'ERROR_CREATING_TOKEN' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    // get organisation data
    const orgData = await commonService.findByCondition(Customer, { id: userData.customer_id }, [
      'id',
      'name',
      'email',
      'address',
      'address_secondary',
      'city',
      'state',
      'postal_code',
      'country',
      'business_size',
      'industry_vertical',
      'status',
      'customer_type'
    ]);
    if (!orgData) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    if (orgData.status == 'inactive') {
      return response.error(req, res, { msgCode: 'SOMEONE_CONTACT_SOON' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // get user role
    const roleData = await commonService.findByCondition(Role, { id: role_id }, ['role_name']);
    if (!roleData) {
      return response.error(req, res, { msgCode: 'ROLE_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const orgOnboarding = await commonService.findByCondition(OnboardingFlow, { user_id: id }, ['step']);
    const result = {
      id,
      email,
      phone,
      status,
      firstName,
      lastName,
      profile_image,
      role_id,
      is_notifies,
      tnc_accepted,
      marketing_email_accepted,
      group_access,
      mpin,
      customer_id,
      role: roleData.role_name,
      token: keyClockData.data.access_token,
      customerData: orgData,
      onboarding_status: orgOnboarding ? orgOnboarding.step : null
    };
    return response.success(req, res, { msgCode: 'LOGIN_SUCCESSFUL', data: result }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('login', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.login = async (req, res, next) => {
  const dbTrans = await db.transaction();
  try {
    const { User, Customer, Role, OnboardingFlow } = db.models;
    // req.body.email = req.body.email.toLowerCase();
    // user data

    const attributes = user_attributes.USER;
    let userCondition = {};

    if (req.data.email) {
      userCondition.email = req.data.email;
    } else {
      return response.error(req, res, { msgCode: 'INVALID_TOKEN' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const customerAttributes = ['id', 'name', 'email', 'address', 'address_secondary', 'city', 'state', 'postal_code', 'country', 'business_size', 'industry_vertical', 'status', 'customer_type'];

    const userData = await authService.getUserDetails(User, Customer, Role, OnboardingFlow, userCondition, {}, {}, {}, attributes, customerAttributes, {}, ['step']);
    console.log('--------', userData);
    if (!userData) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (!userData.Customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (!userData?.Customer || userData?.Customer?.status == 'inactive') {
      return response.error(req, res, { msgCode: 'SOMEONE_CONTACT_SOON' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (!userData.Role) {
      return response.error(req, res, { msgCode: 'ROLE_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // // email: (body.mpin) ? body.mpin : body.email.toLowerCase(),
    // const userData = await authService.GetUserDetail(User, userCondition, attributes);
    // if (!userData) {
    //   return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    //   //   return response.customRes(req, false, { msgCode: 'USER_NOT_FOUND' }, httpStatus.UNAUTHORIZED);
    // }

    const { id, email, status, firstName, lastName, phone, profile_image, role_id, tnc_accepted, is_notifies, marketing_email_accepted, mpin, group_access, customer_id } = userData;

    // if (req.body.email) {
    //   if (!userData.password) {
    //     return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    //   }
    //   // compare passowrd
    //   const isLogin = passwordHash.comparePassword(req.body.password, password);
    //   if (!isLogin) {
    //     return response.error(req, res, { msgCode: 'INVALID_CREDENTIALS' }, httpStatus.UNAUTHORIZED, dbTrans);
    //   }
    // }

    // generate token
    // const token = await signToken({
    //   email: userData.email,
    //   userId: userData.id,
    //   roleId: userData.role_id,
    //   customer_id: userData.customer_id
    // });
    // // save token
    // const saveDeviceToken = await commonService.addDetail(DeviceToken, { user_id: userData.id, auth_token: token, role: userData.role_id, device_id: req.body.device_id, device_token: req.body.device_token, device_type: req.body.device_type }, dbTrans);
    // if (!saveDeviceToken) {
    //   return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    // //creating refresh token of length 20
    // const salt = token + Date.now();
    // const refreshToken = randomToken.create(salt)(100);
    // const updateRefreshToken = await commonService.updateData(User, { refresh_token: refreshToken }, { id }, dbTrans);
    // if (!updateRefreshToken[1]) {
    //   return response.error(req, res, { msgCode: 'ERROR_CREATING_TOKEN' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    // // get organisation data
    // const orgData = await commonService.findByCondition(Customer, { id: userData.customer_id }, [
    //   'id',
    //   'name',
    //   'email',
    //   'address',
    //   'address_secondary',
    //   'city',
    //   'state',
    //   'postal_code',
    //   'country',
    //   'business_size',
    //   'industry_vertical',
    //   'status',
    //   'customer_type'
    // ]);
    // if (!orgData) {
    //   return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    // }

    // // get user role
    // const roleData = await commonService.findByCondition(Role, { id: role_id });
    // if (!roleData) {
    //   return response.error(req, res, { msgCode: 'ROLE_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    // const orgOnboarding = await commonService.findByCondition(OnboardingFlow, { user_id: id }, ['step']);

    let result = {
      id,
      email,
      phone,
      status,
      firstName,
      lastName,
      profile_image,
      role_id,
      is_notifies,
      tnc_accepted,
      marketing_email_accepted,
      group_access,
      mpin,
      customer_id,
      role: userData?.Role.role_name,
      role_level: userData?.Role.role_name,
      customerData: userData?.Customer,
      onboarding_status: userData?.OnboardingFlow ? userData?.OnboardingFlow?.step : null
    };
    // req.data.is_module_head = roleData.is_module_head;
    if (userData?.Role?.is_module_head) {
      result.role = 'Data Protection Officer';
      result.role_level = roleData.role_name;
    }

    if (!userData.firstLogin) {
      const firstLogin = await commonService.updateData(User, { firstLogin: true }, { id: userData?.id }, dbTrans);
      if (!firstLogin[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    // console.log("00000",req?.kauth?.grant?.access_token?.token)
    res.cookie('access_token', req?.kauth?.grant?.access_token?.token); // options is optional
    // res.clearCookie('token');
    return response.success(req, res, { msgCode: 'LOGIN_SUCCESSFUL', data: result }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('login', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.checkEmail = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User, OnboardingFlow } = db.models;
    // decoding email from token
    if (!req.body.email) {
      const decodedToken = jwt.decode(req.body.token);
      req.body.email = decodedToken.email;
    }

    const onboarding = await commonService.getDataAssociate(OnboardingFlow, User, {}, { email: req.body.email }, {}, ['id', 'email', 'phone', 'firstName', 'lastName', 'country_code', 'customer_id', 'role_id']);
    if (onboarding) {
      // to redirect to correct onboarding page
      const onboarding_status = onboarding.step;
      const otp = Math.floor(100000 + Math.random() * 900000);
      const token = await signToken(
        {
          email: req.body.email,
          otp: otp,
          role: onboarding.User.role_id
        },
        3600
      );

      // user update
      const updateUser = await commonService.updateData(User, { otp: otp }, { email: req.body.email }, dbTrans);
      if (!updateUser[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      // send mail to user for verify his email. don't send if email is already verified.
      if (!req.path.endsWith(CLIENT_ONBOARD.client) && onboarding_status !== ONBOARDING_STATUS.EMAIL_VERIFIED && onboarding_status !== ONBOARDING_STATUS.PASSWORD_UPDATED && onboarding_status !== ONBOARDING_STATUS.ONBOARDING_COMPLETED) {
        const current_year = moment().year();
        const subject = `${MESSAGE.VERIFICATION_CODE} ${otp}`;
        const textTemplate = 'email_template.ejs';
        const sendData = {
          name: `${onboarding.User.firstName} ${onboarding.User.lastName}`,
          otp: otp,
          current_year: current_year
        };

        sendMail(req.body.email, sendData, subject, textTemplate);
      }

      return response.success(
        req,
        res,
        {
          msgCode: 'EMAIL_ALREADY_EXISTS',
          data: {
            ...onboarding.User,
            signup_token: token,
            onboarding_status: onboarding.step
          }
        },
        httpStatus.OK,
        dbTrans
      );
    }

    return response.success(req, res, { msgCode: 'EMAIL_NOT_FOUND' }, httpStatus.NO_CONTENT, dbTrans);
  } catch (error) {
    console.log('checkEmail', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.generateAccessToken = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User, DeviceToken } = db.models;

    // check user on basis of refresh token
    const checkRefreshToken = await commonService.findByCondition(User, { refresh_token: req.body.refresh_token }, ['id', 'email', 'customer_id', 'role_id']);
    if (!checkRefreshToken) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const token = await signToken({
      email: checkRefreshToken.email,
      userId: checkRefreshToken.id,
      role: checkRefreshToken.role_id,
      customer_id: checkRefreshToken.customer_id
    });

    const salt = token + Date.now();
    const refreshToken = randomToken.create(salt)(100);

    // update refresh token
    const updateUserToken = await commonService.updateData(User, { refresh_token: refreshToken }, { id: checkRefreshToken.id }, dbTrans);
    if (!updateUserToken[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // save access token
    const deviceToken = await commonService.addDetail(DeviceToken, { user_id: checkRefreshToken.id.id, auth_token: token, role: checkRefreshToken.role_id }, dbTrans);
    if (!deviceToken) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_TOKEN' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(
      req,
      res,
      {
        msgCode: 'ACCESS_TOKEN_CREATED',
        data: {
          token,
          refresh_token: refreshToken
        }
      },
      httpStatus.CREATED,
      dbTrans
    );
  } catch (error) {
    console.log('generateAccessToken', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.signup = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const {
      User,
      Customer,
      Group,
      CustomerResources,
      Role,
      UserRolePrivileges,
      OnboardingFlow,
      GroupUser,
      Resources,
      CustomerRegulations,
      UCFControl,
      UCFCustomControl,
      UCFBusinessRequirement,
      CustomerBusinessRequirements,
      UCFCategory,
      UCFCustomCategory
    } = db.models;

    req.body.email = req.body.email.toLowerCase();
    // check client is onboarding manually or admin is adding
    const customerSignup = req.path.endsWith(CLIENT_ONBOARD.client);

    if (!customerSignup && req.body.email !== req.data.email) {
      return response.error(req, res, { msgCode: 'INVALID_TOKEN' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const userExists = await commonService.findByCondition(User, { email: req.body.email }, ['id'], dbTrans);

    if (userExists) {
      return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (customerSignup) {
      const customerExists = await commonService.findByCondition(Customer, { email: req.body.email }, ['id'], dbTrans);

      if (customerExists) {
        return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      req.body.name = req.body.customer_name;
    }

    // create Customer
    const organisationData = await commonService.addDetail(Customer, req.body, dbTrans);
    if (!organisationData) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const roleData = [
      { role_name: USER_ROLE[2], customer_id: organisationData.id },
      { role_name: USER_ROLE[8], customer_id: organisationData.id }
    ];
    //  console.log("-----roleData-----",roleData);
    // create role
    const createRole = await authService.BulkData(Role, roleData, dbTrans);
    if (!createRole) {
      return response.error(req, res, { msgCode: 'ERROR_CREATE_ROLE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let resource_list = [];

    if (customerSignup) {
      resource_list = req.body.resource_list;
    } else {
      resource_list = await commonService.getListWithoutCount(
        Resources,
        { resource_name: { [Op.in]: ['Account Setup', 'Profile Configuration', 'On-boarding Questionnaire', 'Company Structure', 'Access Management', 'Role Management', 'User Management', 'About GoTrust', 'Support'] }, status: 1 },
        ['resource_id']
      );
      resource_list = resource_list?.map(item => item.resource_id);
    }

    let orgResourceData = [],
      userRolePrivilegesData = [];
    resource_list?.forEach(element => {
      orgResourceData.push({
        customer_id: organisationData.id,
        resource_id: element
      });
      // console.log("-----roleData-----", createRole);
      //
      userRolePrivilegesData.push({
        role_id: createRole[0].role_name !== USER_ROLE[8] ? createRole[0].id : createRole[1].id,
        resource_id: element
      });
    });

    // create organization resource
    const createOrgRole = await authService.BulkData(CustomerResources, orgResourceData, dbTrans);
    if (!createOrgRole) {
      return response.error(req, res, { msgCode: 'ERROR_CREATE_ROLE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // create User Priviledges
    const createUserPrivileges = await authService.BulkData(UserRolePrivileges, userRolePrivilegesData, dbTrans);
    if (!createUserPrivileges) {
      return response.error(req, res, { msgCode: 'ERROR_CREATE_ROLE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // create a otp
    const otp = Math.floor(100000 + Math.random() * 900000);
    // generate token
    const token = await signToken(
      {
        email: req.body.email,
        otp: otp,
        role: createRole[0].id
      },
      3600
    );

    req.body.access_token = token;
    req.body.role_id = createRole[0].id;
    req.body.otp = otp;
    req.body.customer_id = organisationData.id;

    // create User
    const password = crypto.randomBytes(8).toString('hex');
    // Encrypt the password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Add the hashed password to the request body
    req.body.password = hashedPassword;
    const saveUserData = await commonService.addDetail(User, req.body, dbTrans);
    if (!saveUserData) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (customerSignup) {
      const keycloakToken = await keycloakService.getToken();
      if (!keycloakToken) {
        return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      const keycloakUser = {
        username: req.body.email,
        email: req.body.email,
        enabled: true,
        emailVerified: true,
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        attributes: {
          firstLogin: ['true']
        },
        credentials: [{ type: 'password', value: password, temporary: true }]
      };

      const keycloakUserResponse = await keycloakService.createUser(keycloakToken.access_token, keycloakUser);
      if (keycloakUserResponse.statusText === 'Conflict') {
        return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXIST_IN_KEYCLOAK' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      if (!keycloakUserResponse) {
        return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    // console.log("--------------------",keycloakUserResponse)

    // save onboarding flow
    const saveOnboardingFlow = await commonService.addDetail(OnboardingFlow, { user_id: saveUserData.id, step: customerSignup ? ONBOARDING_STATUS.ONBOARDING_COMPLETED : ONBOARDING_STATUS.USER_CREATED }, dbTrans);
    if (!saveOnboardingFlow) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Group data
    const groupDataArray = [
      {
        customer_id: organisationData.id,
        name: req.body.customer_name,
        user_id: saveUserData.id
      }
    ];
    // create Group
    const createGroup = await authService.BulkData(Group, groupDataArray, dbTrans);
    if (!createGroup) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // update User Group Access
    const groupId = createGroup[0].name != 'Unassigned Assets' ? createGroup[0].id : createGroup[1].id;

    const addGroupUser = await commonService.addDetail(GroupUser, { user_id: saveUserData.id, group_id: groupId }, dbTrans);
    if (!addGroupUser) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (req.body.regulation_ids) {
      // Adding Regulations
      const dbTrans1 = await db.transaction();
      const regulationData = {
        customer_id: organisationData.id,
        entity_id: groupId,
        regulation_ids: req.body.regulation_ids
      };
      const addRegulation = await commonService.addDetail(CustomerRegulations, regulationData, dbTrans1);
      if (!addRegulation) {
        await dbTrans1.rollback();
        throw new Error('REGULATION_ADD_FAILED');
      }
      // adding custom business requiremement after adding the regulation

      // const data = await commonService.getListAssociateWithoutCount(UCFControl, UCFBusinessRequirement, {}, {}, ['id'], ['id']);

      // const customData = req.body.regulation_ids.flatMap(regulation_id =>
      //   data.flatMap(control =>
      //     control.UCFBusinessRequirements.map(busiReq => ({
      //       customer_id: organisationData.id,
      //       control_id: control.id,
      //       busi_req_id: busiReq.id,
      //       group_id: groupId,
      //       regulation_id: regulation_id // Assign regulation_id dynamically
      //     }))
      //   )
      // );
      // const addData = await commonService.bulkAdd(CustomerBusinessRequirements, customData, dbTrans);
      // if (!addData) {
      //   return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      // }

      const categoryData = await commonService.getList(UCFCategory, {}, ['id']);
      if (!categoryData) {
        await dbTrans1.rollback();
        throw new Error('CATEGORY_FETCH_FAILED');
      }

      const transformedData = categoryData.rows.map(row => ({ category_id: row.id, customer_id: organisationData.id }));
      const addCategoryData = await commonService.bulkAdd(UCFCustomCategory, transformedData, dbTrans1);
      if (!addCategoryData) {
        await dbTrans1.rollback();
        throw new Error('UPDATE_ERROR');
      }

      const controlData = await commonService.getList(UCFControl, {}, ['id', 'category_id']);
      if (!controlData) {
        await dbTrans1.rollback();
        throw new Error('UPDATE_ERROR');
      }
      // const transformedControlData = controlData.rows.map(row => ({ control_id: row.id, customer_id: organisationData.id }));
      const transformedControlData = controlData.rows.map(row => {
        // Find matching category from addCategoryData
        const matchedCategory = addCategoryData.find(category => category.category_id === row.category_id);
        return {
          control_id: row.id,
          customer_id: organisationData.id,
          category_id: matchedCategory ? matchedCategory.id : null // Fallback to null if not found
        };
      });

      // console.log('transformedControlData', transformedControlData);

      const addControlData = await commonService.bulkAdd(UCFCustomControl, transformedControlData, dbTrans1);
      if (!addControlData) {
        await dbTrans1.rollback();
        throw new Error('UPDATE_ERROR');
      }

      await dbTrans1.commit();
      // dbTrans = undefined;

      // const data = await commonService.getListAssociateWithoutCount(UCFCustomControl, UCFBusinessRequirement, {}, {}, ['id'], ['id']);

      const data = await commonService.getListWith3Models(UCFCustomControl, UCFControl, UCFBusinessRequirement, { customer_id: organisationData.id }, {}, {}, ['id'], ['id'], ['id']);
      if (!data) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      // console.log('data', data[0]);
      // console.log('data', data[0].UCFControl.UCFBusinessRequirements);

      const customData = req.body.regulation_ids.flatMap(regulation_id =>
        data.flatMap(control =>
          control.UCFControl.UCFBusinessRequirements.map(busiReq => ({
            customer_id: organisationData.id,
            control_id: control.id,
            busi_req_id: busiReq.id,
            group_id: groupId,
            regulation_id: regulation_id // Assign regulation_id dynamically
          }))
        )
      );
      // console.log(customData[0]);
      const addData = await commonService.bulkAdd(CustomerBusinessRequirements, customData, dbTrans);
      if (!addData) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    // if (!customerSignup) {
    //   // send mail to user for verify his email
    //   const current_year = moment().year();
    //   const subject = `${MESSAGE.VERIFICATION_CODE} ${otp}`;
    //   const textTemplate = "email_template.ejs";
    //   const sendData = {
    //     name: `${saveUserData.firstName} ${saveUserData.lastName}`,
    //     otp: otp,
    //     current_year: current_year
    //   };

    //   sendMail(
    //     req.body.email,
    //     sendData,
    //     subject,
    //     textTemplate,
    //   );
    // }

    if (customerSignup) {
      // save onboarding flow
      const subject = `Welcome to GoTrust`;
      const textTemplate = 'new-user-password-email.ejs';

      const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
      const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'https://devapi.gotrust.tech';

      const sendData = {
        name: `${req.body.firstName} ${req.body.lastName}`,
        email: req.body.email,
        password: password,
        logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
        login_url: `${frontEndUrl}`,
        email_logo_url: `${backEndUrl}/app/public/email_log.png`
      };
      sendMail(req.body.email, sendData, subject, textTemplate);
    }

    const msgCode = customerSignup ? 'CLIENT_ONBOARDED' : 'SIGNUP_SUCCESSFUL';
    return response.success(
      req,
      res,
      {
        msgCode: msgCode,
        data: customerSignup
          ? {
              customer_id: saveUserData.customer_id
            }
          : {
              id: saveUserData.id,
              email: saveUserData.email,
              phone: saveUserData.phone,
              firstName: saveUserData.firstName,
              lastName: saveUserData.lastName,
              country_code: saveUserData.country_code,
              customer_id: saveUserData.customer_id,
              signup_token: token,
              onboarding_status: ONBOARDING_STATUS.USER_CREATED
            }
      },
      httpStatus.CREATED,
      dbTrans
    );
  } catch (error) {
    console.log('signup', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.verifyOtp = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User, DeviceToken, OnboardingFlow } = db.models;
    req.body.email = req.body.email.toLowerCase();

    // check user
    const userData = await authService.GetUserDetail(User, { email: req.body.email });
    if (!userData) {
      return response.error(req, res, { msgCode: 'EMAIL_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (userData.otp != req.body.otp) {
      return response.error(req, res, { msgCode: 'INVALID_OTP' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    // if (userData.otp == req.body.otp) {
    const token = await signToken({
      email: userData.email,
      userId: userData.id,
      role: userData.role_id
    });
    // save deviceToken
    const saveDeviceToken = await commonService.addDetail(DeviceToken, { user_id: userData.id, auth_token: token, role: userData.role_id }, dbTrans);
    if (!saveDeviceToken) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // update otp null
    const updateUser = await commonService.updateData(User, { otp: null, is_email_verified: true }, { id: userData.id }, dbTrans);
    if (!updateUser[1]) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const onboardingFlow = await commonService.findByCondition(OnboardingFlow, { user_id: userData.id });

    if (onboardingFlow?.step == ONBOARDING_STATUS.ORGANISATION_UPDATED) {
      const subject = `${MESSAGE.SETUP}`;
      const textTemplate = 'set_up_mail.ejs';
      const sendData = {
        name: `${userData.firstName} ${userData.lastName}`,
        url: `${process.env.SERVER_IP}/set-password/${token}`
      };
      // update onboarding flow
      const updateOnboardingFlow = await commonService.updateData(OnboardingFlow, { step: ONBOARDING_STATUS.EMAIL_VERIFIED }, { user_id: userData.id }, dbTrans);
      if (!updateOnboardingFlow[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      sendMail(userData.email, sendData, subject, textTemplate);
    }

    return response.success(req, res, { msgCode: 'EMAIL_VERIFIED', data: { token: token } }, httpStatus.OK, dbTrans);
    // }
    // else {
    //   return response.error(req, res, { msgCode: 'INVALID_OTP' }, httpStatus.BAD_REQUEST);
    // }
  } catch (error) {
    console.log('verifyOtp', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.forgotPassword = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User } = db.models;
    const userData = await authService.GetUserDetail(User, { email: req.body.email });
    if (!userData) {
      return response.error(req, res, { msgCode: 'EMAIL_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const otp = Math.floor(100000 + Math.random() * 900000);

    // generate token
    const token = await signToken(
      {
        email: req.body.email.toLowerCase(),
        otp: otp,
        role: userData.role_id
      },
      3600
    );

    const data = {
      otp,
      access_token: token,
      is_email_verified: false
    };

    // update user data
    const updateUser = await commonService.updateData(User, data, { id: userData.id }, dbTrans);
    if (!updateUser[1]) {
      return response.error(req, res, { msgCode: 'OTP_NOT_GENERATE' }, httpStatus.BAD_REQUEST, dbTrans);
      // return response.customRes(event, false, { msgCode: 'OTP_NOT_GENERATE' }, httpStatus.BAD_REQUEST);
    }

    const current_year = moment().year();
    const subject = MESSAGE.FORGOT_PASSWORD_SUBJECT;
    const textTemplate = 'email_template.ejs';

    const sendData = {
      name: `${userData.firstName} ${userData.lastName}`,
      otp: otp,
      current_year: current_year
    };

    await sendMail(req.body.email, sendData, subject, textTemplate);

    return response.success(req, res, { msgCode: 'OTP_SENT' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('forgotPassword', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.changePassword = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User } = db.models;

    // get user data
    const userData = await authService.GetUserDetail(User, { id: req.data.userId });
    if (!userData) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // match password
    const currentPasswordMatched = await bcrypt.compare(req.body.current_password.trim(), userData.password);
    if (!currentPasswordMatched) {
      return response.error(req, res, { msgCode: 'PASSWORD_NOT_MATCH' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const hash = bcrypt.hashSync(req.body.password, salt);

    // update password
    const updatePassword = await commonService.updateData(User, { password: hash }, { id: userData.id }, dbTrans);
    if (!updatePassword[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'PASSWORD_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('changePassword', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.changePasswordV2 = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { password, current_password } = req.body;
    const { email } = req.data;
    const { User } = db.models;
    // console.log(email, password, current_password);
    const userId = req.kauth?.grant?.access_token?.content?.sub; //keycloak userId
    const data = {
      password: password,
      userId: userId
    };
    const userData = await authService.GetUserDetail(User, { email });
    if (!userData) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // validate credential from keyclock
    const keycloakValidationResult = await keycloakService.checkUserCredsV2({ email: email, password: current_password });
    if (!keycloakValidationResult?.status || keycloakValidationResult?.statusCode != 200) {
      return response.error(req, res, { msgCode: keycloakValidationResult.message.error_description }, keycloakValidationResult.statusCode, dbTrans);
    }
    const updatedPass = await keycloakService.resetPassword(data);
    if (!updatedPass) {
      return response.error(req, res, { msgCode: 'PASSWORD_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const hashPassword = bcrypt.hashSync(password, salt);
    const updateProfile = await commonService.updateData(User, { password: hashPassword }, { email: email }, dbTrans);
    if (!updateProfile[1]) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const current_year = moment().year();
    const subject = `GoTrust Password Successfully Changed`;
    const textTemplate = 'changePassword.ejs';
    const sendData = {
      name: `${userData.firstName}`,
      current_year: current_year
    };
    await sendMail(email, sendData, subject, textTemplate);

    return response.success(req, res, { msgCode: 'PASSWORD_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.resetPassword = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User, DeviceToken } = db.models;
    req.body.email = req.body.email.toLowerCase();
    // check user is exist or not
    const checkUser = await commonService.findByCondition(User, { email: req.body.email, deletedAt: null });
    if (!checkUser) {
      return response.error(req, res, { msgCode: 'EMAIL_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (!checkUser.is_email_verified) {
      return response.error(req, res, { msgCode: 'OTP_NOT_VERIFIED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const hash = bcrypt.hashSync(req.body.password, salt);
    // update Password
    const updateProfile = await commonService.updateData(User, { password: hash }, { id: checkUser.id }, dbTrans);
    if (!updateProfile[1]) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // generate token
    const token = await signToken({
      email: req.body.email,
      userId: updateProfile[1].id,
      role: updateProfile[1].role_id,
      customer_id: updateProfile[1].customer_id
    });

    req.body.user_id = updateProfile[1].id;
    req.body.role = updateProfile[1].role_id;
    req.body.auth_token = token;
    // save token
    const saveDeviceToken = await commonService.addDetail(DeviceToken, req.body, dbTrans);
    if (!saveDeviceToken) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const userProfile = { ...updateProfile[1].dataValues };
    delete userProfile.password;
    delete userProfile.access_token;
    delete userProfile.createdAt;
    delete userProfile.updatedAt;
    delete userProfile.deletedAt;
    userProfile.token = token;

    return response.success(req, res, { msgCode: 'PASSWORD_UPDATED', data: userProfile }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('resetPassword', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.logout = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { DeviceToken } = db.models;
    let token = req.headers.authorization;
    token = token.replace(/^Bearer\s+/, '');

    // delete token
    const deleteToken = await commonService.deleteQuery(DeviceToken, { auth_token: token }, dbTrans);
    if (!deleteToken) {
      return response.error(req, res, { msgCode: 'ERROR_WHILE_DELETE_TOKEN' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'LOGOUT_SUCCESSFUL' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('logout', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.logoutV2 = async (req, res) => {
  try {
    res.clearCookie('access_token', {
      maxAge: 0,
      httpOnly: true,
      secure: env.NODE_ENV !== "development",        
      sameSite: "strict",   
    });

    return response.success(req, res, { msgCode: 'LOGOUT_SUCCESSFUL' }, httpStatus.OK);
  } catch (error) {
    console.error('auth.js: logoutV2: error: ', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.validateUser = async (req, res) => {
  try {
    const { User } = db.models;

    const checkUser = await commonService.findByCondition(User, { id: req.data.userId }, ['id', 'customer_id', 'firstName', 'lastName', 'role_id', 'email', 'status']);
    if (!checkUser) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const data = {
      customer_id: checkUser.customer_id,
      userId: checkUser.id,
      firstName: checkUser.firstName,
      lastName: checkUser.lastName,
      role_id: checkUser.role_id,
      email: checkUser.email,
      status: checkUser.status
    };

    return response.success(req, res, { msgCode: 'USER_VALIDATED', data }, httpStatus.OK);
  } catch (error) {
    console.log('validateUser', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.guestSendOtp = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { GuestUser, DataSubject } = db.models;

    const checkDataSubject = await commonService.findByCondition(DataSubject, { email: req.body.email }, ['id', 'email', 'first_name', 'last_name']);

    if (checkDataSubject) {
      const otp = Math.floor(100000 + Math.random() * 900000);

      //check if user exist.
      const user = await commonService.findByCondition(GuestUser, { email: req.body.email }, ['id', 'email']);
      if (user) {
        //update otp
        const updateUser = await commonService.updateData(GuestUser, { otp: otp }, { email: req.body.email }, dbTrans);
        if (!updateUser[1]) {
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      } else {
        //create user
        let insertData = {
          email: req.body.email,
          otp: otp
        };
        const addUser = await commonService.addDetail(GuestUser, insertData, dbTrans);
        if (!addUser) {
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }

      const current_year = moment().year();
      const subject = `Your GoTrust Login OTP: ${otp}`;
      const textTemplate = 'guest_otp_template.ejs';
      const sendData = {
        name: `${checkDataSubject.first_name} ${checkDataSubject.last_name}`,
        otp: otp,
        current_year: current_year
      };

      sendMail(req.body.email, sendData, subject, textTemplate);

      return response.success(
        req,
        res,
        {
          msgCode: 'OTP_SENT',
          data: {}
        },
        httpStatus.OK,
        dbTrans
      );
    }

    return response.error(req, res, { msgCode: 'EMAIL_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
  } catch (error) {
    console.log('checkEmail', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.guestVerifyOtp = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { GuestUser, DataSubject, DsrRequest, Group, User, DeviceToken, OnboardingFlow } = db.models;
    req.body.email = req.body.email.toLowerCase();

    // check user
    const userData = await authService.GetUserDetail(GuestUser, { email: req.body.email });
    if (!userData) {
      return response.error(req, res, { msgCode: 'EMAIL_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (userData.otp != req.body.otp) {
      return response.error(req, res, { msgCode: 'INVALID_OTP' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const token = await signToken(
      {
        email: userData.email,
        userId: userData.id
      },
      36000
    );

    // update otp null
    const updateUser = await commonService.updateData(GuestUser, { otp: null }, { id: userData.id }, dbTrans);
    if (!updateUser[1]) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const checkDataSubject = await commonService.findByCondition(DataSubject, { email: req.body.email }, ['id', 'email', 'first_name', 'last_name', 'customer_id', 'is_email_verified']);
    const checkDataRequest = await commonService.findByCondition(DsrRequest, { data_subject_id: checkDataSubject.id }, ['business_unit']);
    const getgroup = await commonService.findByCondition(Group, { id: checkDataRequest.business_unit }, ['name']);

    if (!checkDataSubject.is_email_verified) {
      // const baseUrl = process.env.FRONTEND_BASE_URL;
      // const mailUrl = `${baseUrl}/data-subject-rights/my-request`;

      // const sendData = {
      //   content: `Thank you for verifying your email address. To facilitate further communication regarding your request with ${getgroup.name}, you can access the portal using the link below.`,
      //   dsr_link: mailUrl, // Add verification link here
      //   dsr_link_test: 'Click Here',
      //   user: checkDataSubject?.first_name + ' ' + checkDataSubject?.last_name
      // };
      // const textTemplate = 'dsr_confrimation_mail.ejs';
      // const subject = `Confirmation of Email Verification and Portal Access`;

      // await sendMail(userData.email, sendData, subject, textTemplate);
      const updateEmailStatus = await commonService.updateData(DataSubject, { is_email_verified: true }, { email: req.body.email }, dbTrans);
      if (!updateEmailStatus) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.NOT_FOUND, dbTrans);
      }
    }

    const returnUserData = {
      first_name: checkDataSubject.first_name,
      last_name: checkDataSubject.last_name,
      customer_id: checkDataSubject.customer_id,
      token: token
    };
    return response.success(req, res, { msgCode: 'EMAIL_VERIFIED', data: returnUserData }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('verifyOtp', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.verifyotpDsrForm = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { PendingVerification, DsrRequest, GuestUser, DataSubject, CustomRequestTask } = db.models;
    let verified;
    let url;
    const otp = req.body.otp;
    const secretKey = '321@123';
    const encryptedRequestId = req.params.request_id.replaceAll('Por21Ld', '/');
    const reqId = CryptoJS.AES.decrypt(encryptedRequestId, secretKey);
    const requestId = parseInt(reqId.toString(CryptoJS.enc.Utf8));
    const encryptedFormId = req.params.form_id.replaceAll('Por21Ld', '/');
    const formId = CryptoJS.AES.decrypt(encryptedFormId, secretKey);
    const form_id = parseInt(formId.toString(CryptoJS.enc.Utf8));
    const baseUrl = process.env.FRONTEND_BASE_URL;
    const order = [
      ['stage_id', 'ASC'], // First, order by stage_id ascending
      ['id', 'ASC'] // Then, order by id ascending within each stage
    ];

    let automationObj = {
      request_id: requestId
    };

    const findUser = await commonService.findByCondition(PendingVerification, { request_id: requestId, status: 'PENDING' }, {});
    if (!findUser) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const verifyOTP = findUser.otp;
    if (otp !== verifyOTP) {
      return response.error(req, res, { msgCode: 'INVALID_OTP' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const verfication = await commonService.updateData(PendingVerification, { status: 'COMPLETED' }, { request_id: requestId, status: 'PENDING' }, dbTrans);
    if (!verfication[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const firstVerificationDSR = await commonService.updateData(DsrRequest, { first_verification: true }, { id: requestId }, dbTrans);
    if (!firstVerificationDSR[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const userAlreadyExist = await commonService.findByCondition(GuestUser, { email: findUser.email }, {});
    if (!userAlreadyExist) {
      url = `${baseUrl}/data-subject-rights/my-request/email-verification/${req.params.request_id}/${req.params.form_id}`; // Example URL with token

      const sendData = {
        content: `<p>Dear ${findUser?.firstName} ${findUser?.lastName},</p>
        <p>Thank you for submitting your DSR (Data Subject Request) form.</p>
        <p>You're just one click away from getting started!.</p>
        <p>To proceed with processing your request, please verify your email address by clicking the link below:</p>
        <p><a href=${url}>Confirm Your Email</a></p>
        <p>If you didn’t initiate this request, please ignore this email.</p>
        `,
        dsr_link: null, // Add verification link here
        dsr_link_test: null
      };

      const textTemplate = 'dsr_verify_mail.ejs';
      const subject = `DSR [${firstVerificationDSR[1].dsr_id}] Email Verification`;

      await sendMail(findUser.email, sendData, subject, textTemplate);
    } else {
      const secondVerification = await commonService.updateData(DsrRequest, { second_verification: true }, { id: requestId }, dbTrans);
      if (!secondVerification[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      //Automation start here
      await automationOnCreateRequest(automationObj);
      /*
            let taskLimit = 1;
            const getTasks = await commonService.getList(CustomRequestTask,{ request_id: requestId},{},taskLimit,null, order);
            const dataSubjectData = await commonService.getDataAssociate(DsrRequest,DataSubject,{ id: requestId },{},["id", "dsr_id"],["id", "email", "first_name", "last_name", "phone_no"]);
            const automation_id = [];

            for (task of getTasks.rows) {
              if (task.progress !== "COMPLETED" && task.activepieces_automation_id) {
                const data = {
                  task_id: task.id,
                  activepieces_automation_id: task.activepieces_automation_id,
                  dsr_request_id: dataSubjectData.id,
                  data_subject_id: dataSubjectData.DataSubject.id,
                  phone_number: dataSubjectData.DataSubject?.phone_no,
                };
                automation_id.push(data);
              } else {
                break;
              }
            }

            console.log(automation_id);
            if (automation_id) {
              for (let activepiecesObj of automation_id) {
                console.log("=== inside for loop");
                if (activepiecesObj) {
                  console.log("=== inside if condition");
                  const activepiecesWebhookUlr = process.env.ACTIVEPIECES_WEBHOOK_URL? process.env.ACTIVEPIECES_WEBHOOK_URL: "https://workflow-dev.gotrust.tech";
                  let webhookurl =activepiecesWebhookUlr +"/api/v1/webhooks/" +activepiecesObj.activepieces_automation_id;
                  console.log(webhookurl);

                  let paramData = {
                    task_id: activepiecesObj.task_id,
                    activepieces_automation_id: activepiecesObj.activepieces_automation_id,
                    phone_number: activepiecesObj.phone_number,
                    dsr_request_id: activepiecesObj.dsr_request_id,
                    data_subject_id: activepiecesObj.data_subject_id,
                  };
                  let jsonData = JSON.stringify(paramData);
                  console.log(jsonData);
                  webhookurl = `${webhookurl}?custom_data=${jsonData}`;
                  if (webhookurl) {
                    let config = {
                      method: "post",
                      maxBodyLength: Infinity,
                      url: webhookurl,
                      // url: `https://workflow-dev.gotrust.tech/api/v1/webhooks/${activepiecesObj.activepieces_automation_id}?custom_data=${jsonData}`,
                      headers: {},
                    };

                    console.log("---", config);
                    let x = await axios.request(config);
                    console.log(x);
                    console.log("=== webhook is called");
                  }
                }
              }
            }
            */
      //Automation end here

      url = `${baseUrl}/data-subject-rights/my-request`;

      const sendData = {
        content: `<p>Dear ${findUser?.firstName} ${findUser?.lastName},</p>
      <p>Thank you for submitting your Data Subject Request. You can now access your dedicated DSR portal to track the progress and manage your request.</p>
      <p><a href=${url}>Click here to access your portal</a></p>
      <p>For security, please use the same email address you provided during submission to log in.</p>
      <p>If you have any questions or need further assistance, feel free to reach out.</p>`,
        dsr_link: null, // Add verification link here
        dsr_link_test: null
      };

      const textTemplate = 'dsr_verify_mail.ejs';
      const subject = `Access Your Data Subject Request (DSR) [${secondVerification[1]?.dsr_id}] Portal`;

      await sendMail(findUser.email, sendData, subject, textTemplate);
    }

    verified = true;

    return response.success(req, res, { msgCode: 'FORM_VERIFIED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('verifyOtp', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.checkVerification = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { DsrRequest, DataSubject, GuestUser, CustomRequestTask } = db.models;
    const secretKey = '321@123';
    const encryptedRequestId = req.params.request_id.replaceAll('Por21Ld', '/');
    const reqId = CryptoJS.AES.decrypt(encryptedRequestId, secretKey);
    const request_id = parseInt(reqId.toString(CryptoJS.enc.Utf8));
    const encryptedFormId = req.params.form_id.replaceAll('Por21Ld', '/');
    const formId = CryptoJS.AES.decrypt(encryptedFormId, secretKey);
    const form_id = parseInt(formId.toString(CryptoJS.enc.Utf8));
    const order = [
      ['stage_id', 'ASC'], // First, order by stage_id ascending
      ['id', 'ASC'] // Then, order by id ascending within each stage
    ];
    const dsrVerification = await commonService.findByCondition(DsrRequest, { id: request_id }, ['id', 'data_subject_id', 'first_verification', 'second_verification']);
    if (!dsrVerification) {
      return response.error(req, res, { msgCode: 'REQUEST_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const findEmail = await commonService.findByCondition(DataSubject, { id: dsrVerification.data_subject_id }, ['id', 'first_name', 'last_name', 'email', 'phone_no']);

    if (!findEmail) {
      return response.error(req, res, { msgCode: 'EMAIL_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let send_otp_status, email_verification;

    if (dsrVerification.first_verification === true && dsrVerification.second_verification === true) {
      send_otp_status = true;
      email_verification = true;
      return response.success(req, res, { msgCode: 'ALREADY_VERIFIED', data: { send_otp_status, email_verification } }, httpStatus.OK, dbTrans);
    }
    const secondVerification = await commonService.updateData(DsrRequest, { second_verification: true }, { id: request_id }, dbTrans);
    if (!secondVerification[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const baseUrl = process.env.FRONTEND_BASE_URL;

    const user = await commonService.findByCondition(GuestUser, { email: findEmail.email }, ['id', 'email']);
    if (!user) {
      //create user
      let insertData = {
        email: findEmail.email
      };
      const addUser = await commonService.addDetail(GuestUser, insertData, dbTrans);
      if (!addUser) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    // const url = `${baseUrl}/data-subject-rights/my-request/${req.params.request_id}/${req.params.form_id}}`;  // Example URL with token
    const url = `${baseUrl}/data-subject-rights/my-request`;

    const sendData = {
      content: `<p>Dear ${findEmail.first_name} ${findEmail.last_name},</p>
      <p>Thank you for submitting your Data Subject Request. You can now access your dedicated DSR portal to track the progress and manage your request.</p>
      <p><a href=${url}>Click here to access your portal</a></p>
      <p>For security, please use the same email address you provided during submission to log in.</p>
      <p>If you have any questions or need further assistance, feel free to reach out.</p>`,
      dsr_link: null, // Add verification link here
      dsr_link_test: null
    };

    const textTemplate = 'dsr_verify_mail.ejs';
    const subject = `Access Your Data Subject Request (DSR) [${secondVerification[1]?.dsr_id}] Portal`;

    await sendMail(findEmail.email, sendData, subject, textTemplate);

    //Automation start here
    let automationObj = {
      request_id: request_id
    };
    await automationOnCreateRequest(automationObj);
    /*
            let taskLimit = 1;
            const getTasks = await commonService.getList(CustomRequestTask,{ request_id: request_id},{},taskLimit,null, order);
            const dataSubjectData = await commonService.getDataAssociate(DsrRequest,DataSubject,{ id: request_id },{},["id", "dsr_id"],["id", "email", "first_name", "last_name", "phone_no"]);
            const automation_id = [];

            for (task of getTasks.rows) {
              if (task.progress !== "COMPLETED" && task.activepieces_automation_id) {
                const data = {
                  task_id: task.id,
                  activepieces_automation_id: task.activepieces_automation_id,
                  dsr_request_id: dataSubjectData.id,
                  data_subject_id: dataSubjectData.DataSubject.id,
                  phone_number: dataSubjectData.DataSubject?.phone_no,
                };
                automation_id.push(data);
              } else {
                break;
              }
            }



            console.log(automation_id);
            if (automation_id) {
              for (let activepiecesObj of automation_id) {
                console.log("=== inside for loop");
                if (activepiecesObj) {
                  console.log("=== inside if condition");
                  const activepiecesWebhookUlr = process.env.ACTIVEPIECES_WEBHOOK_URL? process.env.ACTIVEPIECES_WEBHOOK_URL: "https://workflow-dev.gotrust.tech";
                  let webhookurl =activepiecesWebhookUlr +"/api/v1/webhooks/" +activepiecesObj.activepieces_automation_id;
                  console.log(webhookurl);

                  let paramData = {
                    task_id: activepiecesObj.task_id,
                    activepieces_automation_id: activepiecesObj.activepieces_automation_id,
                    phone_number: activepiecesObj.phone_number,
                    dsr_request_id: activepiecesObj.dsr_request_id,
                    data_subject_id: activepiecesObj.data_subject_id,
                  };
                  let jsonData = JSON.stringify(paramData);
                  console.log(jsonData);
                  webhookurl = `${webhookurl}?custom_data=${jsonData}`;
                  if (webhookurl) {
                    let config = {
                      method: "post",
                      maxBodyLength: Infinity,
                      url: webhookurl,
                      // url: `https://workflow-dev.gotrust.tech/api/v1/webhooks/${activepiecesObj.activepieces_automation_id}?custom_data=${jsonData}`,
                      headers: {},
                    };

                    console.log("---", config);
                    let x = await axios.request(config);
                    console.log(x);
                    console.log("=== webhook is called");
                  }
                }
              }
            }
            */

    send_otp_status = false;
    email_verification = true;
    return response.success(req, res, { msgCode: 'EMAIL_VERIFIED', data: { send_otp_status, email_verification } }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.resendCredentials = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User } = db.models;
    const { userEmail } = req.body;

    const user = await commonService.findByCondition(User, { email: userEmail }, ['firstName', 'lastName', 'email', 'firstLogin']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (user.firstLogin) {
      return response.error(req, res, { msgCode: 'USER_ALREADY_LOGGED_IN' }, httpStatus.BAD_REQUEST);
    }

    // generate new temp password
    const tempPassword = crypto.randomBytes(8).toString('hex');

    // update password in your DB
    const hashedPassword = await bcrypt.hash(tempPassword, 10);
    const updatePassword = await commonService.updateData(User, { password: hashedPassword }, { email: userEmail }, dbTrans);
    if (!updatePassword[1]) {
      return response.error(req, res, { msgCode: 'PASSWORD_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // update password in Keycloak
    const keycloakToken = await keycloakService.getToken();
    const userId = await keycloakService.getUserIdByEmail(keycloakToken.access_token, userEmail); ///to be make

    await keycloakService.updateUserPassword(keycloakToken.access_token, userId, tempPassword); //to be make

    // resend email
    const frontEndUrl = process.env.FRONTEND_BASE_URL;
    const backEndUrl = process.env.BACKEND_BASE_URL;

    const sendData = {
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
      password: tempPassword,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      login_url: `${frontEndUrl}`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`
    };

    sendMail(user.email, sendData, 'Welcome to GoTrust', 'new-user-password-email.ejs');

    return response.success(req, res, { msgCode: 'CREDENTIALS_SENT' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('resendCredentials error:', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
