const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const vendorService = require('../services/vendor');
const veaService = require('../services/vea');
const assessmentService = require('../services/assessment');
const constant = require('../constant/vendor');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const { deleteFile } = require('../utils/delete-files');
const { downloadFile } = require('../utils/s3-bucket');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { extractTextFromTipTapJSON, transformData, createAssessmentExcelFile, createVendorExcelFile, generateExcelForAuditData } = require('../utils/helper');
// const authService = require('../services/auth');
const moment = require('moment');
const dayjs = require('dayjs');
const { generateEmbedding } = require('../utils/embeddings');
const parsingService = require('../utils/parsing-file');
const { cosineSimilarity } = require('../utils/cosineSimilarity');
//                <<<---------- Vendor External Assessment ----------->>>

exports.assignVEA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    let veaName = null;
    // let approverId = null;
    let assignedToName = null;
    let dept_id = null;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.vea_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVEA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VEA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (checkVEA.Department) {
      // approverId = checkVEA.Department.spoc_id;
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
    } else if (checkVEA.Process) {
      // approverId = checkVEA.Process.Department.spoc_id;
      veaName = checkVEA.Process.name;
      dept_id = checkVEA.Process.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    assignedToName = `${user.firstName} ${user.lastName}`;

    const assigner = await commonService.findByCondition(
      User,
      {
        id: req.data.userId
      },
      ['firstName', 'lastName']
    );
    if (!assigner) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const vea = await commonService.updateData(VendorAssessments, { assigned_to: req.body.user_id }, { id: req.body.vea_id }, dbTrans);
    if (!vea[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const subject = `You have been assigned ${veaName} : We Need Your Input!`;
    const textTemplate = 'vea_assigned.ejs';
    const sendData = {
      assignee: `${user.firstName} ${user.lastName}`,
      veaName: veaName,
      assigner: `${assigner.firstName} ${assigner.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/vea/`
    };

    sendMail(user.email, sendData, subject, textTemplate);

    const auditAction = `Assigned ${veaName} VEA to ${assignedToName}`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VEA', type_id: req.body.vea_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VEA_ASSIGNED', data: checkVEA }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.reviewerVEA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    let veaName = null;
    // let approver = null;
    let reviewerName = null;
    let dept_id = null;

    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.vea_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVEA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VEA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (checkVEA.Department) {
      // approverId = checkVEA.Department.spoc_id;
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
    } else if (checkVEA.Process) {
      // approverId = checkVEA.Process.Department.spoc_id;
      veaName = checkVEA.Process.name;
      dept_id = checkVEA.Process.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    reviewerName = `${user.firstName} ${user.lastName}`;

    // const reviewer = await commonService.findByCondition(User, {
    //     id: req.data.userId
    // }, ['firstName', 'lastName']);
    // if (!reviewer) {
    //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }
    const vea = await commonService.updateData(VendorAssessments, { approver: req.body.user_id }, { id: req.body.vea_id }, dbTrans);
    if (!vea[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const subject = `You have been assigned as Reviewer for ${veaName} - VEA : We Need Your Input!`;
    const textTemplate = 'vea_reviewer.ejs';
    const sendData = {
      reviewer: `${reviewerName}`,
      veaName: veaName,
      // assigner: `${assigner.firstName} ${assigner.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/vea/`
    };

    sendMail(user.email, sendData, subject, textTemplate);

    const auditAction = `Added Reviewer to  ${veaName} VEA to ${reviewerName}`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VEA', type_id: req.body.vea_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VEA_REVIEWER_ADDED', data: vea[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.startVEA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    console.log('================>>>>>');
    const { VendorAssessments, VeaControls, VeaCustomerControls, VeaCollaborator, AuditLog, Departments, Processes, User } = db.models;

    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.params.vea_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status', 'template_id'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // const checkVEA = await veaService.getVEA(VendorAssessments, Departments, User, { id: req.params.vea_id }, {}, {}, ['id','assessment_type','assessment_name','customer_id','vendor_id','department_id','approver','assigned_to','start_date','end_date','risks','progress','status'], {}, ['firstName', 'lastName']);
    // if (!checkVEA) {
    //     return response.error(req, res, { msgCode: "VEA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }

    if (checkVEA.status === constant.status.STARTED || checkVEA.status === constant.status.CHANGES_REQUESTED) {
      if (checkVEA.assigned_to && checkVEA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        const collaborator = await commonService.findByCondition(VeaCollaborator, { vea_id: req.params.vea_id, user_id: req.data.userId }, ['id']);
        if (!collaborator) {
          return response.error(req, res, { msgCode: 'VEA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }
      }
      return response.success(req, res, { msgCode: 'VEA_STARTED' }, httpStatus.OK, dbTrans);
    } else if (checkVEA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VEA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let veaName = null;
    let dept_id = null;
    // let approverId = null;

    if (checkVEA.Department) {
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
      // approverId = checkVEA.Department.spoc_id;
    } else if (checkVEA.Process) {
      veaName = checkVEA.Process.name;
      dept_id = checkVEA.Process.Department.id;
      // approverId = checkVEA.Process.Department.spoc_id;
    }

    if (checkVEA.assigned_to && checkVEA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(VeaCollaborator, { vea_id: req.params.vea_id, user_id: req.data.userId }, ['id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'VEA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }
    }
    console.log(checkVEA);

    const vea = await commonService.updateData(VendorAssessments, { status: constant.status.STARTED, start_date: Date() }, { id: req.params.vea_id }, dbTrans);
    if (!vea[1]) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // //------>>>Fetching the risk of Vendor Internal Assessment
    // const viaRisk = await commonService.findByCondition(VendorAssessments, { vendor_id: checkVEA.vendor_id, assessment_type: 'via' }, ['risks']);
    // if (!viaRisk) {
    //     return response.error(req, res, { msgCode: "RISK_NOT_FETCHED" }, httpStatus.NOT_FOUND, dbTrans);
    // }

    //geting controls on the basis of Internal assessment risk

    let controls = await commonService.getList(VeaControls, { customer_id: req.data.customer_id, template_id: checkVEA.template_id }, ['id', 'category_id', 'parent_id', 'customer_id']);
    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (controls.count == 0) {
      controls = await commonService.getList(VeaControls, { template_id: checkVEA.template_id }, ['id', 'category_id', 'parent_id', 'customer_id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    const parentControls = controls?.rows?.filter(control => control.parent_id === null);
    const childControls = controls?.rows?.filter(control => control.parent_id !== null);

    const customerControlsParents = parentControls?.map(control => {
      return {
        question_id: control.id,
        customer_id: control.customer_id,
        category_id: control.category_id,
        vea_id: Number(req.params.vea_id),
        is_custom: false
      };
    });

    const newCustomerControlsParents = await commonService.bulkAdd(VeaCustomerControls, customerControlsParents, dbTrans);
    if (!newCustomerControlsParents) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
      map[parentControls[index].id] = control.id;
      return map;
    }, {});

    const customerControlsChildren = childControls?.map(control => {
      return {
        question_id: control.id,
        customer_id: control.customer_id,
        category_id: control.category_id,
        vea_id: req.params.vea_id,
        parent_id: parentIdMap[control.parent_id],
        is_custom: false
      };
    });

    const newCustomerControlsChildren = await commonService.bulkAdd(VeaCustomerControls, customerControlsChildren, dbTrans);
    if (!newCustomerControlsChildren) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const auditAction = `${user?.firstName} ${user?.lastName} started ${veaName} VEA`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VEA', type_id: req.params.vea_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VEA_STARTED', data: vea[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getProgress = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaCustomerControls, VeaAnswers, VendorAssessments, ReviewVEA, VeaCollaborator } = db.models;
    const vea_id = req.params.vea_id;

    const vea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status', 'approver', 'assigned_to']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const status = vea.status;
    let controls = null;
    let totalControls = 0;
    let answeredControls = 0;
    let childControls = [];
    let progress = 0;

    if (vea.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && vea.approver !== req.data.userId) {
      const collaborator = await commonService.getList(VeaCollaborator, { vea_id: vea_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'VEA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }

      const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

      controls = await commonService.getListAssociateWithoutCount(VeaCustomerControls, VeaAnswers, { vea_id: vea_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.VeaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);

      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.VeaAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.VeaAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });

      progress = (answeredControls / totalControls) * 100;
      progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

      return response.success(req, res, { msgCode: 'PROGRESS_FETCHED', data: { totalControls, answeredControls, progress } }, httpStatus.OK, dbTrans);
    }

    if (status === constant.status.STARTED) {
      controls = await commonService.getListAssociateWithoutCount(VeaCustomerControls, VeaAnswers, { vea_id: vea_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.VeaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.VeaAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.VeaAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.UNDER_REVIEW) {
      controls = await commonService.getListAssociateWithoutCountWithAlias(VeaCustomerControls, ReviewVEA, 'ReviewVEA', { vea_id: vea_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.ReviewVEA).length;
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
        }
        if (control.ReviewVEA) {
          answeredControls++;
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.CHANGES_REQUESTED) {
      controls = await veaService.getControlsWithAnswersAndReviews(VeaCustomerControls, VeaAnswers, ReviewVEA, { vea_id: vea_id }, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      // totalControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewVEA && control.ReviewVEA.accurate_information === 0).length;
      // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.VeaAnswer?.updatedAt > control.ReviewVEA?.updatedAt).length;
      // childControls = controls.rows?.filter(control => control.parent_id !== null);
      controls.rows?.forEach(control => {
        if (control.parent_id === null) {
          if (control.ReviewVEA && control.ReviewVEA.accurate_information === 0) {
            totalControls++;
          }
          if (control.VeaAnswer?.updatedAt > control.ReviewVEA?.updatedAt && control.ReviewVEA.accurate_information === 0) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});
      Object.entries(childControlsByParent)?.forEach(([parentId, childControls]) => {
        const parentControl = controls.rows?.find(control => control.id == parentId);
        if (parentControl && childControls.every(control => control.VeaAnswer.updatedAt > parentControl.ReviewVEA.updatedAt)) {
          answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewVEA
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.COMPLETED) {
      controls = await commonService.getListAssociateWithoutCount(VeaCustomerControls, VeaAnswers, { vea_id: vea_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.VeaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.VeaAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.VeaAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    }

    progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

    const updateProgress = await commonService.updateData(VendorAssessments, { progress: progress }, { id: vea_id }, dbTrans);
    if (!updateProgress) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'PROGRESS_FETCHED', data: { totalControls, answeredControls, progress } }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
  }
};

exports.getCategories = async (req, res) => {
  try {
    const { VeaCategory, VeaCollaborator, VendorAssessments, VeaCustomerControls, ReviewVEA } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    let veaLevel = req.params.vea_level;
    const vea_id = req.query.vea_id;
    veaLevel = veaLevel.charAt(0).toUpperCase() + veaLevel.slice(1);

    let categoryCondition = { vea_level: veaLevel };
    let conditions = [];

        const vea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status', 'assigned_to', 'approver', 'template_id','is_automated']);
        if (!vea) {
            return response.error(req, res, { msgCode: "VEA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }

        if (vea.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && vea.approver !== req.data.userId) {
            vea.is_automated = false;
            const collaborators = await commonService.getList(VeaCollaborator, { vea_id: vea_id, user_id: req.data.userId }, ['category_id']);
            if (!collaborators) {
                return response.error(req, res, { msgCode: "VEA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
            // categoryCondition.id = { [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) };
            conditions.push({ [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) });

        }

    if (vea.status === constant.status.CHANGES_REQUESTED) {
      const changeReqCategories = await commonService.getListAssociateWithAlias(VeaCustomerControls, ReviewVEA, 'ReviewVEA', { vea_id: vea_id }, { accurate_information: 0 }, ['category_id']);
      if (!changeReqCategories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.BAD_REQUEST);
      }
      // categoryCondition.id = { [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) };
      conditions.push({ [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) });
    } else {
      categoryCondition.customer_id = req.data.customer_id;
      categoryCondition.template_id = vea.template_id;
    }

    if (conditions.length > 0) {
      categoryCondition.id = {
        [Op.and]: conditions
      };
    }

    let categories = await commonService.getList(VeaCategory, categoryCondition, ['id', 'name'], limit, offset, order);
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

        if (categories.count == 0) {
            delete categoryCondition.template_id;
            categoryCondition.customer_id = null;
            categories = await commonService.getList(VeaCategory, categoryCondition, ['id', 'name'], limit, offset, order);
            if (!categories) {
                return response.error(req, res, { msgCode: "CATEGORIES_NOT_FOUND" }, httpStatus.NOT_FOUND);
            }
        }
        const checkAutomatedControls = await commonService.count(VeaCustomerControls, { vea_id: vea_id, category_id: null });
         if (vea.is_automated === true && checkAutomatedControls > 0) {
         categories.rows.push({ id: categories.rows[categories.count - 1].id + 1, name: 'Uncategorized' });
         categories.count = categories.count + 1;
         }

    return response.success(req, res, { msgCode: 'CATEGORIES_FETCHED', data: categories }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getControls = async (req, res) => {
    try {
        const { VeaControls, VeaCustomerControls, VeaAnswers, User, VendorAssessments, VeaCollaborator, ReviewVEA, VeaCategory } = db.models;
        const vea_id = req.params.vea_id;
        let category_id = req.query.category_id;

        const vea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status', 'assigned_to', 'approver','template_id','is_automated']);
        if (!vea) {
            return response.error(req, res, { msgCode: "VEA_NOT_FOUND" }, httpStatus.NOT_FOUND);
        }
        if (vea.status === constant.status.YET_TO_START) {
            return response.error(req, res, { msgCode: "VEA_NOT_STARTED" }, httpStatus.BAD_REQUEST);
        }
        let categoryCondition = {};

    //fetching the categories
            categoryCondition.customer_id = req.data.customer_id;

            if (vea.template_id) {
                categoryCondition.template_id = vea.template_id;
            } else {
            categoryCondition.template_id = null;
            }

         let categories = await commonService.getList(VeaCategory, categoryCondition, ['id', 'name'], null, null, [['id', 'ASC']]);
            if (categories.count == 0) {
              categoryCondition.customer_id = null;
              categories = await commonService.getList(VeaCategory, categoryCondition, ['id', 'name'], null, null, [['id', 'ASC']]);
              if (!categories) {
                return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
              }
            }
        
            if (category_id == categories.rows[categories.count - 1].id + 1) {
              category_id = null;
            }
        
        if (vea.assigned_to !== req.data.userId && vea.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
            const collaborator = await commonService.findByCondition(VeaCollaborator, { vea_id: vea_id, user_id: req.data.userId, category_id: category_id }, ['id']);
            if (!collaborator) {
                return response.error(req, res, { msgCode: "VEA_NOT_ASSIGNED" }, httpStatus.UNAUTHORIZED);
            }
        }

    let controls;
    const controlsAttributes = [
      [sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."title" ELSE "VeaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."description" ELSE "VeaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("VeaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."is_attachment" ELSE "VeaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."question" ELSE "VeaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."fields" ELSE "VeaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input" ELSE "VeaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("VeaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input_fields" ELSE "VeaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."question_id" IS NOT NULL THEN "VeaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    if (vea.status === constant.status.UNDER_REVIEW || vea.status === constant.status.CHANGES_REQUESTED || vea.status === constant.status.COMPLETED) {
      if (vea.status === constant.status.UNDER_REVIEW && vea.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }
      controls = await veaService.getControlsWithReview(
        VeaCustomerControls,
        VeaControls,
        VeaAnswers,
        User,
        ReviewVEA,
        { vea_id: vea_id, category_id: category_id },
        {},
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'risk_score', 'comments'],
        [['question_id', 'ASC']]
      );
    } else {
      controls = await veaService.getControls(
        VeaCustomerControls,
        VeaControls,
        VeaAnswers,
        User,
        { vea_id: vea_id, category_id: category_id },
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        [['question_id', 'ASC']]
      );
    }

    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    for (let control of controls) {
      control.Answer = control.VeaAnswer;
      delete control.VeaAnswer;
      control.Review = control.ReviewVEA;
      delete control.ReviewVEA;
      if (control.Answer) {
        control.answered = true;
        if (control.Answer.extra_answer) {
          control.Answer.extra_answered = true;
        } else {
          control.Answer.extra_answered = false;
        }
      } else {
        control.answered = false;
      }

      if (control.Review) {
        control.reviewed = true;
      } else {
        control.reviewed = false;
      }

      if (vea.assigned_to !== req.data.userId && vea.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        control.is_collaborator = true;
      } else {
        control.is_collaborator = false;
      }
    }

    let parents = controls?.filter(control => control.parent_id === null);
    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    if (vea.status === constant.status.CHANGES_REQUESTED) {
      parents = parents.filter(parent => parent.Review?.accurate_information === 0);
    }

    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: { status: vea.status, controls: parents } }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getArtifactTypes = async (req, res) => {
  try {
    const { VeaControls } = db.models;
    const artifactTypes = VeaControls.rawAttributes.artifact_type.values;
    if (!artifactTypes) {
      return response.error(req, res, { msgCode: 'ARTIFACT_TYPES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'ARTIFACT_TYPES_FETCHED', data: artifactTypes }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    req.body.is_custom = true;
    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: req.body.vea_id }, {}, {}, {}, ['id', 'assessment_name', 'vendor_id', 'department_id'], {}, {}, []);

    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const addedControls = await commonService.addDetail(VeaCustomerControls, req.body, dbTrans);
    if (!addedControls) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Determine VIA name for audit log
    let veaName = null;
    let dept_id = null;

    if (veaDetails.Department) {
      veaName = veaDetails.Department.name;
      dept_id = veaDetails.Department.id;
    } else if (veaDetails.Process) {
      veaName = veaDetails.Process.name;
      dept_id = veaDetails.Process.Department.id;
    }

    const controlTitle = req.body.title || 'Custom Control';
    const auditAction = `${user.firstName} ${user.lastName} added custom control "${controlTitle}" to ${veaName} VEA`;

    // Create audit log
    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'VEA',
        type_id: req.body.vea_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_CREATED', data: addedControls }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateControls = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { VeaControls, VeaCustomerControls,VendorAssessments, Departments, Processes, AuditLog, User   } = db.models;
        const { title, description, artifact_type, question, fields, is_attachment } = req.body;
        const { master } = req.query;
        let veaId = null;
        let veaName = null;
        let dept_id = null;
        if (req.data.roleName !== authConstant.USER_ROLE[2]) {
          return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }
        let raw_question = null;
        const originalQuestion = await commonService.getDataAssociate(VeaControls, VeaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
        if (master === 'true' && originalQuestion) {
          veaId = originalQuestion?.VeaCustomerControls[0]?.vea_id;
          const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
          const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: veaId }, {}, {}, {}, ['id', 'assessment_name', 'vendor_id', 'department_id'], {}, {}, []);

          if (!veaDetails) {
            return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
          }

          if (veaDetails.Department) {
            veaName = veaDetails.Department.name;
            dept_id = veaDetails.Department.id;
          } else if (veaDetails.Process) {
            veaName = veaDetails.Process.name;
            dept_id = veaDetails.Process.Department.id;
          }

          const updateMasterData = {
            title: title,
            description: description
          };
          const updateMasterControl = await commonService.updateData(VeaControls, updateMasterData, { id: originalQuestion?.id }, dbTrans);
          if (!updateMasterControl[1]) {
            return response.error(req, res, { msgCode: 'ERROR_UPDATING_MASTER_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          let auditAction = `${user.firstName} ${user.lastName} updated master control "${originalQuestion?.title}" in ${veaName} VEA`;
          const auditLog = await commonService.addDetail(
            AuditLog,
            {
              type: 'VEA',
              type_id: veaId,
              action: auditAction,
              action_by_id: req.data.userId,
              customer_id: req.data.customer_id,
              dept_id
            },
            dbTrans
          );
          if (!auditLog) {
            return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
          }

          return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updateMasterControl[1] }, httpStatus.OK, dbTrans);
        } else if (originalQuestion && master !== 'true') {
          raw_question = originalQuestion;
        } else {
          const customQuestion = await commonService.findByCondition(VeaCustomerControls, { id: req.params.customer_control_id }, {});
          if (!customQuestion) {
            return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
          }
          raw_question = customQuestion;
        }
        veaId = raw_question.vea_id || originalQuestion?.VeaCustomerControls[0]?.vea_id 
        const veaDetails = await veaService.getVEA(
              VendorAssessments,
              Departments,
              Processes,
              User,
              { id: veaId },
              {},
              {},
              {},
              ['id', 'assessment_name', 'vendor_id', 'department_id'],
              {},
              {},
              []
            );
            
            if (!veaDetails) {
              return response.error(req, res, { msgCode: "VEA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
        const updatedValues = {
            title: title || raw_question?.title,
            description: description || raw_question?.description,
            artifact_type: artifact_type || raw_question?.artifact_type,
            is_attachment: is_attachment || raw_question?.is_attachment,
            question: question || raw_question?.question,
            fields: fields || raw_question?.fields,
            is_custom: true
        }

    const updatedControls = await commonService.updateData(VeaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

        const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
            if (!user) {
              return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
            }
            
            // Determine VIA name for audit log    
            if (veaDetails.Department) {
              veaName = veaDetails.Department.name;
              dept_id = veaDetails.Department.id;
            } else if (veaDetails.Process) {
              veaName = veaDetails.Process.name;
              dept_id = veaDetails.Process.Department.id;
            }
            
            const controlTitle = raw_question.title || 'Control';
            const auditAction = `${user.firstName} ${user.lastName} updated control "${controlTitle}" in ${veaName} VEA`;
            
            // Create audit log
            const auditLog = await commonService.addDetail(AuditLog, {
              type: 'VEA',
              type_id:veaId ,
              action: auditAction,
              action_by_id: req.data.userId,
              dept_id: dept_id,
              customer_id: req.data.customer_id
            }, dbTrans);
            
            if (!auditLog) {
              return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
            }

    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateFields = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaControls, VeaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    const { fields } = req.body;
    let question = null;

    const originalQuestion = await commonService.getDataAssociate(VeaControls, VeaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    if (originalQuestion) {
      question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(VeaCustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      question = customQuestion;
    }
    // console.log("=======>>>>>>>",question);
    const veaId = question?.vea_id || question.VeaCustomerControls[0]?.vea_id;
    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: veaId }, {}, {}, {}, ['id', 'assessment_name', 'vendor_id', 'department_id'], {}, {}, []);

    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const updatedValues = {
      title: question.title,
      description: question.description,
      artifact_type: question.artifact_type,
      is_attachment: question.is_attachment,
      question: question.question,
      fields: fields || question.fields,
      is_custom: true
    };
    const updatedControls = await commonService.updateData(VeaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Determine VIA name for audit log
    let veaName = null;
    let dept_id = null;

    if (veaDetails.Department) {
      veaName = veaDetails.Department.name;
      dept_id = veaDetails.Department.id;
    } else if (veaDetails.Process) {
      veaName = veaDetails.Process.name;
      dept_id = veaDetails.Process.Department.id;
    }

    const controlTitle = question.title || 'Control';
    const auditAction = `${user.firstName} ${user.lastName} updated fields for control "${controlTitle}" in ${veaName} VEA`;

    // Create audit log
    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'VEA',
        type_id: veaId,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.deleteCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;

    const control = await commonService.findByCondition(
      VeaCustomerControls,
      {
        id: req.params.customer_control_id,
        is_custom: true,
        question_id: null
      },
      {}
    );

    if (!control) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: control.vea_id }, {}, {}, {}, ['id', 'assessment_name', 'vendor_id', 'department_id'], {}, {}, []);

    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const deletedControls = await commonService.deleteQuery(VeaCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, dbTrans);
    if (!deletedControls) {
      return response.error(req, res, { msgCode: 'ERROR_DELETING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Determine VIA name for audit log
    let veaName = null;
    let dept_id = null;

    if (veaDetails.Department) {
      veaName = veaDetails.Department.name;
      dept_id = veaDetails.Department.id;
    } else if (veaDetails.Process) {
      veaName = veaDetails.Process.name;
      dept_id = veaDetails.Process.Department.id;
    }

    const controlTitle = control.title || 'Custom Control';
    const auditAction = `${user.firstName} ${user.lastName} deleted custom control "${controlTitle}" from ${veaName} VEA`;

    // Create audit log
    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'VEA',
        type_id: control.vea_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );

    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_DELETED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getveaCollaborators = async (req, res) => {
  try {
    const { VeaCollaborator, User, VendorAssessments, VeaCategory } = db.models;
    const vea_id = req.params.vea_id;

    const vea = await veaService.getVEAWithAssignee(VendorAssessments, User, { id: vea_id }, {}, ['status', 'assigned_to', 'approver'], ['firstName', 'lastName']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    if (vea.assigned_to !== req.data.userId && vea.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }
    const collaborators = await veaService.getveaCollaborators(VeaCategory, VeaCollaborator, User, {}, { vea_id: vea_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
    if (!collaborators) {
      return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }
    collaborators?.forEach(collaborator => {
      collaborator.Collaborators = collaborator.VeaCollaborators;
      delete collaborator.VeaCollaborators;
    });

    const assignee = `${vea.AssignedTo?.firstName} ${vea.AssignedTo?.lastName}`;

    return response.success(req, res, { msgCode: 'COLLABORATORS_FETCHED', data: { assignee: assignee, collaborators } }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addveaCollaborator = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaCollaborator, AuditLog, VendorAssessments, Departments, Processes, User, VeaCategory } = db.models;

    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.vea_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVEA.assigned_to !== req.data.userId && checkVEA.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const userList = req.body.collaborators?.flatMap(collaborator => collaborator.users?.map(user => user.id));

    const users = await commonService.getListWithoutCount(
      User,
      {
        id: { [Op.in]: userList }
      },
      ['id', 'firstName', 'lastName', 'email']
    );
    if (!users) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const categories = await commonService.getListWithoutCount(
      VeaCategory,
      {
        id: { [Op.in]: req.body.collaborators.map(collaborator => collaborator.category_id) }
      },
      ['id', 'name']
    );
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!invitee) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let collaboratorData = [];
    const auditData = [];
    let veaName = null;
    let dept_id = null;
    if (checkVEA.Department) {
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
    }
    // else if (checkVEA.Process) {
    //     veaName = checkVEA.Process.name;
    //     dept_id = checkVEA.Process.Department.id;
    // }

    // const names = [];
    for (let collaborator of req.body.collaborators) {
      const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

      for (let collaboratingUser of collaborator.users) {
        const user = users?.find(user => user.id === collaboratingUser.id);
        let userName = `${user?.firstName} ${user?.lastName}`;
        // names.push(userName);

        if (collaboratingUser.action === 'add') {
          collaboratorData.push({
            vea_id: req.body.vea_id,
            user_id: collaboratingUser.id,
            category_id: collaborator.category_id
          });

          auditData.push({
            type: 'VEA',
            type_id: req.body.vea_id,
            action: `Added ${userName} as a collaborator for ${veaName} VEA under ${categoryName} category`,
            action_by_id: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id
          });

          //send mail
          const subject = `Collaboration Request: Assistance Needed with VEA in ${categoryName}`;
          const textTemplate = 'vea_collaborator.ejs';
          const sendData = {
            collaboratorName: userName,
            inviteeName: `${invitee.firstName} ${invitee.lastName}`,
            veaName: veaName,
            categoryName: categoryName,
            url: `${process.env.SERVER_IP}/privacy/vea/`
          };

          sendMail(user.email, sendData, subject, textTemplate);
        } else if (collaboratingUser.action === 'remove') {
          const oldveaCollaborator = await commonService.deleteQuery(VeaCollaborator, { vea_id: req.body.vea_id, user_id: collaboratingUser.id, category_id: collaborator.category_id }, dbTrans, true);
          if (!oldveaCollaborator) {
            return response.error(req, res, { msgCode: 'ERROR_DELETING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
          }

          auditData.push({
            type: 'VEA',
            type_id: req.body.vea_id,
            action: `Removed ${userName} as a collaborator for ${veaName} VEA under ${categoryName} category`,
            action_by_id: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id
          });
        }
      }
    }

    const newveaCollaborators = await commonService.bulkAdd(VeaCollaborator, collaboratorData, dbTrans);
    if (!newveaCollaborators) {
      return response.error(req, res, { msgCode: 'ERROR_ADDING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'COLLABORATOR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createOrUpdateAnswers = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaAnswers, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    const answers = req.body.answers;
    const vea_id = req.body.vea_id;

    const checkVEA = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status']);
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVEA.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'VEA_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VEA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Separate answers into two arrays based on the type
    const addAnswers = answers?.filter(answer => answer.type === 'add');
    const updateAnswers = answers?.filter(answer => answer.type === 'update');

    // Add 'answered_by' field to all answers
    addAnswers?.forEach(answer => (answer.answered_by = req.data.userId));
    updateAnswers?.forEach(answer => (answer.answered_by = req.data.userId));
    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: vea_id }, {}, {}, {}, ['id', 'assessment_name', 'vendor_id', 'department_id'], {}, {}, []);

    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    // Bulk add or update answers
    if (addAnswers.length > 0) {
      const addNewAnswers = await commonService.bulkAdd(VeaAnswers, addAnswers, dbTrans);
      if (!addNewAnswers) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateAnswers.length > 0) {
      for (let answer of updateAnswers) {
        const updateAnswers = await commonService.updateData(VeaAnswers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
        if (!updateAnswers[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    if (addAnswers.length > 0 || updateAnswers.length > 0) {
      // Get user info for audit log
      const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
      if (!user) {
        return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // Determine VIA name for audit log
      let veaName = null;
      let dept_id = null;

      if (veaDetails.Department) {
        veaName = veaDetails.Department.name;
        dept_id = veaDetails.Department.id;
      } else if (veaDetails.Process) {
        veaName = veaDetails.Process.name;
        dept_id = veaDetails.Process.Department.id;
      }

      const auditAction = `${user.firstName} ${user.lastName} answered ${addAnswers.length + updateAnswers.length} control(s) for ${veaName} VEA`;

      // Create audit log
      const auditLog = await commonService.addDetail(
        AuditLog,
        {
          type: 'VEA',
          type_id: vea_id,
          action: auditAction,
          action_by_id: req.data.userId,
          dept_id: dept_id,
          customer_id: req.data.customer_id
        },
        dbTrans
      );

      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    return response.success(req, res, { msgCode: 'ANSWER_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitVEA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, AuditLog, Departments, Processes, User, VeaAnswers, VeaCustomerControls } = db.models;
    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.params.vea_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName', 'email']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVEA.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'VEA_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVEA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VEA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const checkAnswerStatus = await commonService.getListAssociateWithoutCount(VeaCustomerControls, VeaAnswers, { vea_id: req.params.vea_id }, {}, [[sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], {});

    const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
    const childrenMap = checkAnswerStatus?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const unansweredQuestions = parents?.reduce((acc, parent) => {
      if (parent.children.length > 0) {
        parent.children.forEach(child => {
          if (child.VeaAnswer === null) {
            acc.push({ customer_question_id: child.customer_question_id });
          }
        });
      } else if (parent.VeaAnswer === null) {
        acc.push({ customer_question_id: parent.customer_question_id });
      }
      return acc;
    }, []);

    if (unansweredQuestions.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_ANSWERED', data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const vea = await commonService.updateData(VendorAssessments, { status: constant.status.UNDER_REVIEW }, { id: req.params.vea_id }, dbTrans);
    if (!vea[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let veaName = null;
    let dept_id = null;

    if (checkVEA.Department) {
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
    }
    // else if (checkVEA.Process) {
    //     veaName = checkVEA.Process.name;
    //     dept_id = checkVEA.Process.Department.id;
    // }

    const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!submitter) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const submitterName = `${submitter.firstName} ${submitter.lastName}`;

    const subject = `Request for Review: Vendor External Assessment (VEA) - ${veaName} Submission by ${submitterName}`;
    const textTemplate = 'vea_submit.ejs';
    const sendData = {
      assignee: submitterName,
      veaName: veaName,
      reviewer: `${checkVEA.Approver?.firstName} ${checkVEA.Approver?.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/vea/`
    };

    sendMail(checkVEA.Approver.email, sendData, subject, textTemplate);

    const auditAction = `Submitted ${veaName} VEA for review`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VEA', type_id: req.params.vea_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VEA_SUBMITTED', data: vea[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.reviewVEA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ReviewVEA, VendorDetail, VeaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    const reviews = req.body.reviews;
    const findVea = await commonService.findByCondition(VeaCustomerControls, { id: reviews[0].customer_question_id }, ['vea_id']);
    if (!findVea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    // Separate answers into two arrays based on the type
    const addReviews = reviews?.filter(answer => answer.type === 'add');
    const updateReviews = reviews?.filter(answer => answer.type === 'update');

    // Add 'areviewer_id' field to all answers
    addReviews?.forEach(review => (review.reviewer_id = req.data.userId));
    updateReviews?.forEach(review => (review.reviewer_id = req.data.userId));
    const veaId = findVea.vea_id;
    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: veaId }, {}, {}, {}, ['id', 'assessment_name', 'vendor_id', 'department_id'], {}, {}, []);

    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    // Bulk add or update reviews
    if (addReviews.length > 0) {
      const addNewReviews = await commonService.bulkAdd(ReviewVEA, addReviews, dbTrans);
      if (!addNewReviews) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateReviews.length > 0) {
      for (let review of updateReviews) {
        const updateReviews = await commonService.updateData(ReviewVEA, review, { customer_question_id: review.customer_question_id }, dbTrans);
        if (!updateReviews[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }
    if (addReviews.length > 0 || updateReviews.length > 0) {
      // Get user info for audit log
      const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
      if (!user) {
        return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // Determine VIA name for audit log
      let veaName = null;
      let dept_id = null;

      if (veaDetails.Department) {
        veaName = veaDetails.Department.name;
        dept_id = veaDetails.Department.id;
      } else if (veaDetails.Process) {
        veaName = veaDetails.Process.name;
        dept_id = veaDetails.Process.Department.id;
      }

      const auditAction = `${user.firstName} ${user.lastName} added review comments to ${addReviews.length + updateReviews.length} control(s) for ${veaName} VEA`;

      // Create audit log
      const auditLog = await commonService.addDetail(
        AuditLog,
        {
          type: 'VEA',
          type_id: veaId,
          action: auditAction,
          action_by_id: req.data.userId,
          dept_id: dept_id,
          customer_id: req.data.customer_id
        },
        dbTrans
      );

      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    return response.success(req, res, { msgCode: 'REVIEW_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitReview = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, VeaCustomerControls, AuditLog, Departments, Processes, User, ReviewVEA, VendorDetail } = db.models;
    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.params.vea_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName', 'email']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVEA.status !== constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VEA_NOT_SUBMITTED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let veaName = null;
    let dept_id = null;

    if (checkVEA.Department) {
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
    }
    // else if (checkVEA.Process) {
    //     veaName = checkVEA.Process.name;
    //     dept_id = checkVEA.Process.Department.id;
    // }

    let status = constant.status.COMPLETED;
    let end_date = Date();
    const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(
      VeaCustomerControls,
      ReviewVEA,
      'ReviewVEA',
      { vea_id: req.params.vea_id },
      {},
      [[sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      ['accurate_information', 'risk_score']
    );

    const unreviewedControls = checkReviewStatus?.filter(review => review.ReviewVEA === null && review.parent_id === null);
    if (unreviewedControls.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_REVIEWED', data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const unapprovedControls = checkReviewStatus?.filter(review => review.ReviewVEA?.accurate_information === 0);
    if (unapprovedControls.length > 0) {
      status = constant.status.CHANGES_REQUESTED;
      end_date = null;
    }

    let riskLevel = null;
    let riskTier = null;
    let totalRiskScore = 0;
    let totalControls = 0;

    if (status === constant.status.COMPLETED) {
      // risk assessment logic
      checkReviewStatus?.forEach(review => {
        totalRiskScore += Number(review.ReviewVEA?.risk_score);
        ++totalControls;
      });
      totalRiskScore = totalRiskScore / totalControls;
      totalRiskScore = Math.round(totalRiskScore * 100) / 100;
      // risk level logic
      if (totalRiskScore <= 3) {
        riskLevel = 'Low';
        riskTier = 'LOW';
      } else if (totalRiskScore <= 6) {
        riskLevel = 'Medium';
        riskTier = 'MEDIUM';
      } else {
        riskLevel = 'High';
        riskTier = 'HIGH';
      }

      // update vendor status
      const stageUpdate = await commonService.updateData(VendorDetail, { stage: 'MITIGATION', risk_score: totalRiskScore, risk_tier: riskTier }, { vendor_id: checkVEA.vendor_id }, dbTrans);
      if (!stageUpdate[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_VEA_FAILED' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      // const question_id = [constant.question.CHILDREN_DATA_COLLECTION, constant.question.CROSS_BORDER_DATA_TRANSFER, constant.question.DATA_SUBJECTS_CATEGORIES, constant.question.PERSONAL_DATA, constant.question.SENSITIVE_DATA, constant.question.THIRD_PARTY_VENDORS];

      // const answers = await commonService.getListAssociate(VeaAnswers, VeaCustomerControls, {}, { vea_id: req.params.vea_id, question_id: { [Op.in]: question_id } }, {}, {});

      // //create a map with question_id as key and answer as value
      // const answerMap = answers?.reduce((map, answer) => {
      //     map[answer.CustomerControl.question_id] = answer.answer;
      //     return map;
      // }, {});

      // const riskAttributes = {
      //     PERSONAL_DATA: 0,
      //     SENSITIVE_DATA: 0,
      //     DATA_SUBJECTS_CATEGORIES: 0,
      //     CHILDREN_DATA_COLLECTION: 0,
      //     THIRD_PARTY_VENDORS: 0,
      //     CROSS_BORDER_DATA_TRANSFER: 0
      // }

      // for (let key in answerMap) {
      //     switch (parseInt(key)) {
      //         case constant.question.PERSONAL_DATA:
      //             riskAttributes.PERSONAL_DATA = answerMap[key].length;
      //             break;
      //         case constant.question.SENSITIVE_DATA:
      //             riskAttributes.SENSITIVE_DATA = answerMap[key].length;
      //             break;
      //         case constant.question.DATA_SUBJECTS_CATEGORIES:
      //             riskAttributes.DATA_SUBJECTS_CATEGORIES = answerMap[key].length;
      //             break;
      //         case constant.question.CHILDREN_DATA_COLLECTION:
      //             riskAttributes.CHILDREN_DATA_COLLECTION = answerMap[key];
      //             break;
      //         case constant.question.THIRD_PARTY_VENDORS:
      //             riskAttributes.THIRD_PARTY_VENDORS = answerMap[key].length;
      //             break;
      //         case constant.question.CROSS_BORDER_DATA_TRANSFER:
      //             riskAttributes.CROSS_BORDER_DATA_TRANSFER = answerMap[key].length;
      //             break;
      //     }
      // }

      // if (riskAttributes.PERSONAL_DATA > 7 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 7 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 5) {
      //     riskLevel = 'High';
      // } else if (riskAttributes.PERSONAL_DATA > 3 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 3 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 2) {
      //     riskLevel = 'Medium';
      // }

      // if (riskAttributes.CHILDREN_DATA_COLLECTION[0] === '0') {
      //     riskLevel = 'High';
      // }

      // if (riskAttributes.SENSITIVE_DATA > 1 || riskAttributes.THIRD_PARTY_VENDORS > 1) {
      //     riskLevel = 'High';
      // }

      const subject = `VEA Completed: Review Suggested Risks for Comptiance Enhancement`;
      const textTemplate = 'vea_review_submit.ejs';
      const sendDataAssignedTo = {
        assignee: `${checkVEA?.AssignedTo?.firstName} ${checkVEA?.AssignedTo?.lastName}`,
        veaName: veaName,
        status: checkVEA?.status,
        reviewer: `${checkVEA?.Approver?.firstName} ${checkVEA?.Approver?.lastName}`,
        url: `${process.env.SERVER_IP}/privacy/vea/`
      };
      console.log('sendDataAssignedTo====>>>>', sendDataAssignedTo);

      sendMail(checkVEA?.AssignedTo?.email, sendDataAssignedTo, subject, textTemplate);

      const sendDataApprover = {
        assignee: `${checkVEA?.Approver?.firstName} ${checkVEA?.Approver?.lastName}`,
        veaName: veaName,
        status: checkVEA?.status,
        reviewer: `${checkVEA?.Approver?.firstName} ${checkVEA?.Approver?.lastName}`,
        url: `${process.env.SERVER_IP}/privacy/vea/`
      };
      console.log('sendDataApprover====>>>>', sendDataApprover);
      sendMail(checkVEA?.Approver?.email, sendDataApprover, subject, textTemplate);
    }

    const vea = await commonService.updateData(VendorAssessments, { status: status, end_date: end_date, risks: riskLevel, risk_score: totalRiskScore }, { id: req.params.vea_id }, dbTrans);
    if (!vea[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const auditAction = `Submitted review for ${veaName} VEA with status '${status}'`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VEA', type_id: req.params.vea_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VEA_REVIEWED', data: vea[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getAuditLog = async (req, res) => {
  try {
    const { AuditLog, User, Departments } = db.models;

    const { vea_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    const auditCondition = { type: 'VEA', customer_id: req.data.customer_id };
    const userCondition = {};

    if (search) {
      userCondition[Op.or] = [{ firstName: { [Op.iLike]: `%${search}%` } }, { lastName: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }];
    }

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
      if (!deptHead) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }

      const deptIds = deptHead.rows?.map(dept => dept.id);
      auditCondition.dept_id = { [Op.in]: deptIds };
    }

    if (vea_id) {
      auditCondition.type_id = vea_id;
    }

    const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

    if (!auditData) {
      return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }
    // getting name initials and added to the audit data
    auditData.rows?.map(row => {
      const name = row?.User?.firstName + ' ' + row?.User?.lastName;
      row.name = name;
      const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
      row.initials = initials;
      delete row.User;
    });

    return response.success(req, res, { msgCode: 'AUDIT_LOG_FETCHED', data: auditData }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

// exports.downloadAuditLog = async (req, res) => {
//     try {
//         const { AuditLog, User, Departments,Customer,VendorAssessments, VendorList } = db.models;

//         const  vea_id  = req.params.req_id;
//         const sort_by = 'createdAt', sort_order = 'DESC'

//         const auditCondition = { type: 'VEA', customer_id: req.data.customer_id };
//         const userCondition = {};
//         const order = [[sort_by, sort_order]];

//         if (req.data.roleName !== authConstant.USER_ROLE[2]) {
//             const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
//             if (!deptHead) {
//                 return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
//             }

//             const deptIds = deptHead.rows?.map(dept => dept.id);
//             auditCondition.dept_id = { [Op.in]: deptIds };
//         }

//         if (vea_id) {
//             auditCondition.type_id = vea_id;
//         }

//         const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id });
//         if (!customer) {
//             return response.error(req, res, { msgCode: "CUSTOMER_NOT_FOUND" }, httpStatus.NOT_FOUND);
//         }

//         const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt', 'type_id'], ['firstName', 'lastName'],undefined, undefined, order);

//         if (!auditData) {
//             return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
//         }

//         // getting name initials and added to the audit data
//         await Promise.all(auditData.rows?.map(async (row) => {
//             const name = row?.User?.firstName + ' ' + row?.User?.lastName;
//             row.name = name;
//             const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
//             row.initials = initials;

//             const vendorAssessments = await commonService.findByCondition(VendorAssessments, { id: row.type_id });
//             if (!vendorAssessments) {
//                 return response.error(req, res, { msgCode: "VENDOR_ASSESSMENTS_NOT_FOUND" }, httpStatus.NOT_FOUND);
//             }

//             const vendorList = await commonService.findByCondition(VendorList, { id: vendorAssessments.vendor_id });
//             if (!vendorList) {
//                 return response.error(req, res, { msgCode: "VENDOR_LIST_NOT_FOUND" }, httpStatus.NOT_FOUND);
//             }

//             row.typeName = vendorList?.name

//           delete row.User;
//       }));

//         const excelData = auditData?.rows;
//         const typeName = auditData?.rows[0]?.typeName ? auditData.rows[0].typeName : 'Unknown Type'; // get the vendor name

//         // remove the unnecessary fields from the excel data using map
//         excelData?.map(row => {
//             const istTime = moment(row.createdAt).tz('Asia/Kolkata');
//             const date = istTime.format('YYYY-MM-DD');
//             const time = istTime.format('HH:mm:ss.SSSZ');
//             row.date = date;
//             row.time = time;
//             delete row.createdAt;
//             delete row.id;
//             delete row.initials;
//             delete row.action_by_id;
//             delete row.typeName
//         });
//         // console.log('excelData', excelData);

//         const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

//         const excelFile = await generateExcelForAuditData(excelData, date, customer?.name, vea_id,'VEA', typeName );

//         const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
//         if (!user) {
//             return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND);
//         }

//         const mailData = {
//             name: `${user?.firstName} ${user?.lastName}`,
//             type: 'VEA',
//         }

//         await sendMailWithAttach(
//             req.data.email,
//             mailData,
//             'Your Copy of VEA Audit Log file made on GoTrust',
//             'audit_log_download.ejs',
//             excelFile
//         );

//         return response.success(req, res, { msgCode: 'AUDIT_DOWNLOADED'}, httpStatus.OK );
//     } catch (error) {
//         console.log('error', error);
//         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
//     }
// }

exports.uploadControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaControls, VeaCategory, VeaTemplate } = db.models;
    const controls = [];
    let parentId = null;
    const childControlsData = [];
    const uniqueCategories = new Map(); // Map to store unique categories

    const artifactTypeMapping = {
      radio: 'radio',
      dropdown: 'select',
      table: 'table',
      'text box': 'textarea',
      'upload attachment': 'attachment'
    };

    //uploading and then adding template to veaTemplate table
    const template = await commonService.addDetail(VeaTemplate, { temp_name: req.body.temp_name, url: req.body.url, customer_id: req.data.customer_id }, dbTrans);
    if (!template) {
      return response.error(req, res, { msgCode: 'TEMPLATE_NOT_ADDED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const requiredHeaders = ['VEA Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
    const { isValid, missingHeader } = await veaService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required in the CSV file.` }, httpStatus.BAD_REQUEST, dbTrans);
    }

    fs.createReadStream(req.files[0].path)
      .pipe(csv())
      .on('data', async row => {
        const controlData = {
          VEALevel: row['VEA Level'],
          veaCategory: row['Category'],
          title: row['Title'],
          description: row['Explanation'],
          artifact_type: row['Artifact Type'],
          question: row['Question'],
          fields: row['Fields'],
          customer_id: req.data.customer_id,
          is_attachment: row['Has Attachment'],
          extra_input: row['Extra Input Required'],
          extra_input_type: row['Extra Input Type'],
          extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields']
        };

        // Check if all properties of controlData are empty
        if (!Object.values(controlData).every(x => x === '')) {
          if (controlData.title === '' && controlData.question === '') {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'Title and Question are missing in the data row.' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          controls.push(controlData);
        }
      })
      .on('end', async () => {
        // Insert the data into the database
        const categoryTitleQuestionSet = new Set();

        for (let row of controls) {
          if (!row['VEALevel'] || !row['veaCategory']) {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'VEA Level or Category is missing.' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          const key = `${row['VEALevel']}_${row['veaCategory']}`;
          if (row['title']) {
            // Parent question
            const titleText = row['title']?.trim();
            const duplicateKey = `${key}_${titleText}`;
            if (categoryTitleQuestionSet.has(duplicateKey)) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'DUPLICATE_QUESTION_TITLE', data: `Duplicate control found in category [${key}] with title [${titleText}]` }, httpStatus.BAD_REQUEST, dbTrans);
            }
            categoryTitleQuestionSet.add(duplicateKey);
            if (!uniqueCategories.has(key)) {
              // Create or retrieve category and store in the map
              const [vea_level, name] = key.split('_');
              let category = await commonService.findByCondition(VeaCategory, { vea_level, name, template_id: template.id, customer_id: req.data.customer_id }, {});
              if (!category) {
                category = await VeaCategory.create({ name, vea_level, template_id: template.id, customer_id: req.data.customer_id }, { transaction: dbTrans });
              }
              uniqueCategories.set(key, category);
            }

            let artifactType = null;
            if (row['artifact_type'] !== '') {
              artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
              if (!artifactType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE', data: `Invalid artifact type: ${row['artifact_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

                        let extraInputType = null;
                        if (row['extra_input_type'] !== '') {
                            extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                            if (!extraInputType) {
                                await deleteFile(req.files[0].path);
                                return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE", data: `Invalid extra input type: ${row['extra_input_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                            }
                        }
                        let embedding;
                        let str;
                        if (row['artifact_type']) {
                          str = row['title'] + ' and ' + row['description'] + ' and ' + row['question'];
                          embedding = await generateEmbedding(str);
                        }

                        // Create parent control
                        const control = await commonService.addDetail(VeaControls, {
                            title: row['title'],
                            description: row['description'],
                            artifact_type: artifactType,
                            question: row['question'],
                            fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
                            is_attachment: row['is_attachment'] === 'Yes',
                            extra_input: row['extra_input'] === 'Yes',
                            extra_input_type: extraInputType,
                            extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                            category_id: uniqueCategories.get(key).id,
                            parent_id: null,
                            template_id: template.id,
                            customer_id: req.data.customer_id,
                            industry_vertical_id: req.body.industry_vertical_id,
                            embedding: embedding
                        }, dbTrans);

            if (!control) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: 'Error creating parent control.' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            // Update parent ID for potential child questions
            parentId = control.id;
          } else {
            // Child question
            if (parentId) {
              // Create child control
              let artifactType = null;
              if (row['artifact_type'] !== '') {
                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                if (!artifactType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE', data: `Invalid artifact type for child control: ${row['artifact_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              let extraInputType = null;
              if (row['extra_input_type'] !== '') {
                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                if (!extraInputType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE', data: `Invalid extra input type for child control: ${row['extra_input_type']}.` }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }
              const str = row['title'] + ' and ' + row['description'] + ' and ' + row['question'];
              const embedding = await generateEmbedding(str);
              childControlsData.push({
                title: null,
                description: null,
                artifact_type: artifactType,
                customer_id: req.data.customer_id,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: parentId,
                template_id: template.id,
                industry_vertical_id: req.body.industry_vertical_id,
                embedding: embedding
              });
            } else {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'INVALID_DATA', data: 'Child control found without a parent control.' }, httpStatus.BAD_REQUEST, dbTrans);
            }
          }
        }
        // Batch create child controls
        const childControls = await commonService.bulkAdd(VeaControls, childControlsData, dbTrans);
        if (!childControls) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL', data: 'Error creating child controls.' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        await deleteFile(req.files[0].path);

        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getVeaData = async (req, res) => {
  try {
    const { VeaControls, VeaCustomerControls, VeaAnswers, User, VendorAssessments, VeaCategory, Customer, Departments, Processes } = db.models;
    const vea_id = req.params.vea_id;

    const vea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status', 'assigned_to', 'approver', 'department_id', 'risks']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (vea.status !== constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_NOT_COMPLETED' }, httpStatus.BAD_REQUEST);
    }

    const controlsAttributes = [
      [sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."title" ELSE "VeaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."description" ELSE "VeaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("VeaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."is_attachment" ELSE "VeaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."question" ELSE "VeaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."fields" ELSE "VeaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input" ELSE "VeaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("VeaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input_fields" ELSE "VeaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."question_id" IS NOT NULL THEN "VeaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    const controls = await assessmentService.getControlsWithCategory(
      VeaCustomerControls,
      VeaControls,
      VeaAnswers,
      User,
      VeaCategory,
      { vea_id: vea_id },
      {},
      {},
      {},
      {},
      controlsAttributes,
      [],
      ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      [['question_id', 'ASC']]
    );
    for (let control of controls) {
      control.Answer = control.VeaAnswer;
      delete control.VeaAnswer;
      control.Category = control.VeaCategory;
      delete control.VeaCategory;
    }
    let parents = controls?.filter(control => control.parent_id === null);

    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const excelData = transformData(parents);

    const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

    const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    let deptName = '';
    let procName = '';

    if (vea.department_id) {
      const dept = await commonService.findByCondition(Departments, { id: vea.department_id }, ['name']);
      deptName = dept?.name;
    }
    // else if (vea.process_id) {
    //     const proc = await commonService.getDataAssociate(Processes, Departments, { id: vea.process_id }, {}, ['name'], ['name']);
    //     procName = proc?.name;
    //     deptName = proc?.Department?.name;
    // }

    const excelFile = await createAssessmentExcelFile(excelData, 'VEA (Vendor External Assessment)', date, customer?.name, deptName, procName);

    await sendMailWithAttach(req.data.email, { name: `${user?.firstName} ${user?.lastName}` }, 'Your Copy of VEA (Vendor External Assessment) file made on GoTrust', 'vea_download.ejs', excelFile);

    return response.success(req, res, { msgCode: 'VEA_DOWNLOADED', data: 'VEA data sent vea E-mail' }, httpStatus.OK);
  } catch (err) {
    console.log('assessmentDownloadError', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getControlsMitigation = async (req, res) => {
  try {
    const { VeaControls, VeaCustomerControls, VeaAnswers, User, VendorAssessments, ReviewVEA } = db.models;

    let risk = req.query.risk.toUpperCase();

    const vea = await commonService.findByCondition(VendorAssessments, { vendor_id: req.params.vendor_id, assessment_type: 'vea' }, ['id', 'status', 'assigned_to', 'approver']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const vea_id = vea.id;
    let controls;
    const controlsAttributes = [
      [sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."title" ELSE "VeaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."description" ELSE "VeaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("VeaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."is_attachment" ELSE "VeaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."question" ELSE "VeaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."fields" ELSE "VeaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input" ELSE "VeaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("VeaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input_fields" ELSE "VeaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."question_id" IS NOT NULL THEN "VeaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];
    if (risk === 'HIGH') {
      controls = await veaService.getControlsWithReview1(
        VeaCustomerControls,
        VeaControls,
        VeaAnswers,
        User,
        ReviewVEA,
        { vea_id: vea_id },
        {},
        {},
        {},
        { risk_score: { [Op.in]: [7, 8, 9] } },
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'risk_score', 'mitigation_plan', 'attachment', 'comments'],
        [['question_id', 'ASC']]
      );
    } else if (risk === 'MEDIUM') {
      controls = await veaService.getControlsWithReview1(
        VeaCustomerControls,
        VeaControls,
        VeaAnswers,
        User,
        ReviewVEA,
        { vea_id: vea_id },
        {},
        {},
        {},
        { risk_score: { [Op.in]: [4, 5, 6] } },
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'risk_score', 'mitigation_plan', 'attachment', 'comments'],
        [['question_id', 'ASC']]
      );
    } else {
      controls = await veaService.getControlsWithReview1(
        VeaCustomerControls,
        VeaControls,
        VeaAnswers,
        User,
        ReviewVEA,
        { vea_id: vea_id },
        {},
        {},
        {},
        { risk_score: { [Op.in]: [1, 2, 3] } },
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'risk_score', 'mitigation_plan', 'attachment', 'comments'],
        [['question_id', 'ASC']]
      );
    }

    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }
    for (let control of controls) {
      control.Answer = control.VeaAnswer;
      delete control.VeaAnswer;
      control.Review = control.ReviewVEA;
      delete control.ReviewVEA;
      if (control.Answer) {
        control.answered = true;
        if (control.Answer.extra_answer) {
          control.Answer.extra_answered = true;
        } else {
          control.Answer.extra_answered = false;
        }
      } else {
        control.answered = false;
      }
      if (control.Review) {
        control.reviewed = true;
      } else {
        control.reviewed = false;
      }
      if (vea.assigned_to !== req.data.userId && vea.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        control.is_collaborator = true;
      } else {
        control.is_collaborator = false;
      }
    }
    let parents = controls?.filter(control => control.parent_id === null);
    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});
    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });
    // if (vea.status === constant.status.CHANGES_REQUESTED) {
    //     parents = parents.filter(parent => parent.Review?.accurate_information === 0);
    // }
    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: { status: vea.status, controls: parents } }, httpStatus.OK);
  } catch (err) {
    console.log('getMitigationError', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.controlsMitigation = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const { ReviewVEA, VendorAssessments, VeaCustomerControls, VendorDetail, AuditLog, User } = db.models;
    const mitigation = req.body.mitigation;

    const vea = await commonService.findByCondition(VendorAssessments, { vendor_id: req.params.vendor_id, assessment_type: 'vea' }, ['id', 'status', 'assigned_to', 'approver', 'department_id', 'risks']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    if (req.data.userId !== vea.Approver && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    if (mitigation.length > 0) {
      for (let item of mitigation) {
        const updateMitigation = await commonService.updateData(ReviewVEA, item, { customer_question_id: item.customer_question_id }, dbTrans);
        if (!updateMitigation[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }
    await dbTrans.commit();
    dbTrans = undefined;

    let riskLevel = null;
    let riskTier = null;
    let totalRiskScore = 0;
    let totalControls = 0;

    const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(
      VeaCustomerControls,
      ReviewVEA,
      'ReviewVEA',
      { vea_id: vea.id },
      {},
      [[sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      ['customer_question_id', 'risk_score']
    );

    // risk assessment logic
    checkReviewStatus?.forEach(review => {
      totalRiskScore += Number(review.ReviewVEA?.risk_score);
      ++totalControls;
    });
    totalRiskScore = totalRiskScore / totalControls;
    totalRiskScore = Math.round(totalRiskScore * 100) / 100;
    // risk level logic
    if (totalRiskScore <= 3) {
      riskLevel = 'Low';
      riskTier = 'LOW';
    } else if (totalRiskScore <= 6) {
      riskLevel = 'Medium';
      riskTier = 'MEDIUM';
    } else {
      riskLevel = 'High';
      riskTier = 'HIGH';
    }

    // update vendor status
    const stageUpdate = await commonService.updateData(VendorDetail, { risk_score: totalRiskScore, risk_tier: riskTier }, { vendor_id: req.params.vendor_id }, dbTrans);
    if (!stageUpdate[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_VEA_FAILED' }, httpStatus.BAD_REQUEST);
    }

    const veaUpdate = await commonService.updateData(VendorAssessments, { risks: riskLevel, risk_score: totalRiskScore }, { id: vea.id });
    if (!veaUpdate[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId });
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    //Entry in Audit Log
    const auditData = {
      type: 'VEA',
      type_id: vea.id,
      action: `${user?.firstName} ${user?.lastName} updated mitigation responses for Vendor Assessment.`,
      action_by_id: req.data.userId
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'MITIGATION_COMPLETED' }, httpStatus.OK);
  } catch (err) {
    console.log('updateMitigationError', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.countByRisks = async (req, res) => {
  try {
    const { ReviewVEA, VendorAssessments, VeaCustomerControls } = db.models;
    const vea = await commonService.findByCondition(VendorAssessments, { vendor_id: req.params.vendor_id, assessment_type: 'vea' }, ['id', 'status', 'assigned_to', 'approver']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const vea_id = vea.id;
    // counting the assessments using GroupBy "RISKS"
    let veaData = await veaService.getListGroupBy(ReviewVEA, VeaCustomerControls, {}, { vea_id: vea_id }, ['risk_score', [sequelize.fn('COUNT', 'id'), 'count']], [], ['risk_score']);
    if (!veaData) {
      return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }
    let count = 0;
    veaData.forEach(row => {
      count = count + Number(row.count);
    });

    // Map to store the corresponding x and y values for each risk_score
    const riskScoreMap = {
      1: { x: 1, y: 3 },
      2: { x: 1, y: 2 },
      3: { x: 2, y: 3 },
      4: { x: 1, y: 1 },
      5: { x: 2, y: 2 },
      6: { x: 3, y: 3 },
      7: { x: 2, y: 1 },
      8: { x: 3, y: 2 },
      9: { x: 3, y: 1 }
    };

    // Create a list of all risk scores from 1 to 9
    const allRiskScores = [1, 2, 3, 4, 5, 6, 7, 8, 9];

    // Ensure that each risk score from 1 to 9 is present in the output
    veaData = allRiskScores.map(riskScore => {
      const item = veaData.find(d => d.risk_score === riskScore) || { risk_score: riskScore, count: '0' };
      const { x, y } = riskScoreMap[riskScore];
      return { ...item, x, y };
    });

    return response.success(req, res, { msgCode: 'RISK_MATRIX', data: veaData }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

//-------->>> Template controllers <<<-----------

exports.templateList = async (req, res) => {
  try {
    const { VeaTemplate } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC', search } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    const templateCondition = { customer_id: req.data.customer_id };
    if (search) {
      templateCondition.temp_name = {
        [Op.iLike]: `%${search}%`
      };
    }
    const list = await commonService.getList(VeaTemplate, templateCondition, {}, limit, offset, order);
    if (!list) {
      return response.error(req, res, { msgCode: 'TEMPLATE_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }
    return response.success(req, res, { msgCode: 'LIST_FETCHED', data: list }, httpStatus.OK);
  } catch (err) {
    console.log('template listing error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.deleteTemplate = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    check = await commonService.findByCondition(VeaTemplate, { url: req.body.url }, ['id']);
    if (!check) {
      return response.error(req, res, { msgCode: 'DOCUMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const deleteControls = await commonService.deleteQuery(VeaControls, { template_id: check.id }, dbTrans);
    if (deleteControls) {
      return response.error(req, res, { msgCode: 'DOCUMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const deleteData = await commonService.deleteQuery(VeaTemplate, { id: check.id }, dbTrans);
    if (deleteData) {
      return response.error(req, res, { msgCode: 'DOCUMENT_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'DOCUMENT_DELETED', data: deleteData }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.downloadTemplate = async (req, res) => {
  try {
    const { VeaTemplate, User } = db.models;
    const template = await commonService.findByCondition(VeaTemplate, { id: req.params.template_id }, ['id', 'url', 'temp_name']);
    if (!template) {
      return response.error(req, res, { msgCode: 'DOCUMENT_DELETED_ERROR' }, httpStatus.BAD_REQUEST);
    }
    const filePath = path.join(__dirname, '../utils/excel', template.temp_name); // Example directory name
    await downloadFile(template.url, filePath);

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    await sendMailWithAttach(req.data.email, { name: `${user?.firstName} ${user?.lastName}` }, 'Your Copy of External Assessment Template made on GoTrust', 'vea_template_download.ejs', filePath);
    return response.success(req, res, { msgCode: 'VEA_DOWNLOADED' }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getVeaDataWithMitigation = async (req, res) => {
  try {
    const { VendorsMapping, VeaControls, VendorDetail, VeaCustomerControls, VeaAnswers, Customer, User, VendorAssessments, VeaCategory, ReviewVEA, Departments, Processes } = db.models;
    const vea_id = req.params.vea_id;

    const vea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status', 'assigned_to', 'approver', 'department_id', 'risks', 'vendor_id']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const vendorStatus = await vendorService.getList(VendorsMapping, VendorDetail, Customer, 'Vendor', Departments, { customer_id: req.data.customer_id }, { vendor_id: vea?.vendor_id }, {}, {}, [], ['stage'], ['name'], ['name']);
    // if (vendorStatus.rows[0].VendorDetail.stage !== 'MITIGATION') {
    //     return response.error(req, res, { msgCode: "MITIGATION_NOT_COMPLETED" }, httpStatus.BAD_REQUEST);
    // }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (vea.status !== constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VEA_NOT_COMPLETED' }, httpStatus.BAD_REQUEST);
    }

    const controlsAttributes = [
      [sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."title" ELSE "VeaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."description" ELSE "VeaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("VeaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."is_attachment" ELSE "VeaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."question" ELSE "VeaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."fields" ELSE "VeaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input" ELSE "VeaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("VeaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input_fields" ELSE "VeaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."question_id" IS NOT NULL THEN "VeaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    const controls = await vendorService.getControlsWithCategoryAndMitigation(
      VeaCustomerControls,
      VeaControls,
      VeaAnswers,
      User,
      VeaCategory,
      ReviewVEA,
      { vea_id: vea_id },
      {},
      {},
      {},
      {},
      {},
      controlsAttributes,
      [],
      ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      ['accurate_information', 'comments', 'risk_score', 'mitigation_plan', 'attachment'],
      [['question_id', 'ASC']]
    );
    // const controls = await assessmentService.getControlsWithCategory(VeaCustomerControls, VeaControls, VeaAnswers, User, VeaCategory, { vea_id: vea_id }, {}, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'], ['id', 'firstName', 'lastName'], ['id', 'name'], [['question_id', 'ASC']]);
    for (let control of controls) {
      control.Answer = control.VeaAnswer;
      delete control.VeaAnswer;
      control.Category = control.VeaCategory;
      delete control.VeaCategory;
      control.Review = control.ReviewVEA;
      delete control.ReviewVEA;
      if (control?.Review?.mitigation_plan) {
        // control.Review.mitigation_plan = JSON.parse(control.Review.mitigation_plan);
        control.Review.mitigation_plan = extractTextFromTipTapJSON(control.Review.mitigation_plan);
        // console.log("=======",control.Review.mitigation_plan);
      }
    }
    let parents = controls?.filter(control => control.parent_id === null);

    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const excelData = transformData(parents);

    const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

    const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const excelFile = await createVendorExcelFile(excelData, date, customer?.name, vendorStatus?.rows[0]?.Vendor?.name, vendorStatus?.rows[0]?.VendorDetail?.Department?.name);

    await sendMailWithAttach(req.data.email, { name: `${user?.firstName} ${user?.lastName}` }, 'Your Copy of Vendor Assessment Data file made on GoTrust', 'vea_download.ejs', excelFile);

    return response.success(req, res, { msgCode: 'VEA_DOWNLOADED', data: 'Vendor data sent via E-mail' }, httpStatus.OK);
  } catch (err) {
    console.log('Vendors Mitigation Error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.submitMitigation = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, VeaCustomerControls, AuditLog, Departments, Processes, User, ReviewVEA, VendorDetail } = db.models;
    const checkVEA = await veaService.getVEA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { vendor_id: req.params.vendor_id, assessment_type: 'vea' },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVEA) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // if (checkVEA.status !== 'MITIGATION') {
    //     return response.error(req, res, { msgCode: "VEA_NOT_SUBMITTED" }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    let veaName = null;
    let dept_id = null;

    if (checkVEA.Department) {
      veaName = checkVEA.Department.name;
      dept_id = checkVEA.Department.id;
    }

    // let stage = 'COMPLETED';
    let data = {
      stage: 'COMPLETED',
      end_date: Date.now()
    };
    const checkMitigationStatus = await commonService.getListAssociateWithoutCountWithAlias(
      VeaCustomerControls,
      ReviewVEA,
      'ReviewVEA',
      { vea_id: checkVEA.id },
      {},
      [[sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      ['accurate_information', 'risk_score', 'mitigation_plan']
    );

    // const unreviewedControls = checkMitigationStatus?.filter(review => review.ReviewVEA === null && review.parent_id === null);
    // if (unreviewedControls.length > 0) {
    //     return response.error(req, res, { msgCode: "ALL_NOT_REVIEWED", data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    const unmitigatedControls = checkMitigationStatus?.filter(review => review.ReviewVEA?.mitigation_plan === null);
    if (unmitigatedControls.length > 0) {
      return response.error(req, res, { msgCode: 'MITIGATION_PLAN_NOT_FILLED' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const vendorUpdate = await commonService.updateData(VendorDetail, data, { vendor_id: req.params.vendor_id }, dbTrans);
    if (!vendorUpdate[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const auditAction = `Submitted mitigation for ${veaName} VEA `;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VEA', type_id: req.params.vea_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'MITIGATION_SAVED', data: data }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.viewtemplate = async (req, res) => {
  try {
    const { VeaCategory, VeaControls } = db.models;
    let { template_id } = req.query;
    if (!template_id) {
      template_id = null;
    }

    let controls = await vendorService.getCategorieswiseControls(
      VeaCategory,
      VeaControls,
      { customer_id: req.data.customer_id, template_id: template_id },
      {},
      ['id', 'name'],
      ['id', 'title', 'description', 'fields', 'artifact_type', 'is_attachment', 'question', 'fields', 'extra_input', 'extra_input_type', 'extra_input_fields']
    );
    if (controls && controls.length === 0) {
      controls = await vendorService.getCategorieswiseControls(
        VeaCategory,
        VeaControls,
        { template_id: null },
        {},
        ['id', 'name'],
        ['id', 'title', 'description', 'fields', 'artifact_type', 'is_attachment', 'question', 'fields', 'extra_input', 'extra_input_type', 'extra_input_fields']
      );
    }
    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    controls = controls?.map(category => ({
      ...category,
      VeaControls: category?.VeaControls?.filter(control => control.title !== null)
    }));
    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: controls }, httpStatus.OK);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.collaboratorProgress = async (req, res) => {
  try {
    const { VeaCategory, VeaCustomerControls, VeaAnswers, VendorAssessments, ReviewVEA, VeaCollaborator, User } = db.models;
    const veaId = req.params.vea_id;

    const vea = await commonService.findByCondition(VendorAssessments, { id: veaId }, ['status','is_automated']);
    if (!vea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const findCategoryAndDetails = await commonService.getDistinct(VeaCustomerControls, { vea_id: veaId }, ['category_id'], ['category_id']);
    // console.log('------------------------------', findCategoryAndDetails);
    findCategoryAndDetails.sort((a, b) => a.category_id - b.category_id);
    if (!findCategoryAndDetails) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    let categoryProgress = [];

    for (let category of findCategoryAndDetails) {
      const category_id = category.category_id;

      let totalControls = 0;
      let answeredControls = 0;
      let childControls = [];
      let progress = 0;

      let controls = null;
      const condition = { vea_id: veaId, category_id };

      if (vea.status === constant.status.STARTED || vea.status === constant.status.COMPLETED || vea.status === constant.status.UNDER_REVIEW) {
        controls = await commonService.getListAssociateWithoutCount(VeaCustomerControls, VeaAnswers, condition, {}, ['id', 'parent_id'], ['id']);
        controls?.forEach(control => {
          if (control.parent_id === null) {
            totalControls++;
            if (control.VeaAnswer) answeredControls++;
          } else {
            childControls.push(control);
          }
        });
      } else if (vea.status === constant.status.CHANGES_REQUESTED) {
        controls = await veaService.getControlsWithAnswersAndReviews(VeaCustomerControls, VeaAnswers, ReviewVEA, condition, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);

        const parentControls = controls.rows?.filter(
          control => control?.parent_id === null && control?.ReviewVEA // only where review marked answer as accurate
        );
        parentControls?.forEach(control => {
          totalControls++;
          if (control.VeaAnswer?.updatedAt > control.ReviewVEA?.updatedAt || (control.VeaAnswer && control?.ReviewVEA?.accurate_information === 1)) {
            answeredControls++;
          }
        });

        childControls = controls.rows?.filter(
          ctrl => ctrl.parent_id !== null && ctrl.VeaAnswer // only include reviewed-accurate child controls
        );
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.entries(childControlsByParent)?.forEach(([parentId, childList]) => {
          const parentControl = parentControls?.find(control => control.id == parentId);
          if (parentControl && childList.every(ctrl => ctrl.VeaAnswer?.updatedAt > parentControl?.ReviewVEA?.updatedAt || (ctrl?.VeaAnswer && ctrl?.ReviewVEA?.accurate_information === 1))) {
            answeredControls += 1;
          }
        });
      }

      if (vea.status !== constant.status.CHANGES_REQUESTED) {
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.values(childControlsByParent)?.forEach(childList => {
          if (childList.every(control => control.VeaAnswer)) {
            answeredControls += 1;
          }
        });
      }

      progress = totalControls ? parseFloat(((answeredControls / totalControls) * 100).toFixed(2)) : 0;

      const details = await commonService.getListWith3Models(VeaCategory, VeaCollaborator, User, { id: category_id }, { vea_id: veaId, category_id }, {}, ['id', 'name'], {}, ['id', 'firstName', 'lastName', 'email']);
      const categoryDetails = details[0] || {};
      const collaborators =
        categoryDetails?.VeaCollaborators?.map(collab => ({
          id: collab.User?.id,
          name: `${collab.User?.firstName} ${collab.User?.lastName}`
        })) || [];

       if (vea.is_automated) {
        categoryProgress.push({
          category_id,
          category_name: categoryDetails?.name || 'Uncategorized',
          progress,
          collaborators: collaborators.length ? collaborators : null
        });
      } else {
        categoryProgress.push({
          category_id,
          category_name: categoryDetails?.name || 'Unknown',
          progress,
          collaborators: collaborators.length ? collaborators : null
        });
      }
    }

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: categoryProgress }, httpStatus.OK);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.createEmbeddings = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaControls, ViaCustomerControls } = db.models;
    const controls = await commonService.getList(VeaControls, { customer_id: null }, ['id', 'description', 'question', 'title']);
    if (!controls) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    for (const control of controls.rows) {
      const str = control.title + ' and ' + control.description + ' and ' + control.question;
      const embedding = await generateEmbedding(str);
      if (embedding) {
        await commonService.updateData(VeaControls, { embedding }, { id: control.id });
      }
    }
    return response.success(req, res, { msgCode: 'EMBEDDINGS_CREATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.automateFilledVea = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaControls, VendorAssessments, VeaCustomerControls, VeaAnswers,User, AuditLog, Departments, Processes } = db.models;
    const vea_id = req.params.vea_id;

    const vea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, ['status', 'assigned_to', 'approver', 'template_id']);
    if (!vea) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const updateVea = await commonService.updateData(VendorAssessments, { is_automated: true }, { id: vea_id }, dbTrans);
    if (!updateVea[1]) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'FAILED_TO_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (vea.status === constant.status.YET_TO_START) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ROPA_NOT_STARTED' }, httpStatus.BAD_REQUEST);
    }
    // console.log(req.data);

    // let categoryCondition = {};
    // //fetching the categories
    // categoryCondition.customer_id = req.data.customer_id;

    // if (ropa.is_already_performed === true) {
    //   categoryCondition.template_id = ropa.template_id;
    // } else {
    //   categoryCondition.template_id = null;
    // }
    // let categories = await commonService.getList(Category, categoryCondition, ['id', 'name'], null, null, [['id', 'ASC']]);
    // if (categories.count == 0) {
    //   categoryCondition.customer_id = null;
    //   categories = await commonService.getList(Category, categoryCondition, ['id', 'name'], null, null, [['id', 'ASC']]);
    //   if (!categories) {
    //     await deleteFile(req.files[0].path);
    //     return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    //   }
    // }
    // const automatedCategoryId = categories.rows[categories.count - 1].id + 1;

    let controls;
    const controlsAttributes = [
      [sequelize.literal(`"VeaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."title" ELSE "VeaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."description" ELSE "VeaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("VeaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."is_attachment" ELSE "VeaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."question" ELSE "VeaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."fields" ELSE "VeaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input" ELSE "VeaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN CAST("VeaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("VeaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN "VeaCustomerControls"."extra_input_fields" ELSE "VeaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."is_custom" THEN null ELSE "VeaControl"."embedding" END`), 'embedding'],
      [sequelize.literal(`CASE WHEN "VeaCustomerControls"."question_id" IS NOT NULL THEN "VeaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    if (vea.status === constant.status.UNDER_REVIEW && vea.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }

    controls = await veaService.getControls(VeaCustomerControls, VeaControls, VeaAnswers, User, { vea_id: vea_id }, {}, {}, {}, controlsAttributes, [], ['id', 'answer', 'extra_answer'], ['id', 'firstName', 'lastName'], [['question_id', 'ASC']]);
    if (!controls) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const filePath = req.files[0].path;
    const mimeType = req.files[0].mimetype;

    const extractedData = await parsingService.parseFile(filePath, mimeType);
    // console.log('----------', extractedData);

    const matchedResults = [];
    const unmatchedResults = [];
    for (const fileRow of extractedData) {
      if (fileRow.question === undefined || fileRow.question === '') continue;
      let unmatch = true;
      for (const control of controls) {
        if (control.embedding) {
          const score = cosineSimilarity(control.embedding, fileRow.embedding);
          if (score >= 0.85 && matchedResults.filter(r => r.customer_question_id === control.customer_question_id).length === 0) {
            unmatch = false;
            matchedResults.push({
              customer_question_id: control.customer_question_id,
              answer: [fileRow.answer],
              answered_by: req.data.userId,
              is_automated: true
            });

            //updating question
            const data = {
              title: control.title,
              description: control.description,
              artifact_type: 'textarea',
              is_attachment: control.is_attachment,
              question: control.question,
              fields: control.fields,
              extra_input: control.extra_input,
              extra_input_type: control.extra_input_type,
              extra_input_fields: control.extra_input_fields,
              endpoint: control.endpoint,
              is_custom: true,
              is_automated: true
            };
            const updateQuestion = await commonService.updateData(VeaCustomerControls, data, { id: control.customer_question_id }, dbTrans);
          }
        }
      }
      if (unmatch) {
        const data = {
          title: fileRow.question,
          category_id: null,
          customer_id: req.data.customer_id,
          artifact_type: 'textarea',
          vea_id: vea_id,
          is_custom: true,
          is_automated: true
        };

        const addUnmatcedControl = await commonService.addDetail(VeaCustomerControls, data, dbTrans);
        if (!addUnmatcedControl) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        unmatchedResults.push({
          customer_question_id: addUnmatcedControl.id,
          answer: [fileRow.answer],
          answered_by: req.data.userId,
          is_automated: true
        });
      }
    }
    const addMatchedControls = await commonService.bulkAdd(VeaAnswers, matchedResults, dbTrans);
    if (!addMatchedControls) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const addUnmatchedControls = await commonService.bulkAdd(VeaAnswers, unmatchedResults, dbTrans);
    if (!addUnmatchedControls) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // console.log(matchedResults);
    // console.log(unmatchedResults);

    await deleteFile(req.files[0].path);
    //audit logic
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: req.params.vea_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    let veaName = null;
    let dept_id = null;
    if (veaDetails?.Department) {
      veaName = veaDetails.Department?.name;
      dept_id = veaDetails.Department?.id;
    } else if (veaDetails?.Process) {
      veaName = veaDetails.Process?.name;
      dept_id = veaDetails.Process?.Department?.id;
    }
    const auditAction = `${user?.firstName} ${user?.lastName} automated answers upload for ${veaName} VIA`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'VIA',
        type_id: req.params.vea_id,
        action: auditAction,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id,
        dept_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.categorizationOfControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VeaCustomerControls, VeaControls, VendorAssessments,AuditLog, User, Departments, Processes, VeaCategory } = db.models;
    const { category, vea_id, global, customer_question_id } = req.body;
    const checkVea = await commonService.findByCondition(VendorAssessments, { id: vea_id }, {});
    if (!checkVea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const checkControl = await commonService.findByCondition(VeaCustomerControls, { id: customer_question_id }, {});
    if (!checkControl) {
      return response.error(req, res, { msgCode: 'CONTROL_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    let customer_id = null;

    //auditlog
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    const veaDetails = await veaService.getVEA(VendorAssessments, Departments, Processes, User, { id: vea_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!veaDetails) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    let veaName = null;
    let dept_id = null;
    if (veaDetails?.Department) {
      veaName = veaDetails.Department?.name;
      dept_id = veaDetails.Department?.id;
    } else if (veaDetails?.Process) {
      veaName = veaDetails.Process?.name;
      dept_id = veaDetails.Process?.Department?.id;
    }

    if (global === true) {
      const str = checkControl.title + ' and ' + checkControl.description + ' and ' + checkControl.question;
      const embedding = await generateEmbedding(str);
      if (checkVea.template_id) {
        customer_id = checkVea.customer_id;
      }
      const data = {
        title: checkControl.title,
        description: checkControl.description,
        customer_id: customer_id,
        category_id: category,
        industry_vertical_id: 1,
        artifact_type: checkControl.artifact_type,
        is_attachment: checkControl.is_attachment,
        question: checkControl.question,
        fields: checkControl.fields,
        extra_input: checkControl.extra_input,
        extra_input_type: checkControl.extra_input_type,
        extra_input_fields: checkControl.extra_input_fields,
        endpoint: checkControl.endpoint,
        template_id: checkVea.template_id,
        embedding: embedding
      };
      const addMasterControl = await commonService.addDetail(VeaControls, data, dbTrans);
      if (!addMasterControl) {
        return response.error(req, res, { msgCode: 'CONTROL_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      const updateCustomerControl = await commonService.updateData(VeaCustomerControls, { category_id: category }, { id: customer_question_id }, dbTrans);
      if (!updateCustomerControl[1]) {
        return response.error(req, res, { msgCode: 'CONTROL_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      //find category name
      const categoryObj = await commonService.findByCondition(VeaCategory, { id: category }, ['name']);
      if (!categoryObj) {
        return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      const controlTitle = checkControl?.title;
      const auditAction = `${user?.firstName} ${user?.lastName} moved the control ${controlTitle} to the ${categoryObj?.name} category as a master control in ${veaName} ROPA`;
      const auditLog = await commonService.addDetail(
        AuditLog,
        {
          type: 'VEA',
          type_id: vea_id,
          action: auditAction,
          action_by_id: req.data.userId,
          customer_id: req.data.customer_id,
          dept_id
        },
        dbTrans
      );
      if (!auditLog) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updateCustomerControl[1] }, httpStatus.OK, dbTrans);
    }
    const updateControl = await commonService.updateData(VeaCustomerControls, { category_id: category }, { id: customer_question_id }, dbTrans);
    if (!updateControl[1]) {
      return response.error(req, res, { msgCode: 'CONTROL_NOT_UPDATED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    //audit
    const categoryObj = await commonService.findByCondition(VeaCategory, { id: category }, ['name']);
    if (!categoryObj) {
      return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const controlTitle = checkControl?.title;
    const auditAction = `${user?.firstName} ${user?.lastName} moved the control "${controlTitle}" to the "${categoryObj?.name}" category in ${veaName} ROPA`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'VEA',
        type_id: vea_id,
        action: auditAction,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id,
        dept_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_FOUND', data: checkControl }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateRecurrenceDate = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, VendorsMapping, User, AuditLog } = db.models;
    const formattedDate = dayjs(req.body.recurrence_date).format('DD-MM-YYYY');

    const checkVea = await commonService.findByCondition(VendorAssessments, { id: req.params.vea_id, assessment_type: 'vea' }, ['id', 'assigned_to', 'approver', 'vendor_id', 'customer_id', 'assessment_name']);
  
    if (!checkVea) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (req.data.userId !== checkVea.assigned_to && req.data.userId !== checkVea.approver) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const updateVea = await commonService.updateData(VendorAssessments, req.body, { id: req.params.vea_id }, dbTrans);
    if (!updateVea[1]) {
      return response.error(req, res, { msgCode: 'VEA_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //corresponding VIA 
    const correspondingVia = await commonService.findByCondition(
      VendorAssessments,
      {
        vendor_id: checkVea.vendor_id,
        customer_id: checkVea.customer_id,
        assessment_type: 'via',
        is_latest: true
      },
      ['id', 'assessment_name']
    );

    if (correspondingVia) {
      const updateVia = await commonService.updateData(VendorAssessments, { recurrence_date: req.body.recurrence_date }, { id: correspondingVia.id }, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    const veaName = checkVea.assessment_name || 'VEA';
    const auditAction = `${user?.firstName} ${user?.lastName} updated recurrence date to ${formattedDate} for ${veaName}`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'VEA',
        type_id: req.params.vea_id,
        action: auditAction,
        action_by_id: req.data.userId,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'RECURRENCE_DATE_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
