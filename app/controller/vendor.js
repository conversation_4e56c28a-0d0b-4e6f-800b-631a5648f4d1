const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const vendorService = require('../services/vendor');
// const viaService = require('../services/via');
// const assessmentService = require('../services/assessment');
const constant = require('../constant/vendor');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
// const csv = require('csv-parser');
// const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach, sendMailV2 } = require('../config/email');
// const { transformData, createAssessmentExcelFile } = require('../utils/helper');
const authService = require('../services/auth');
const { signToken } = require('../config/helper');
const { USER_ROLE, CLIENT_ONBOARD, MESSAGE, ONBOARDING_STATUS } = require('../constant/common');

const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const keycloakService = require('../services/keycloak');

exports.vendorListForCustomer = async (req, res) => {
  try {
    const { VendorsMapping, VendorDetail, Customer, User, Departments, Group } = db.models;
    const { page, size, start_date, end_date, search, search_key, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    let vendorFilter = { customer_id: req.data.customer_id };
    let searchCondition = {};

    if (search && !search_key) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('VendorDetail.Department.name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Vendor.name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('VendorDetail.Assign.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('VendorDetail.Assign.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('VendorDetail.Review.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('VendorDetail.Review.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('VendorDetail.Created.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('VendorDetail.Created.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('VendorDetail.risk_tier'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('VendorDetail.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        ]
      };
    }

    if (search && search_key) {
      if (search_key === 'Department') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('VendorDetail.Department.name'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'AssignedTo') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('VendorDetail.Assign.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('VendorDetail.Assign.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Approver') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('VendorDetail.Review.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('VendorDetail.Review.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Vendor') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Vendor.name'), { [Op.iLike]: `%${search}%` })]
        };
      }
    }

    if (start_date) {
      searchCondition = {
        ...searchCondition,
        review_date: {
          [Op.gte]: req.query.start_date
        }
      };
    }
    if (end_date) {
      searchCondition = {
        ...searchCondition,
        review_date: {
          [Op.lte]: req.query.end_date
        }
      };
    }

    if (start_date && end_date) {
      searchCondition = { ...searchCondition, review_date: { [Op.between]: [start_date, end_date] } };
    }

    const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
    const departmentIds = departments?.rows?.map(department => department.id);
    let vendorFilter1 = {};
    if (req.data.roleName === authConstant.USER_ROLE[2]) {
      userType = 'DPO';
      vendorFilter1 = {};
    } else if (departmentIds.length > 0) {
      userType = 'Department Head';
      vendorFilter1 = {
        [Op.or]: [{ assigned_to: req.data.userId }, { reviewer_id: req.data.userId }, { department_id: { [Op.in]: departmentIds } }]
      };
    } else {
      userType = 'Employee';
      vendorFilter1 = {
        [Op.or]: [{ assigned_to: req.data.userId }, { reviewer_id: req.data.userId }]
      };
    }
    let listFilter = {};
    if (req.query.group_id) {
      listFilter = { group_id: req.query.group_id, ...vendorFilter1 };
    } else {
      listFilter = { ...vendorFilter1 };
    }

    const data = await vendorService.getVendorList(
      VendorsMapping,
      VendorDetail,
      Customer,
      User,
      Departments,
      Group,
      {
        [Op.and]: [vendorFilter, searchCondition]
      },
      { ...listFilter },

      {},
      {},
      {},
      {},
      ['id', 'vendor_id', 'customer_id'],
      ['id', 'stage', 'risk_tier', 'risk_score', 'status', 'contact_mail', 'contact'],
      ['name'],
      ['firstName', 'lastName'],
      ['name'],
      ['name'],
      limit,
      offset,
      order
    );

    if (!data) {
      return response.error(req, res, { msgCode: 'FETCHING_ERROR' }, httpStatus.BAD_REQUEST);
    }

    const customer_email = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['email']);
    const owner = await commonService.findByCondition(User, { email: customer_email.email }, ['firstName', 'lastName', 'email']);

    data['rows'] = data?.rows?.map(row => {
      return {
        id: row?.id,
        vendor_id: row?.vendor_id,
        customer_id: row?.customer_id,
        VendorDetail: {
          id: row?.VendorDetail?.id,
          risk_tier: row?.VendorDetail?.risk_tier,
          risk_score: row?.VendorDetail?.risk_score,
          status: row?.VendorDetail?.status,
          stage: row?.VendorDetail?.stage,
          Created: {
            name: `${row?.VendorDetail?.Created?.firstName} ${row?.VendorDetail?.Created?.lastName}`
          },
          Updated: row?.VendorDetail?.Updated ? { name: `${row?.VendorDetail?.Updated?.firstName} ${row?.VendorDetail?.Updated?.lastName}` } : null,
          Assign: {
            name: `${row?.VendorDetail?.Assign?.firstName} ${row?.VendorDetail?.Assign?.lastName}`
          },
          Review: {
            name: `${row?.VendorDetail?.Review?.firstName} ${row?.VendorDetail?.Review?.lastName}`
          },
          Department: {
            name: row?.VendorDetail?.Department?.name
          },
          Group: {
            name: row?.VendorDetail?.Group?.name
          }
        },
        Customer: {
          name: row?.Customer?.name,
          owner: `${owner?.firstName} ${owner?.lastName}`
        },
        Vendor: {
          name: row?.Vendor?.name
        }
      };
    });
    data.userType = userType;

    return response.success(req, res, { msgCode: 'VENDOR_LIST_FETCHED', data: data }, httpStatus.OK);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.createNewVendor = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const { User, Customer, Group, CustomerResources, Role, UserRolePrivileges, OnboardingFlow, GroupUser, Resources, VendorsMapping, VendorList } = db.models;

    req.body.email = req.body.email.toLowerCase();

    const userExists = await commonService.findByCondition(User, { email: req.body.email }, ['id'], dbTrans);
    if (userExists) {
      return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const customerExists = await commonService.findByCondition(Customer, { email: req.body.email }, ['id'], dbTrans);
    if (customerExists) {
      return response.error(req, res, { msgCode: 'EMAIL_ALREADY_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let orgData = {};

    orgData.name = req.body.vendor_name;
    orgData.email = req.body.email;
    orgData.customer_type = 'VENDOR';
    orgData.status = 'active';
    orgData.phone = req.body.phone;
    orgData.country_code = req.body.country_code;
    orgData.address = req.body.address;

    // create Customer
    const organisationData = await commonService.addDetail(Customer, orgData, dbTrans);
    if (!organisationData) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let vendorData = {};
    vendorData.name = req.body.vendor_name;
    vendorData.email = req.body.email;
    vendorData.customer_id = req.data.customer_id;
    vendorData.status = 'active';
    vendorData.poc_type = 'EMAIL';
    vendorData.phone = req.body.phone;
    vendorData.address = req.body.address;

    //Adding in VendorList Table
    const addVendor = await commonService.addDetail(VendorList, vendorData, dbTrans);
    if (!addVendor) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const roleData = [
      // { role_name: USER_ROLE[2], customer_id: organisationData.id },
      { role_name: USER_ROLE[4], customer_id: organisationData.id },
      { role_name: USER_ROLE[8], customer_id: organisationData.id }
    ];

    // create role
    const createRole = await authService.BulkData(Role, roleData, dbTrans);
    if (!createRole) {
      return response.error(req, res, { msgCode: 'ERROR_CREATE_ROLE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let resource_list = [];
    resource_list = await commonService.getList(Resources, { resource_name: { [Op.in]: ['Vendor Risk Management', 'Task Overview'] }, status: 1 }, ['resource_id', 'resource_name']);
    resource_list = resource_list.rows.map(item => {
      if (item?.resource_name === 'Task Overview' || item?.resource_name === 'Vendor Risk Management') {
        return item?.resource_id;
      }
    });

    let orgResourceData = [],
      userRolePrivilegesData = [];
    resource_list?.forEach(element => {
      orgResourceData.push({
        customer_id: organisationData.id,
        resource_id: element
      });
      userRolePrivilegesData.push({
        role_id: createRole[0].role_name !== USER_ROLE[8] ? createRole[0].id : createRole[1].id,
        resource_id: element
      });
    });

    // create organization resource
    const createOrgRole = await authService.BulkData(CustomerResources, orgResourceData, dbTrans);
    if (!createOrgRole) {
      return response.error(req, res, { msgCode: 'ERROR_CREATE_ROLE' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // create User Priviledges
    const createUserPrivileges = await authService.BulkData(UserRolePrivileges, userRolePrivilegesData, dbTrans);
    if (!createUserPrivileges) {
      return response.error(req, res, { msgCode: 'ERROR_CREATE_ROLE' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    // create a otp
    const otp = Math.floor(100000 + Math.random() * 900000);
    // generate token
    const token = await signToken(
      {
        email: req.body.email,
        otp: otp,
        role: createRole[0].id
      },
      3600
    );

    req.body.access_token = token;
    req.body.role_id = createRole[0].id;
    req.body.otp = otp;
    req.body.customer_id = organisationData.id;

    // create User
    const password = crypto.randomBytes(8).toString('hex');
    // Encrypt the password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Add the hashed password to the request body
    req.body.password = hashedPassword;
    const saveUserData = await commonService.addDetail(User, req.body, dbTrans);
    if (!saveUserData) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // save onboarding flow
    const saveOnboardingFlow = await commonService.addDetail(OnboardingFlow, { user_id: saveUserData.id, step: ONBOARDING_STATUS.ONBOARDING_COMPLETED }, dbTrans);
    if (!saveOnboardingFlow) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // adding to keycloak
    const keycloakToken = await keycloakService.getToken();
    if (!keycloakToken) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const keycloakUser = {
      username: req.body.email,
      email: req.body.email,
      enabled: true,
      emailVerified: true,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      credentials: [{ type: 'password', value: password }]
    };

    const keycloakUserResponse = await keycloakService.createUser(keycloakToken.access_token, keycloakUser);
    if (!keycloakUserResponse) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Group data
    const groupDataArray = [
      {
        customer_id: organisationData.id,
        name: req.body.vendor_name,
        user_id: saveUserData.id
      }
    ];
    // create Group
    const createGroup = await authService.BulkData(Group, groupDataArray, dbTrans);
    if (!createGroup) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // update User Group Access
    const groupId = createGroup[0].name != 'Unassigned Assets' ? createGroup[0].id : createGroup[1].id;

    const addGroupUser = await commonService.addDetail(GroupUser, { user_id: saveUserData.id, group_id: groupId }, dbTrans);
    if (!addGroupUser) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // save onboarding flow
    const subject = `Welcome to GoTrust`;
    const textTemplate = 'new-user-password-email.ejs';
    const sendData = {
      name: `${req.body.firstName} ${req.body.lastName}`,
      email: req.body.email,
      password: password
    };
    sendMail(req.body.email, sendData, subject, textTemplate);

    //adding vendor in vendor mapping table
    const vendorMapping = await commonService.addDetail(VendorsMapping, { customer_id: req.data.customer_id, vendor_id: saveUserData.customer_id }, dbTrans);
    if (!vendorMapping) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const saveData = {
      admin_id: saveUserData.id,
      email: saveUserData.email,
      phone: saveUserData.phone,
      firstName: saveUserData.firstName,
      lastName: saveUserData.lastName,
      country_code: saveUserData.country_code,
      vendor_id: saveUserData.customer_id,
      vendorName: req.body.vendor_name,
      signup_token: token,
      onboarding_status: ONBOARDING_STATUS.USER_CREATED
    };

    saveData.signup_token = token;
    saveData.onboarding_status = ONBOARDING_STATUS.ONBOARDING_COMPLETED;
    saveData.vendor = vendorMapping;

    return response.success(req, res, { msgCode: 'VENDOR_ADDED', data: { saveData } }, httpStatus.CREATED, dbTrans);
  } catch (error) {
    console.log('vendor', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.addVendor = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const { VendorAssessments, VendorDetail, User, AuditLog } = db.models;
    req.body.customer_id = req.data.customer_id;
    req.body.created_by = req.data.userId;
    const template_id = req.body.template_id;
    delete req.body.template_id;

    const addVendor = await commonService.addDetail(VendorDetail, req.body, dbTrans);
    if (!addVendor) {
      return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // getting the user info
    const user = await commonService.findByCondition(User, { id: req.data.userId });
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    //Entry in Audit Log
    const auditData = {
      type: 'VENDOR',
      type_id: addVendor?.id,
      action: `${user?.firstName} ${user?.lastName} Created a new vendor`,
      action_by_id: req.data.userId
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    //Entry in Vendor Assessment
    const data = {
      assessment_type: 'via',
      assessment_name: 'Data Collection And Acquisition',
      status: 'Yet to Start',
      customer_id: req.data.customer_id,
      vendor_id: req.body.vendor_id,
      approver: req.body.reviewer_id,
      assigned_to: req.body.assigned_to,
      entity_id: req.body.group_id,
      department_id: req.body.department_id,
      template_id: template_id
    };

    const vendorAssessment = await commonService.addDetail(VendorAssessments, data, dbTrans);
    if (!vendorAssessment) {
      return response.error(req, res, { msgCode: 'INTERNAL_ASSESSMENT_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    addVendor.assessmentDetail = vendorAssessment;

    return response.success(req, res, { msgCode: 'VENDOR_ADDED', data: addVendor }, httpStatus.CREATED, dbTrans);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateVendor = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const { VendorDetail, User, AuditLog } = db.models;
    const check = await commonService.findByCondition(VendorDetail, { vendor_id: req.params.vendor_id }, ['id', 'stage']);
    if (!check) {
      return response.error(req, res, { msgCode: 'VENDOR_NOT_FOUND' }, httpStatus.NO_CONTENT, dbTrans);
    }
    req.body.updated_by_id = req.data.userId;
    if (check.stage !== 'CREATE') {
      delete req.body.stage;
    }
    const updateVendor = await commonService.updateData(VendorDetail, req.body, { vendor_id: req.params.vendor_id }, dbTrans);
    if (!updateVendor[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.NOT_MODIFIED, dbTrans);
    }

    // getting the user info
    const user = await commonService.findByCondition(User, { id: req.data.userId });
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    //Entry in Audit Log
    const auditData = {
      type: 'VENDOR',
      type_id: updateVendor[1].id,
      action: `${user?.firstName} ${user?.lastName} Created a new vendor`,
      action_by_id: req.data.userId
    };
    const audit = await commonService.addDetail(AuditLog, auditData, dbTrans);
    if (!audit) {
      return response.error(req, res, { msgCode: 'ERROR_IN_AUDIT' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VENDOR_UPDATED', data: updateVendor }, httpStatus.CREATED, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.getVendorDetails = async (req, res) => {
  try {
    const { VendorDetail, VendorsMapping, User, Customer, Departments } = db.models;
    const check = await vendorService.getVendor(VendorsMapping, Customer, { id: req.params.vendor_id }, {}, ['id', 'vendor_id', 'customer_id'], ['name']);
    if (!check) {
      return response.error(req, res, { msgCode: 'VENDOR_NOT_EXIST' }, httpStatus.BAD_REQUEST);
    }
    const vendorData = await vendorService.getVendorDetail(
      VendorsMapping,
      VendorDetail,
      Customer,
      User,
      Departments,
      { id: req.params.vendor_id },
      {},
      {},
      {},
      {},
      ['id', 'vendor_id', 'customer_id'],
      [
        'id',
        'vendor_id',
        'vpoc_id',
        'description',
        'contact_mail',
        'contact',
        'created_by',
        'createdAt',
        'updatedAt',
        'updated_by',
        'group_id',
        'customer_id',
        'department_id',
        'reviewer_id',
        'assigned_to',
        'status',
        'stage',
        'risk_tier',
        'risk_score',
        'type_id',
        're_assessment_status',
        'reminder_date',
        'next_review',
        'completion_date'
      ],
      ['name'],
      ['firstName', 'lastName', 'email', 'country_code', 'phone'],
      ['name']
    );

    const customer_email = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['email']);
    const owner = await commonService.findByCondition(User, { email: customer_email.email }, ['firstName', 'lastName', 'email']);
    const transformedData = {
      ...vendorData,
      VendorDetail: {
        ...vendorData.VendorDetail,
        owner: { name: `${owner?.firstName} ${owner?.lastName}` },
        Created: vendorData?.VendorDetail?.Created ? { name: `${vendorData?.VendorDetail?.Created?.firstName} ${vendorData?.VendorDetail?.Created?.lastName}` } : null,
        Assign: vendorData?.VendorDetail?.Assign ? { name: `${vendorData?.VendorDetail?.Assign?.firstName} ${vendorData?.VendorDetail?.Assign?.lastName}` } : null,
        Review: vendorData?.VendorDetail?.Review ? { name: `${vendorData?.VendorDetail?.Review?.firstName} ${vendorData?.VendorDetail?.Review?.lastName}` } : null,
        VendorPOC: vendorData?.VendorDetail?.VendorPOC
          ? {
              name: `${vendorData?.VendorDetail?.VendorPOC?.firstName} ${vendorData?.VendorDetail?.VendorPOC?.lastName}`,
              email: `${vendorData?.VendorDetail?.VendorPOC?.email}`,
              country_code: vendorData?.VendorDetail?.VendorPOC?.country_code,
              phone: vendorData?.VendorDetail?.VendorPOC?.phone
            }
          : null,
        Department: vendorData?.VendorDetail?.Department
          ? {
              name: `${vendorData?.VendorDetail?.Department?.name} `,
              SPOC: {
                name: `${vendorData?.VendorDetail?.Department?.User?.firstName} ${vendorData?.VendorDetail?.Department?.User?.lastName}`,
                email: `${vendorData?.VendorDetail?.Department?.User?.email}`,
                country_code: vendorData?.VendorDetail?.Department?.User?.country_code,
                phone: vendorData?.VendorDetail?.Department?.User?.phone
              }
            }
          : null
      }
    };

    return response.success(req, res, { msgCode: 'VENDOR_FETHCED', data: transformedData }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.viaList = async (req, res) => {
  try {
    const { VendorAssessments, User, Departments, ViaCollaborator, Group } = db.models;
    const { page, size, search, search_key, is_assigned, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let userType = null;
    let searchCondition = {};

    if (search && !search_key) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        ]
      };
    }

    if (search && search_key) {
      if (search_key === 'Department') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'AssignedTo') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Approver') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Owner') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Status') {
        searchCondition = {
          status: sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'Risks') {
        searchCondition = {
          risks: sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'is_assigned') {
        searchCondition = {
          is_Assigned: true
        };
      }
    }

    const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
    const departmentIds = departments?.rows?.map(department => department.id);

    if (req.data.roleName === authConstant.USER_ROLE[2]) {
      userType = 'DPO';
    } else if (departmentIds.length > 0) {
      userType = 'Department Head';
    } else {
      userType = 'Employee';
    }

    const collaboratorVia = await commonService.getList(ViaCollaborator, { user_id: req.data.userId }, ['via_id']);
    const collaboratorViaIds = collaboratorVia?.rows?.map(collaborator => collaborator.via_id);

    let viaFilter = { customer_id: req.data.customer_id, vendor_id: req.params.vendor_id, assessment_type: 'via' };

    if (userType === 'DPO') {
      viaFilter = { ...viaFilter };
    } else if (userType === 'Department Head') {
      viaFilter = {
        ...viaFilter,
        [Op.or]: [{ assigned_to: req.data.userId }, { approver: req.data.userId }, { id: { [Op.in]: collaboratorViaIds } }, { department_id: { [Op.in]: departmentIds } }]
      };
    } else {
      viaFilter = {
        ...viaFilter,
        [Op.or]: [{ assigned_to: req.data.userId }, { approver: req.data.userId }, { id: { [Op.in]: collaboratorViaIds } }]
      };
    }
    const list = await vendorService.getListWithMultipleAssociates2(
      VendorAssessments,
      Departments,
      User,
      'AssignedTo',
      User,
      'Approver',
      User,
      'Owner',
      Group,
      {
        [Op.and]: [
          viaFilter,
          {
            [Op.or]: [sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('VendorAssessments.entity_id'), { [Op.ne]: null })]
          },
          is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
          searchCondition
        ]
      },
      {},
      {},
      {},
      {},
      {},
      ['id', 'vendor_id', 'assessment_type', 'assessment_name', 'entity_id', 'risks', 'progress', 'start_date', 'end_date', 'status'],
      ['id', 'name'],
      ['id', 'firstName', 'lastName'],
      ['id', 'firstName', 'lastName'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      limit,
      offset,
      order
    );
    if (!list) {
      return response.error(req, res, { msgCode: 'FETCHING_ERROR' }, httpStatus.NOT_FOUND);
    }

    list['rows'] = list?.rows?.map(row => {
      return {
        id: row?.id,
        vendor_id: row?.vendor_id,
        Assessment: {
          key: row?.assessment_type,
          assessment_name: row?.assessment_name // corrected the spelling
        },
        risks: row?.risks,
        progress: row?.progress,
        start_date: row?.start_date,
        end_date: row?.end_date,
        status: row?.status,
        Department: {
          id: row?.Department?.id,
          name: row?.Department?.name
        },
        AssignedTo: {
          id: row?.AssignedTo?.id,
          name: `${row?.AssignedTo?.firstName} ${row?.AssignedTo?.lastName}`
        },
        Approver: {
          id: row?.Approver?.id,
          name: `${row?.Approver?.firstName} ${row?.Approver?.lastName}`
        },
        Owner: {
          id: row?.Owner?.id,
          name: `${row?.Owner?.firstName} ${row?.Owner?.lastName}`
        },
        Group: {
          id: row?.Group?.id,
          name: row?.Group?.name
        },
        isCollaborator: collaboratorViaIds.includes(row.id)
      };
    });
    list.user_type = userType;

    if (list.rows[0].AssignedTo) {
      if (list.rows[0]?.AssignedTo?.id === req.data.userId) {
        list.rows[0].isAssigned = true;
      } else {
        list.rows[0].isAssigned = false;
      }
    }

    return response.success(req, res, { msgCode: 'VENDOR_LIST_FETCHED', data: list }, httpStatus.OK);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.veaList = async (req, res) => {
  try {
    const { VendorAssessments, VendorsMapping, User, Departments, VeaCollaborator, Group } = db.models;
    const { page, size, search, search_key, is_assigned, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let userType = null;
    let searchCondition = {};
    let vendor_id;
    let veaFilter = {};
    // console.log("hii", req.data);

    if (req.query.vendor_id) {
      vendor_id = req.query.vendor_id;
      veaFilter = { customer_id: req.data.customer_id, vendor_id: vendor_id, assessment_type: 'vea' };
    } else {
      const vendor = await commonService.findByCondition(VendorsMapping, { vendor_id: req.data.customer_id }, ['id']);
      if (!vendor) {
        return response.error(req, res, { msgCode: 'VENDOR_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
      vendor_id = vendor.id;
      veaFilter = { vendor_id: vendor.id, assessment_type: 'vea' };
    }

    if (search && !search_key) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        ]
      };
    }

    if (search && search_key) {
      if (search_key === 'Department') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Department.name'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'AssignedTo') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('AssignedTo.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('AssignedTo.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Approver') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Approver.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('Approver.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Owner') {
        searchCondition = {
          [Op.or]: [sequelize.where(sequelize.col('Owner.firstName'), { [Op.iLike]: `%${search}%` }), sequelize.where(sequelize.col('Owner.lastName'), { [Op.iLike]: `%${search}%` })]
        };
      } else if (search_key === 'Status') {
        searchCondition = {
          status: sequelize.where(sequelize.cast(sequelize.col('status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'Risks') {
        searchCondition = {
          risks: sequelize.where(sequelize.cast(sequelize.col('risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'is_assigned') {
        searchCondition = {
          is_Assigned: true
        };
      }
    }

    const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
    const departmentIds = departments?.rows?.map(department => department.id);

    if (req.data.roleName === authConstant.USER_ROLE[2]) {
      userType = 'DPO';
    } else if (departmentIds.length > 0) {
      userType = 'Department Head';
    } else {
      userType = 'Employee';
    }

    const collaboratorVea = await commonService.getList(VeaCollaborator, { user_id: req.data.userId }, ['vea_id']);
    const collaboratorVeaIds = collaboratorVea?.rows?.map(collaborator => collaborator.vea_id);

    // let veaFilter = { customer_id: req.data.customer_id, vendor_id: vendor_id, assessment_type: 'vea' }

    if (userType === 'DPO') {
      veaFilter = { ...veaFilter };
    } else if (userType === 'Department Head') {
      veaFilter = {
        ...veaFilter,
        [Op.or]: [{ assigned_to: req.data.userId }, { approver: req.data.userId }, { id: { [Op.in]: collaboratorVeaIds } }, { department_id: { [Op.in]: departmentIds } }]
      };
    } else {
      veaFilter = {
        ...veaFilter,
        [Op.or]: [{ assigned_to: req.data.userId }, { approver: req.data.userId }, { id: { [Op.in]: collaboratorVeaIds } }]
      };
    }

    const check = await commonService.findByCondition(VendorsMapping, { id: vendor_id }, ['vendor_id', 'customer_id']);
    if (!check) {
      return response.error(req, res, { msgCode: 'FETCHING_ERROR' }, httpStatus.NOT_FOUND);
    }
    //Authorisation check
    if (check.customer_id !== req.data.customer_id && check.vendor_id !== req.data.customer_id) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    } else {
      if (check.customer_id === req.data.customer_id) {
        if (userType !== 'DPO' && userType !== 'Department Head') {
          return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
        }
      }
    }

    //Fetching the status of Vendor Internal Assessment
    const viaStatus = await commonService.findByCondition(VendorAssessments, { vendor_id: vendor_id, assessment_type: 'via' }, ['status']);
    if (!viaStatus) {
      return response.error(req, res, { msgCode: 'STATUS_NOT_FETCHED' }, httpStatus.NOT_FOUND);
    } else if (viaStatus.status !== 'Completed') {
      return response.error(req, res, { msgCode: 'VIA_NOT_COMPLETED' }, httpStatus.NOT_FOUND);
    }
    const list = await vendorService.getListWithMultipleAssociates2(
      VendorAssessments,
      Departments,
      User,
      'AssignedTo',
      User,
      'Approver',
      User,
      'Owner',
      Group,
      {
        [Op.and]: [
          veaFilter,
          {
            [Op.or]: [sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('VendorAssessments.entity_id'), { [Op.ne]: null })]
          },
          is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
          searchCondition
        ]
      },
      {},
      {},
      {},
      {},
      {},
      ['id', 'vendor_id', 'assessment_type', 'assessment_name', 'risks', 'risk_score', 'progress', 'start_date', 'end_date', 'status'],
      ['id', 'name'],
      ['id', 'firstName', 'lastName'],
      ['id', 'firstName', 'lastName'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      limit,
      offset,
      order
    );
    if (!list) {
      return response.error(req, res, { msgCode: 'FETCHING_ERROR' }, httpStatus.NOT_FOUND);
    }

    list['rows'] = list?.rows.map(row => {
      return {
        id: row?.id,
        vendor_id: row?.vendor_id,
        Assessment: {
          key: row?.assessment_type,
          assessment_name: row?.assessment_name
        },
        risks: row?.risks,
        risk_score: row?.risk_score,
        progress: row?.progress,
        start_date: row?.start_date,
        end_date: row?.end_date,
        status: row?.status,
        Department: {
          id: row?.Department?.id,
          name: row?.Department?.name
        },
        AssignedTo: {
          id: row?.AssignedTo?.id,
          name: `${row?.AssignedTo?.firstName} ${row?.AssignedTo?.lastName}`
        },
        Approver: {
          id: row.Approver?.id,
          name: `${row?.Approver?.firstName} ${row?.Approver?.lastName}`
        },
        Owner: {
          id: row?.Owner?.id,
          name: `${row?.Owner?.firstName} ${row?.Owner?.lastName}`
        },
        Group: {
          id: row?.Group?.id,
          name: row?.Group?.name
        },
        isCollaborator: collaboratorVeaIds.includes(row.id)
      };
    });
    list.user_type = userType;

    if (list?.rows[0]?.AssignedTo) {
      if (list?.rows[0]?.AssignedTo?.id === req.data.userId) {
        list.rows[0].isAssigned = true;
      } else {
        list.rows[0].isAssigned = false;
      }
    }

    return response.success(req, res, { msgCode: 'VENDOR_LIST_FETCHED', data: list }, httpStatus.OK);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.vendorType = async (req, res) => {
  try {
    const { VendorType } = db.models;
    const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    const getList = await commonService.getList(VendorType, {}, {}, limit, offset, order);
    if (!getList) {
      return response.error(req, res, { msgCode: 'NO_DATA_FOUND' }, httpStatus.BAD_REQUEST);
    }

    return response.success(req, res, { msgCode: 'VENDOR_TYPE_LIST_FETCHED', data: getList }, httpStatus.OK);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.auditVRM = async (req, res) => {
  try {
    const { AuditLog, User } = db.models;

    const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    // const auditData = await commonService.getListAssociateWithCount(AuditLog, User , { type:'POLICY' , type_id: req.params.id }, {}, ['id', 'action','flag' ,'action_by_id', 'createdAt'],['firstName' , 'lastName'] , ['comment'] , limit, offset, order);
    auditData = await commonService.getList2(AuditLog, User, { type: { [Op.in]: ['VIA', 'VEA'] } }, {}, ['id', 'action', 'type', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);
    if (!auditData) {
      return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    // getting name initials and also check flag for comment and delete it if flag is false and added to the audit data
    auditData.rows?.forEach(row => {
      const name = row?.User?.firstName + ' ' + row?.User?.lastName;
      row.name = name;
      const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
      row.initials = initials;
      delete row.User;
    });

    return response.success(req, res, { msgCode: 'AUDIT_LOG_FETCHED', data: auditData }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

// VRM Dashboard APIs

exports.countByStageOrRisk = async (req, res) => {
  try {
    const { VendorDetail } = db.models;
    const key = req.query.key;
    let aggregateRisk = 0;
    // counting the assessments using GroupBy "RISKS"
    const vendorData = await commonService.getListGroupBy(VendorDetail, { customer_id: req.data.customer_id }, [`${key}`, [sequelize.fn('COUNT', 'id'), 'count']], [`${key}`]);
    if (!vendorData) {
      return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }
    let count = 0;
    vendorData.forEach(row => {
      count = count + parseInt(row.count);
    });
    let data = {};
    if (key === 'stage') {
      vendorData.push({ stage: 'TOTAL', count: String(count) });
      data = {
        vendorData: vendorData
      };
    } else {
      let count = 0;
      let totalCount = 0;
      let totalRisk = 0;

      const risk_score = await commonService.getList(VendorDetail, { customer_id: req.data.customer_id }, ['risk_score']);
      risk_score.rows.forEach(risk => {
        ++totalCount;
        if (risk.risk_score !== null) {
          ++count;
          totalRisk += risk.risk_score;
        }
      });
      aggregateRisk = totalRisk / count;
      // console.log("aggregateRisk", aggregateRisk);
      vendorData.push({ risk_tier: 'TOTAL', count: String(totalCount) });
      data = {
        aggregateRisk: aggregateRisk.toFixed(2),
        vendorData: vendorData
      };
    }

    return response.success(req, res, { msgCode: 'API_SUCCESS', data: data }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.countByDepartmentOrEntity = async (req, res) => {
  try {
    const { VendorDetail, Departments, Group } = db.models;
    const key = req.query.key + '_id';

    // Extract name of department or entity into an array
    let vendorDetails = {};
    let vendorData = {};
    if (key === 'department_id') {
      vendorData = await commonService.getListGroupBy(VendorDetail, { customer_id: req.data.customer_id }, [`${key}`, 'risk_tier', [sequelize.fn('COUNT', 'id'), 'count']], [`${key}`, 'risk_tier']);
      if (!vendorData) {
        return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
      }
      const ids = vendorData?.map(vendor => vendor.department_id);
      // Fetch Department details from Department model
      vendorDetails = await commonService.getListWithoutCount(Departments, { id: { [Op.in]: ids } }, ['name', 'id']);
      if (!vendorDetails) {
        return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
      }
      const transformedData = [];

      vendorData.forEach(vendor => {
        // Check if the department already exists in the transformedData
        const existingDepartment = transformedData.find(dep => dep.department_id === vendor.department_id);

        if (existingDepartment) {
          // If the department already exists, push the risk and update the count
          existingDepartment.risk.push({
            risk_tier: vendor.risk_tier,
            count: vendor.count
          });
          // Update the total count for the department
          existingDepartment.count += Number(vendor.count);
        } else {
          // If the department does not exist, create a new entry
          transformedData.push({
            department_id: vendor.department_id,
            risk: [
              {
                risk_tier: vendor.risk_tier,
                count: vendor.count
              }
            ],
            count: Number(vendor.count)
          });
        }
      });
      vendorData = transformedData;
      delete transformedData;
    } else {
      vendorData = await commonService.getListGroupBy(VendorDetail, { customer_id: req.data.customer_id }, [`${key}`, [sequelize.fn('COUNT', 'id'), 'count']], [`${key}`]);
      if (!vendorData) {
        return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
      }
      const ids = vendorData?.map(vendor => vendor.group_id);
      // Fetch Group details from Group model
      vendorDetails = await commonService.getListWithoutCount(Group, { id: { [Op.in]: ids } }, ['name', 'id']);
    }
    // Update assessment data with owner details
    const updatedVendorData = vendorData?.map(vendor => {
      const vendorDetail = vendorDetails?.find(detail => detail.id === vendor[key]);
      if (vendorDetail) {
        return {
          ...vendor,
          name: vendorDetail.name
        };
      } else {
        return { ...vendor }; // If no match found, keep the email as it is
      }
    });

    return response.success(req, res, { msgCode: 'API_SUCCESS', data: updatedVendorData }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.sendAlert = async (req, res) => {
  try {
    const { VendorsMapping, VendorDetail, VendorAssessments, Departments, Customer, User } = db.models;

    response.success(req, res, { msgCode: 'ALERT_SENT', data: 'Alerts are being sent in the background.' }, httpStatus.OK);

    // Asynchronous background processing
    for (const status of req.body.status) {
      if (status === 'CREATE' || status === 'INTERNAL_ASSESSMENT') {
        // Send alert mail to Created_by (vendor) and assign_to
        const data = await vendorService.getVendorsDetails(
          VendorsMapping,
          VendorDetail,
          User,
          Customer,
          { customer_id: req.data.customer_id },
          { stage: status },
          {},
          {},
          ['id', 'vendor_id', 'customer_id'],
          ['id', 'stage', 'risk_tier', 'risk_score', 'status', 'contact_mail', 'contact'],
          ['firstName', 'lastName', 'email'],
          ['name']
        );
        // console.log(data.rows[0]);

        for (const vendor of data.rows) {
          const subject = `Alert : Internal Assessment Pending for Vendor`;
          const textTemplate = 'vrm_alert_mail1.ejs';
          const sendData1 = {
            name: `${vendor?.VendorDetail?.Created?.firstName} ${vendor?.VendorDetail?.Created?.lastName}`,
            vendorName: `${vendor?.Vendor?.name}`
          };
          const sendData2 = {
            name: `${vendor?.VendorDetail?.Assign?.firstName} ${vendor?.VendorDetail?.Assign?.lastName}`,
            vendorName: `${vendor?.Vendor?.name}`
          };
          // Run mail sending in the background
          sendMail(vendor?.VendorDetail?.Created?.email, sendData1, subject, textTemplate).catch(error => {
            console.error('Failed to send mail:', error);
          });
          sendMail(vendor?.VendorDetail?.Assign?.email, sendData2, subject, textTemplate).catch(error => {
            console.error('Failed to send mail:', error);
          });
        }
      } else if (status === 'VENDOR_ASSESSMENT') {
        // Mail to vendor
        const list = await vendorService.getListWithMultipleAssociates(
          VendorAssessments,
          Departments,
          User,
          'AssignedTo',
          User,
          'Approver',
          { assessment_type: 'vea', customer_id: req.data.customer_id },
          {},
          {},
          {},
          ['id', 'vendor_id', 'assessment_type', 'assessment_name', 'status'],
          [],
          ['id', 'firstName', 'lastName', 'email'],
          ['id', 'firstName', 'lastName', 'email']
        );
        // console.log("list---->>>>", list.rows[0]);
        for (const vendor of list.rows) {
          const subject = `Alert : Vendor Assessment Delayed - Immediate Action Required`;
          const textTemplate = 'vrm_alert_mail2.ejs';
          const sendData = {
            name: `${vendor?.AssignedTo?.firstName} ${vendor?.AssignedTo?.lastName}`
          };

          // Run mail sending in the background
          sendMail(vendor?.AssignedTo?.email, sendData, subject, textTemplate).catch(error => {
            console.error('Failed to send mail:', error);
          });
        }
      } else {
        // Mail to department head and DPO
        const dpoEmail = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['email']);
        const dpoName = await commonService.findByCondition(User, { email: dpoEmail?.email }, ['firstName', 'lastName']);
        const list = await vendorService.getList(VendorsMapping, VendorDetail, Customer, 'Vendor', Departments, { customer_id: req.data.customer_id }, { stage: 'MITIGATION' }, {}, {}, [], ['stage'], ['name'], ['name']);
        console.log(list.rows[0]);
        for (const vendor of list.rows) {
          const subject = `Alert for Completion of Vendor details and its assessments`;
          const textTemplate = 'vrm_alert_mail3.ejs';
          const sendData = {
            name: `${dpoName?.firstName} ${dpoName?.lastName}`,
            vendorName: `${vendor?.VendorDetail?.Department?.name}`,
            deptName: `${vendor?.Vendor?.name}`
          };

          // Send mail to DPO
          sendMail(dpoEmail?.email, sendData, subject, textTemplate).catch(error => {
            console.error('Failed to send DPO mail:', error);
          });
        }

        // Send mail to department heads
        // const departments = await commonService.getList(VendorDetail, { customer_id: req.data.customer_id, stage: 'MITIGATION' }, ['department_id']);
        // const deptIds = departments.rows.map(dept => dept.department_id);
        // const deptEmails = await commonService.getList2(Departments, User, { id: { [Op.in]: deptIds } }, {}, [], ['firstName','lastName','email']);

        const data = await vendorService.getList2(
          VendorsMapping,
          VendorDetail,
          Customer,
          'Vendor',
          Departments,
          User,
          { customer_id: req.data.customer_id },
          { stage: 'MITIGATION' },
          {},
          {},
          {},
          ['id', 'status'],
          ['stage', 'risk_score'],
          ['name'],
          ['name'],
          ['firstName', 'lastName', 'email']
        );
        // console.log("------>>>",data.rows[0]);
        for (const row of data.rows) {
          const subject = `Alert for Completion of Vendor details and its assessments`;
          const textTemplate = 'vrm_alert_mail3.ejs';
          const sendData = {
            name: `${row?.VendorDetail?.Department?.User?.firstName} ${row?.VendorDetail?.Department?.User?.lastName}`,
            vendorName: `${row?.Vendor?.name}`,
            deptName: `${row?.VendorDetail?.Department?.name}`
          };
          sendMail(row?.VendorDetail?.Department?.User?.email, sendData, subject, textTemplate).catch(error => {
            console.error('Failed to send department mail:', error);
          });
        }
      }
    }
  } catch (err) {
    console.log('Error:', err);
    // response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
