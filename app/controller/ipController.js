// controllers/ipController.js
const axios = require('axios');
const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');

exports.getDetailedIpInfo = async (req, res) => {
  try {
    // Step 1: Get public IP
    // const ipRes = await axios.get('https://api64.ipify.org?format=json');
    // const ip = ipRes.data.ip;

    const ip = req.params.ip;
    // Step 2: Get geolocation info
    let geoRes = await axios.get(`https://api.db-ip.com/v2/${process.env.IP_INFO_KEY}/${ip}`);
    // console.log('-----------------', geoRes.data);

    if (geoRes.status !== 200) {
      geoRes = await axios.get(`https://ipwho.is/${ip}`);
      const d = geoRes.data;
      // console.log('===================', d);
      const normalized = {
        ipAddress: d.ip,
        continentCode: d.continent_code,
        continentName: d.continent,
        countryCode: d.country_code,
        countryName: d.country,
        city: d.city,
        stateProv: d.region,
        latitude: d.latitude,
        longitude: d.longitude,
        timeZone: d.timezone?.id || null,
        isp: d.connection?.isp || null,
        currencyCode: d.currency?.code || null,
        currencyName: d.currency?.name || null,
        isEuMember: d.is_eu,
        phonePrefix: d.calling_code ? `+${d.calling_code}` : null,
        languages: d.languages || []
      };

      // console.log('++++++++++++++++++++++++++', normalized);
      return response.success(
        req,
        res,
        {
          msgCode: 'IP_LOOKUP_SUCCESS',
          data: normalized
        },
        httpStatus.OK
      );
    }

    return response.success(
      req,
      res,
      {
        msgCode: 'IP_LOOKUP_SUCCESS',
        data: geoRes.data
      },
      httpStatus.OK
    );
  } catch (error) {
    console.error('❌ IP lookup error:', error.message);
    return response.error(req, res, { msgCode: 'SERVER_ERROR', message: 'Failed to fetch IP info', error: error.message }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
