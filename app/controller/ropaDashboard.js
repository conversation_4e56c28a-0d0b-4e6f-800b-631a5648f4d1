const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');


exports.getStatusCounts = async (req, res) => {
  try {
    const { ROPA, Group } = db.models;
    const attributes = [
      [sequelize.col('ROPA.group_id'), 'group_id'],
      [sequelize.col('ROPA.status'), 'status'],
      [sequelize.fn('COUNT', sequelize.col('ROPA.id')), 'count']
    ];
    const statusCounts = await commonService.getListAssociateWithGroup2(ROPA, Group, { customer_id: req.data.customer_id, group_id: { [Op.ne]: null } }, {}, attributes, ['id', 'name'], ['ROPA.group_id', 'ROPA.status', 'Group.id', 'Group.name']);
    const statusMapping = {
      'Yet to Start': 'yet_to_start',
      'Started': 'started',
      'Under Review': 'under_review',
      'Change Request': 'change_request',
      'Completed': 'completed'
    };

    const grouped = statusCounts.reduce((acc, { group_id, status, count, Group }) => {
      if (!acc[group_id]) {
        acc[group_id] = {
          group_id,
          group_name: Group.name,
          counts: {
            yet_to_start: 0,
            started: 0,
            under_review: 0,
            change_request: 0,
            completed: 0
          },
          total: 0
        };
      }
      
      // Use the mapped status name (without spaces) as the key
      const mappedStatus = statusMapping[status] || status.toLowerCase().replace(/\s+/g, '_');
      acc[group_id].counts[mappedStatus] = parseInt(count, 10);
      acc[group_id].total += parseInt(count, 10);
      return acc;
    }, {});

    const result = Object.values(grouped);

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: result }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.getProgressStatsByGroup = async (req, res) => {
    try {
      const { ROPA, Group } = db.models;
  
      const attributes = [
        [sequelize.col('ROPA.group_id'), 'group_id'],
        [sequelize.col('ROPA.status'), 'status'],
        [sequelize.fn('COUNT', sequelize.col('ROPA.id')), 'count']
      ];
  
      const statusCounts = await commonService.getListAssociateWithGroup2(
        ROPA,
        Group,
        {
          customer_id: req.data.customer_id,
          group_id: { [Op.ne]: null }
        },
        {},
        attributes,
        ['id', 'name'],
        ['ROPA.group_id', 'ROPA.status', 'Group.id', 'Group.name']
      );
  
      const filteredStatuses = ['Started', 'Under Review', 'Change Request'];
      const grouped = {};
      let overallTotal = 0;
  
      for (const item of statusCounts) {
        console.log(item)
        const { group_id, status, count, Group: group } = item;

        if (!filteredStatuses.includes(status)) continue;
  
        if (!grouped[group_id]) {
          grouped[group_id] = {
            group_id,
            group_name: group.name,
            count: 0
          };
        }
  
        grouped[group_id].count += parseInt(count, 10);
        overallTotal += parseInt(count, 10);
      }
  
      const result = Object.values(grouped);
  
      return response.success(req, res, {msgCode: 'DATA_FETCHED', data: {entities: result,total: overallTotal}}, httpStatus.OK);
  
    } catch (error) {
      console.log('error', error);
      return response.error(req, res, {msgCode: 'INTERNAL_SERVER_ERROR'}, httpStatus.INTERNAL_SERVER_ERROR);
    }
  };
  exports.getRopaStatusByDepartment = async (req, res) => {
    try {
      const { ROPA, Departments,Group } = db.models;
      let { group_id } = req.query;
      let departmentWhere,ropaWhere,groups;

      if (!group_id) {
        groups = await commonService.getList(Group, { customer_id: req.data.customer_id }, ['id']);
        if (!groups || groups.rows.length === 0) {
          return response.error(req, res, { msgCode: 'NO_GROUPS_FOUND' }, httpStatus.NOT_FOUND);
        }
        group_id = groups.rows.map(group => group.id);
      } else {
        group_id = [group_id]; // Wrap single group_id in an array for consistency
      }
  
      departmentWhere = {
        customer_id: req.data.customer_id,
        group_id: { [Op.in]: group_id }
      };
  
      ropaWhere = {
        customer_id: req.data.customer_id,
        department_id: { [Op.ne]: null },
        status: ['Started', 'Under Review'],
        group_id: { [Op.in]: group_id }
      };
      const allDepartments = await commonService.getListWithoutCount(Departments, departmentWhere, ['id', 'name']);
      // ROPA filter
     
      const attributes = [
        [sequelize.col('ROPA.department_id'), 'department_id'],
        [sequelize.col('ROPA.status'), 'status'],
        [sequelize.fn('COUNT', sequelize.col('ROPA.id')), 'count']
      ];

      const statusCounts = await commonService.getListAssociateWithGroup2(ROPA, Departments, ropaWhere, { group_id: group_id }, attributes, ['id', 'name'], ['ROPA.department_id', 'ROPA.status', 'Department.id', 'Department.name']);
      console.log(statusCounts);
      const statusTypes = ['Started', 'Under Review'];
      const grouped = {};
      let totalStarted = 0;
      let totalUnderReview = 0;

      // Initialize all departments with 0
      for (const dept of allDepartments) {
        grouped[dept.id] = {
          department_id: dept.id,
          department_name: dept.name,
          Started: 0,
          UnderReview: 0,
          total: 0
        };
      }

      // Fill actual counts
      for (const item of statusCounts) {
        const { department_id, status, count } = item;
        const parsedCount = parseInt(count, 10);

        if (!grouped[department_id]) continue;

        if (status === 'Started') {
          grouped[department_id].Started += parsedCount;
          totalStarted += parsedCount;
        } else if (status === 'Under Review') {
          grouped[department_id].UnderReview += parsedCount;
          totalUnderReview += parsedCount;
        }

        grouped[department_id].total += parsedCount;
      }

      const responseData = {
        departments: Object.values(grouped),
        total: {
          Started: totalStarted,
          UnderReview: totalUnderReview,
          overall: totalStarted + totalUnderReview
        }
      };

      return response.success(req, res, { msgCode: 'DATA_FETCHED', data: responseData }, httpStatus.OK);
    } catch (error) {
      console.error('Error fetching department ROPA stats:', error);
      return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
  };
  
  
  