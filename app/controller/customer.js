const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const customerService = require('../services/customer');
const { Op } = require('sequelize');
const authService = require('../services/auth');
const { USER_ROLE } = require('../constant/common');
const { getPagination } = require('../config/helper');

exports.getCustomerDetail = async (req, res) => {
  try {
    const { User, Customer, CustomerResources, Resources, Role, CustomerRegulations, GroupUser, IndustryVertical } = db.models;

    const getDPORoleId = await commonService.findByCondition(Role, { customer_id: req.params.id, role_name: { [Op.in]: [USER_ROLE[2], USER_ROLE[4]] } });
    if (!getDPORoleId) {
      return response.error(req, res, { msgCode: 'DPO_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    // get user Details
    // const userData = await customerService.getCustomerDetails(
    //   User,
    //   Customer,
    //   CustomerResources,
    //   Resources,
    //   { role_id: getDPORoleId.id },
    //   { id: req.params.id },
    //   {},
    //   { status: 1 },
    //   ['id', 'firstName', 'lastName', 'role_id', 'email', 'country_code', 'phone', 'status'],
    //   ['id', 'name', 'email', 'address', 'status'],
    //   ['customer_resource_id', 'resource_id', 'customer_id'],
    //   ['resource_id', 'resource_name', 'status']
    // );

    const userData = await customerService.getCustomerDetailsV2(
      User,
      Customer,
      CustomerResources,
      Resources,
      IndustryVertical,
      { role_id: getDPORoleId.id },
      { id: req.params.id },
      {},
      { status: 1 },
      {},
      ['id', 'firstName', 'lastName', 'role_id', 'email', 'country_code', 'phone', 'status'],
      ['id', 'name', 'email', 'address', 'status', 'industry_vertical'],
      ['customer_resource_id', 'resource_id', 'customer_id'],
      ['resource_id', 'resource_name', 'status'],
      ['id', 'name']
    );
    if (!userData) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    // userData[0].status = userData[0].Customer.status;
    // delete userData[0].Customer.status;

    const groupData = await commonService.findByCondition(GroupUser, { user_id: userData.id });
    if (groupData) {
      userData.groups = groupData;
    }

    const customRegulations = await commonService.findByCondition(CustomerRegulations, { customer_id: req.params.id }, {}, [['entity_id', 'ASC']]);
    // const regulation = await commonService.getList(RegulationsV2, { id: { [Op.in]: customRegulations?.regulation_ids } }, {});
    // Extract authoritative_source values
    // const authoritativeSources = regulation.rows.map(row => row.authoritative_source);
    // const data = {
    //     source: regulation?.rows[0]?.source,
    //     authoritative_source: authoritativeSources,
    // }
    // userData.regulations = {...data };
    userData.regulations = customRegulations;

    return response.success(req, res, { msgCode: 'USER_FETCHED', data: userData }, httpStatus.OK);
  } catch (error) {
    console.log('getCustomerDetail', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getCustomerList = async (req, res) => {
  try {
    const { User, Customer, Role } = db.models;
    let { page, size, search, sort_by = 'createdAt', sort_order = 'DESC', start_date, end_date } = req.query;

    let customerCondition = {};
    if (search) {
      customerCondition[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }];
    }
    // Add date range condition
    if (start_date && end_date) {
      start_date = new Date(start_date);
      start_date.setHours(0, 0, 0, 0);
      start_date = start_date.toISOString();

      end_date = new Date(end_date);
      end_date.setHours(23, 59, 32, 312);
      end_date = end_date.toISOString();

      customerCondition.createdAt = {
        [Op.between]: [start_date, end_date]
      };
    }
    if (!start_date && end_date) {
      end_date = new Date(end_date);
      end_date.setHours(23, 59, 32, 312);
      end_date = end_date.toISOString();

      customerCondition.createdAt = {
        [Op.lte]: end_date // Less than end date
      };
    }
    if (start_date && !end_date) {
      start_date = new Date(start_date);
      start_date.setHours(0, 0, 0, 0);
      start_date = start_date.toISOString();

      customerCondition.createdAt = {
        [Op.gte]: start_date // Greater than start date
      };
    }

    const { limit, offset } = getPagination(page, size);

    const order = [[sort_by, sort_order]];

    // get customer List
    const customerList = await commonService.getListWithInnerAssociate(
      Customer,
      User,
      Role,
      customerCondition,
      {},
      { role_name: { [Op.in]: [USER_ROLE[2], USER_ROLE[4]] } },
      ['id', 'name', 'email', 'address', 'status', 'createdAt', 'updatedAt'],
      ['firstName', 'lastName', 'firstLogin'],
      [],
      limit,
      offset,
      order
    );

    return response.success(req, res, { msgCode: 'CUSTOMER_FETCHED', data: customerList }, httpStatus.OK);
  } catch (error) {
    console.log('getCustomerList', error);
    response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.updateCustomer = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { User, Customer, CustomerResources, Role, UserRolePrivileges } = db.models;

    const checkUser = await customerService.getCustomerData(User, Customer, { id: req.body.user_id }, { id: req.params.id });
    if (!checkUser) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (!checkUser?.Customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // // check customer
    // const checkCustomer = await commonService.findByCondition(Customer, { id: req.params.id });
    // if (!checkCustomer) {
    //     return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    // // check User
    // const checkUser = await commonService.findByCondition(User, { id: req.body.user_id });
    // if (!checkUser) {
    //     return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    // }

    const userData = {};
    if (req.body.first_name) userData.firstName = req.body.first_name;
    if (req.body.last_name) userData.lastName = req.body.last_name;
    if (req.body.phone) userData.phone = req.body.phone;
    if (req.body.country_code) userData.country_code = req.body.country_code;

    const customerData = {};
    if (req.body.customer_name) customerData.name = req.body.customer_name;
    if (req.body.address) customerData.address = req.body.address;
    if (req.body.status) customerData.status = req.body.status;
    if (req.body.industry_vertical) customerData.industry_vertical = req.body.industry_vertical;

    // update User
    if (Object.keys(userData).length > 0) {
      const updateUser = await commonService.updateData(User, userData, { id: req.body.user_id }, dbTrans);
      if (!updateUser[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    // update organisation
    if (Object.keys(customerData).length > 0) {
      const updateOrganisation = await commonService.updateData(Customer, customerData, { id: req.params.id }, dbTrans);
      if (!updateOrganisation[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    if (req.body?.resource_list) {
      let orgResourceData = [],
        deleteResourceId = [],
        userRolePrivilegesData = [];

      // Get role
      const getRole = await commonService.getListWithoutCount(Role, { customer_id: req.params.id });
      const roleId = getRole?.map(items => {
        return items.id;
      });
      const DPO = getRole?.find(role => role.role_name == USER_ROLE[2] || role.role_name == USER_ROLE[4]);
      console.log(DPO);
      req.body?.resource_list?.forEach(element => {
        if (element.type == 'delete') {
          deleteResourceId.push(element.id);
        } else if (element.type == 'add') {
          orgResourceData.push({ customer_id: req.params.id, resource_id: element.id });

          userRolePrivilegesData.push({
            resource_id: element.id,
            role_id: DPO.id
          });
        }
      });

      // check if we have deleted id
      if (deleteResourceId.length > 0) {
        // delete Organisation resouces
        const deleteOrgResourcesData = {
          [Op.and]: {
            resource_id: {
              [Op.in]: deleteResourceId
            },
            customer_id: req.params.id
          }
        };
        // const deleteOrgResources = await commonService.deleteQuery(CustomerResources, deleteOrgResourcesData, dbTrans);
        await commonService.deleteQuery(CustomerResources, deleteOrgResourcesData, dbTrans);
        // if (!deleteOrgResources) {
        //     return response.error(req, res, { msgCode: 'ERROR_RESOURCE_DELETE' }, httpStatus.BAD_REQUEST, dbTrans);
        // }

        // delete User Role
        const deleteUserRoleData = {
          [Op.and]: {
            resource_id: {
              [Op.in]: deleteResourceId
            },
            role_id: {
              [Op.in]: roleId
            }
          }
        };
        await commonService.deleteQuery(UserRolePrivileges, deleteUserRoleData, dbTrans);
        //const deleteUserRole = await commonService.deleteQuery(UserRolePrivileges, deleteUserRoleData, dbTrans);
        // if (!deleteUserRole) {
        //     return response.error(req, res, { msgCode: 'ERROR_RESOURCE_DELETE' }, httpStatus.BAD_REQUEST, dbTrans);
        // }
      }

      if (orgResourceData.length > 0) {
        // insert organisation resouces
        const orgResource = await authService.BulkData(CustomerResources, orgResourceData, dbTrans);
        if (!orgResource) {
          return response.error(req, res, { msgCode: 'ERROR_RESOURCE_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // insert user role privileges
        const userRolePrivileges = await authService.BulkData(UserRolePrivileges, userRolePrivilegesData, dbTrans);
        if (!userRolePrivileges) {
          return response.error(req, res, { msgCode: 'ERROR_RESOURCE_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    return response.success(req, res, { msgCode: 'CUSTOMER_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.log('updateCustomer', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
