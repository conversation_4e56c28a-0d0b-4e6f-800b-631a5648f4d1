const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const policyService = require('../services/policy');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');


exports.addAction = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const{ Actions }=db.models;
        
        // const check = commonService.findByCondition(Actions,{customer_id: req.data.customer_id, title: req.body.title, duty_id: req.body.duty_id});
        // if(check){
        //     return response.error(req, res, { msgCode: 'ACTION_ALREADY_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        // }
        req.body.customer_id = req.data.customer_id;
        const addAction = await commonService.addDetail(Actions, req.body, dbTrans);
        if(!addAction){
            return response.error(req, res, { msgCode: 'ERROR_ADDING_ACTION' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req,res, {msgCode:'ACTION_ADDED',data:addAction}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("add action error",error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.listAction = async(req,res)=>{
    try{
        const{ Actions,User }=db.models;
        const { page, size, status, start_date, end_date, search, sort_by = 'createdAt', sort_order = 'DESC', data_breach_id } = req.query;

        const actionCondtion = {
            customer_id:req.data.customer_id,
            entity_id: req.query.entity_id
        }

        if (search) {
            actionCondtion[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
            ];
        }
        if (status) {
            actionCondtion.status = status
        }
        if (start_date && end_date) {
            actionCondtion = {assigned_date: { [Op.between]: [start_date, end_date] } }
        }

        if (start_date) {
            actionCondtion= {
                assigned_date: {
                    [Op.gte]: req.query.start_date
                }
            }
        }
        if (end_date) {
            actionCondtion = {
                assigned_date: {
                    [Op.lte]: req.query.end_date
                }
            }
        }

        if (data_breach_id) {
            actionCondtion.data_breach_id = data_breach_id
        }

        const { limit, offset } = getPagination(page, size);

        const order = [[sort_by, sort_order]];
        const listData =  await policyService.getListWithMultipleAssociates3(Actions,User,User,{},'AssignedTo','AssignedBy',actionCondtion,{},{},{},['id','firstName','lastName'],['id','firstName','lastName'],limit,offset,order);

        listData.rows?.map(item=>{
            item.AssignedBy.name = item?.AssignedBy?.firstName+" "+item?.AssignedBy?.lastName;
            item.AssignedBy.initial = item?.AssignedBy?.firstName.charAt(0).toUpperCase() +item?.AssignedBy?.lastName.charAt(0).toUpperCase()
            delete item.AssignedBy.firstName
            delete item.AssignedBy.lastName
            item.AssignedTo.name  = item?.AssignedTo?.firstName+" "+item?.AssignedTo?.lastName;
            item.AssignedTo.initial = item?.AssignedTo?.firstName.charAt(0).toUpperCase() +item?.AssignedTo?.lastName.charAt(0).toUpperCase()
            delete item.AssignedTo.firstName
            delete item.AssignedTo.lastName
            delete item.assigned_by
            delete item.assigned_to
            return item;
        });

        return response.success(req,res, {msgCode:'ACTION_LIST_FETCHED',data: listData}, httpStatus.OK);
    }
    catch(error){
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}


exports.detailAction = async(req,res)=>{
    try{
        const{ Actions }=db.models;

        const actionCondition = {
            id: req.params.id
        }

        const actionData = await commonService.findByCondition(Actions,actionCondition);

        if(!actionData){
            return response.error(req,res,{msgCode:'ACTION_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }

        return response.success(req,res, {msgCode:'ACTION_FETCHED',data: actionData}, httpStatus.OK);
        
    }
    catch(error){
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.countActions = async(req,res)=>{
    try{
        const {Actions}=db.models;
        const Data = await commonService.getListGroupBy(
            Actions, 
            { customer_id: req.data.customer_id, entity_id: req.query.entity_id }, 
            ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
            ['status']
        );

        let totalCount = 0;
        let activeCount = 0;
        let archiveCount = 0;
        

        Data.forEach(row => {
            const count = parseInt(row.count || 0);
            totalCount += count;

            switch(row.status) {
                case 'Open':
                    activeCount = count;
                    break;
                case 'Closed':
                    archiveCount = count;
                    break;
            }
        });

        const data = [
            { 
                actions: 'TOTAL', 
                count: String(totalCount)
            },
            { 
                actions: 'OPEN', 
                count: String(activeCount)
            },
            { 
                actions: 'CLOSED', 
                count: String(archiveCount)
            }
        ];
        return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);

    }
}
exports.uploadDocument= async(req,res)=>{
    const dbTrans = await db.transaction();
    try {

        const { Actions } = db.models;
  
        //adjusting the name of the file to store in database
        const bulkData = [];
        req.body?.uploadData.forEach(data => {
            bulkData.push({
                original_name: data.originalName,
                url: data.url
            })
        })

        return response.success(req, res, { msgCode: "DOCUMENT_UPLOADED", data: bulkData }, httpStatus.CREATED, dbTrans);


    } catch(err){
        console.log("error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}