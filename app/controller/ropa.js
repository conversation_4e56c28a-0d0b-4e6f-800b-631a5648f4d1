const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const ropaService = require('../services/ropa');
const constant = require('../constant/ROPA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');
const { question } = require('../constant/ROPA');
const csv = require('csv-parser');
const fs = require('fs');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createExcelFile, transformBasicInfo, exportRopa, transformDataForAllRopa } = require('../utils/helper');
const { IAM } = require('aws-sdk');
const { createClient } = require('@clickhouse/client');
const dayjs = require('dayjs');
const moment = require('moment');


//Get all Ropa by Department
exports.getAllRopasByDepartment = async (req, res) => {
  try {
    const { ROPA, Departments, User, Processes } = db.models;
    const customer_id = req.data.customer_id;
    
    // Get all departments for the customer
    const departments = await commonService.getList(
      Departments,
      { customer_id: customer_id },
      ['id', 'name', 'spoc_id']
    );
    
    // Prepare result structure
    const result = {
      departments: []
    };
    
    // For each department, get its ROPAs
    for (const department of departments.rows) {
      // Get department head/SPOC details
      const departmentHead = await commonService.findByCondition(
        User,
        { id: department.spoc_id },
        ['id', 'firstName', 'lastName', 'email']
      );
      
      // Get ROPAs directly associated with the department
      const departmentRopas = await commonService.getList(
        ROPA,
        {
          customer_id: customer_id,
          department_id: department.id,
          is_already_performed: true // Only get performed ROPAs
        },
        ['id', 'start_date', 'end_date', 'status', 'risks', 'progress', 'assigned_to', 'approver']
      );
      
      // Get processes associated with the department
      const processes = await commonService.getList(
        Processes,
        { department_id: department.id },
        ['id', 'name']
      );
      
      // Get ROPAs associated with the department's processes
      let processRopas = [];
      if (processes && processes.count > 0) {
        const processIds = processes.rows.map(process => process.id);
        
        const ropasForProcesses = await commonService.getList(
          ROPA,
          {
            customer_id: customer_id,
            process_id: { [Op.in]: processIds },
            is_already_performed: true // Only get performed ROPAs
          },
          ['id', 'start_date', 'end_date', 'status', 'risks', 'progress', 'assigned_to', 'approver', 'process_id']
        );
        
        if (ropasForProcesses && ropasForProcesses.count > 0) {
          // Map each ROPA to its process
          processRopas = await Promise.all(ropasForProcesses.rows.map(async (ropa) => {
            const process = await commonService.findByCondition(
              Processes,
              { id: ropa.process_id },
              ['id', 'name']
            );
            
            return {
              ...ropa.dataValues,
              process_name: process ? process.name : 'Unknown Process'
            };
          }));
        }
      }
    }
  }catch(err){
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
}


exports.updateRopa = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, RopaDocuments } = db.models;
    const checkRopa = await commonService.findByCondition(ROPA, { id: req.body.ropa_id }, {});
    if (!checkRopa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (checkRopa.is_already_performed === false) {
      return response.error(req, res, { msgCode: 'ROPA_ALREADY_STARTED' }, httpStatus.BAD_REQUEST);
    }
    if (req.body.is_already_performed === false) {
      const data = {
        is_already_performed: false,
        status: 'Yet to Start'
      };
      const updateRopa = await commonService.updateData(ROPA, data, { id: req.body.ropa_id }, dbTrans);
      if (!updateRopa[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    } else {
      const updateRopa = await commonService.updateData(ROPA, { status: 'Completed' }, { id: req.body.ropa_id }, dbTrans);

      if (!updateRopa[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
      const ropaDoc = await commonService.addDetail(RopaDocuments, req.body, dbTrans);
      if (!ropaDoc) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    return response.success(req, res, { msgCode: 'ROPA_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getROPAList = async (req, res) => {
  try {
    const { Departments, Processes, ROPA, User, Collaborator, Group, RopaDocuments } = db.models;
    const { page, size, search, search_key, is_assigned, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let userType = null;
    let customerFilter = { customer_id: req.data.customer_id };
    let userFilter = {};

    let searchCondition = {};

    if (search && !search_key) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('Department.name'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('Process.name'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('AssignedTo.firstName'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('AssignedTo.lastName'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('Approver.firstName'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('Approver.lastName'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('Department.User.firstName'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.col('Department.User.lastName'), {
            [Op.iLike]: `%${search}%`
          }),
          sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), {
            [Op.iLike]: `%${search}%`
          })
        ]
      };
    }

    if (search && search_key) {
      if (search_key === 'Department') {
        searchCondition = {
          [Op.or]: [
            sequelize.where(sequelize.col('Department.name'), {
              [Op.iLike]: `%${search}%`
            })
          ]
        };
      } else if (search_key === 'Process') {
        searchCondition = {
          [Op.or]: [
            sequelize.where(sequelize.col('Process.name'), {
              [Op.iLike]: `%${search}%`
            })
          ]
        };
      } else if (search_key === 'AssignedTo') {
        searchCondition = {
          [Op.or]: [
            sequelize.where(sequelize.col('AssignedTo.firstName'), {
              [Op.iLike]: `%${search}%`
            }),
            sequelize.where(sequelize.col('AssignedTo.lastName'), {
              [Op.iLike]: `%${search}%`
            })
          ]
        };
      } else if (search_key === 'Reviewer') {
        searchCondition = {
          [Op.or]: [
            sequelize.where(sequelize.col('Approver.firstName'), {
              [Op.iLike]: `%${search}%`
            }),
            sequelize.where(sequelize.col('Approver.lastName'), {
              [Op.iLike]: `%${search}%`
            })
          ]
        };
      } else if (search_key === 'SPOC') {
        searchCondition = {
          [Op.or]: [
            sequelize.where(sequelize.col('Department.User.firstName'), {
              [Op.iLike]: `%${search}%`
            }),
            sequelize.where(sequelize.col('Department.User.lastName'), {
              [Op.iLike]: `%${search}%`
            })
          ]
        };
      } else if (search_key === 'Status') {
        searchCondition = {
          status: sequelize.where(sequelize.cast(sequelize.col('ROPA.status'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      } else if (search_key === 'Risks') {
        searchCondition = {
          risks: sequelize.where(sequelize.cast(sequelize.col('ROPA.risks'), 'TEXT'), { [Op.iLike]: `%${search}%` })
        };
      }
    }

    if (search_key && search_key === 'Department') {
      searchCondition = {
        ...searchCondition,
        department_id: { [Op.ne]: null }
      };
    } else if (search_key && search_key === 'Process') {
      searchCondition = {
        ...searchCondition,
        process_id: { [Op.ne]: null }
      };
    }

    // Uncomment below lines to get all the ROPAs of the group and its child groups

    // // To get the group hierarchy (parent group with all its child groups)
    // const groups = await commonService.getGroupHierarchy(db, req.params.entity_id);
    // if (!groups) {
    //     return response.error(req, res, { msgCode: "GROUP_NOT_FOUND" }, httpStatus.NOT_FOUND);
    // }
    // const groupIds = groups.map(group => group.id);

    const groupIds = [req.params.entity_id];

    const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
    const departmentIds = departments?.rows?.map(department => department.id);
    const processes = await commonService.getList(Processes, { department_id: { [Op.in]: departmentIds } }, ['id']);
    const processIds = processes?.rows?.map(process => process.id);

    if (req.data.roleName === authConstant.USER_ROLE[2]) {
      userType = 'DPO';
    } else if (departmentIds.length > 0) {
      userType = 'Department Head';
    } else {
      userType = 'Employee';
    }

    const collaboratorRopa = await commonService.getList(Collaborator, { user_id: req.data.userId }, ['ropa_id']);
    const collaboratorRopaIds = collaboratorRopa?.rows?.map(collaborator => collaborator.ropa_id);

    let ropaFilter = {};

    if (userType === 'DPO') {
      ropaFilter = {};
    } else if (userType === 'Department Head') {
      ropaFilter = {
        [Op.or]: [{ assigned_to: req.data.userId }, { id: { [Op.in]: collaboratorRopaIds } }, { department_id: { [Op.in]: departmentIds } }, { process_id: { [Op.in]: processIds } }]
      };
    } else {
      ropaFilter = {
        [Op.or]: [{ assigned_to: req.data.userId }, { id: { [Op.in]: collaboratorRopaIds } }]
      };
    }

    const ropa = await ropaService.getEmployeeROPAWithDocs(
      ROPA,
      Departments,
      Processes,
      User,
      RopaDocuments,
      {
        [Op.and]: [
          ropaFilter,
          {
            [Op.or]: [
              sequelize.where(sequelize.col('Department.id'), {
                [Op.ne]: null
              }),
              sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
            ]
          },
          is_assigned && is_assigned === 'true' ? { assigned_to: req.data.userId } : {},
          searchCondition
        ]
      },
      { ...customerFilter, group_id: { [Op.in]: groupIds } },
      customerFilter,
      userFilter,
      {},
      ['id', 'start_date', 'end_date', 'tentative_completion_date', 'status', 'risks', 'progress', 'is_already_performed', 'createdAt', 'updatedAt'],
      ['id', 'name', 'group_id'],
      ['id', 'name'],
      ['id', 'firstName', 'lastName'],
      {},
      limit,
      offset,
      order
    );

    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.NOT_FOUND);
    }

    const ropaStr = {
      user_type: userType,
      rows: ropa.rows?.map(ropaItem => {
        if (ropaItem.Process) {
          if (ropaItem.Process.User == null) {
            ropaItem.Department = ropaItem?.Process?.Department;
            delete ropaItem.Process.Department;
            ropaItem.SPOC = { id: null, name: `-` };
            delete ropaItem.Process.User;
            delete ropaItem.Department.User;
          } else {
            ropaItem.Department = ropaItem?.Process?.Department;
            delete ropaItem.Process.Department;
            ropaItem.SPOC = {
              id: ropaItem?.Process?.User?.id,
              name: `${ropaItem?.Process?.User?.firstName} ${ropaItem?.Process?.User?.lastName}`
            };
            delete ropaItem.Process.User;
            delete ropaItem.Department.User;
          }
        } else {
          if (ropaItem.Department.User == null) {
            ropaItem.SPOC = { id: null, name: `-` };
            delete ropaItem.Department.User;
          } else {
            ropaItem.SPOC = {
              id: ropaItem?.Department?.User?.id,
              name: `${ropaItem?.Department?.User?.firstName} ${ropaItem?.Department?.User?.lastName}`
            };
            delete ropaItem.Department.User;
          }
        }

        if (ropaItem.AssignedTo) {
          if (ropaItem.AssignedTo.id === req.data.userId) {
            ropaItem.isAssigned = true;
          } else {
            ropaItem.isAssigned = false;
          }
          ropaItem.AssignedTo.name = `${ropaItem.AssignedTo.firstName} ${ropaItem.AssignedTo.lastName}`;
          delete ropaItem.AssignedTo.firstName;
          delete ropaItem.AssignedTo.lastName;
        }

        if (ropaItem.Approver) {
          ropaItem.Approver.name = `${ropaItem.Approver.firstName} ${ropaItem.Approver.lastName}`;
          delete ropaItem.Approver.firstName;
          delete ropaItem.Approver.lastName;
        }

        ropaItem.isCollaborator = collaboratorRopaIds.includes(ropaItem.id);

        ropaItem.group_id = ropaItem.Department.group_id;
        delete ropaItem.Department.group_id;

        return ropaItem;
      }),
      count: ropa.count
    };

    return response.success(req, res, { msgCode: 'ROPA_FETCHED', data: ropaStr }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getDepartmentsROPA = async (req, res) => {
  try {
    const { Departments, Processes, ROPA, User, Collaborator } = db.models;
    const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let ropaCondition = { [Op.not]: { department_id: null } };
    let empCheck = false;
    let roleBasedData = [];
    let filter = { customer_id: req.data.customer_id };

    if (search) {
      filter[Op.or] = [{ name: { [Op.iLike]: `%${search}%` } }];
    }

    // DPO can see all data, so no changes to ropaCondition
    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      // Fetch department details
      const departments = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);

      if (departments && departments.rows.length > 0) {
        // Department head can only see their own departments
        const departmentIds = departments?.rows?.map(department => department.id);
        ropaCondition.department_id = { [Op.in]: departmentIds };
      } else {
        // Employee can only see their own ROPA
        empCheck = true;
      }
    }

    if (empCheck) {
      const collaboratorRopa = await commonService.getList(Collaborator, { user_id: req.data.userId }, ['ropa_id']);
      const collaboratorRopaIds = collaboratorRopa?.rows?.map(collaborator => collaborator.ropa_id);
      const ropa = await ropaService.getEmployeeROPA(
        ROPA,
        Departments,
        Processes,
        User,
        {
          [Op.and]: [
            {
              [Op.or]: [{ assigned_to: req.data.userId }, { id: { [Op.in]: collaboratorRopaIds } }]
            },
            {
              [Op.or]: [
                sequelize.where(sequelize.col('Department.id'), {
                  [Op.ne]: null
                }),
                sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
              ]
            }
          ]
        },
        { ...filter, group_id: req.params.entity_id },
        filter,
        {},
        ['id', 'start_date', 'end_date', 'status', 'risks'],
        ['id', 'name'],
        ['id', 'name'],
        ['id', 'firstName', 'lastName'],
        limit,
        offset,
        order
      );
      if (!ropa) {
        return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.NOT_FOUND);
      }

      roleBasedData = ropa;
      roleBasedData.rows = roleBasedData.rows.map(ropaItem => {
        if (ropaItem.Process) {
          ropaItem.department_name = ropaItem.Process.Department.name;
          delete ropaItem.Process.Department;
        } else {
          ropaItem.department_name = ropaItem.Department.name;
        }

        return {
          ...ropaItem,
          isCollaborator: collaboratorRopaIds.includes(ropaItem.id)
        };
      });
    } else {
      const ropa = await ropaService.getDepartmentsROPA(
        ROPA,
        Departments,
        User,
        ropaCondition,
        { ...filter, group_id: req.params.entity_id },
        {},
        ['id', 'start_date', 'end_date', 'status', 'risks'],
        ['id', 'name'],
        ['id', 'firstName', 'lastName'],
        limit,
        offset,
        order
      );
      if (!ropa) {
        return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.NOT_FOUND);
      }
      roleBasedData = ropa;

      // Add assigned ROPAs
      const collaboratorRopa = await commonService.getList(Collaborator, { user_id: req.data.userId }, ['ropa_id']);
      const collaboratorRopaIds = collaboratorRopa?.rows?.map(collaborator => collaborator.ropa_id);
      const assignedRopa = await ropaService.getEmployeeROPA(
        ROPA,
        Departments,
        Processes,
        User,
        {
          [Op.and]: [
            {
              [Op.or]: [{ assigned_to: req.data.userId }, { id: { [Op.in]: collaboratorRopaIds } }]
            },
            {
              [Op.or]: [
                sequelize.where(sequelize.col('Department.id'), {
                  [Op.ne]: null
                }),
                sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
              ]
            }
          ],
          status: { [Op.ne]: constant.status.COMPLETED }
        },
        { ...filter, group_id: req.params.entity_id },
        filter,
        {},
        ['id', 'start_date', 'end_date', 'status', 'risks'],
        ['id', 'name'],
        ['id', 'name'],
        ['id', 'firstName', 'lastName'],
        limit,
        offset,
        order
      );
      if (assignedRopa) {
        roleBasedData.assigned = assignedRopa.rows.map(ropaItem => {
          if (ropaItem.Process) {
            ropaItem.department_name = ropaItem.Process.Department.name;
            delete ropaItem.Process.Department;
          } else {
            ropaItem.department_name = ropaItem.Department.name;
          }

          return {
            ...ropaItem,
            isCollaborator: collaboratorRopaIds.includes(ropaItem.id)
          };
        });
      }
    }
    return response.success(req, res, { msgCode: 'ROPA_FETCHED', data: roleBasedData }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getProcessesROPA = async (req, res) => {
  try {
    const { Processes, ROPA, User } = db.models;
    const department_id = req.params.department_id;
    const ropa = await ropaService.getProcessesROPA(
      ROPA,
      Processes,
      User,
      { [Op.not]: { process_id: null } },
      { customer_id: req.data.customer_id, department_id: department_id },
      {},
      ['id', 'start_date', 'end_date', 'status', 'risks'],
      ['id', 'name'],
      ['id', 'firstName', 'lastName'],
      [['id', 'ASC']]
    );
    // const ropa = await privacyService.getProcessesROPA(req.data.customer_id, department_id);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'ROPA_FETCHED', data: ropa }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.assignROPA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, Departments, Processes, AuditLog, User } = db.models;
    let ropaName = null;
    let approverId = null;
    let assignedToName = null;
    let dept_id = null;

    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.body.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkROPA.is_already_performed === true) {
      return response.error(req, res, { msgCode: 'ROPA_CANT_ASSIGN' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkROPA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ROPA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ROPA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (checkROPA?.Department) {
      approverId = checkROPA?.Department?.spoc_id;
      ropaName = checkROPA?.Department?.name;
      dept_id = checkROPA?.Department?.id;
    } else if (checkROPA?.Process) {
      approverId = checkROPA?.Process?.Department?.spoc_id;
      ropaName = checkROPA?.Process?.name;
      dept_id = checkROPA?.Process?.Department.id;
    }
    
    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    assignedToName = `${user.firstName} ${user.lastName}`;

    const assigner = await commonService.findByCondition(
      User,
      {
        id: req.data.userId
      },
      ['firstName', 'lastName']
    );
    if (!assigner) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const ropa = await commonService.updateData(ROPA, { assigned_to: req.body.user_id, approver: approverId }, { id: req.body.ropa_id }, dbTrans);
    if (!ropa[1]) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const currentDate = moment().tz('Asia/Kolkata');
    const completionDate = moment(checkROPA?.tentative_completion_date);
    const daysUntilCompletion = completionDate.diff(currentDate, 'days');

    const subject = `New RoPA Assessment Assigned – Action Required in GoTrust Platform`;
    const textTemplate = 'ropa_assigned.ejs';
    const baseUrl = req.protocol + '://' + req.get('host');
    const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'http://localhost:9000';
    const sendData = {
      assignee: `${user.firstName} ${user.lastName}`,
      ropaName: ropaName,
      assigner: `${assigner.firstName} ${assigner.lastName}`,
      //url: `${process.env.SERVER_IP}/privacy/ropa/`,
      url: `${frontEndUrl}/data-mapping/ropa/`,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`,
      daysLeft: daysUntilCompletion
    };

    sendMail(user.email, sendData, subject, textTemplate);

    const auditAction = `Assigned ${ropaName} ROPA to ${assignedToName}`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ROPA',
        type_id: req.body.ropa_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ROPA_ASSIGNED', data: ropa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.assignReviewerROPA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, Departments, Processes, AuditLog, User } = db.models;
    let ropaName = null;
    let reviewerToName = null;
    let dept_id = null;

    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.body.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }


    if (checkROPA?.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ROPA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA?.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ROPA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (checkROPA?.Department) {
      ropaName = checkROPA?.Department?.name;
      dept_id = checkROPA?.Department?.id;
    } else if (checkROPA?.Process) {
      ropaName = checkROPA?.Process?.name;
      dept_id = checkROPA?.Process?.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const assigner = await commonService.findByCondition(
      User,
      {
        id: req.data.userId
      },
      ['firstName', 'lastName']
    );
    if (!assigner) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    reviewerToName = `${user?.firstName} ${user?.lastName}`;
    const ropa = await commonService.updateData(ROPA, { approver: req.body.user_id}, { id: req.body.ropa_id }, dbTrans);
    if (!ropa[1]) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const subject = `You have been assigned as a reviewer ${ropaName} RoPA: We Need Your Input!`;
    const textTemplate = 'ropa_reviewer.ejs';
    const baseUrl = req.protocol + '://' + req.get('host');
    const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
    const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'http://localhost:9000';
    const sendData = {
      assignee: `${user.firstName} ${user.lastName}`,
      ropaName: ropaName,
      assigner: `${assigner.firstName} ${assigner.lastName}`,
      //url: `${process.env.SERVER_IP}/privacy/ropa/`,
      url: `${frontEndUrl}/data-mapping/ropa/`,
      logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
      email_logo_url: `${backEndUrl}/app/public/email_log.png`,
    };

    sendMail(user.email, sendData, subject, textTemplate);

    const auditAction = `Reviewer Assigned ${ropaName} ROPA to ${reviewerToName}`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ROPA',
        type_id: req.body.ropa_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ROPA_ASSIGNED', data: ropa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.startROPA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, Controls, CustomerControls, Collaborator, AuditLog, Departments, Processes, User, BasicInfoAnswers } = db.models;

    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.params.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkROPA.is_already_performed === true) {
      return response.error(req, res, { msgCode: 'ROPA_CANT_START' }, httpStatus.NOT_FOUND, dbTrans);
    }
    let basicInfo = false;
    const checkBasicInfo = await commonService.getList(BasicInfoAnswers, { ropa_id: req.params.ropa_id }, ['question_id', 'answer']);
    if (!checkBasicInfo) {
      return response.error(req, res, { msgCode: 'QUESTIONS_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (checkBasicInfo.count >= 3) {
      basicInfo = true;
    }

    if (checkROPA.status === constant.status.STARTED || checkROPA.status === constant.status.CHANGES_REQUESTED) {
      if (checkROPA.assigned_to && checkROPA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        const collaborator = await commonService.findByCondition(Collaborator, { ropa_id: req.params.ropa_id, user_id: req.data.userId }, ['id']);
        if (!collaborator) {
          return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }
      }
      return response.success(req, res, { msgCode: 'ROPA_STARTED', data: { basicInfo: basicInfo } }, httpStatus.OK, dbTrans);
    } else if (checkROPA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ROPA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ROPA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let ropaName = null;
    let dept_id = null;
    let approverId = null;

    if (checkROPA.Department) {
      ropaName = checkROPA.Department.name;
      dept_id = checkROPA.Department.id;
      approverId = checkROPA.Department.spoc_id;
    } else if (checkROPA.Process) {
      ropaName = checkROPA.Process.name;
      dept_id = checkROPA.Process.Department.id;
      approverId = checkROPA.Process.Department.spoc_id;
    }

    if (checkROPA.assigned_to && checkROPA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(Collaborator, { ropa_id: req.params.ropa_id, user_id: req.data.userId }, ['id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }
    }

    const ropa = await commonService.updateData(
      ROPA,
      {
        status: constant.status.STARTED,
        start_date: Date(),
        assigned_to: req.data.userId,
        approver: approverId
      },
      { id: req.params.ropa_id },
      dbTrans
    );
    if (!ropa[1]) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let controls = await commonService.getList(Controls, { industry_vertical_id: 1, customer_id: req.data.customer_id, template_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    if (controls.count == 0) {
      controls = await commonService.getList(Controls, { industry_vertical_id: 1, customer_id: null, template_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
    }

    console.log('www', controls);

    const parentControls = controls.rows?.filter(control => control.parent_id === null);
    const childControls = controls.rows?.filter(control => control.parent_id !== null);

    const customerControlsParents = parentControls?.map(control => {
      return {
        question_id: control.id,
        customer_id: control.customer_id,
        category_id: control.category_id,
        ropa_id: Number(req.params.ropa_id),
        is_custom: false
      };
    });

    const newCustomerControlsParents = await commonService.bulkAdd(CustomerControls, customerControlsParents, dbTrans);
    if (!newCustomerControlsParents) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
      map[parentControls[index].id] = control.id;
      return map;
    }, {});

    const customerControlsChildren = childControls?.map(control => {
      return {
        question_id: control.id,
        category_id: control.category_id,
        customer_id: control.customer_id,
        ropa_id: req.params.ropa_id,
        parent_id: parentIdMap[control.parent_id],
        is_custom: false
      };
    });

    const newCustomerControlsChildren = await commonService.bulkAdd(CustomerControls, customerControlsChildren, dbTrans);
    if (!newCustomerControlsChildren) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const auditAction = `${user?.firstName} ${user?.lastName} started ${ropaName} ROPA`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ROPA',
        type_id: req.params.ropa_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    ropa[1].basicInfo = basicInfo;
    return response.success(req, res, { msgCode: 'ROPA_STARTED', data: ropa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.restartROPA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, AuditLog, Departments, Processes, User } = db.models;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.params.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // if (checkROPA.is_already_performed === true) {
    //   return response.error(req, res, { msgCode: 'ROPA_CANT_START' }, httpStatus.NOT_FOUND, dbTrans);
    // }

    if (checkROPA.status === constant.status.YET_TO_START) {
      return response.success(req, res, { msgCode: 'ROPA_YET_TO_START' }, httpStatus.OK, dbTrans);
    } else if (checkROPA.status === constant.status.STARTED) {
      return response.success(req, res, { msgCode: 'ROPA_STARTED' }, httpStatus.OK, dbTrans);
    } else if (checkROPA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ROPA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA.status === constant.status.CHANGES_REQUESTED) {
      return response.error(req, res, { msgCode: 'ROPA_CHANGES_REQUESTED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let ropaName = null;
    let dept_id = null;
    let approverId = null;

    if (checkROPA.Department) {
      ropaName = checkROPA.Department.name;
      dept_id = checkROPA.Department.id;
      approverId = checkROPA.Department.spoc_id;
    } else if (checkROPA.Process) {
      ropaName = checkROPA.Process.name;
      dept_id = checkROPA.Process.Department.id;
      approverId = checkROPA.Process.Department.spoc_id;
    }

    const ropa = await commonService.updateData(
      ROPA,
      {
        status: constant.status.UNDER_REVIEW,
        start_date: Date(),
        assigned_to: req.data.userId,
        approver: approverId
      },
      { id: req.params.ropa_id },
      dbTrans
    );
    if (!ropa[1]) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const auditAction = `${user?.firstName} ${user?.lastName} restarted ${ropaName} ROPA`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ROPA',
        type_id: req.params.ropa_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ROPA_STARTED', data: ropa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getBasicInfo = async (req, res) => {
  try {
    const { BasicInfoQuestions, BasicInfoAnswers, ROPA, Departments, Processes, User, Group } = db.models;
    const ropa_level = req.params.ropa_level;
    const ropa_id = req.params.ropa_id;
    let answerData = {};
    let extraProcQues = false;

    if (ropa_level === 'department') {
      const deptBasicInfo = await ropaService.getDepartmentBasicInfo(ROPA, Departments, User, Group, Processes, { id: ropa_id }, {}, {}, {}, {}, [], ['name'], ['firstName', 'lastName'], ['name'], ['name']);
      const procBasicInfo = await ropaService.getProcessBasicInfo(ROPA, Processes, Departments, User, Group, { id: ropa_id }, {}, {}, {}, {}, [], ['name'], ['name'], ['firstName', 'lastName'], ['name']);

      if (deptBasicInfo) {
        answerData = {
          [`${constant.basicInfoDept.DEPARTMENT_NAME}`]: deptBasicInfo?.Department?.name,
          [`${constant.basicInfoDept.ENTITY_NAME}`]: deptBasicInfo?.Department?.Group?.name,
          [`${constant.basicInfoDept.DEPARTMENT_HEAD}`]: `${deptBasicInfo?.Department?.User?.firstName} ${deptBasicInfo?.Department?.User?.lastName}`,
          [`${constant.basicInfoDept.SPOC}`]: `${deptBasicInfo?.Department?.User?.firstName} ${deptBasicInfo?.Department?.User?.lastName}`,
          [`${constant.basicInfoDept.SUBFUNCTION}`]: deptBasicInfo?.Department?.Processes?.map(process => process.name).join(', ')
        };
      } else if (procBasicInfo) {
        extraProcQues = true;
        answerData = {
          [`${constant.basicInfoDept.DEPARTMENT_NAME}`]: procBasicInfo?.Process?.Department?.name,
          [`${constant.basicInfoDept.ENTITY_NAME}`]: procBasicInfo?.Process?.Department?.Group.name,
          [`${constant.basicInfoDept.DEPARTMENT_HEAD}`]: `${procBasicInfo?.Process?.Department?.User.firstName} ${procBasicInfo?.Process?.Department?.User.lastName}`,
          [`${constant.basicInfoDept.SPOC}`]: `${procBasicInfo?.Process?.Department?.User.firstName} ${procBasicInfo?.Process?.Department?.User.lastName}`,
          [`${constant.basicInfoDept.SUBFUNCTION}`]: procBasicInfo?.Process?.children?.map(process => process.name).join(', '),
          [`${constant.basicInfoProc.PROCESS_NAME}`]: procBasicInfo?.Process?.name
        };
      }
      // } else if (ropa_level === 'process') {
      //     const procBasicInfo = await ropaService.getProcessBasicInfo(ROPA, Processes, Departments, User, { id: ropa_id }, {}, {}, {}, [], ['name'], ['name'], ['firstName', 'lastName']);
      //     answerData = {
      //         [`${constant.basicInfoProc.DEPARTMENT_NAME}`]: procBasicInfo?.Process.Department.name,
      //         [`${constant.basicInfoProc.DEPARTMENT_HEAD}`]: `${procBasicInfo?.Process.Department.User.firstName} ${procBasicInfo?.Process.Department.User.lastName}`,
      //         [`${constant.basicInfoProc.SPOC}`]: `${procBasicInfo?.Process.User.firstName} ${procBasicInfo?.Process.User.lastName}`,
      //         [`${constant.basicInfoProc.SUBPROCESS}`]: procBasicInfo?.Process.children?.map(process => process.name).join(', ')
      //     }
      // }
    } else {
      return response.error(req, res, { msgCode: 'INVALID_ROPA_LEVEL' }, httpStatus.BAD_REQUEST);
    }
    // console.log(answerData);

    let basicInfoQuestions = await commonService.getListAssociateWithoutCount(BasicInfoQuestions, BasicInfoAnswers, {}, { ropa_id: ropa_id }, ['id', 'question', 'fields', 'artifact_type', 'ropa_level'], ['answer', 'answered_by'], null, null, [
      ['id', 'ASC']
    ]);
    // console.log("basicInfoQuestions", basicInfoQuestions)
    if (!basicInfoQuestions) {
      return response.error(req, res, { msgCode: 'QUESTIONS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    basicInfoQuestions?.forEach(question => {
      question.editable = false;
      switch (question.id) {
        case constant.basicInfoDept.DEPARTMENT_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.DEPARTMENT_NAME}`]];
          break;
        case constant.basicInfoProc.PROCESS_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.PROCESS_NAME}`]];
          break;
        case constant.basicInfoDept.ENTITY_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.ENTITY_NAME}`]];
          break;
        case constant.basicInfoDept.DEPARTMENT_HEAD:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.DEPARTMENT_HEAD}`]];
          break;
        case constant.basicInfoDept.SPOC:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.SPOC}`]];
          break;
        // case constant.basicInfoDept.SUBFUNCTION:
        //     question.BasicInfoAnswer = {};
        //     question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.SUBFUNCTION}`]];
        //     break;
        // case constant.basicInfoDept.IS_ALREADY_PERFORMED:
        //     question.BasicInfoAnswer = {};
        //     question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.IS_ALREADY_PERFORMED}`]];
        //     break;
        case constant.basicInfoProc.DEPARTMENT_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.DEPARTMENT_NAME}`]];
          break;
        case constant.basicInfoProc.DEPARTMENT_HEAD:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.DEPARTMENT_HEAD}`]];
          break;
        case constant.basicInfoProc.SPOC:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.SPOC}`]];
          break;
        case constant.basicInfoProc.SUBPROCESS:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.SUBPROCESS}`]];
          break;
        // case constant.basicInfoProc.IS_ALREADY_PERFORMED:
        //     question.BasicInfoAnswer = {};
        //     question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.IS_ALREADY_PERFORMED}`]];
        //     break;
        default:
          question.editable = true;
      }
    });

    // console.log("basicInfoQuestions", basicInfoQuestions)

    if (!extraProcQues) {
      basicInfoQuestions = basicInfoQuestions.filter(question => question.ropa_level === 'department');
    }

    return response.success(req, res, { msgCode: 'QUESTIONS_FETCHED', data: basicInfoQuestions }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.answerBasicInfo = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { BasicInfoAnswers, ROPA } = db.models;
    const answers = req.body.answers;

    // Separate answers into two arrays based on the type
    const addAnswers = answers?.filter(answer => answer.type === 'add');
    const updateAnswers = answers?.filter(answer => answer.type === 'update');
    // Add 'answered_by' field to all answers
    addAnswers?.forEach(answer => (answer.answered_by = req.data.userId));
    updateAnswers?.forEach(answer => (answer.answered_by = req.data.userId));

    // Bulk add or update answers
    if (addAnswers.length > 0) {
      const addNewAnswers = await commonService.bulkAdd(BasicInfoAnswers, addAnswers, dbTrans);
      if (!addNewAnswers) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    // if (updateAnswers.length > 0) {
    //     for (let answer of updateAnswers) {
    //         const updateAnswers = await commonService.updateData(BasicInfoAnswers, answer, { question_id: answer.question_id, ropa_id: answer.ropa_id }, dbTrans);
    //         if (!updateAnswers[1]) {
    //             return response.error(req, res, { msgCode: "ERROR_UPDATING_ANSWER" }, httpStatus.BAD_REQUEST, dbTrans);
    //         }
    //     }
    // }

    delete req.body.answers;
    if (req.body.is_already_performed === true) {
      req.body.status = constant.status.COMPLETED;
    }
    const updateRopa = await commonService.updateData(ROPA, req.body, { id: req.params.ropa_id }, dbTrans);
    if (!updateRopa[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ANSWER_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getProgress = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerControls, Answers, ROPA, ReviewROPA, Collaborator } = db.models;
    const ropa_id = req.params.ropa_id;

    const ropa = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status', 'approver', 'assigned_to']);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const status = ropa.status;
    let controls = null;
    let totalControls = 0;
    let answeredControls = 0;
    let childControls = [];
    let progress = 0;

    if (ropa.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && ropa.approver !== req.data.userId) {
      const collaborator = await commonService.getList(Collaborator, { ropa_id: ropa_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }

      const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

      controls = await commonService.getListAssociateWithoutCount(CustomerControls, Answers, { ropa_id: ropa_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.Answer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.Answer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.Answer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });

      progress = (answeredControls / totalControls) * 100;
      progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

      return response.success(
        req,
        res,
        {
          msgCode: 'PROGRESS_FETCHED',
          data: { totalControls, answeredControls, progress }
        },
        httpStatus.OK,
        dbTrans
      );
    }

    if (status === constant.status.STARTED) {
      controls = await commonService.getListAssociateWithoutCount(CustomerControls, Answers, { ropa_id: ropa_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.Answer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.Answer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.Answer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.UNDER_REVIEW) {
      controls = await commonService.getListAssociateWithoutCount(CustomerControls, ReviewROPA, { ropa_id: ropa_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls.filter(control => control.parent_id === null).length;
      // answeredControls = controls.filter(control => control.ReviewROPA).length;
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
        }
        if (control.ReviewROPA) {
          answeredControls++;
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.CHANGES_REQUESTED) {
      controls = await ropaService.getControlsWithAnswersAndReviews(CustomerControls, Answers, ReviewROPA, { ropa_id: ropa_id }, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      const parentControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewROPA && control.ReviewROPA.accurate_information === 0);
      // totalControls = parentControls.length;
      // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.Answer?.updatedAt > control.ReviewROPA?.updatedAt && control.ReviewROPA?.accurate_information === 0).length;
      // childControls = controls.rows?.filter(control => control.parent_id !== null);
      controls.rows?.forEach(control => {
        if (control.parent_id === null) {
          if (control.ReviewROPA && control.ReviewROPA.accurate_information === 0) {
            totalControls++;
          }
          if (control.Answer?.updatedAt > control.ReviewROPA?.updatedAt && control.ReviewROPA.accurate_information === 0) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});
      Object.entries(childControlsByParent)?.forEach(([parentId, childControls]) => {
        const parentControl = parentControls?.find(control => control.id == parentId);
        if (parentControl && childControls.every(control => control.Answer.updatedAt > parentControl.ReviewROPA.updatedAt)) {
          answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewROPA
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.COMPLETED) {
      controls = await commonService.getListAssociateWithoutCount(CustomerControls, Answers, { ropa_id: ropa_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.Answer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.Answer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.Answer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    }

    progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

    const updateProgress = await commonService.updateData(ROPA, { progress: progress }, { id: ropa_id }, dbTrans);
    if (!updateProgress) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(
      req,
      res,
      {
        msgCode: 'PROGRESS_FETCHED',
        data: { totalControls, answeredControls, progress }
      },
      httpStatus.OK,
      dbTrans
    );
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getCategories = async (req, res) => {
  try {
    const { Category, Collaborator, ROPA, CustomerControls, ReviewROPA } = db.models;

    let ropaLevel = req.params.ropa_level;
    const ropa_id = req.query.ropa_id;
    ropaLevel = ropaLevel.charAt(0).toUpperCase() + ropaLevel.slice(1);

    let categoryCondition = {};

    const ropa = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status', 'assigned_to', 'approver', 'is_already_performed', 'template_id']);

    // console.log('ropa---------->>>>>', ropa);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (ropa.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && ropa.approver !== req.data.userId) {
      const collaborators = await commonService.getList(Collaborator, { ropa_id: ropa_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborators) {
        return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED);
      }
      categoryCondition.id = {
        [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id)
      };
    }

    if (ropa.status === constant.status.CHANGES_REQUESTED) {
      const changeReqCategories = await commonService.getListAssociate(CustomerControls, ReviewROPA, { ropa_id: ropa_id }, { accurate_information: 0 }, ['category_id']);
      if (!changeReqCategories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
      categoryCondition.id = {
        [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id)
      };
    } else {
      categoryCondition.customer_id = req.data.customer_id;
    }

    if (ropa.is_already_performed === true) {
      categoryCondition.template_id = ropa.template_id;
    } else {
      categoryCondition.template_id = null;
    }

    let categories = await commonService.getList(Category, categoryCondition, ['id', 'name']);
    if (categories.count == 0) {
      categoryCondition.customer_id = null;
      categories = await commonService.getList(Category, categoryCondition, ['id', 'name']);
      if (!categories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }

    return response.success(req, res, { msgCode: 'CATEGORIES_FETCHED', data: categories }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getControls = async (req, res) => {
  try {
    const { Controls, CustomerControls, Answers, User, ROPA, Collaborator, ReviewROPA } = db.models;
    const ropa_id = req.params.ropa_id;
    const category_id = req.query.category_id;

    const ropa = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status', 'assigned_to', 'approver', 'is_already_performed', 'template_id']);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (ropa.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_STARTED' }, httpStatus.BAD_REQUEST);
    }

    if (ropa.assigned_to !== req.data.userId && ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(
        Collaborator,
        {
          ropa_id: ropa_id,
          user_id: req.data.userId,
          category_id: category_id
        },
        ['id']
      );
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'ROPA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED);
      }
    }

    let controls;
    const controlsAttributes = [
      [sequelize.literal(`"CustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."title" ELSE "Control"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."description" ELSE "Control"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."artifact_type" AS TEXT) ELSE CAST("Control"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."is_attachment" ELSE "Control"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."question" ELSE "Control"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."fields" ELSE "Control"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input" ELSE "Control"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."extra_input_type" AS TEXT) ELSE CAST("Control"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input_fields" ELSE "Control"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."question_id" IS NOT NULL THEN "Control"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    if (ropa.status === constant.status.UNDER_REVIEW || ropa.status === constant.status.CHANGES_REQUESTED || ropa.status === constant.status.COMPLETED) {
      if (ropa.status === constant.status.UNDER_REVIEW && ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }

      console.log('ROPA STATUS', ropa.status);
      controls = await ropaService.getControlsWithReview(
        CustomerControls,
        Controls,
        Answers,
        User,
        ReviewROPA,
        { ropa_id: ropa_id, category_id: category_id },
        {},
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'comments'],
        [['question_id', 'ASC']]
      );
    } else {
      controls = await ropaService.getControls(
        CustomerControls,
        Controls,
        Answers,
        User,
        { ropa_id: ropa_id, category_id: category_id },
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        [['question_id', 'ASC']]
      );
    }

    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    for (let control of controls) {
      if (control.Answer) {
        control.answered = true;
        if (control.Answer.extra_answer) {
          control.Answer.extra_answered = true;
        } else {
          control.Answer.extra_answered = false;
        }
      } else {
        control.answered = false;
      }

      if (control.ReviewROPA) {
        control.reviewed = true;
      } else {
        control.reviewed = false;
      }

      if (ropa.assigned_to !== req.data.userId && ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        control.is_collaborator = true;
      } else {
        control.is_collaborator = false;
      }
    }

    let parents = controls?.filter(control => control.parent_id === null);
    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    if (ropa.status === constant.status.CHANGES_REQUESTED) {
      parents = parents.filter(parent => parent.ReviewROPA?.accurate_information === 0);
    }

    return response.success(
      req,
      res,
      {
        msgCode: 'CONTROLS_FETCHED',
        data: { status: ropa.status, controls: parents }
      },
      httpStatus.OK
    );
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getArtifactTypes = async (req, res) => {
  try {
    const { Controls } = db.models;
    const artifactTypes = Controls.rawAttributes.artifact_type.values;
    if (!artifactTypes) {
      return response.error(req, res, { msgCode: 'ARTIFACT_TYPES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'ARTIFACT_TYPES_FETCHED', data: artifactTypes }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerControls } = db.models;
    req.body.is_custom = true;

    const addedControls = await commonService.addDetail(CustomerControls, req.body, dbTrans);
    if (!addedControls) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_CREATED', data: addedControls }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Controls, CustomerControls } = db.models;
    const { title, description, artifact_type, question, fields, is_attachment } = req.body;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    let raw_question = null;
    const originalQuestion = await commonService.getDataAssociate(Controls, CustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    if (originalQuestion) {
      raw_question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(CustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      raw_question = customQuestion;
    }

    const updatedValues = {
      title: title || raw_question.title,
      description: description || raw_question.description,
      artifact_type: artifact_type || raw_question.artifact_type,
      is_attachment: is_attachment || raw_question.is_attachment,
      question: question || raw_question.question,
      fields: fields || raw_question.fields,
      extra_input: raw_question.extra_input,
      extra_input_type: raw_question.extra_input_type,
      extra_input_fields: raw_question.extra_input_fields,
      is_custom: true
    };

    const updatedControls = await commonService.updateData(CustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateFields = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Controls, CustomerControls } = db.models;
    const { fields } = req.body;
    let question = null;

    const originalQuestion = await commonService.getDataAssociate(Controls, CustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    if (originalQuestion) {
      question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(CustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      question = customQuestion;
    }

    const updatedValues = {
      title: question.title,
      description: question.description,
      artifact_type: question.artifact_type,
      is_attachment: question.is_attachment,
      question: question.question,
      fields: fields || question.fields,
      is_custom: true,
      extra_input: question.extra_input,
      extra_input_type: question.extra_input_type,
      extra_input_fields: question.extra_input_fields
    };

    const updatedControls = await commonService.updateData(CustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.deleteCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerControls } = db.models;

    const deletedControls = await commonService.deleteQuery(
      CustomerControls,
      {
        id: req.params.customer_control_id,
        is_custom: true,
        question_id: null
      },
      dbTrans
    );
    if (!deletedControls) {
      return response.error(req, res, { msgCode: 'ERROR_DELETING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_DELETED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getCollaborators = async (req, res) => {
  try {
    const { Collaborator, User, ROPA, Category, CustomerControls, ReviewROPA } = db.models;
    const ropa_id = req.params.ropa_id;
    // console.log(ropa_id);
    const ropa = await ropaService.getROPAWithAssignee(ROPA, User, { id: ropa_id }, {}, ['status', 'assigned_to', 'approver', 'is_already_performed', 'template_id'], ['firstName', 'lastName']);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    // console.log('-------->>>>>>>', ropa);

    if (ropa.assigned_to !== req.data.userId && ropa.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }

    const categoryCondition = { customer_id: req.data.customer_id };
    
    if(ropa.status === constant.status.CHANGES_REQUESTED){
      const changeReqCategories = await commonService.getListAssociate(CustomerControls, ReviewROPA, { ropa_id: ropa_id }, { accurate_information: 0 }, ['category_id']);
      if (!changeReqCategories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
      categoryCondition.id = {
        [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id)
      };
    }
    if (ropa.is_already_performed === true) {
      categoryCondition.template_id = ropa.template_id;
    } else {
      categoryCondition.template_id = null;
    }

    let collaborators = await ropaService.getCollaborators(Category, Collaborator, User, { ...categoryCondition }, { ropa_id: ropa_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
    if (!collaborators) {
      return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    // console.log('collaborators', collaborators);

    if (collaborators.length === 0) {
      categoryCondition.customer_id = null;
      collaborators = await ropaService.getCollaborators(Category, Collaborator, User, { ...categoryCondition }, { ropa_id: ropa_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
      if (!collaborators) {
        return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }

    if (collaborators.length === 0) {
      collaborators = await ropaService.getCollaborators(Category, Collaborator, User, { customer_id: null }, { ropa_id: ropa_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
      if (!collaborators) {
        return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }

    const assignee = `${ropa.AssignedTo?.firstName} ${ropa.AssignedTo?.lastName}`;

    return response.success(
      req,
      res,
      {
        msgCode: 'COLLABORATORS_FETCHED',
        data: { assignee: assignee, collaborators }
      },
      httpStatus.OK
    );
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCollaborator = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Collaborator, AuditLog, ROPA, Departments, Processes, User, Category } = db.models;

    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.body.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkROPA.assigned_to !== req.data.userId && checkROPA.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const userList = req.body.collaborators?.flatMap(collaborator => collaborator.users?.map(user => user.id));

    const users = await commonService.getListWithoutCount(
      User,
      {
        id: { [Op.in]: userList }
      },
      ['id', 'firstName', 'lastName', 'email']
    );
    if (!users) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const categories = await commonService.getListWithoutCount(
      Category,
      {
        id: {
          [Op.in]: req.body.collaborators.map(collaborator => collaborator.category_id)
        }
      },
      ['id', 'name']
    );
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!invitee) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let collaboratorData = [];
    const auditData = [];
    let ropaName = null;
    let dept_id = null;
    if (checkROPA.Department) {
      ropaName = checkROPA.Department.name;
      dept_id = checkROPA.Department.id;
    } else if (checkROPA.Process) {
      ropaName = checkROPA.Process.name;
      dept_id = checkROPA.Process.Department.id;
    }

    // const names = [];
    for (let collaborator of req.body.collaborators) {
      const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

      for (let collaboratingUser of collaborator.users) {
        const user = users?.find(user => user.id === collaboratingUser.id);
        let userName = `${user?.firstName} ${user?.lastName}`;
        // names.push(userName);

        if (collaboratingUser.action === 'add') {
          collaboratorData.push({
            ropa_id: req.body.ropa_id,
            user_id: collaboratingUser.id,
            category_id: collaborator.category_id
          });

          auditData.push({
            type: 'ROPA',
            type_id: req.body.ropa_id,
            action: `Added ${userName} as a collaborator for ${ropaName} ROPA under ${categoryName} category`,
            action_by_id: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id
          });

          const currentDate = moment().tz('Asia/Kolkata');
          const completionDate = moment(checkROPA?.tentative_completion_date);
          const daysUntilCompletion = completionDate.diff(currentDate, 'days');

          //send mail
          const subject = `Invitation to Contribute: RoPA Assessment on  ${categoryName} – ${daysUntilCompletion} Days Remaining`;
          const textTemplate = 'ropa_collaborator.ejs';
          const baseUrl = req.protocol + '://' + req.get('host');
          const frontEndUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : 'https://dev.gotrust.tech';
          const backEndUrl = process.env.BACKEND_BASE_URL ? process.env.BACKEND_BASE_URL : 'https://devapi.gotrust.tech';
          const sendData = {
            collaborator: userName,
            inviteeName: `${invitee.firstName} ${invitee.lastName}`,
            category: categoryName,
            //url: `${process.env.SERVER_IP}/privacy/ropa/`,
            url: `${frontEndUrl}/data-mapping/ropa/`,
            logo_url: `${backEndUrl}/app/public/gotrustlogo.svg`,
            email_logo_url: `${backEndUrl}/app/public/email_log.png`,
            daysLeft: daysUntilCompletion
          };

          sendMail(user.email, sendData, subject, textTemplate);
        } else if (collaboratingUser.action === 'remove') {
          const oldCollaborator = await commonService.deleteQuery(
            Collaborator,
            {
              ropa_id: req.body.ropa_id,
              user_id: collaboratingUser.id,
              category_id: collaborator.category_id
            },
            dbTrans,
            true
          );
          if (!oldCollaborator) {
            return response.error(req, res, { msgCode: 'ERROR_DELETING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
          }

          auditData.push({
            type: 'ROPA',
            type_id: req.body.ropa_id,
            action: `Removed ${userName} as a collaborator for ${ropaName} ROPA under ${categoryName} category`,
            action_by_id: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id
          });
        }
      }
    }

    const newCollaborators = await commonService.bulkAdd(Collaborator, collaboratorData, dbTrans);
    if (!newCollaborators) {
      return response.error(req, res, { msgCode: 'ERROR_ADDING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'COLLABORATOR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createOrUpdateAnswers = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Answers, ROPA } = db.models;
    const answers = req.body.answers;
    const ropa_id = req.body.ropa_id;

    const checkROPA = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkROPA.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ROPA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Separate answers into two arrays based on the type
    const addAnswers = answers?.filter(answer => answer.type === 'add');
    const updateAnswers = answers?.filter(answer => answer.type === 'update');

    // Add 'answered_by' field to all answers
    addAnswers?.forEach(answer => (answer.answered_by = req.data.userId));
    updateAnswers?.forEach(answer => (answer.answered_by = req.data.userId));

    // Bulk add or update answers
    if (addAnswers.length > 0) {
      const addNewAnswers = await commonService.bulkAdd(Answers, addAnswers, dbTrans);
      if (!addNewAnswers) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateAnswers.length > 0) {
      for (let answer of updateAnswers) {
        const updateAnswers = await commonService.updateData(Answers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
        if (!updateAnswers[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    return response.success(req, res, { msgCode: 'ANSWER_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.uploadDocument = async (req, res) => {
  try {
    const data = {
      url: req.body.url,
      filename: req.body.originalName
    };
    return response.success(req, res, { msgCode: 'DOCUMENT_UPLOADED', data: data }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.deleteDocuments = async (req, res) => {
  try {
    return response.success(req, res, { msgCode: 'DOCUMENT_DELETED' }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.submitROPA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, AuditLog, Departments, Processes, User, Answers, CustomerControls } = db.models;
    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.params.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkROPA.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ROPA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkROPA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ROPA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const checkAnswerStatus = await commonService.getListAssociateWithoutCount(CustomerControls, Answers, { ropa_id: req.params.ropa_id }, {}, [[sequelize.literal(`"CustomerControls"."id"`), 'customer_question_id'], 'parent_id'], {});

    const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
    const childrenMap = checkAnswerStatus?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const unansweredQuestions = parents?.reduce((acc, parent) => {
      if (parent.children.length > 0) {
        parent.children.forEach(child => {
          if (child.Answer === null) {
            acc.push({ customer_question_id: child.customer_question_id });
          }
        });
      } else if (parent.Answer === null) {
        acc.push({ customer_question_id: parent.customer_question_id });
      }
      return acc;
    }, []);

    if (unansweredQuestions.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_ANSWERED', data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const ropa = await commonService.updateData(ROPA, { status: constant.status.UNDER_REVIEW }, { id: req.params.ropa_id }, dbTrans);
    if (!ropa[1]) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let ropaName = null;
    let dept_id = null;

    if (checkROPA.Department) {
      ropaName = checkROPA.Department.name;
      dept_id = checkROPA.Department.id;
    } else if (checkROPA.Process) {
      ropaName = checkROPA.Process.name;
      dept_id = checkROPA.Process.Department.id;
    }

    const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!submitter) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const submitterName = `${submitter.firstName} ${submitter.lastName}`;

    const subject = `Action Required: Review Submitted RoPA Assessment – ${ropaName}`;
    const textTemplate = 'ropa_submit.ejs';
    const sendData = {
      ropaName: ropaName,
      reviewer: `${checkROPA.Approver?.firstName} ${checkROPA.Approver?.lastName}`,
      url: `${process.env.SERVER_IP}/data-mapping/ropa/`
    };

    sendMail(checkROPA.Approver.email, sendData, subject, textTemplate);

    const auditAction = `Submitted ${ropaName} ROPA for review`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ROPA',
        type_id: req.params.ropa_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'ROPA_SUBMITTED', data: ropa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.reviewROPA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ReviewROPA, Answers, ROPA } = db.models;
    const reviews = req.body.reviews;
    const answers = req.body.answers;
    // const ropa_id = req.body.ropa_id;

    // const checkROPA = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status']);
    // if (!checkROPA) {
    //     return response.error(req, res, { msgCode: "ROPA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }

    // Separate answers into two arrays based on the type
    const addReviews = reviews?.filter(answer => answer.type === 'add');
    const updateReviews = reviews?.filter(answer => answer.type === 'update');

    // Add 'areviewer_id' field to all answers
    addReviews?.forEach(review => (review.reviewer_id = req.data.userId));
    updateReviews?.forEach(review => (review.reviewer_id = req.data.userId));

    // Bulk add or update reviews
    if (addReviews.length > 0) {
      const addNewReviews = await commonService.bulkAdd(ReviewROPA, addReviews, dbTrans);
      if (!addNewReviews) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateReviews.length > 0) {
      for (let review of updateReviews) {
        const updateReviews = await commonService.updateData(ReviewROPA, review, { customer_question_id: review.customer_question_id }, dbTrans);
        if (!updateReviews[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }
    // -------------Updationg answer also-----------
    // Separate answers into two arrays based on the type
    const addAnswers = answers?.filter(answer => answer.type === 'add');
    const updateAnswers = answers?.filter(answer => answer.type === 'update');

    // Add 'answered_by' field to all answers
    addAnswers?.forEach(answer => (answer.answered_by = req.data.userId));
    updateAnswers?.forEach(answer => (answer.answered_by = req.data.userId));

    // Bulk add or update answers
    if (addAnswers.length > 0) {
      const addNewAnswers = await commonService.bulkAdd(Answers, addAnswers, dbTrans);
      if (!addNewAnswers) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateAnswers.length > 0) {
      for (let answer of updateAnswers) {
        const updateAnswers = await commonService.updateData(Answers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
        if (!updateAnswers[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    return response.success(req, res, { msgCode: 'REVIEW_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitReview = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, CustomerControls, Answers, AuditLog, Departments, Processes, User, ReviewROPA } = db.models;
    const checkROPA = await ropaService.getROPA(ROPA, Departments, Processes, User, { id: req.params.ropa_id }, {}, {}, {}, {}, {}, {}, ['firstName', 'lastName', 'email']);
    if (!checkROPA) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    if (checkROPA.status !== constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_SUBMITTED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let ropaName = null;
    let dept_id = null;

    if (checkROPA.Department) {
      ropaName = checkROPA.Department.name;
      dept_id = checkROPA.Department.id;
    } else if (checkROPA.Process) {
      ropaName = checkROPA.Process.name;
      dept_id = checkROPA.Process.Department.id;
    }

    let status = constant.status.COMPLETED;
    let end_date = Date();
    const checkReviewStatus = await commonService.getListAssociateWithoutCount(
      CustomerControls,
      ReviewROPA,
      { ropa_id: req.params.ropa_id },
      {},
      [[sequelize.literal(`"CustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      ['accurate_information']
    );

    const unreviewedControls = checkReviewStatus?.filter(review => review.ReviewROPA === null && review.parent_id === null);
    if (unreviewedControls.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_REVIEWED', data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const unapprovedControls = checkReviewStatus?.filter(review => review.ReviewROPA?.accurate_information === 0);
    if (unapprovedControls.length > 0) {
      status = constant.status.CHANGES_REQUESTED;
      end_date = null;
    }

    let riskLevel = null;

    if (status === constant.status.COMPLETED) {
      // risk assessment logic
      riskLevel = 'Low';

      const question_id = [
        constant.question.CHILDREN_DATA_COLLECTION,
        constant.question.CROSS_BORDER_DATA_TRANSFER,
        constant.question.DATA_SUBJECTS_CATEGORIES,
        constant.question.PERSONAL_DATA,
        constant.question.SENSITIVE_DATA,
        constant.question.THIRD_PARTY_VENDORS
      ];

      const answers = await commonService.getListAssociate(Answers, CustomerControls, {}, { ropa_id: req.params.ropa_id, question_id: { [Op.in]: question_id } }, {}, {});

      //create a map with question_id as key and answer as value
      const answerMap = answers?.reduce((map, answer) => {
        map[answer.CustomerControl.question_id] = answer.answer;
        return map;
      }, {});

      const riskAttributes = {
        PERSONAL_DATA: 0,
        SENSITIVE_DATA: 0,
        DATA_SUBJECTS_CATEGORIES: 0,
        CHILDREN_DATA_COLLECTION: 0,
        THIRD_PARTY_VENDORS: 0,
        CROSS_BORDER_DATA_TRANSFER: 0
      };

      for (let key in answerMap) {
        switch (parseInt(key)) {
          case constant.question.PERSONAL_DATA:
            riskAttributes.PERSONAL_DATA = answerMap[key].length;
            break;
          case constant.question.SENSITIVE_DATA:
            riskAttributes.SENSITIVE_DATA = answerMap[key].length;
            break;
          case constant.question.DATA_SUBJECTS_CATEGORIES:
            riskAttributes.DATA_SUBJECTS_CATEGORIES = answerMap[key].length;
            break;
          case constant.question.CHILDREN_DATA_COLLECTION:
            riskAttributes.CHILDREN_DATA_COLLECTION = answerMap[key];
            break;
          case constant.question.THIRD_PARTY_VENDORS:
            riskAttributes.THIRD_PARTY_VENDORS = answerMap[key].length;
            break;
          case constant.question.CROSS_BORDER_DATA_TRANSFER:
            riskAttributes.CROSS_BORDER_DATA_TRANSFER = answerMap[key].length;
            break;
        }
      }

      if (riskAttributes.PERSONAL_DATA > 7 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 7 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 5) {
        riskLevel = 'High';
      } else if (riskAttributes.PERSONAL_DATA > 3 || riskAttributes.DATA_SUBJECTS_CATEGORIES > 3 || riskAttributes.CROSS_BORDER_DATA_TRANSFER > 2) {
        riskLevel = 'Medium';
      }

      if (riskAttributes.CHILDREN_DATA_COLLECTION[0] === '0') {
        riskLevel = 'High';
      }

      if (riskAttributes.SENSITIVE_DATA > 1 || riskAttributes.THIRD_PARTY_VENDORS > 1) {
        riskLevel = 'High';
      }

      const subject = `${ropaName} RoPA Assessment Finalized – Review Identified Risk Insights`;
      const textTemplate = 'ropa_review_submit.ejs';
      const sendDataAssignedTo = {
        recipient: `${checkROPA.AssignedTo?.firstName} ${checkROPA.AssignedTo?.lastName}`,
        ropaName: ropaName,
        url: `${process.env.SERVER_IP}/data-mapping/ropa/`
      };

      sendMail(checkROPA.AssignedTo?.email, sendDataAssignedTo, subject, textTemplate);

      const sendDataApprover = {
        assignee: `${checkROPA.Approver?.firstName} ${checkROPA.Approver?.lastName}`,
        ropaName: ropaName,
        url: `${process.env.SERVER_IP}/data-mapping/ropa/`
      };

      sendMail(checkROPA.AssignedTo?.email, sendDataApprover, subject, textTemplate);
    }

    const ropa = await commonService.updateData(ROPA, { status: status, end_date: end_date, risks: riskLevel }, { id: req.params.ropa_id }, dbTrans);
    if (!ropa[1]) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const auditAction = `Submitted review for ${ropaName} ROPA with status '${status}'`;

    const auditLog = await commonService.addDetail(
      AuditLog,
      {
        type: 'ROPA',
        type_id: req.params.ropa_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      },
      dbTrans
    );
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (ropa[1].status === 'Completed' && req.body.automation === true) {
      const requestData = {
        customer_id: req.data.customer_id,
        entity_id: ropa[1].group_id
      };
      const url = `${process.env.BASE_URL_UNIVERSAL}/api/v1/ai/ucm-lab`;

      // Fire the API call without waiting
      ropaService.makeApiCall(url, requestData, {
        Authorization: `Bearer ${req.kauth.grant.access_token.token}`,
        'Content-Type': 'application/json'
      });
    }

    return response.success(req, res, { msgCode: 'ROPA_REVIEWED', data: ropa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getAuditLog = async (req, res) => {
  try {
    const { AuditLog, User, Departments } = db.models;

    const { ropa_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    const auditCondition = { type: 'ROPA', customer_id: req.data.customer_id };
    const userCondition = {};

    if (search) {
      userCondition[Op.or] = [{ firstName: { [Op.iLike]: `%${search}%` } }, { lastName: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }];
    }

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
      if (!deptHead) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }

      const deptIds = deptHead.rows?.map(dept => dept.id);
      auditCondition.dept_id = { [Op.in]: deptIds };
    }

    if (ropa_id) {
      auditCondition.type_id = ropa_id;
    }

    const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

    if (!auditData) {
      return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    // getting name initials and added to the audit data
    auditData.rows?.map(row => {
      const name = row?.User?.firstName + ' ' + row?.User?.lastName;
      row.name = name;
      const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
      row.initials = initials;
      delete row.User;
    });

    return response.success(req, res, { msgCode: 'AUDIT_LOG_FETCHED', data: auditData }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.uploadControls = async (req, res) => {
  let dbTrans = await db.transaction();
  try {
    const { Controls, Category, ROPA, CustomerControls, BasicInfoAnswers } = db.models;
    const controls = [];
    let parentId = null;
    const childControlsData = [];
    const uniqueCategories = new Map(); // Map to store unique categories
    const artifactTypeMapping = {
      radio: 'radio',
      dropdown: 'select',
      table: 'table',
      'text box': 'textarea',
      'upload attachment': 'attachment'
    };

    // const transaction = await db.transaction();
    try {
      const getRopa = await commonService.getList(
        ROPA,
        {
          customer_id: req.data.customer_id,
          status: { [Op.ne]: 'Yet to Start' },
          template_id: null
        },
        ['id', 'status']
      );

      const ropaIds = getRopa?.rows?.map(item => item.id);

      // Delete CustomerControls if they exist
      const checkCustomerControls = await commonService.getList(CustomerControls, { ropa_id: { [Op.in]: ropaIds } });
      if (checkCustomerControls?.rows?.length > 0) {
        await commonService.deleteQuery(CustomerControls, { ropa_id: { [Op.in]: ropaIds } }, dbTrans, true);
      }

      // Delete Controls if they exist
      const checkControls = await commonService.getList(Controls, {
        customer_id: req.data.customer_id,
        template_id: null
      });
      if (checkControls?.rows?.length > 0) {
        await commonService.deleteQuery(Controls, { customer_id: req.data.customer_id, template_id: null }, dbTrans, true);
      }

      // Delete Categories if they exist
      const checkCategory = await commonService.getList(Category, {
        customer_id: req.data.customer_id,
        template_id: null
      });
      if (checkCategory?.rows?.length > 0) {
        await commonService.deleteQuery(Category, { customer_id: req.data.customer_id, template_id: null }, dbTrans, true);
      }

      const checkBasicInfo = await commonService.getList(BasicInfoAnswers, { ropa_id: { [Op.in]: ropaIds } }, ['question_id', 'answer']);
      if (checkBasicInfo.count >= 3) {
        await commonService.deleteQuery(BasicInfoAnswers, { ropa_id: { [Op.in]: ropaIds } }, dbTrans, true);
      }

      // Update ROPA records sequentially
      for (const { id, status } of getRopa.rows) {
        await commonService.updateData(
          ROPA,
          {
            start_date: null,
            end_date: null,
            // assigned_to: null,
            // approver: null,
            progress: null,
            tentative_completion_date: null,
            status: 'Yet to Start'
          },
          { id: id },
          dbTrans
        );
      }

      // Commit the transaction
      await dbTrans.commit();
    } catch (error) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'TRANSACTION_FAILED' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }

    // Start a new transaction for the file processing
    dbTrans = await db.transaction();

    const requiredHeaders = ['ROPA Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
    const { isValid, missingHeader } = await ropaService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const fileStream = fs.createReadStream(req.files[0].path);
    const csvParser = csv();
    fileStream
      .pipe(csvParser)
      .on('data', async row => {
        const controlData = {
          ROPALevel: row['ROPA Level'],
          Category: row['Category'],
          title: row['Title'],
          description: row['Explanation'],
          artifact_type: row['Artifact Type'],
          question: row['Question'],
          fields: row['Fields'],
          is_attachment: row['Has Attachment'],
          extra_input: row['Extra Input Required'],
          customer_id: req.data.customer_id,
          extra_input_type: row['Extra Input Type'],
          extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields']
        };

        // Check if all properties of controlData are empty
        if (!Object.values(controlData).every(x => x === '')) {
          if (controlData.title === '' && controlData.question === '') {
            fileStream.destroy();
            csvParser.end();
            csvParser.removeAllListeners();
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          controls.push(controlData);
        }
      })
      .on('end', async () => {
        // Insert the data into the database
        const categoryTitleQuestionSet = new Set();
        for (let row of controls) {
          if (!row['ROPALevel'] || !row['Category']) {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          const key = `${row['ROPALevel']}_${row['Category']}`;
          if (row['title']) {
            const titleText = row['title']?.trim();
            const duplicateKey = `${key}_${titleText}`;

            if (categoryTitleQuestionSet.has(duplicateKey)) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'DUPLICATE_QUESTION_TITLE', data: `Duplicate control found in category [${key}] with title [${titleText}]` }, httpStatus.BAD_REQUEST, dbTrans);
            }
            categoryTitleQuestionSet.add(duplicateKey);
            // Parent question
            if (!uniqueCategories.has(key)) {
              // Create or retrieve category and store in the map
              const [ropa_level, name] = key.split('_');
              let category = await commonService.findByCondition(Category, { ropa_level, name, customer_id: req.data.customer_id, template_id: null }, {});
              if (!category) {
                category = await Category.create({ name, ropa_level, customer_id: req.data.customer_id, template_id: null }, { transaction: dbTrans });
              }
              uniqueCategories.set(key, category);
            }

            let artifactType = null;
            if (row['artifact_type'] !== '') {
              artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
              if (!artifactType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            let extraInputType = null;
            if (row['extra_input_type'] !== '') {
              console.log('row', row['extra_input_type']);
              extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
              if (!extraInputType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            // Create parent control
            const control = await commonService.addDetail(
              Controls,
              {
                title: row['title'],
                description: row['description'],
                artifact_type: artifactType,
                question: row['question'],
                customer_id: req.data.customer_id,
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                industry_vertical_id: 1,
                parent_id: null
              },
              dbTrans
            );

            if (!control) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            // Update parent ID for potential child questions
            parentId = control.id;
          } else {
            // Child question
            if (parentId) {
              // Create child control
              let artifactType = null;
              if (row['artifact_type'] !== '') {
                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                if (!artifactType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              let extraInputType = null;
              if (row['extra_input_type'] !== '') {
                console.log('row', row['extra_input_type']);
                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                if (!extraInputType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              childControlsData.push({
                title: null,
                description: null,
                artifact_type: artifactType,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                customer_id: req.data.customer_id,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                industry_vertical_id: 1,
                parent_id: parentId
              });
            } else {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
            }
          }
        }
        // Batch create child controls
        const childControls = await commonService.bulkAdd(Controls, childControlsData, dbTrans);
        if (!childControls) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        await deleteFile(req.files[0].path);

        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getPolicyList = async (req, res) => {
  try {
    const { Policy, PolicyDocument } = db.models;
    const policies = await commonService.getListAssociateWithoutCount(Policy, PolicyDocument, { entity_id: req.query.entity_id }, {}, ['id', 'name'], ['url', 'original_name']);
    if (!policies) {
      return response.error(req, res, { msgCode: 'POLICIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const formattedPolicies = policies.map(policy => ({
      id: policy.id,
      name: policy.name,
      file:
        policy.PolicyDocuments?.map(doc => {
          return {
            url: doc.url,
            original_name: doc.original_name
          };
        }) || null
    }));

    return response.success(req, res, { msgCode: 'POLICIES_FETCHED', data: formattedPolicies }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaDashboard = async (req, res) => {
  try {
    const { Departments, Processes } = db.models;

    // get department Data
    const departmentData = await commonService.getListAssociateWithGroup(Departments, Processes, { customer_id: req.data.customer_id }, {}, ['id', 'name', [sequelize.fn('COUNT', sequelize.col('Processes.id')), 'ProcessesCount']], []);

    let totalProcessesCount = 0;

    departmentData?.forEach(items => {
      items.ProcessesCount = parseInt(items.ProcessesCount);
      totalProcessesCount += items.ProcessesCount;
    });

    // // const totalProcessesCount = departmentData?.reduce((sum, department) => {
    // //     const processesCount = parseInt(department.ProcessesCount) || 0; // Convert to integer or default to 0
    // //     return sum + processesCount;
    // // }, 0); // Initial value of sum is 0

    // console.log('s', req.data.customer_id);

    // // get customer control id using question id
    // const LawfulData = await privacyService.getAnswers(Answers, CustomerControls, {}, { question_id: question.LAWFUL_BASIS });

    // console.log('law', LawfulData);

    // const selectedIds = LawfulData?.reduce((acc, control) => {
    //     return acc.concat(control.answer.map(Number)); // Convert answer strings to numbers
    // }, []);

    // // const getAnswers
    // // Count occurrences of each control ID
    // const countMap = selectedIds.reduce((acc, id) => {
    //     acc[id] = (acc[id] || 0) + 1;
    //     return acc;
    // }, {});

    // // If you have a mapping of control IDs to their names
    // const controlNames = {
    //     0: 'Consent',
    //     1: 'Performance of a contract',
    //     2: 'A legitimate interest',
    //     3: 'A vital interest',
    //     4: 'A legal requirement',
    //     5: 'A public interest'
    // };

    // // Display counts with control names
    // const result = {};
    // Object.keys(countMap).forEach((id) => {
    //     const controlName = controlNames[id];
    //     if (controlName) {
    //         result[controlName] = countMap[id];
    //     }
    // });

    // console.log(result);

    const data = {
      total_processes: totalProcessesCount,
      total_department: departmentData
    };

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDashboard', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaDashboardOrgRole = async (req, res) => {
  try {
    const { CustomerControls, Answers, User } = db.models;

    // If you have a mapping of control IDs to their names
    const controlNames = {
      0: 'Consent',
      1: 'Performance of a contract',
      2: 'A legitimate interest',
      3: 'A vital interest',
      4: 'A legal requirement',
      5: 'A public interest'
    };

    // get customer control id using question id
    const LawfulData = await ropaService.getAnswers(Answers, CustomerControls, User, {}, { question_id: question.LAWFUL_BASIS }, { customer_id: req.data.customer_id });
    const countMap = {};

    // Initialize countMap with all control options set to 0
    Object.keys(controlNames)?.forEach(id => {
      countMap[id] = 0;
    });

    // Iterate through each record and update countMap based on selected options
    LawfulData?.forEach(control => {
      control.answer.forEach(id => {
        countMap[id] += 1;
      });
    });

    // Convert control IDs to names in the result object
    const result = {};
    Object.keys(countMap)?.forEach(id => {
      const controlName = controlNames[id];
      result[controlName] = countMap[id];
    });

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data: result }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDashboardOrgRole', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaDashboardPersonalData = async (req, res) => {
  try {
    const { CustomerControls, Answers, ROPA, Departments } = db.models;

    // get customer control id using question id
    // const sensitiveData = await privacyService.getAnswers(Answers, CustomerControls, {}, { question_id: question.SENSITIVE_DATA });

    // get customer control id using question id
    // const thirdPartyPersonalData = await privacyService.getRopaThirdParty(Answers, CustomerControls, ROPA, Departments, {}, { question_id: question.PERSONAL_DATA }, {}, {}, ['id', 'customer_question_id', 'answer'], ['id'], ['id'], ['id', 'name']);

    const thirdPartyPersonalData = await ropaService.getRopaThirdParty(
      Answers,
      CustomerControls,
      ROPA,
      Departments,
      {},
      {
        question_id: {
          [Op.in]: [question.PERSONAL_DATA, question.SENSITIVE_DATA]
        }
      },
      {},
      { customer_id: req.data.customer_id },
      ['id', 'customer_question_id', 'answer'],
      ['id', 'question_id'],
      ['id'],
      ['id', 'name']
    );

    // Group responses by department name and count personal_data and sensitive_data
    const groupedData = thirdPartyPersonalData?.reduce((result, response) => {
      const {
        CustomerControl: {
          ROPA: { Department }
        }
      } = response;
      const { id: departmentId, name: departmentName } = Department;

      // Initialize department entry in result object if not already created
      if (!result[departmentName]) {
        result[departmentName] = {
          department_name: departmentName,
          personal_data_count: 0,
          sensitive_data_count: 0
        };
      }

      // Determine the type of data based on customer_question_id
      const { answer, CustomerControl } = response;
      const isPersonalData = CustomerControl.question_id === question.PERSONAL_DATA;

      // Increment the corresponding count based on the data type
      if (isPersonalData) {
        result[departmentName].personal_data_count += answer.length;
      } else {
        result[departmentName].sensitive_data_count += answer.length;
      }

      return result;
    }, {});

    // Convert groupedData object values to an array of department data
    const finalResult = Object.values(groupedData);

    // const thirdPartySensitiveData = await privacyService.getRopaThirdParty(Answers, CustomerControls, ROPA, Departments, {}, { question_id: question.SENSITIVE_DATA }, {}, {}, ['id', 'customer_question_id', 'answer'], ['id'], ['id'], ['id', 'name']);

    // thirdPartyPersonalData?.forEach(items=>{
    //     items.count = items.answer.length;
    // })

    // thirdPartySensitiveData?.forEach(items=>{
    //     items.count = items.answer.length;
    // })

    // const data = {
    //     personal_data: thirdPartyPersonalData,
    //     senstive_data: thirdPartySensitiveData
    // }

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data: finalResult }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDashboardOrgRole', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaDashboardThirdParty = async (req, res) => {
  try {
    const { CustomerControls, Answers, ROPA, Departments } = db.models;

    // get customer control id using question id
    let thirdParty = await ropaService.getRopaThirdParty(
      Answers,
      CustomerControls,
      ROPA,
      Departments,
      {},
      { question_id: question.THIRD_PARTY_VENDORS },
      {},
      { customer_id: req.data.customer_id },
      ['id', 'customer_question_id', 'answer'],
      ['id'],
      ['id'],
      ['id', 'name']
    );

    // Iterate through each response object and process its answers
    const processedResponses = thirdParty?.map(response => {
      // Destructure values from the response object
      const { id, customer_question_id, answer, CustomerControl } = response;
      const { id: controlId, ROPA } = CustomerControl;
      const { id: departmentId, Department } = ROPA;
      const { name: departmentName } = Department;

      // Parse each answer string into a JavaScript object and extract specific values
      const parsedAnswers = answer?.map(answerString => {
        const parsedAnswer = JSON.parse(answerString);
        const { 0: company, 1: businessType, 2: department, 3: location, 4: personalData } = parsedAnswer;

        return {
          company,
          businessType,
          department,
          location,
          personalData
        };
      });

      // Construct the processed response object
      return {
        id,
        customer_question_id,
        controlId,
        departmentId,
        departmentName,
        answers: parsedAnswers
      };
    });

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data: processedResponses }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDashboardOrgRole', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaDashboardDataSystem = async (req, res) => {
  try {
    const { CustomerControls, Answers, ROPA, Departments } = db.models;

    // get customer control id using question id
    let thirdParty = await ropaService.getRopaThirdParty(
      Answers,
      CustomerControls,
      ROPA,
      Departments,
      {},
      { question_id: question.DETAILS_OF_THIRD_PARTY },
      {},
      { customer_id: req.data.customer_id },
      ['id', 'customer_question_id', 'answer'],
      ['id'],
      ['id'],
      ['id', 'name']
    );

    // Iterate through each response object and process its answers
    const processedResponses = thirdParty?.map(response => {
      // Destructure values from the response object
      const { id, customer_question_id, answer, CustomerControl } = response;
      const { id: controlId, ROPA } = CustomerControl;
      const { id: departmentId, Department } = ROPA;
      const { name: departmentName } = Department;

      // Parse each answer string into a JavaScript object and extract specific values
      const parsedAnswers = answer?.map(answerString => {
        const parsedAnswer = JSON.parse(answerString);
        const { 0: details, 1: purpose, 2: type_of_tool, 3: name, 4: location } = parsedAnswer;

        return {
          details,
          purpose,
          type_of_tool,
          name,
          location
        };
      });

      // Construct the processed response object
      return {
        id,
        customer_question_id,
        controlId,
        departmentId,
        departmentName,
        answers: parsedAnswers
      };
    });

    processedResponses?.forEach(items => {
      items.processing_count = items.answers.length;
    });

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data: processedResponses }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDashboardOrgRole', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaDpiaRequirement = async (req, res) => {
  try {
    const { ROPA, Departments, Processes } = db.models;

    // get customer control id using question id
    const thirdParty = await ropaService.getRopaDpiaReuqirement(Departments, Processes, ROPA, { customer_id: req.data.customer_id }, {}, { risks: 'High' }, ['id', 'name'], ['id', 'name'], ['id', 'risks']);

    thirdParty?.forEach(items => {
      items.high_risk_processes = items.Processes.length;
    });

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data: thirdParty }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDashboardOrgRole', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.ropaOrgRole = async (req, res) => {
  try {
    const { BasicInfoQuestions, BasicInfoAnswers, User } = db.models;

    // get customer control id using question id
    const basicInforData = await commonService.getListWithMultiAssociate(BasicInfoAnswers, BasicInfoQuestions, User, { question_id: question.BASIC_INFO_ORG_ROLE }, {}, { customer_id: req.data.customer_id });

    // Initialize fieldCounts object with all field names and counts set to zero
    const fieldCounts = {};

    // Extract all possible field names and initialize counts to zero
    basicInforData.rows[0]?.BasicInfoQuestion?.fields?.forEach(({ name }) => {
      fieldCounts[name] = 0;
    });

    // Aggregate field counts from all responses
    basicInforData.rows?.forEach(({ answer, BasicInfoQuestion: { fields } }) => {
      // Create a Set of selected field IDs for fast lookup
      const selectedFieldIds = new Set(answer);

      // Iterate over all possible fields to count occurrences
      fields?.forEach(({ id, name }) => {
        if (selectedFieldIds.has(id)) {
          fieldCounts[name]++; // Increment count for selected field
        }
      });
    });

    // Calculate the total count of all values
    const totalCount = Object.values(fieldCounts).reduce((total, count) => total + count, 0);
    fieldCounts.total_count = totalCount;

    return response.success(req, res, { msgCode: 'DASHBOARD_FETCHED', data: fieldCounts }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDpiaRequirement', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getRopaData = async (req, res) => {
  try {
    const { Controls, CustomerControls, Answers, User, ROPA, Category, Customer, Departments, Processes, Group, BasicInfoQuestions, BasicInfoAnswers } = db.models;
    const ropa_id = req.params.ropa_id;

    const ropa = await commonService.findByCondition(ROPA, { id: ropa_id }, ['status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks']);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (ropa.status !== constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_COMPLETED' }, httpStatus.BAD_REQUEST);
    }

    const controlsAttributes = [
      [sequelize.literal(`"CustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."title" ELSE "Control"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."description" ELSE "Control"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."artifact_type" AS TEXT) ELSE CAST("Control"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."is_attachment" ELSE "Control"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."question" ELSE "Control"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."fields" ELSE "Control"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input" ELSE "Control"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."extra_input_type" AS TEXT) ELSE CAST("Control"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input_fields" ELSE "Control"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "CustomerControls"."question_id" IS NOT NULL THEN "Control"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    const controls = await ropaService.getControlsWithCategory(
      CustomerControls,
      Controls,
      Answers,
      User,
      Category,
      { ropa_id: ropa_id },
      {},
      {},
      {},
      {},
      controlsAttributes,
      [],
      ['id', 'answer', 'attachment_link', 'extra_answer'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name', 'ropa_level'],
      [['question_id', 'ASC']]
    );

    let parents = controls.filter(control => control.parent_id === null);
    const childrenMap = controls.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    let extraProcQues = false;
    const deptBasicInfo = await ropaService.getDepartmentBasicInfo(ROPA, Departments, User, Group, Processes, { id: ropa_id }, {}, {}, {}, {}, [], ['name'], ['firstName', 'lastName'], ['name'], ['name']);
    const procBasicInfo = await ropaService.getProcessBasicInfo(ROPA, Processes, Departments, User, Group, { id: ropa_id }, {}, {}, {}, {}, [], ['name'], ['name'], ['firstName', 'lastName'], ['name']);
    if (deptBasicInfo) {
      answerData = {
        [`${constant.basicInfoDept.DEPARTMENT_NAME}`]: deptBasicInfo?.Department?.name,
        [`${constant.basicInfoDept.ENTITY_NAME}`]: deptBasicInfo?.Department?.Group?.name,
        [`${constant.basicInfoDept.DEPARTMENT_HEAD}`]: `${deptBasicInfo?.Department?.User?.firstName} ${deptBasicInfo?.Department?.User?.lastName}`,
        [`${constant.basicInfoDept.SPOC}`]: `${deptBasicInfo?.Department?.User?.firstName} ${deptBasicInfo?.Department?.User?.lastName}`,
        [`${constant.basicInfoDept.SUBFUNCTION}`]: deptBasicInfo?.Department?.Processes?.map(process => process.name).join(', ')
      };
    } else if (procBasicInfo) {
      extraProcQues = true;
      answerData = {
        [`${constant.basicInfoDept.DEPARTMENT_NAME}`]: procBasicInfo?.Process?.Department?.name,
        [`${constant.basicInfoDept.ENTITY_NAME}`]: procBasicInfo?.Process?.Department?.Group.name,
        [`${constant.basicInfoDept.DEPARTMENT_HEAD}`]: `${procBasicInfo?.Process?.Department?.User.firstName} ${procBasicInfo?.Process?.Department?.User.lastName}`,
        [`${constant.basicInfoDept.SPOC}`]: `${procBasicInfo?.Process?.Department?.User.firstName} ${procBasicInfo?.Process?.Department?.User.lastName}`,
        [`${constant.basicInfoDept.SUBFUNCTION}`]: procBasicInfo?.Process?.children?.map(process => process.name).join(', '),
        [`${constant.basicInfoProc.PROCESS_NAME}`]: procBasicInfo?.Process?.name
      };
    }

    let basicInfoQuestions = await commonService.getListAssociateWithoutCount(BasicInfoQuestions, BasicInfoAnswers, {}, { ropa_id: ropa_id }, ['id', 'question', 'fields', 'artifact_type', 'ropa_level'], ['answer', 'answered_by'], null, null, [
      ['id', 'ASC']
    ]);
    if (!basicInfoQuestions) {
      return response.error(req, res, { msgCode: 'QUESTIONS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    basicInfoQuestions?.forEach(question => {
      question.editable = false;
      switch (question.id) {
        case constant.basicInfoDept.DEPARTMENT_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.DEPARTMENT_NAME}`]];
          break;
        case constant.basicInfoProc.PROCESS_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.PROCESS_NAME}`]];
          break;
        case constant.basicInfoDept.ENTITY_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.ENTITY_NAME}`]];
          break;
        case constant.basicInfoDept.DEPARTMENT_HEAD:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.DEPARTMENT_HEAD}`]];
          break;
        case constant.basicInfoDept.SPOC:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.SPOC}`]];
          break;
        case constant.basicInfoDept.SUBFUNCTION:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoDept.SUBFUNCTION}`]];
          break;
        case constant.basicInfoProc.DEPARTMENT_NAME:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.DEPARTMENT_NAME}`]];
          break;
        case constant.basicInfoProc.DEPARTMENT_HEAD:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.DEPARTMENT_HEAD}`]];
          break;
        case constant.basicInfoProc.SPOC:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.SPOC}`]];
          break;
        case constant.basicInfoProc.SUBPROCESS:
          question.BasicInfoAnswer = {};
          question.BasicInfoAnswer.answer = [answerData[`${constant.basicInfoProc.SUBPROCESS}`]];
          break;
        default:
          question.editable = true;
      }
    });

    if (!extraProcQues) {
      basicInfoQuestions = basicInfoQuestions.filter(question => question?.ropa_level === 'department');
    }

    const excelData = transformData(parents);

    let basicInfoData = transformBasicInfo(basicInfoQuestions);

    const sensitive_data_control = controls?.filter(control => control.question === 'Sensitive Personally Identifiable Information');
    const sensitive_data_collected = sensitive_data_control[0]?.Answer?.answer?.length > 0 ? 'Yes' : 'No';

    basicInfoData = basicInfoData?.filter(question => question?.question !== 'Sub Function');
    basicInfoData.push(
      {
        question: 'Is Sensitive Personal Data Processed?',
        answer: sensitive_data_collected
      },
      {
        question: 'Is a DPIA required?',
        answer: ropa.risks === 'High' ? 'Yes' : 'No'
      },
      {
        question: "Controller's Representative/ DPO Contact details",
        answer: `Name: ${user?.firstName} ${user?.lastName}, Email: ${user?.email}`
      }
    );

    const date = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    let deptName = '';
    let procName = '';

    if (ropa.department_id) {
      const dept = await commonService.findByCondition(Departments, { id: ropa.department_id }, ['name']);
      deptName = dept?.name;
    } else if (ropa.process_id) {
      const proc = await commonService.getDataAssociate(Processes, Departments, { id: ropa.process_id }, {}, ['name'], ['name']);
      procName = proc?.name;
      deptName = proc?.Department?.name;
    }

    const excelFile = await createExcelFile(excelData, basicInfoData, date, customer?.name, deptName, procName);

    await sendMailWithAttach(req.data.email, { name: `${user?.firstName} ${user?.lastName}` }, 'Your Copy of RoPA file made on GoTrust', 'ropa_download.ejs', excelFile);

    return response.success(req, res, { msgCode: 'ROPA_DOWNLOADED', data: 'RoPA data sent via E-mail' }, httpStatus.OK);
  } catch (err) {
    console.log('ropaDownloadError', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getExportRopa = async (req, res) => {
  try {
    const { Controls, Category } = db.models;

    const getExportRopa = await commonService.getListAssociate(Controls, Category, { customer_id: req.data.customer_id }, {});

    let excelData = [];

    // 'radio': 'radio',
    //     'dropdown': 'select',
    //     'table': 'table',
    //     'text box': 'textarea',
    //     'upload attachment': 'attachment'

    getExportRopa?.forEach(items => {
      const artifactType = items.artifact_type == 'textarea' ? 'text box' : items.artifact_type == 'select' ? 'dropdown' : items.artifact_type == 'attachment' ? 'upload attachment' : items.artifact_type;
      let formattedNames;
      if (Array.isArray(items.fields)) {
        const fieldNames = items.fields.map(field => field.name.trim()); // .trim() removes any extra spaces

        // Join the names with a newline character to create the desired output
        formattedNames = fieldNames.join('\n');
      } else {
        formattedNames = items.fileds;
      }

      const attachment = items.is_attachment ? 'Yes' : 'No';
      const extraInput = items.extra_input ? 'Yes' : 'No';

      const ropaData = [items.Category.ropa_level, items.Category.name, items.title, items.description, artifactType, items.question, formattedNames, attachment, extraInput, items.extra_input_type, items.extra_input_fields];
      excelData.push(ropaData);
    });

    const excelFile = await exportRopa(excelData);

    await sendMailWithAttach(req.data.email, { name: `Praveen Kumar` }, 'Your Copy of RoPA file made on GoTrust', 'ropa_download.ejs', excelFile);

    return response.success(req, res, { msgCode: 'ROPA_DOWNLOADED', data: getExportRopa }, httpStatus.OK);
  } catch (err) {
    console.log('getExportRopa', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.updateTentativeDate = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ROPA, BasicInfoAnswers } = db.models;
    const checkRopa = await commonService.findByCondition(ROPA, { id: req.params.ropa_id }, ['id', 'assigned_to', 'approver']);
    if (!checkRopa) {
      return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    if (req.data.userId !== checkRopa.assigned_to && req.data.userId !== checkRopa.approver) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const updateRopa = await commonService.updateData(ROPA, req.body, { id: req.params.ropa_id }, dbTrans);
    if (!updateRopa[1]) {
      return response.error(req, res, { msgCode: 'FAILED_TO_UPDATE' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const formattedDate = dayjs(req.body.tentative_completion_date).format('DD-MM-YYYY');
    const updateAns = await commonService.updateData(BasicInfoAnswers, { answer: [formattedDate] }, { ropa_id: req.params.ropa_id, question_id: 8 }, dbTrans);

    return response.success(req, res, { msgCode: 'ROPA_UPDATED', data: updateRopa[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
  }
};

exports.uploadFilledRopa = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Controls, Category, ROPA, CustomerControls, Answers, RopaTemplate } = db.models;
    const controls = [];
    let parentId = null;
    let customerControlsparentId = null;
    // const childControlsData = [];
    const uniqueCategories = new Map(); // Map to store unique categories
    const artifactTypeMapping = {
      radio: 'radio',
      dropdown: 'select',
      table: 'table',
      'text box': 'textarea',
      'upload attachment': 'attachment'
    };

    const ropa = await commonService.findByCondition(ROPA, { id: req.params.ropa_id }, ['id', 'status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks']);
    if (!ropa) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const addTemplate = await commonService.addDetail(
      RopaTemplate,
      {
        temp_name: req.body.originalName,
        url: req.body.url,
        customer_id: req.data.customer_id,
        entity_id: req.body.entity_id
      },
      dbTrans
    );
    if (!addTemplate) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const templateId = addTemplate.id;
    const updateRopa = await commonService.updateData(ROPA, { approver: req.data.userId, is_already_performed: true, status: constant.status.UNDER_REVIEW, progress: 100, template_id: templateId }, { id: req.params.ropa_id }, dbTrans);
    if (!updateRopa[1]) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const requiredHeaders = [
      'ROPA Level',
      'Category',
      'Title',
      'Explanation',
      'Artifact Type',
      'Question',
      'Fields',
      'Has Attachment',
      'Extra Input Required',
      'Extra Input Type',
      'Extra Input Fields',
      'Answer',
      'Attachment Link',
      'Extra Answer',
      'Raw Url'
    ];
    const { isValid, missingHeader } = await ropaService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }

    fs.createReadStream(req.files[0].path)
      .pipe(csv())
      .on('data', async row => {
        const controlData = {
          ROPALevel: row['ROPA Level'],
          Category: row['Category'],
          title: row['Title'],
          description: row['Explanation'],
          artifact_type: row['Artifact Type'],
          question: row['Question'],
          fields: row['Fields'],
          is_attachment: row['Has Attachment'],
          extra_input: row['Extra Input Required'],
          customer_id: req.data.customer_id,
          extra_input_type: row['Extra Input Type'],
          extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields'],
          template_id: templateId,
          answer: row['Answer'],
          extra_answer: row['Extra Answer'] === '' ? null : row['Extra Answer'],
          attachment_link: row['Attachment Link'] === '' ? null : row['Attachment Link'],
          raw_url: row['Raw Url'] === 'Yes' ? true : false
        };

        // Check if all properties of controlData are empty
        if (!Object.values(controlData).every(x => x === '')) {
          if (controlData.title === '' && controlData.question === '') {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          controls.push(controlData);
        }
      })
      .on('end', async () => {
        // Insert the data into the database
        for (let row of controls) {
          if (!row['ROPALevel'] || !row['Category']) {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          const key = `${row['ROPALevel']}_${row['Category']}`;
          if (row['title']) {
            // Parent question
            if (!uniqueCategories.has(key)) {
              // Create or retrieve category and store in the map
              const [ropa_level, name] = key.split('_');
              let category = await commonService.findByCondition(
                Category,
                {
                  ropa_level,
                  name,
                  customer_id: req.data.customer_id,
                  template_id: templateId
                },
                {}
              );
              if (!category) {
                category = await Category.create(
                  {
                    name,
                    ropa_level,
                    customer_id: req.data.customer_id,
                    template_id: templateId
                  },
                  { transaction: dbTrans }
                );
              }
              uniqueCategories.set(key, category);
            }

            let artifactType = null;
            if (row['artifact_type'] !== '') {
              artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
              if (!artifactType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            let extraInputType = null;
            if (row['extra_input_type'] !== '') {
              console.log('row', row['extra_input_type']);
              extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
              if (!extraInputType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }
            let fields = row['fields']
              ? row['fields']
                  .split('\n')
                  .map(line => line.replace('\r', ''))
                  .map((name, id) => ({ id, name }))
              : null;
            let extraInputFields = row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null;
            // Create parent control
            const control = await commonService.addDetail(
              Controls,
              {
                title: row['title'],
                description: row['description'],
                artifact_type: artifactType,
                question: row['question'],
                customer_id: req.data.customer_id,
                fields: fields,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: extraInputFields,
                category_id: uniqueCategories.get(key).id,
                industry_vertical_id: 1,
                template_id: templateId,
                parent_id: null
              },
              dbTrans
            );

            if (!control) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
            }
            // const test = {
            //   question_id: control.id,
            //   customer_id: req.data.customer_id,
            //   catgory_id: uniqueCategories.get(key).id,
            //   ropa_id: req.params.ropa_id,
            //   is_custom: false
            // }
            // console.log('Customer control', test);
            //adding customer controls
            const customerControl = await commonService.addDetail(
              CustomerControls,
              {
                question_id: control.id,
                customer_id: req.data.customer_id,
                category_id: uniqueCategories.get(key).id,
                ropa_id: req.params.ropa_id,
                is_custom: false
              },
              dbTrans
            );

            if (!customerControl) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            //ANswers

            // Find the object whose name matches the answer string from the CSV row
            // console.log('row------------------->>>>>>', fields);
            let foundAnswer = fields?.find(item => item.name === row['answer']);
            // If found, wrap its id in an array; otherwise, set to null
            let answerIdArray = foundAnswer ? [foundAnswer.id] : [row['answer']];
            let foundExtraAnswer = extraInputFields?.find(item => item.name === row['extra_answer']);
            let extraAnswerIdArray = foundExtraAnswer ? [foundExtraAnswer.id] : row['extra_answer'] === null ? null : [row['extra_answer']];

            // let data = {
            //   customer_question_id: customerControl.id,
            //   answer: answerIdArray,
            //   extra_answer: extraAnswerIdArray,
            //   attachment_link: row['attachment_link'],
            //   answered_by: req.data.userId,
            //   raw_url: row['raw_url']
            // };

            // console.log(data);
            const answer = await commonService.addDetail(
              Answers,
              {
                customer_question_id: customerControl.id,
                answer: answerIdArray,
                extra_answer: extraAnswerIdArray,
                attachment_link: row['attachment_link'],
                answered_by: req.data.userId,
                raw_url: row['raw_url']
              },
              dbTrans
            );

            if (!answer) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            // Update parent ID for potential child questions
            parentId = control.id;
            customerControlsparentId = customerControl.id;
          } else {
            // Child question
            if (parentId) {
              // Create child control
              let artifactType = null;
              if (row['artifact_type'] !== '') {
                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                if (!artifactType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              let extraInputType = null;
              if (row['extra_input_type'] !== '') {
                console.log('row', row['extra_input_type']);
                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                if (!extraInputType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
                }
                const childControl = await commonService.addDetail(
                  Controls,
                  {
                    title: null,
                    description: null,
                    artifact_type: artifactType,
                    question: row['question'],
                    fields: fields,
                    is_attachment: row['is_attachment'] === 'Yes',
                    extra_input: row['extra_input'] === 'Yes',
                    extra_input_type: extraInputType,
                    customer_id: req.data.customer_id,
                    extra_input_fields: extraInputFields,
                    category_id: uniqueCategories.get(key).id,
                    industry_vertical_id: 1,
                    template_id: templateId,
                    parent_id: parentId
                  },
                  dbTrans
                );

                if (!childControl) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
                }

                //adding customer controls
                const childCustomerControl = await commonService.addDetail(
                  CustomerControls,
                  {
                    question_id: childControl.id,
                    customer_id: req.data.customer_id,
                    catgory_id: uniqueCategories.get(key).id,
                    ropa_id: req.params.ropa_id,
                    is_custom: false,
                    parent_id: customerControlsparentId
                  },
                  dbTrans
                );
                if (!childCustomerControl) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
                }

                //child question answe

                const childAnswer = await commonService.addDetail(
                  Answers,
                  {
                    customer_question_id: childCustomerControl.id,
                    answer: answerIdArray,
                    extra_answer: extraAnswerIdArray,
                    attachment_link: row['attachment_link'],
                    answered_by: req.data.userId,
                    raw_url: row['raw_url']
                  },
                  dbTrans
                );
                if (!childAnswer) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              } else {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }
          }
        }
        await deleteFile(req.files[0].path);

        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.collaboratorProgress = async (req, res) => {
  try {
    const { Category, ROPA, CustomerControls, Answers, Collaborator, User } = db.models;
    const ropaId = req.params.ropa_id;

    const findCategoryAndDetails = await commonService.getDistinct(CustomerControls, { ropa_id: ropaId }, ['category_id'], ['category_id']);
    findCategoryAndDetails.sort((a, b) => a.category_id - b.category_id);
    if (!findCategoryAndDetails) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    let categoryProgress = [];

    let controls = null;
    let totalControls = 0;
    let answeredControls = 0;
    let childControls = [];
    let progress = 0;

    for (let category of findCategoryAndDetails) {
      const { category_id } = category;

      controls = await commonService.getListAssociateWithoutCount(CustomerControls, Answers, { ropa_id: ropaId, category_id: category_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      console.log(controls);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.Answer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });

      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.Answer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });

      progress = (answeredControls / totalControls) * 100;
      progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));
      progress = progress === 100 ? 100 : parseFloat(progress.toFixed(2));

      const details = await commonService.getListWith3Models(Category, Collaborator, User, { id: category_id }, { ropa_id: ropaId, category_id: category_id }, {}, ['id', 'name'], {}, ['id', 'firstName', 'lastName', 'email']);

      const categoryDetails = details[0] || {}; // Ensure we access a valid object
      const collaboratorDetails =
        categoryDetails?.Collaborators?.map(collab => ({
          id: collab.User?.id,
          name: `${collab.User?.firstName} ${collab.User?.lastName}`
        })) || [];

      categoryProgress.push({
        category_id,
        category_name: categoryDetails?.name || 'Unknown',
        progress: progress, // Format percentage
        collaborators: collaboratorDetails.length > 0 ? collaboratorDetails : null
      });

      totalControls = 0;
      answeredControls = 0;
      childControls = [];
      progress = 0;
    }

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: categoryProgress }, httpStatus.OK);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

//************************************** Services for Python  *********************************************************************/

exports.getAllRopas = async (req, res) => {
  try {
    const { Controls, CustomerControls, Answers, User, ROPA, Category, Customer, Departments, Processes, Group, BasicInfoQuestions, BasicInfoAnswers } = db.models;
    const { entity_id } = req.query;
    const customer_id = req.data.customer_id;
    const condition = {
      customer_id: customer_id
    };
    if (entity_id) {
      condition.group_id = entity_id;
    }

    console.log(condition);
    // Step 1: Get all ROPAs for the given customer_id
    const ropaList = await commonService.getListWithoutCount(ROPA, condition, ['id', 'status', 'assigned_to', 'approver', 'department_id', 'process_id', 'risks']);

    if (!ropaList || ropaList.length === 0) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    let allRopaData = [];

    for (const ropa of ropaList) {
      if (ropa.status !== constant.status.COMPLETED) {
        continue; // Skip ROPAs that are not completed
      }

      const controlsAttributes = [
        [sequelize.literal(`"CustomerControls"."id"`), 'customer_question_id'],
        'question_id',
        'category_id',
        'parent_id',
        'is_custom',
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."title" ELSE "Control"."title" END`), 'title'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."description" ELSE "Control"."description" END`), 'description'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."artifact_type" AS TEXT) ELSE CAST("Control"."artifact_type" AS TEXT) END`), 'artifact_type'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."is_attachment" ELSE "Control"."is_attachment" END`), 'is_attachment'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."question" ELSE "Control"."question" END`), 'question'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."fields" ELSE "Control"."fields" END`), 'fields'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input" ELSE "Control"."extra_input" END`), 'extra_input'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN CAST("CustomerControls"."extra_input_type" AS TEXT) ELSE CAST("Control"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."is_custom" THEN "CustomerControls"."extra_input_fields" ELSE "Control"."extra_input_fields" END`), 'extra_input_fields'],
        [sequelize.literal(`CASE WHEN "CustomerControls"."question_id" IS NOT NULL THEN "Control"."endpoint" ELSE NULL END`), 'endpoint']
      ];
      // Step 2: Fetch Controls Data for this ROPA
      const controls = await ropaService.getControlsWithCategory(
        CustomerControls,
        Controls,
        Answers,
        User,
        Category,
        { ropa_id: ropa.id },
        {},
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'name', 'ropa_level'],
        [['question_id', 'ASC']]
      );

      let parents = controls.filter(control => control.parent_id === null);
      const childrenMap = controls.reduce((map, control) => {
        if (control.parent_id !== null) {
          if (!map[control.parent_id]) {
            map[control.parent_id] = [];
          }
          map[control.parent_id].push(control);
        }
        return map;
      }, {});

      parents.forEach(parent => {
        parent.children = childrenMap[parent.customer_question_id] || [];
      });
      // Step 5: Transform Data for Excel Output
      const excelData = transformDataForAllRopa(parents);
      // Store in allRopaData array
      allRopaData.push({
        ropa_id: ropa.id,
        excelData
      });
    }

    return response.success(req, res, { msgCode: 'ROPA_FETCHED', data: allRopaData }, httpStatus.OK);
  } catch (error) {
    console.error('Error fetching ROPA records:', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.collaboratorProgressV2 = async (req, res) => {
  try {
    const { Category, ROPA, CustomerControls, Answers, ReviewROPA, Collaborator, User } = db.models;
    const ropaId = req.params.ropa_id;

    const ropa = await commonService.findByCondition(ROPA, { id: ropaId }, ['status']);
    if (!ropa) {
      return response.error(req, res, { msgCode: 'ROPA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const findCategoryAndDetails = await commonService.getDistinct(CustomerControls, { ropa_id: ropaId }, ['category_id'], ['category_id']);
    findCategoryAndDetails.sort((a, b) => a.category_id - b.category_id);
    if (!findCategoryAndDetails) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    let categoryProgress = [];

    for (let category of findCategoryAndDetails) {
      const category_id = category.category_id;

      let totalControls = 0;
      let answeredControls = 0;
      let childControls = [];
      let progress = 0;

      let controls = null;
      const condition = { ropa_id: ropaId, category_id };

      if (ropa.status === constant.status.STARTED || ropa.status === constant.status.COMPLETED || ropa.status === constant.status.UNDER_REVIEW) {
        controls = await commonService.getListAssociateWithoutCount(CustomerControls, Answers, condition, {}, ['id', 'parent_id'], ['id']);
        controls?.forEach(control => {
          if (control.parent_id === null) {
            totalControls++;
            if (control.Answer) answeredControls++;
          } else {
            childControls.push(control);
          }
        });
      } else if (ropa.status === constant.status.CHANGES_REQUESTED) {
        controls = await ropaService.getControlsWithAnswersAndReviews(
          CustomerControls,
          Answers,
          ReviewROPA,
          condition,
          {},
          {},
          ['id', 'parent_id'],
          ['updatedAt'],
          ['accurate_information', 'updatedAt']
        );
      
        const parentControls = controls.rows?.filter(control =>
          control?.parent_id === null && control?.ReviewROPA  // only where review marked answer as accurate
        );
        parentControls?.forEach(control => {
          totalControls++;
          if ((control.Answer?.updatedAt > control.ReviewROPA?.updatedAt) || (control.Answer && control?.ReviewROPA?.accurate_information === 1)) {
            answeredControls++;
          }
        });
        
        childControls = controls.rows?.filter(ctrl =>
          ctrl.parent_id !== null &&
          ctrl.Answer // only include reviewed-accurate child controls
        );
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});
      
        Object.entries(childControlsByParent)?.forEach(([parentId, childList]) => {
          const parentControl = parentControls?.find(control => control.id == parentId);
          if (parentControl && childList.every(ctrl => ((ctrl.Answer?.updatedAt > parentControl?.ReviewROPA?.updatedAt) || (ctrl?.Answer && ctrl?.ReviewROPA?.accurate_information === 1)))) {
            answeredControls += 1;
          }
        });
      }
      

      if (ropa.status !== constant.status.CHANGES_REQUESTED) {
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.values(childControlsByParent)?.forEach(childList => {
          if (childList.every(control => control.Answer)) {
            answeredControls += 1;
          }
        });
      }

      progress = totalControls ? parseFloat(((answeredControls / totalControls) * 100).toFixed(2)) : 0;

      const details = await commonService.getListWith3Models(Category, Collaborator, User, { id: category_id }, { ropa_id: ropaId, category_id }, {}, ['id', 'name'], {}, ['id', 'firstName', 'lastName', 'email']);
      const categoryDetails = details[0] || {};
      const collaborators = categoryDetails?.Collaborators?.map(collab => ({
        id: collab.User?.id,
        name:`${collab.User?.firstName} ${collab.User?.lastName}`
      })) || [];


      categoryProgress.push({
        category_id,
        category_name: categoryDetails?.name || 'Unknown',
        progress,
        collaborators: collaborators.length ? collaborators : null
      });
    }

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: categoryProgress }, httpStatus.OK);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};