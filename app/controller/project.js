const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const randomToken = require('random-token');


exports.createClient = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        // importing the Project model 
        const { Project } = db.models;
        
        // check domain name
        const checkDomain = await commonService.findByCondition(Project, { domain_name: req.body.domain_name });
        if (checkDomain) {
            return response.error(req, res, { msgCode: 'DOMAIN_NAME_ALREADY_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        //creating token for client of length 20
        const salt = req.body.domain_name + Date.now();
        const token = randomToken.create(salt)(20);

        req.body.token = token;
        const responseData = {
            token: token
        };

        // req.body.ip = req.ip;
        // create User
        const createClient = await commonService.addDetail(Project, req.body, dbTrans);
        if (!createClient) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "CLIENT_REGISTRATION_SUCCESSFUL", data: responseData }, httpStatus.OK, dbTrans);
    } catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.aboutUs =  async(req, res) => {
    try{
        // importing the Project model 
        const { ProjectDetail } = db.models;
        
        // check domain name
        const checkproject = await commonService.findByCondition(ProjectDetail, { client_name: req.params.client_name });
        if (!checkproject) {
            return response.error(req, res, { msgCode: 'CLIENT_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
        }
        return response.success(req, res, { msgCode: "API_SUCCESS", data: checkproject }, httpStatus.OK);

    }catch (error) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};