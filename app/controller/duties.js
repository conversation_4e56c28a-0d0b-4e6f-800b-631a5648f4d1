const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');



exports.createDuty = async (req, res) => {
    const dbTrans = db.transaction();
    try {
        const { Duties } = db.models;
        const addDuty = await commonService.addDetail(Duties, req.body, dbTrans);
        if(!addDuty){
            return response.error( req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans); 
        }
        return response.success( req, res, { msgCode: 'DUTY_CREATED', data: addDuty }, httpStatus.OK, dbTrans );
    }catch (er){
        console.log("Errror",er);
        return response.error( req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.countDuty = async (req, res) => {
    try {
        const { Duties } = db.models;
        const dutyData = await commonService.getListGroupBy(Duties, { customer_id: req.data.customer_id, status: policyConstant.POLICY_STATUS[3] }, [`status`, [sequelize.fn('COUNT', 'id'), 'count']], [`status`]);
        if (!dutyData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        let count = 0;
        dutyData?.forEach(row => {
            count = count + parseInt(row.count);
        });

        dutyData.push({ status: 'TOTAL', count: String(count) ?? 0 });


        return response.success(req, res, { msgCode: "API_SUCCESS", data: dutyData }, httpStatus.OK);
    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}