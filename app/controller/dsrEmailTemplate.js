const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const constant = require('../constant/PDA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
// const { deleteFile } = require('../utils/delete-files');
const { sendMail } = require('../config/email');


exports.mailListing = async (req, res) => {
    try{
        const { EmailTemplate } = db.models;
        const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];
        const list = await commonService.getList( EmailTemplate, {customer_id: req.params.customer_id }, {}, limit, offset, order );
        if(!list){
            return response.error( req, res, { msgCode: 'MAIL_LIST_ERROR' }, httpStatus.BAD_REQUEST );
        }

        return response.success(req, res, { msgCode: 'MAIL_LIST_FETCHED', data: list }, httpStatus.OK );
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.createMail = async(req, res) => {
    const dbTrans = await db.transaction(); 
    try{
        const { EmailTemplate } = db.models;
        req.body.customer_id = req.data.customer_id;
        const mail = await commonService.addDetail( EmailTemplate, req.body, dbTrans );
               
        if(!mail){
            return response.error( req, res, { msgCode: 'MAIL_CREATE_ERROR' }, httpStatus.BAD_REQUEST , dbTrans);
        }
        return response.success( req, res, { msgCode: 'MAIL_CREATED', data: mail }, httpStatus.OK , dbTrans );
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.updateMail = async(req, res) => {
    try{
        const { EmailTemplate } = db.models;
        
        const check = await commonService.findByCondition(EmailTemplate, { id: req.params.id });
        if (!check) {
            return response.error(req, res, { msgCode: 'MAIL_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
        }

        const mail = await commonService.updateData( EmailTemplate, req.body, {id : req.params.id} );
               
        if(!mail){
            return response.error( req, res, { msgCode: 'MAIL_UPDATE_ERROR' }, httpStatus.BAD_REQUEST );
        }
        return response.success( req, res, { msgCode: 'MAIL_UPDATED', data: mail }, httpStatus.OK  );
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.deleteMail = async(req, res) => {
    try{
        const { EmailTemplate } = db.models;
        
        const check = await commonService.findByCondition(EmailTemplate, { id: req.params.id });
        if (!check) {
            return response.error(req, res, { msgCode: 'MAIL_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
        }

        const mail = await commonService.deleteQuery( EmailTemplate, {id : req.params.id} );
               
        if(!mail){
            return response.error( req, res, { msgCode: 'MAIL_DELETE_ERROR' }, httpStatus.BAD_REQUEST );
        }
        return response.success( req, res, { msgCode: 'MAIL_DELETE', data: mail }, httpStatus.OK  );
    }catch(err){
        console.log(" error", err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};