const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const privacyOpsService = require('../services/privacyOps');
const { deleteFile } = require('../utils/delete-files');
const sequelize = require('sequelize');
const csv = require('csv-parser');
const fs = require('fs');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');

exports.regulationList = async (req, res) => {
  try {
    const { Regulations } = db.models;
    const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let searchCondition = {};
    if (search) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('abbreviation'), { [Op.iLike]: `%${search}%` })
          // sequelize.where(sequelize.col('applicable_regulation'), { [Op.iLike]: `%${search}%`}),
        ]
      };
    }

    const list = await commonService.getList(Regulations, { ...searchCondition }, {}, limit, offset, order);
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'REGULATION_LIST_FETCHED', data: list }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.regulationListV2 = async (req, res) => {
  try {
    const { RegulationsV2 } = db.models;
    const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let searchCondition = {};
    if (search) {
      searchCondition = {
        [Op.or]: [
          sequelize.where(sequelize.col('geography'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('mapping_column_header'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('source'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('authoritative_source'), { [Op.iLike]: `%${search}%` })
        ]
      };
    }

    const list = await commonService.getList(RegulationsV2, { customer_id: null, available: true, ...searchCondition }, {}, limit, offset, order);
    // const list = await commonService.getListGroupBy(RegulationsV2, {  ...searchCondition } , ["id", "geography", "mapping_column_header", "source", "authoritative_source", "version", "url", "createdAt", "updatedAt", "deletedAt", [sequelize.fn('COUNT', 'id'), 'count']], ['geography'], limit, offset, order);
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'REGULATION_LIST_FETCHED', data: list }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.customerRegulationList = async (req, res) => {
  try {
    const { CustomerRegulations, RegulationsV2, RegulationComplianceStatus } = db.models;
    const { page, size, search, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let searchCondition = { customer_id: req.data.customer_id, entity_id: req.query.entity_id };

    const regulations = await commonService.findByCondition(CustomerRegulations, { ...searchCondition }, {});
    if (!regulations) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }

    let regulationCondition = { id: { [Op.in]: regulations?.regulation_ids } };
    if (search) {
      regulationCondition = {
        ...regulationCondition,
        // id: { [Op.in]: regulations?.regulation_ids},
        [Op.or]: [
          // sequelize.where(sequelize.col('source'), { [Op.iLike]: `%${search}%` }),
          sequelize.where(sequelize.col('authoritative_source'), { [Op.iLike]: `%${search}%` })
          // sequelize.where(sequelize.col('mapping_column_header'), { [Op.iLike]: `%${search}%`}),
        ]
      };
    }

    const data = await commonService.getListAssociateWithoutCount(RegulationsV2, RegulationComplianceStatus, { ...regulationCondition }, { entity_id: req.query.entity_id }, {}, {}, limit, offset, order);
    if (!data) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'REGULATION_LIST_FETCHED', data: data }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getControls = async (req, res) => {
  try {
    const { UCFControl, UCFCustomControl } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let categoryCondition = { customer_id: req.data.customer_id };

    // const list = await commonService.getList(UCFControl, {}, {}, limit, offset, order);
    if (req.query.category_id) {
      categoryCondition = { ...categoryCondition, category_id: req.query.category_id };
    }

    const list = await commonService.getListAssociateWithCount(UCFCustomControl, UCFControl, { ...categoryCondition }, {}, {}, {}, limit, offset, order);

    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }

    const transformed = {
      count: list.count,
      rows: list.rows.map(row => {
        if (row.control_id !== null && row.UCFControl && Object.keys(row.UCFControl).length) {
          // Merge keys from UCFControl except 'id'
          Object.keys(row.UCFControl).forEach(key => {
            if (key !== 'id') {
              row[key] = row.UCFControl[key];
            }
          });
        }
        // Remove the UCFControl property regardless of control_id
        delete row.UCFControl;
        return row;
      })
    };

    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: transformed }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getCategories = async (req, res) => {
  try {
    const { UCFCategory, UCFCustomCategory } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    const list = await commonService.getListAssociateWithCount(UCFCustomCategory, UCFCategory, { customer_id: req.data.customer_id }, {}, {}, {}, limit, offset, order);

    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }

    const transformed = {
      count: list.count,
      rows: list.rows.map(row => {
        if (row.category_id !== null && row.UCFCategory && Object.keys(row.UCFCategory).length) {
          // Merge keys from UCFCategory except 'id'
          Object.keys(row.UCFCategory).forEach(key => {
            if (key !== 'id') {
              row[key] = row.UCFCategory[key];
            }
          });
        }
        // Remove the UCFControl property regardless of control_id
        delete row.UCFCategory;
        return row;
      })
    };

    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: transformed }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.complaiRegulationAndBusinessReq = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerRegulations, UCFControl, UCFBusinessRequirement, CustomerBusinessRequirements, UCFCustomControl, UCFCategory, UCFCustomCategory } = db.models;

    let check = await commonService.findByCondition(CustomerRegulations, { customer_id: req.body.customer_id, entity_id: req.body.entity_id }, {});

    console.log('---->>>>>', check);
    if (check) {
      const dbTrans2 = await db.transaction();
      const getCustomCategoryData = await commonService.getList(UCFCustomCategory, { customer_id: req.body.customer_id }, ['id']);
      // console.log('------========', getCustomCategoryData);
      if (!getCustomCategoryData.rows[0]) {
        const categoryData = await commonService.getList(UCFCategory, {}, ['id']);
        if (!categoryData) {
          await dbTrans.rollback();
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans2);
        }

        const transformedData = categoryData.rows.map(row => ({ category_id: row.id, customer_id: req.body.customer_id }));

        const addCategoryData = await commonService.bulkAdd(UCFCustomCategory, transformedData, dbTrans2);
        if (!addCategoryData) {
          await dbTrans.rollback();
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans2);
        }

        const controlData = await commonService.getList(UCFControl, {}, ['id', 'category_id']);
        if (!controlData) {
          await dbTrans.rollback();
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans2);
        }
        const transformedControlData = controlData.rows.map(row => {
          // Find matching category from addCategoryData
          const matchedCategory = addCategoryData.find(category => category.category_id === row.category_id);
          return {
            control_id: row.id,
            customer_id: req.body.customer_id,
            category_id: matchedCategory ? matchedCategory.id : null // Fallback to null if not found
          };
        });

        const addControlData = await commonService.bulkAdd(UCFCustomControl, transformedControlData, dbTrans2);
        if (!addControlData) {
          await dbTrans.rollback();
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans2);
        }
        
      }
      await dbTrans2.commit();
      // checking whether the regulation_ids are updated or not
      const sorted1 = [...check.regulation_ids].sort();
      const sorted2 = [...req.body.regulation_ids].sort();
      if (sorted1.length === sorted2.length) {
        if (sorted1.every((value, index) => value === sorted2[index])) {

          return response.success(req, res, { msgCode: 'REGULATION_UPDATED' }, httpStatus.OK, dbTrans);
        }
      }

      const deleteData = await commonService.deleteQuery(CustomerBusinessRequirements, { customer_id: req.body.customer_id, group_id: req.body.entity_id }, dbTrans, true);
      // console.log('--------------------------------------------------');
      const regulation = await commonService.updateData(CustomerRegulations, { regulation_ids: req.body.regulation_ids }, { customer_id: req.body.customer_id, entity_id: req.body.entity_id }, dbTrans);
      // console.log('regulation---->>>', regulation);
      if (!regulation[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    } else {
      const dbTrans1 = await db.transaction();
      const addRegulation = await commonService.addDetail(CustomerRegulations, req.body, dbTrans1);
      // console.log(addRegulation);

      if (!addRegulation) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans1);
      }
      check = addRegulation;

      const categoryData = await commonService.getList(UCFCategory, {}, ['id']);
      if (!categoryData) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans1);
      }

      const transformedData = categoryData.rows.map(row => ({ category_id: row.id, customer_id: req.body.customer_id }));

      const getCustomCategoryData = await commonService.getList(UCFCustomCategory, { customer_id: req.body.customer_id }, ['id']);
      if (getCustomCategoryData.rows[0]) {
        await commonService.deleteQuery(UCFCustomCategory, { customer_id: req.body.customer_id }, dbTrans1, true);
      }
      const addCategoryData = await commonService.bulkAdd(UCFCustomCategory, transformedData, dbTrans1);
      if (!addCategoryData) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans1);
      }

      const controlData = await commonService.getList(UCFControl, {}, ['id', 'category_id']);
      if (!controlData) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans1);
      }
      // const transformedControlData = controlData.rows.map(row => ({ control_id: row.id, customer_id: organisationData.id }));
      const transformedControlData = controlData.rows.map(row => {
        // Find matching category from addCategoryData
        const matchedCategory = addCategoryData.find(category => category.category_id === row.category_id);
        return {
          control_id: row.id,
          customer_id: req.body.customer_id,
          category_id: matchedCategory ? matchedCategory.id : null // Fallback to null if not found
        };
      });

      const getCustomControlData = await commonService.getList(UCFCustomControl, { customer_id: req.body.customer_id }, ['id']);
      if (getCustomControlData.rows[0]) {
        await commonService.deleteQuery(UCFCustomControl, { customer_id: req.body.customer_id }, dbTrans1, true);
      }
      const addControlData = await commonService.bulkAdd(UCFCustomControl, transformedControlData, dbTrans1);
      if (!addControlData) {
        await dbTrans.rollback();
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans1);
      }
      await dbTrans1.commit();
    }

    const data = await commonService.getListWith3Models(UCFCustomControl, UCFControl, UCFBusinessRequirement, { customer_id: req.body.customer_id }, {}, {}, {}, ['id'], ['id']);
    if (!data) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const customData = req.body.regulation_ids.flatMap(regulation_id =>
      data.flatMap(control => {
        if (control.control_id) {
          return control.UCFControl.UCFBusinessRequirements.map(busiReq => ({
            customer_id: req.body.customer_id,
            control_id: control.id,
            busi_req_id: busiReq.id,
            group_id: req.body.entity_id,
            regulation_id: regulation_id
          }));
        } else {
          return [];
        }
      })
    );

    const getcustomData = await commonService.getList(CustomerBusinessRequirements, { customer_id: req.body.customer_id, group_id: req.body.entity_id, regulation_id: check.regulation_ids[0], is_custom: true }, {});

    // console.log(getcustomData);

    req.body.regulation_ids.forEach(regulation_id => {
      getcustomData.rows.forEach(data => {
        customData.push({
          customer_id: req.body.customer_id,
          control_id: data.control_id,
          busi_req_id: null,
          group_id: req.body.entity_id,
          regulation_id: regulation_id,
          reference_no: data.reference_no,
          business_requirement: data.business_requirement,
          articles: [],
          is_custom: true
        });
      });
    });

    // console.log(getcustomData);
    const getData = await commonService.getList(CustomerBusinessRequirements, { customer_id: req.body.customer_id, group_id: req.body.entity_id }, ['id']);
    // console.log('getData', getData);
    if (getData.rows[0]) {
      await commonService.deleteQuery(CustomerBusinessRequirements, { customer_id: req.body.customer_id, group_id: req.body.entity_id }, dbTrans, true);
    }

    const addData = await commonService.bulkAdd(CustomerBusinessRequirements, customData, dbTrans);
    if (!addData) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'REGULATION_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

// exports.customBusinessReq = async (req, res) => {
//     const dbTrans = await db.transaction();
//     try {
//         const { CustomerBusinessRequirements, UCFControl, UCFBusinessRequirement, RegulationBusiRequirementMapping } = db.models;
//         const data = await commonService.getListAssociateWithoutCount(UCFControl, UCFBusinessRequirement, {}, {}, ['id'], ['id']);
//         // console.log(data[0]);
//         // Generate customData array
//         const customData = data.flatMap(control =>
//             control.UCFBusinessRequirements.map(busiReq => ({
//                 customer_id: 750,
//                 control_id: control.id,
//                 busi_req_id: busiReq.id
//             }))
//         );
//         const addData = await commonService.bulkAdd(CustomerBusinessRequirements, customData, dbTrans);
//         if(!addData){
//             return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
//         }

//         return response.success(req, res, { msgCode: "CUSTOM_REGULATION_ADDED", data: customData }, httpStatus.OK, dbTrans);
//     } catch (err) {
//         console.log('error', err);
//         return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
//     }
// };

exports.getBusinessRequirements = async (req, res) => {
  try {
    const { CustomerBusinessRequirements, UCFBusinessRequirement, RegulationBusiRequirementMapping, Policy } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC', group_id } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    let searchCondition = { customer_id: req.data.customer_id, control_id: req.params.control_id, regulation_id: req.query.regulation_id };

    if (group_id) {
      searchCondition = { group_id: group_id, ...searchCondition };
    }

    const list = await commonService.getListWith4Models(
      CustomerBusinessRequirements,
      UCFBusinessRequirement,
      RegulationBusiRequirementMapping,
      Policy,
      { ...searchCondition },
      {},
      { regulation_id: req.query.regulation_id },
      {},
      {},
      {},
      ['id', 'busi_requirement_id', 'regulation_id', 'articles'],
      ['id', 'name', 'description', 'recurrence', 'result'],
      limit,
      offset,
      order
    );
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }
    return response.success(req, res, { msgCode: 'BUSINESS_REQUIREMENTS', data: list }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getControlWiseBusiReq = async (req, res) => {
  try {
    const { CustomerBusinessRequirements, UCFBusinessRequirement, RegulationBusiRequirementMapping, RegulationsV2, CustomerRegulations, Group } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    // console.log(req.data);
    const groups = await commonService.getList(Group, { customer_id: req.data.customer_id }, ['id']);
    let searchCondition = { customer_id: req.data.customer_id, control_id: req.params.control_id, group_id: groups.rows[0].id };
    const reg_ids = await commonService.getList(CustomerRegulations, { customer_id: req.data.customer_id }, {}, null, null, [['id', 'ASC']]);
    const allRegulationIds = reg_ids.rows.flatMap(row => row.regulation_ids);
    // console.log('All Regulation IDs:', allRegulationIds);

    let list = await privacyOpsService.getListWith4Models(
      CustomerBusinessRequirements,
      UCFBusinessRequirement,
      RegulationBusiRequirementMapping,
      RegulationsV2,
      { ...searchCondition, regulation_id: reg_ids?.rows[0]?.regulation_ids[0] },
      {},
      { regulation_id: { [Op.in]: allRegulationIds } },
      {},
      {},
      {},
      ['id', 'busi_requirement_id', 'regulation_id', 'articles'],
      ['authoritative_source']
    );

    // const list = await privacyOpsService.getListWith4ModelsV2(
    //   UCFBusinessRequirement,
    //   CustomerBusinessRequirements,
    //   RegulationBusiRequirementMapping,
    //   RegulationsV2,
    //   {},
    //   { ...searchCondition },
    //   { regulation_id: { [Op.in]: reg_ids?.rows[0]?.regulation_ids } },
    //   {},
    //   {},
    //   ['id', 'customer_id', 'group_id'],
    //   ['id', 'busi_requirement_id', 'regulation_id', 'articles'],
    //   {}
    // );
    // console.log('wwwww', list[0]['UCFBusinessRequirement'].RegulationBusiRequirementMappings);
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }

    if (list[0]?.is_custom === true) {
      const data = await commonService.getList2(CustomerBusinessRequirements, RegulationsV2, { customer_id: req.data.customer_id, regulation_id: { [Op.in]: allRegulationIds }, control_id: req.params.control_id }, {}, {}, {});
      // console.log('data', list);
      const groupedByBusinessRequirement = data?.rows.reduce((acc, item) => {
        const key = item.business_requirement || 'Unknown';
        if (!acc[key]) {
          acc[key] = [];
        }
        // acc[key].push(item)
        acc[key].push({
          id: item.id,
          Regula: item?.RegulationsV2?.authoritative_source,
          customer_id: item?.RegulationsV2?.customer_id,
          articl: item.articles,
          regula: item.regulation_id
        });

        return acc;
      }, {});
      // console.log('groupedByBusinessRequirement', groupedByBusinessRequirement);

      const enrichedData = list.map(entry => {
        const businessReq = entry.business_requirement || 'Unknown';

        const relatedMappings = groupedByBusinessRequirement[businessReq] || [];
        return {
          ...entry,
          RegulationBusiRequirementMappings: relatedMappings
        };
      });

      return response.success(req, res, { msgCode: 'BUSINESS_REQUIREMENTS', data: enrichedData }, httpStatus.OK);
    }

    // console.log(list);
    return response.success(req, res, { msgCode: 'BUSINESS_REQUIREMENTS', data: list }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCategoryControlBusiReq = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { UCFCustomCategory, UCFCustomControl, UCFBusinessRequirement, CustomerBusinessRequirements, RegulationBusiRequirementMapping, Group, CustomerRegulations } = db.models;
    // const groups = await commonService.getList(Group, { customer_id: req.data.customer_id }, ['id']);
    // console.log('----->>>groups', groups);
    const reg_ids = await commonService.getList(CustomerRegulations, { customer_id: req.data.customer_id }, {});
    // console.log('----->>>reg_ids', reg_ids);
    const categoryData = {
      category_name: req.body.category_name,
      category_no: req.body.category_no,
      customer_id: req.data.customer_id
    };
    const addCategory = await commonService.addDetail(UCFCustomCategory, categoryData, dbTrans);
    // console.log('addCategory', addCategory);
    if (!addCategory) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const controlData = {
      control_no: req.body.control_no,
      control_description: req.body.control_description,
      category_id: addCategory.id,
      customer_id: req.data.customer_id
    };
    const addControl = await commonService.addDetail(UCFCustomControl, controlData, dbTrans);
    // console.log('addControl', addControl);
    if (!addControl) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const busiReqData = [];

    let counter = -1;
    req.body.references.forEach(ref => {
      // console.log('ref', ref);
      counter++;
      reg_ids?.rows.forEach(data => {
        // console.log('ids--->', data?.regulation_ids);
        data?.regulation_ids.forEach(reg_id => {
          busiReqData.push({
            customer_id: data.customer_id,
            control_id: addControl.id,
            group_id: data.entity_id,
            regulation_id: reg_id,
            reference_no: ref,
            business_requirement: req.body.BRs[counter],
            articles: req.body.mapping[counter][reg_id],
            is_custom: true
          });
        });
      });
    });
    // console.log('busiReqData', busiReqData);
    const addBusiReq = await commonService.bulkAdd(CustomerBusinessRequirements, busiReqData, dbTrans);
    if (!addBusiReq) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'BUSINESS_REQUIREMENTS_ADDED', data: addBusiReq }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getCustomerRegulations = async (req, res) => {
  try {
    const { CustomerRegulations, RegulationsV2 } = db.models;

    const list = await commonService.getList(CustomerRegulations, { customer_id: req.data.customer_id }, ['regulation_ids']);

    const ids = [...new Set(list.rows.flatMap(row => row.regulation_ids))];

    const data = await commonService.getList(RegulationsV2, { id: { [Op.in]: ids } }, ['id', 'source', 'authoritative_source']);
    if (!data.rows[0]) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'REGULATION_LIST_FETCHED', data: data }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.createRegulation = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerRegulations, RegulationsV2, CustomerBusinessRequirements, UCFCustomControl, UCFControl, UCFBusinessRequirement, RegulationBusiRequirementMapping } = db.models;
    req.body.customer_id = req.data.customer_id;
    const addRegulation = await commonService.addDetail(RegulationsV2, req.body, dbTrans);
    if (!addRegulation) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const getRegulation = await commonService.getList(CustomerRegulations, { entity_id: { [Op.in]: req.body.group_ids } }, {});

    getRegulation.rows.forEach(async data => {
      data.regulation_ids.push(addRegulation.id);
      const updateRegulation = await commonService.updateData(CustomerRegulations, { regulation_ids: data.regulation_ids }, { entity_id: data.entity_id }, dbTrans);
      if (!updateRegulation[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    });

    const data = await commonService.getListWith3Models(UCFCustomControl, UCFControl, UCFBusinessRequirement, { customer_id: req.data.customer_id }, {}, {}, {}, ['id'], ['id']);
    if (!data) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // console.log('data----', data);

    // const customData1 = req.body.group_ids.flatMap(groupId =>
    //   data.flatMap(control =>
    //     control.UCFControl.UCFBusinessRequirements.map(busiReq => ({
    //       customer_id: req.data.customer_id,
    //       control_id: control.id,
    //       busi_req_id: busiReq.id,
    //       group_id: groupId,
    //       regulation_id: addRegulation.id
    //       // is_custom: true
    //     }))
    //   )
    // );

    const customData = req.body.group_ids.flatMap(groupId =>
      data.flatMap(control => {
        if (control.control_id) {
          return control.UCFControl.UCFBusinessRequirements.map(busiReq => ({
            customer_id: req.body.customer_id,
            control_id: control.id,
            busi_req_id: busiReq.id,
            group_id: groupId,
            regulation_id: addRegulation.id
          }));
        } else {
          return [];
        }
      })
    );

    // console.log(customData);

    const getcustomData = await commonService.getList(CustomerBusinessRequirements, { customer_id: req.data.customer_id, group_id: req.body.group_ids[0], regulation_id: getRegulation.rows[0].regulation_ids[0], is_custom: true }, {});

    // console.log(getcustomData);

    req.body.group_ids.forEach(groupId => {
      getcustomData?.rows?.forEach(data => {
        customData.push({
          customer_id: req.data.customer_id,
          control_id: data.control_id,
          busi_req_id: null,
          group_id: groupId,
          regulation_id: addRegulation.id,
          reference_no: data.reference_no,
          business_requirement: data.business_requirement,
          articles: [],
          is_custom: true
        });
      });
    });

    // console.log(customData[0]);

    // console.log(customData[customData.length - 1]);

    const addData = await commonService.bulkAdd(CustomerBusinessRequirements, customData, dbTrans);
    if (!addData) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const mappingData = data.flatMap(control => {
      if (control.control_id) {
        return control.UCFControl.UCFBusinessRequirements.map(busiReq => ({
          customer_id: req.data.customer_id,
          control_id: control.id,
          busi_requirement_id: busiReq.id,
          regulation_id: addRegulation.id,
          articles: []
        }));
      } else {
        return [];
      }
    });

    // console.log(mappingData);
    const addMappingData = await commonService.bulkAdd(RegulationBusiRequirementMapping, mappingData, dbTrans);
    if (!addMappingData) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    // ['id'];

    return response.success(req, res, { msgCode: 'REGULATION_ADDED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateBusiReqArticles = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerBusinessRequirements, RegulationBusiRequirementMapping } = db.models;
    req.body.forEach(async data => {
      if (data.is_custom === true && data.customer_id !== null) {
        const updateData = await commonService.updateData(CustomerBusinessRequirements, { articles: data.articles }, { id: data.id }, dbTrans);
        if (!updateData[1]) {
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      } else {
        const updateMapping = await commonService.updateData(RegulationBusiRequirementMapping, { articles: data.articles }, { id: data.id }, dbTrans);
        if (!updateMapping[1]) {
          return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    });

    return response.success(req, res, { msgCode: 'BUSINESS_REQUIREMENTS_UPDATED' }, httpStatus.OK, dbTrans);
  } catch {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

// exports.detailBusinesRequirement = async (req, res) => {
//   try {
//     const { CustomerBusinessRequirements, UCFBusinessRequirement, RegulationBusiRequirementMapping, RegulationsV2, CustomerRegulations } = db.models;
//     const { page, size, sort_by = 'id', sort_order = 'ASC', group_id } = req.query;
//     const { limit, offset } = getPagination(page, size);
//     const order = [[sort_by, sort_order]];
//     let searchCondition = { customer_id: req.data.customer_id, id: req.params.busi_req_id };
//     // if (group_id) {
//     //   searchCondition = { group_id: group_id, ...searchCondition };
//     // }

//     const reg_ids = await commonService.getList(CustomerRegulations, { customer_id: req.data.customer_id }, {});
//     // console.log(reg_ids.rows[0].regulation_ids);

//     const list = await privacyOpsService.getListWith4Models(
//       CustomerBusinessRequirements,
//       UCFBusinessRequirement,
//       RegulationBusiRequirementMapping,
//       RegulationsV2,
//       { ...searchCondition },
//       {},
//       { regulation_id: { [Op.in]: reg_ids?.rows[0]?.regulation_ids } },
//       {},
//       ['id', 'customer_id', 'control_id', 'busi_req_id', 'reference_no', 'business_requirement'],
//       ['id', 'control_id', 'reference_no', 'business_requirement'],
//       ['id', 'busi_requirement_id', 'regulation_id', 'articles'],
//       ['id', 'source', 'authoritative_source'],
//       limit,
//       offset,
//       order
//     );

//     // const list = await commonService.getListWith3Models(
//     //   CustomerBusinessRequirements,
//     //   RegulationBusiRequirementMapping,
//     //   RegulationsV2,
//     //   { ...searchCondition },
//     //   {},
//     //   { regulation_id: { [Op.in]: reg_ids?.rows[0]?.regulation_ids } },
//     //   {},
//     //   {},
//     //   {},
//     //   limit,
//     //   offset,
//     //   order
//     // );
//     if (!list) {
//       return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
//     }
//     return response.success(req, res, { msgCode: 'BUSINESS_REQUIREMENTS', data: list }, httpStatus.OK);
//   } catch (err) {
//     console.log('error', err);
//     return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
//   }
// };

exports.getStatus = async (req, res) => {
  try {
    const { CustomerBusinessRequirements, UCFBusinessRequirement, RegulationBusiRequirementMapping } = db.models;
    const list = await commonService.getListWith3Models(
      CustomerBusinessRequirements,
      UCFBusinessRequirement,
      RegulationBusiRequirementMapping,
      { customer_id: req.data.customer_id, control_id: req.params.control_id },
      {},
      { regulation_id: req.query.regulation_id },
      {},
      {},
      ['id', 'busi_requirement_id', 'regulation_id', 'articles'],
      limit,
      offset,
      order
    );
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.allDocs = async (req, res) => {
  try {
    const { BusinessRequirementPolicy } = db.models;
    const list = await commonService.getList(BusinessRequirementPolicy, { customer_id: req.data.customer_id, regulation_id: req.params.regulation_id }, {});
    if (!list) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.BAD_REQUEST);
    }
    return response.success(req, res, { msgCode: 'DOCUMENTS_FETCHED', data: list }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.uploadControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Regulations, UCFCategory, UCFControl, UCFBusinessRequirement, RegulationBusiRequirementMapping } = db.models;

    const requiredHeaders = [
      'Control Category',
      'Control Number',
      'Control Description',
      'Reference',
      'Business Requirement',
      'EU GDPR',
      'EU GDPR Recital',
      'UK GDPR (DPA 2018)',
      'UAE PDPL',
      'DoH Privacy',
      'UAE CPR/CPS',
      'DFIC Privacy Regulation',
      'NIST DPF',
      'ISO 27701',
      'ADGM',
      'ADHICS',
      'CB UAE (SVF Regulations)',
      'DPDPA',
      'Summary of In-Scope Regulations',
      'Required Documentation'
    ];
    // console.log(req.files)

    const { isValid, missingHeader } = await pdaService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      await deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const controls = [];
    const uniqueCategories = new Map();
    const uniqueControls = new Map();
    // const regulations = await commonService.getList(Regulations, {}, {});

    const processRow = (row, num) => {
      const controlData = {
        category_name: row['Control Category'],
        control_no: row['Control Number'],
        control_description: row['Control Description'],
        reference: row['Reference'],
        business_requirement: row['Business Requirement'],
        required_docs: row['Required Documentation'],
        in_scope_regulations: row['Summary of In-Scope Regulations'],
        EU_GDPR: row['EU GDPR'],
        EU_GDPR_Recital: row['EU GDPR Recital'],
        UK_GDPR: row['UK GDPR (DPA 2018)'],
        UAE_PDPL: row['UAE PDPL'],
        DOH: row['DoH Privacy'],
        UAE_CPR_CPS: row['UAE CPR/CPS'],
        DFIC_Privacy: row['DFIC Privacy Regulation'],
        NIST_DPF: row['NIST DPF'],
        ISO_27701: row['ISO 27701'],
        ADGM: row['ADGM'],
        ADHICS: row['ADHICS'],
        CB_UAE_SVF: row['CB UAE (SVF Regulations)'],
        DPDPA: row['DPDPA']
      };

      if (!Object.values(controlData).every(x => x === '')) {
        if (!controlData.category_name && !controlData.control_description) {
          throw new Error('INVALID_DATA');
        }
        controls.push(controlData);
      }
    };

    const bulkInsertMappings = async (businessRequirementId, row) => {
      const regulationMappings = [
        { regulation_id: 1, article: row['EU_GDPR'] },
        { regulation_id: 2, article: row['EU_GDPR_Recital'] },
        { regulation_id: 3, article: row['UK_GDPR'] },
        { regulation_id: 4, article: row['UAE_PDPL'] },
        { regulation_id: 5, article: row['DOH'] },
        { regulation_id: 6, article: row['UAE_CPR_CPS'] },
        { regulation_id: 7, article: row['DFIC_Privacy'] },
        { regulation_id: 8, article: row['NIST_DPF'] },
        { regulation_id: 9, article: row['ISO_27701'] },
        { regulation_id: 10, article: row['ADGM'] },
        { regulation_id: 11, article: row['ADHICS'] },
        { regulation_id: 12, article: row['CB_UAE_SVF'] },
        { regulation_id: 13, article: row['DPDPA'] }
      ].map(mapping => ({
        busi_requirement_id: businessRequirementId,
        regulation_id: mapping.regulation_id,
        articles: [mapping.article]
      }));

      return await commonService.bulkAdd(RegulationBusiRequirementMapping, regulationMappings, dbTrans);
    };

    fs.createReadStream(req.files[0].path)
      .pipe(csv())
      .on('data', row => processRow(row))
      .on('end', async () => {
        let num = 0;
        for (const row of controls) {
          const categoryKey = row['category_name'];
          const controlKey = row['control_description'];
          // Handle category creation
          if (!uniqueCategories.has(categoryKey)) {
            let category = await commonService.findByCondition(UCFCategory, { category_name: categoryKey }, {});
            if (!category) {
              let data = {
                category_name: categoryKey,
                category_no: ++num,
                required_docs: row['required_docs']
              };
              category = await commonService.addDetail(UCFCategory, data, dbTrans);
              // console.log("--------",category)
            }
            uniqueCategories.set(categoryKey, category);
          }

          // Handle control creation
          if (!uniqueControls.has(controlKey)) {
            let control = await commonService.findByCondition(UCFControl, { control_description: controlKey }, {});
            if (!control) {
              let data = {
                control_no: row['control_no'],
                category_id: uniqueCategories.get(categoryKey).id,
                control_description: controlKey,
                in_scope_regulations: row['in_scope_regulations']
              };
              control = await commonService.addDetail(UCFControl, data, dbTrans);
              // console.log("--------",control)
            }
            uniqueControls.set(controlKey, control);
          }

          // Handle business requirement and mapping
          const businessRequirement = await commonService.addDetail(
            UCFBusinessRequirement,
            {
              reference_no: row['reference'],
              control_id: uniqueControls.get(controlKey).id,
              business_requirement: row['business_requirement']
            },
            dbTrans
          );
          console.log('--------', businessRequirement);
          if (!(await bulkInsertMappings(businessRequirement.id, row))) {
            throw new Error('ERROR_CREATING_MAPPING');
          }
        }

        await deleteFile(req.files[0].path);
        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      })
      .on('error', async err => {
        console.error('Stream error:', err);
        await deleteFile(req.files[0].path);
        return response.error(req, res, { msgCode: 'STREAM_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
      });
  } catch (err) {
    console.error('Error--------->>>>', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: err.message || 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
exports.updateCustomRequirement = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { CustomerBusinessRequirements } = db.models;
    const check = await commonService.findByCondition(CustomerBusinessRequirements, { id: req.params.id, customer_id: req.data.customer_id });

    if (!check) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const updateData = await commonService.updateData(CustomerBusinessRequirements, req.body, { id: req.params.id }, dbTrans);

    if (!updateData[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'REQUIREMENT_UPDATED', data: updateData[1] }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.error('Error--------->>>>', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.uploadAuthoritative = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { RegulationsV2 } = db.models;
    const data = [];
    const requiredHeaders = ['Geography', 'Mapping Column Header', 'Source', 'Authoritative Source - Statutory / Regulatory / Contractual / Industry Framework', 'Version', 'URL - Authoritative Source'];
    const { isValid, missingHeader } = await piaService.validateHeaders(req.files[0].path, requiredHeaders);

    if (!isValid) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }

    fs.createReadStream(req.files[0].path)
      .pipe(csv())
      .on('data', async row => {
        const uploadData = {
          geography: row['Geography'],
          mapping_column_header: row['Mapping Column Header'],
          source: row['Source'],
          authoritative_source: row['Authoritative Source - Statutory / Regulatory / Contractual / Industry Framework'],
          version: row['Version'],
          url: row['URL - Authoritative Source']
        };

        // Check if all properties of uploadData are empty
        if (!Object.values(uploadData).every(x => x === '')) {
          if (uploadData.mapping_column_header === '' && uploadData.authoritative_source === '') {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          data.push(uploadData);
        }
      })
      .on('end', async () => {
        // Insert the data into the database
        for (let row of data) {
          // if (!row['PIALevel'] || !row['piaCategory']) {
          //     await deleteFile(req.files[0].path);
          //     return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
          // }
          // const key = `${row['PIALevel']}_${row['piaCategory']}`;
          // if (row['title']) { // Parent question
          //     if (!uniqueCategories.has(key)) {
          //         // Create or retrieve category and store in the map
          //         const [pia_level, name] = key.split('_');
          //         let category = await commonService.findByCondition(piaCategory, { pia_level, name, customer_id: req.data.customer_id }, {});
          //         if (!category) {
          //             category = await piaCategory.create({ name, pia_level, customer_id: req.data.customer_id }, { transaction: dbTrans });
          //         }
          //         uniqueCategories.set(key, category);
          //     }
          //     let artifactType = null;
          //     if (row['artifact_type'] !== '') {
          //         artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase().trim()];
          //         if (!artifactType) {
          //             await deleteFile(req.files[0].path);
          //             return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
          //         }
          //     }
          //     let extraInputType = null;
          //     if (row['extra_input_type'] !== '') {
          //         extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
          //         if (!extraInputType) {
          //             await deleteFile(req.files[0].path);
          //             return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
          //         }
          //     }
          //     // Create parent control
          //     const control = await commonService.addDetail(piadata, {
          //         title: row['title'],
          //         description: row['description'],
          //         artifact_type: artifactType,
          //         customer_id: req.data.customer_id,
          //         question: row['question'],
          //         fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
          //         is_attachment: row['is_attachment'] === 'Yes',
          //         extra_input: row['extra_input'] === 'Yes',
          //         extra_input_type: extraInputType,
          //         extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
          //         category_id: uniqueCategories.get(key).id,
          //         parent_id: null,
          //         industry_vertical_id: 1
          //     }, dbTrans);
          //     if (!control) {
          //         await deleteFile(req.files[0].path);
          //         return response.error(req, res, { msgCode: "ERROR_CREATING_CONTROL" }, httpStatus.BAD_REQUEST, dbTrans);
          //     }
          //     // Update parent ID for potenpial child questions
          //     parentId = control.id;
          // } else { // Child question
          //     if (parentId) {
          //         // Create child control
          //         let artifactType = null;
          //         if (row['artifact_type'] !== '') {
          //             artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
          //             if (!artifactType) {
          //                 await deleteFile(req.files[0].path);
          //                 return response.error(req, res, { msgCode: "INVALID_ARTIFACT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
          //             }
          //         }
          //         let extraInputType = null;
          //         if (row['extra_input_type'] !== '') {
          //             extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
          //             if (!extraInputType) {
          //                 await deleteFile(req.files[0].path);
          //                 return response.error(req, res, { msgCode: "INVALID_EXTRA_INPUT_TYPE" }, httpStatus.BAD_REQUEST, dbTrans);
          //             }
          //         }
          //         childdataData.push({
          //             title: null,
          //             description: null,
          //             artifact_type: artifactType,
          //             customer_id: req.data.customer_id,
          //             question: row['question'],
          //             fields: row['fields'] ? row['fields'].split('\n').map(line => line.replace('\r', '')).map((name, id) => ({ id, name })) : null,
          //             is_attachment: row['is_attachment'] === 'Yes',
          //             extra_input: row['extra_input'] === 'Yes',
          //             extra_input_type: extraInputType,
          //             extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
          //             category_id: uniqueCategories.get(key).id,
          //             parent_id: parentId,
          //             industry_vertical_id: 1
          //         });
          //     } else {
          //         await deleteFile(req.files[0].path);
          //         return response.error(req, res, { msgCode: "INVALID_DATA" }, httpStatus.BAD_REQUEST, dbTrans);
          //     }
          // }
        }
        // Batch add data
        const uploadedData = await commonService.bulkAdd(RegulationsV2, data, dbTrans);
        if (!uploadedData) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        await deleteFile(req.files[0].path);

        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.complianceStatusPerControlPerRegulation = async (req, res) => {
  try {
    const { RegulationsV2, CustomerBusinessRequirements, CustomerRegulations, RegulationComplianceStatus } = db.models;

    const data = await commonService.getListGroupBy(
      CustomerBusinessRequirements,
      { group_id: req.params.group_id, regulation_id: req.query.regulation_id },
      ['control_id', 'compliance_status', [sequelize.fn('COUNT', 'compliance_status'), 'count']],
      ['control_id', 'compliance_status'],
      1000,
      0,
      [['control_id', 'ASC']]
    );
    if (!data) {
      return response.error(req, res, { msgCode: 'LIST_ERROR' }, httpStatus.NOT_FOUND);
    }

    const controlStats = {};
    let totalCompliant = 0,
      totalPartialCompliant = 0,
      totalNonCompliant = 0;
    data.forEach(entry => {
      const { control_id, compliance_status, count } = entry;
      const numCount = parseInt(count, 10);

      if (!controlStats[control_id]) {
        controlStats[control_id] = { compliant: 0, partial_compliant: 0, non_compliant: 0, total: 0 };
      }

      if (compliance_status === 'COMPLIANT') {
        controlStats[control_id].compliant += numCount;
        totalCompliant += numCount;
      } else if (compliance_status === 'PARTIAL_COMPLIANT') {
        controlStats[control_id].partial_compliant += numCount;
        totalPartialCompliant += numCount;
      } else {
        controlStats[control_id].non_compliant += numCount;
        totalNonCompliant += numCount;
      }

      controlStats[control_id].total += numCount;
    });

    const compliancePercentagePerControl = Object.keys(controlStats).map(control_id => {
      const { compliant, partial_compliant, non_compliant, total } = controlStats[control_id];
      const compliantPercentage = total > 0 ? ((compliant / total) * 100).toFixed(2) + '%' : '0.00%';
      const partialCompliantPercentage = total > 0 ? ((partial_compliant / total) * 100).toFixed(2) + '%' : '0.00%';
      const nonCompliantPercentage = total > 0 ? ((non_compliant / total) * 100).toFixed(2) + '%' : '0.00%';
      return {
        control_id: Number(control_id),
        compliant_percentage: compliantPercentage,
        partial_compliant_percentage: partialCompliantPercentage,
        non_compliant_percentage: nonCompliantPercentage
      };
    });

    const totalResponses = totalCompliant + totalPartialCompliant + totalNonCompliant;
    const overallCompliantPercentage = totalResponses > 0 ? ((totalCompliant / totalResponses) * 100).toFixed(1) + '%' : '0.0%';
    const overallPartialCompliantPercentage = totalResponses > 0 ? ((totalPartialCompliant / totalResponses) * 100).toFixed(1) + '%' : '0.0%';
    const overallNonCompliantPercentage = totalResponses > 0 ? ((totalNonCompliant / totalResponses) * 100).toFixed(1) + '%' : '0.0%';

    // console.log('Compliance Percentage Per Control:', compliancePercentagePerControl);
    // console.log('Overall Compliance Percentage:', overallCompliantPercentage);
    // console.log('Overall Partial-Compliance Percentage:', overallPartialCompliantPercentage);
    // console.log('Overall Non-Compliance Percentage:', overallNonCompliantPercentage);
    const statData = {
      compliancePercentagePerControl: compliancePercentagePerControl,
      overallCompliantPercentage: overallCompliantPercentage,
      overallPartialCompliantPercentage: overallPartialCompliantPercentage,
      overallNonCompliantPercentage: overallNonCompliantPercentage
    };
    const progressData = {
      customer_id: req.data.customer_id,
      entity_id: req.params.group_id,
      regulation_id: req.query.regulation_id,
      compliant_percentage: parseFloat(overallCompliantPercentage),
      partial_compliant_percentage: parseFloat(overallPartialCompliantPercentage),
      non_compliant_percentage: parseFloat(overallNonCompliantPercentage)
    };
    // console.log(progressData)
    const dbTrans = await db.transaction();
    const check = await commonService.findByCondition(RegulationComplianceStatus, { entity_id: req.params.group_id, regulation_id: req.query.regulation_id }, {});
    if (check) {
      const progressUpdate = await commonService.updateData(RegulationComplianceStatus, progressData, { entity_id: req.params.group_id, regulation_id: req.query.regulation_id }, dbTrans);
      if (!progressUpdate[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    } else {
      const addData = await commonService.addDetail(RegulationComplianceStatus, progressData, dbTrans);
      if (!addData) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    return response.success(req, res, { msgCode: 'REGULATION_LIST_FETCHED', data: statData }, httpStatus.OK, dbTrans);
  } catch (error) {
    console.error('Error--------->>>>', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
