const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');
const bcrypt = require('bcryptjs');
const salt = bcrypt.genSaltSync(10);
const { sendMail } = require('../config/email');
const { signToken } = require('../config/helper');
const { Op } = require('sequelize');
const { USER_ROLE, MESSAGE, ONBOARDING_STATUS } = require('../constant/common');
const randomToken = require('random-token');

exports.updatePassword = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { User, DeviceToken, OnboardingFlow } = db.models;

        const tokenData = req.data;
        req.body.email = req.body.email.toLowerCase();

        if (tokenData.email != req.body.email) {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        // check user is exist or not
        const checkUser = await commonService.findByCondition(User, { email: req.body.email });
        if (!checkUser) {
            return response.error(req, res, { msgCode: 'EMAIL_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const updateCondition = {
            id: checkUser.id
        }
        const hash = bcrypt.hashSync(req.body.password, salt);

        // generate token
        const token = await signToken({
            email: req.body.email,
            userId: checkUser.id,
            role: USER_ROLE[7],
            customer_id: checkUser.customer_id
        });

        //creating refresh token of length 20
        const tokenSalt = token + Date.now();
        const refreshToken = randomToken.create(tokenSalt)(100);

        const updateData = {
            password: hash,
            refresh_token: refreshToken
        }
        // update user password and refresh token
        const updateProfile = await commonService.updateData(User, updateData, updateCondition, dbTrans);
        if (!updateProfile[1]) {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
        };

        const updateOnboardingFlow = await commonService.updateData(OnboardingFlow, { step: ONBOARDING_STATUS.PASSWORD_UPDATED }, { user_id: checkUser.id }, dbTrans);
        if (!updateOnboardingFlow[1]) {
            return response.error(req, res, { msgCode: 'PASSWORD_UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        req.body.user_id = updateProfile[1].id;
        req.body.role = USER_ROLE[1];
        req.body.auth_token = token;

        // save token
        const saveDeviceToken = await commonService.addDetail(DeviceToken, req.body, dbTrans);
        if (!saveDeviceToken) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        delete updateProfile[1].password;
        delete updateProfile[1].access_token;
        delete updateProfile[1].createdAt;
        delete updateProfile[1].updatedAt;
        delete updateProfile[1].deletedAt;
        delete updateProfile[1].otp;

        updateProfile[1].token = token;

        const subject = `${MESSAGE.WELCOME}`;
        const textTemplate = "welcome_template.ejs";
        const sendData = {
            name: `${updateProfile[1].firstName} ${updateProfile[1].lastName}`
        };

        sendMail(
            req.body.email,
            sendData,
            subject,
            textTemplate,
        );

        return response.success(req, res, { msgCode: "PASSWORD_UPDATED", data: updateProfile[1] }, httpStatus.OK, dbTrans);

    } catch (error) {
        console.log("updatePassword", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.updateOrganisation = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { User, Customer, OnboardingFlow, Group } = db.models;

        // check organisation is exist or not
        const checkOrganisation = await commonService.findByCondition(Customer, { id: req.params.id });
        if (!checkOrganisation) {
            return response.error(req, res, { msgCode: 'ORGANISATION_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
        }

        // const emailExists = await commonService.findByCondition(Customer, { email: req.body.email });
        // if (emailExists) {
        //     return response.error(req, res, { msgCode: 'EMAIL_EXISTS' }, httpStatus.BAD_REQUEST, dbTrans);
        // }

        const user_email = req.data.email;

        // check if user belongs to the organisation
        const checkUser = await commonService.findByCondition(User, { email: user_email, customer_id: req.params.id });
        if (!checkUser) {
            return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }

        // update organisation
        req.body.status = 'active';
        const updateOrganisation = await commonService.updateData(Customer, req.body, { id: req.params.id }, dbTrans);
        if (!updateOrganisation[1]) {
            return response.error(req, res, { msgCode: 'ORGANISATION_UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const updateOnboardingFlow = await commonService.updateData(OnboardingFlow, { step: ONBOARDING_STATUS.PASSWORD_UPDATED }, { user_id: checkUser.id }, dbTrans);
        if (!updateOnboardingFlow[1]) {
            return response.error(req, res, { msgCode: 'ORGANISATION_UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        // Updating Group name
        const condition = {
            [Op.and]: [
                { customer_id: req.params.id },
                { name: null }
            ]
        };
        const updateGroup = await commonService.updateData(Group, { name: `${req.body.name}` }, condition, dbTrans);
        if (!updateGroup[1]) {
            return response.error(req, res, { msgCode: 'SAVE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }


        return response.success(req, res, { msgCode: "ORGANISATION_CREATED" }, httpStatus.OK, dbTrans);
    } catch (error) {
        console.log("updateOrganisation", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

// exports.billingAddress = async (req, res) => {
//     try {
//     const { BillingAddress } = db.models;
//     const dbTrans = await db.transaction();
//         // Create Billing address
//         const createBillingAddress = await commonService.addDetail(BillingAddress, req.body, dbTrans);
//         if (!createBillingAddress) {
//             return response.error(req, res, { msgCode: 'BILLING_ADDRESS_CREATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
//         }

//         return response.success(req, res, { msgCode: "BILLING_ADDRESS", data: createBillingAddress }, httpStatus.OK, dbTrans);
//     } catch (error) {
//         console.log("billingAddress", error);
//         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
//     }
// };

exports.getServices = async (req, res) => {
    try {
        const { Services } = db.models;
        // get Servicess data
        const getServicesData = await commonService.getListWithoutCount(Services);
        if (getServicesData.length < 0) {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        return response.success(req, res, { msgCode: "SERVICE_FETCHED", data: getServicesData }, httpStatus.OK);
    } catch (error) {
        console.log("getServices", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getQuestionnaires = async (req, res) => {
    try {
        const { Questionnaires } = db.models;
        const { ambition_id, page, size } = req.query;
        const { limit, offset } = getPagination(page, size);
        // get Questionnaires data
        const getQuestionnairesData = await commonService.getList(Questionnaires, { ambition_id: { [Op.in]: ambition_id } }, ['id', 'question', 'ambition_id'], limit, offset);

        return response.success(req, res, { msgCode: "QUESTIONNAIRES_FETCHED", data: getQuestionnairesData }, httpStatus.OK);
    } catch (error) {
        console.log("getQuestionnaires", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getPackages = async (req, res) => {
    try {
        const { Packages, PackagesInfo } = db.models;

        // get packages data
        const getPackagesData = await commonService.getListAssociateWithoutCount(Packages, PackagesInfo, {}, {}, ['id', 'name', 'price', 'description'], ['id', 'title', 'description']);
        if (getPackagesData.length <= 0) {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        return response.success(req, res, { msgCode: "PACKAGES_FETCHED", data: getPackagesData }, httpStatus.OK);
    } catch (error) {
        console.log("getPackages", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getPackagesDetails = async (req, res) => {
    try {
        const { PackagesInfo } = db.models;

        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);
        // get packages Info data
        const getPackagesData = await commonService.getList(PackagesInfo, { package_id: req.params.id }, ['id', 'title', 'description'], limit, offset);

        return response.success(req, res, { msgCode: "PACKAGES_FETCHED", data: getPackagesData }, httpStatus.OK);
    } catch (error) {
        console.log("getPackagesDetails", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getAmbitions = async (req, res) => {
    try {
        const { Ambitions } = db.models;
        // get Services data
        const getAmbitionData = await commonService.getListWithoutCount(Ambitions);
        if (getAmbitionData.length < 0) {
            return response.error(req, res, { msgCode: 'NOT_FOUND' }, httpStatus.NOT_FOUND);
        }

        return response.success(req, res, { msgCode: "AMBITION_CREATED", data: getAmbitionData }, httpStatus.OK);
    } catch (error) {
        console.log("getAmbitions", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getSidebar = async (req, res) => {
    try {
        const { User, Resources, Role, UserRolePrivileges } = db.models;

        const user_id = req.data.userId;

        // get user data
        const getUserData = await commonService.getListWithTripleAssociate(User, Role, UserRolePrivileges, Resources, { id: user_id }, {}, {}, { status: 1 }, ['id', 'firstName', 'lastName', 'email', 'role_id', 'customer_id'], ['id', 'role_name'], ['user_role_priviledge_id', 'role_id', 'resource_id'], ['resource_id', 'resource_name', 'description', 'parent_id', 'icon', 'route', 'order']);
        if (getUserData.count <= 0) {
            return response.error(req, res, { msgCode: 'NO_RESOURCES' }, httpStatus.NOT_FOUND);
        }

        const resources = {};
        const result = getUserData?.rows[0]?.Role.UserRolePrivileges;

        result?.forEach(({ Resource }) => {
            resources[Resource.resource_id] = { ...Resource, children: [] };
        });

        Object.values(resources)?.forEach(resource => {
            if (resource.parent_id) {
                resources[resource.parent_id]?.children.push(resource);
            }
        });

        Object.values(resources)?.forEach(resource => {
            resource.children.sort((a, b) => a.order - b.order);
        });

        // Filter out the child nodes from the top level
        const tree = Object.values(resources)?.filter(resource => !resource.parent_id);

        tree.sort((a, b) => a.order - b.order);

        return response.success(req, res, { msgCode: "SIDEBAR_FETCHED", data: tree }, httpStatus.OK);
    } catch (error) {
        console.log("getSidebar", error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}
