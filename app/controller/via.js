const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
// const vendorService = require('../services/vendor');
const viaService = require('../services/via');
const assessmentService = require('../services/assessment');
const constant = require('../constant/vendor');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const { Op } = require('sequelize');
const { getPagination } = require('../config/helper');
const csv = require('csv-parser');
const fs = require('fs');
const { deleteFile } = require('../utils/delete-files');
const { sendMail, sendMailWithAttach } = require('../config/email');
const { transformData, createAssessmentExcelFile, generateExcelForAuditData } = require('../utils/helper');
// const authService = require('../services/auth');
const moment = require('moment');
//                <<<---------- Vendor Internal Assessment ----------->>>

exports.assignVIA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, VendorDetail, Departments, Processes, AuditLog, User } = db.models;
    let viaName = null;
    // let approverId = null;
    let assignedToName = null;
    let dept_id = null;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const checkVIA = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.via_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVIA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VIA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VIA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (checkVIA.Department) {
      // approverId = checkVIA.Department.spoc_id;
      viaName = checkVIA.Department.name;
      dept_id = checkVIA.Department.id;
    } else if (checkVIA.Process) {
      // approverId = checkVIA.Process.Department.spoc_id;
      viaName = checkVIA.Process.name;
      dept_id = checkVIA.Process.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    assignedToName = `${user.firstName} ${user.lastName}`;

    const assigner = await commonService.findByCondition(
      User,
      {
        id: req.data.userId
      },
      ['firstName', 'lastName']
    );
    if (!assigner) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }
    const via = await commonService.updateData(VendorAssessments, { assigned_to: req.body.user_id }, { id: req.body.via_id }, dbTrans);
    if (!via[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //also updating in the vendor Detail table
    const updatedDetail = await commonService.updateData(VendorDetail, { assigned_to: req.body.user_id }, { vendor_id: checkVIA.vendor_id }, dbTrans);
    if (!updatedDetail[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const subject = `You have been assigned ${viaName} : We Need Your Input!`;
    const textTemplate = 'via_assigned.ejs';
    const sendData = {
      assignee: `${user.firstName} ${user.lastName}`,
      viaName: viaName,
      assigner: `${assigner.firstName} ${assigner.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/via/`
    };

    sendMail(user.email, sendData, subject, textTemplate);

    const auditAction = `Assigned ${viaName} VIA to ${assignedToName}`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VIA', type_id: req.body.via_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VIA_ASSIGNED', data: checkVIA }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.reviewerVIA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, VendorDetail, Departments, Processes, AuditLog, User } = db.models;
    let viaName = null;
    // let approver = null;
    let reviewerName = null;
    let dept_id = null;

    const checkVIA = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.via_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVIA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VIA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VIA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    if (checkVIA.Department) {
      // approverId = checkVIA.Department.spoc_id;
      viaName = checkVIA.Department.name;
      dept_id = checkVIA.Department.id;
    } else if (checkVIA.Process) {
      // approverId = checkVIA.Process.Department.spoc_id;
      viaName = checkVIA.Process.name;
      dept_id = checkVIA.Process.Department.id;
    }

    const user = await commonService.findByCondition(
      User,
      {
        id: req.body.user_id
      },
      ['firstName', 'lastName', 'email']
    );
    if (!user) {
      return response.error(req, res, { msgCode: 'USERS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    reviewerName = `${user.firstName} ${user.lastName}`;

    // const reviewer = await commonService.findByCondition(User, {
    //     id: req.data.userId
    // }, ['firstName', 'lastName']);
    // if (!reviewer) {
    //     return response.error(req, res, { msgCode: "USERS_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }

    const via = await commonService.updateData(VendorAssessments, { approver: req.body.user_id }, { id: req.body.via_id }, dbTrans);
    if (!via[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    //also updating in the vendor Detail table
    const updatedDetail = await commonService.updateData(VendorDetail, { reviewer_id: req.body.user_id }, { vendor_id: checkVIA.vendor_id }, dbTrans);
    if (!updatedDetail[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const subject = `You have been assigned as Reviewer for ${viaName} - VIA : We Need Your Input!`;
    const textTemplate = 'via_reviewer.ejs';
    const sendData = {
      reviewer: `${reviewerName}`,
      viaName: viaName,
      // assigner: `${assigner.firstName} ${assigner.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/via/`
    };

    sendMail(user.email, sendData, subject, textTemplate);

    const auditAction = `Added Reviewer to  ${viaName} VIA to ${reviewerName}`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VIA', type_id: req.body.via_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VIA_REVIEWER_ADDED', data: via[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.startVIA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, ViaControls, ViaCustomerControls, ViaCollaborator, AuditLog, Departments, Processes, User } = db.models;

    const checkVIA = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.params.via_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    // const checkVIA = await viaService.getVIA(VendorAssessments, Departments, User, { id: req.params.via_id }, {}, {}, ['id','assessment_type','assessment_name','customer_id','vendor_id','department_id','approver','assigned_to','start_date','end_date','risks','progress','status'], {}, ['firstName', 'lastName']);
    // if (!checkVIA) {
    //     return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    // }

    if (checkVIA.status === constant.status.STARTED || checkVIA.status === constant.status.CHANGES_REQUESTED) {
      if (checkVIA.assigned_to && checkVIA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        const collaborator = await commonService.findByCondition(ViaCollaborator, { via_id: req.params.via_id, user_id: req.data.userId }, ['id']);
        if (!collaborator) {
          return response.error(req, res, { msgCode: 'VIA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
        }
      }
      return response.success(req, res, { msgCode: 'VIA_STARTED' }, httpStatus.OK, dbTrans);
    } else if (checkVIA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VIA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VIA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let viaName = null;
    let dept_id = null;
    // let approverId = null;

    if (checkVIA.Department) {
      viaName = checkVIA.Department.name;
      dept_id = checkVIA.Department.id;
      // approverId = checkVIA.Department.spoc_id;
    } else if (checkVIA.Process) {
      viaName = checkVIA.Process.name;
      dept_id = checkVIA.Process.Department.id;
      // approverId = checkVIA.Process.Department.spoc_id;
    }

    if (checkVIA.assigned_to && checkVIA.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(ViaCollaborator, { via_id: req.params.via_id, user_id: req.data.userId }, ['id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'VIA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }
    }

    const via = await commonService.updateData(VendorAssessments, { status: constant.status.STARTED, start_date: Date() }, { id: req.params.via_id }, dbTrans);
    if (!via[1]) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let controls = await commonService.getList(ViaControls, { industry_vertical_id: 1, customer_id: req.data.customer_id }, ['id', 'category_id', 'parent_id', 'customer_id']);
    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (controls.count == 0) {
      controls = await commonService.getList(ViaControls, { industry_vertical_id: 1, customer_id: null }, ['id', 'category_id', 'parent_id', 'customer_id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
    }

    const parentControls = controls?.rows?.filter(control => control.parent_id === null);
    const childControls = controls?.rows?.filter(control => control.parent_id !== null);

    const customerControlsParents = parentControls?.map(control => {
      return {
        question_id: control.id,
        customer_id: control.customer_id,
        category_id: control.category_id,
        via_id: Number(req.params.via_id),
        is_custom: false
      };
    });

    const newCustomerControlsParents = await commonService.bulkAdd(ViaCustomerControls, customerControlsParents, dbTrans);
    if (!newCustomerControlsParents) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const parentIdMap = newCustomerControlsParents?.reduce((map, control, index) => {
      map[parentControls[index].id] = control.id;
      return map;
    }, {});

    const customerControlsChildren = childControls?.map(control => {
      return {
        question_id: control.id,
        customer_id: control.customer_id,
        category_id: control.category_id,
        via_id: req.params.via_id,
        parent_id: parentIdMap[control.parent_id],
        is_custom: false
      };
    });

    const newCustomerControlsChildren = await commonService.bulkAdd(ViaCustomerControls, customerControlsChildren, dbTrans);
    if (!newCustomerControlsChildren) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const auditAction = `${user?.firstName} ${user?.lastName} started ${viaName} VIA`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VIA', type_id: req.params.via_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VIA_STARTED', data: via[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getProgress = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaCustomerControls, ViaAnswers, VendorAssessments, ReviewVIA, ViaCollaborator } = db.models;
    const via_id = req.params.via_id;

    const via = await commonService.findByCondition(VendorAssessments, { id: via_id }, ['status', 'approver', 'assigned_to']);
    if (!via) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const status = via.status;
    let controls = null;
    let totalControls = 0;
    let answeredControls = 0;
    let childControls = [];
    let progress = 0;

    if (via.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && via.approver !== req.data.userId) {
      const collaborator = await commonService.getList(ViaCollaborator, { via_id: via_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'VIA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED, dbTrans);
      }

      const categories = collaborator.rows?.map(collaborator => collaborator.category_id);

      controls = await commonService.getListAssociateWithoutCount(ViaCustomerControls, ViaAnswers, { via_id: via_id, category_id: { [Op.in]: categories } }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.ViaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.ViaAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.ViaAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });

      progress = (answeredControls / totalControls) * 100;
      progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

      return response.success(req, res, { msgCode: 'PROGRESS_FETCHED', data: { totalControls, answeredControls, progress } }, httpStatus.OK, dbTrans);
    }

    if (status === constant.status.STARTED) {
      controls = await commonService.getListAssociateWithoutCount(ViaCustomerControls, ViaAnswers, { via_id: via_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.ViaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.ViaAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.ViaAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.UNDER_REVIEW) {
      controls = await commonService.getListAssociateWithoutCountWithAlias(ViaCustomerControls, ReviewVIA, 'ReviewVIA', { via_id: via_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.ReviewVIA).length;
      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
        }
        if (control.ReviewVIA) {
          answeredControls++;
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.CHANGES_REQUESTED) {
      controls = await viaService.getControlsWithAnswersAndReviews(ViaCustomerControls, ViaAnswers, ReviewVIA, { via_id: via_id }, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      // totalControls = controls.rows?.filter(control => control.parent_id === null && control.ReviewVIA && control.ReviewVIA.accurate_information === 0).length;
      // answeredControls = controls.rows?.filter(control => control.parent_id === null && control.ViaAnswer?.updatedAt > control.ReviewVIA?.updatedAt).length;
      // childControls = controls.rows?.filter(control => control.parent_id !== null);
      controls.rows?.forEach(control => {
        if (control.parent_id === null) {
          if (control.ReviewVIA && control.ReviewVIA.accurate_information === 0) {
            totalControls++;
          }
          if (control.ViaAnswer?.updatedAt > control.ReviewVIA?.updatedAt && control.ReviewVIA.accurate_information === 0) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });
      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});
      Object.entries(childControlsByParent)?.forEach(([parentId, childControls]) => {
        const parentControl = controls.rows?.find(control => control.id == parentId);
        if (parentControl && childControls.every(control => control.ViaAnswer.updatedAt > parentControl.ReviewVIA.updatedAt)) {
          answeredControls += 1; // Increment if all child controls of this parent are "answered" based on parent's ReviewVIA
        }
      });
      progress = (answeredControls / totalControls) * 100;
    } else if (status === constant.status.COMPLETED) {
      controls = await commonService.getListAssociateWithoutCount(ViaCustomerControls, ViaAnswers, { via_id: via_id }, {}, ['id', 'parent_id'], ['id']);
      if (!controls) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }

      // totalControls = controls?.filter(control => control.parent_id === null).length;
      // answeredControls = controls?.filter(control => control.parent_id === null && control.ViaAnswer).length;
      // childControls = controls?.filter(control => control.parent_id !== null);

      controls?.forEach(control => {
        if (control.parent_id === null) {
          totalControls++;
          if (control.ViaAnswer) {
            answeredControls++;
          }
        } else {
          childControls.push(control);
        }
      });

      const childControlsByParent = childControls?.reduce((acc, control) => {
        if (!acc[control.parent_id]) {
          acc[control.parent_id] = [];
        }
        acc[control.parent_id].push(control);
        return acc;
      }, {});

      Object.values(childControlsByParent)?.forEach(childControls => {
        if (childControls.every(control => control.ViaAnswer)) {
          answeredControls += 1; // Increment if all child controls of this parent are answered
        }
      });
      progress = (answeredControls / totalControls) * 100;
    }

    progress = parseFloat(((answeredControls / totalControls) * 100).toFixed(2));

    const updateProgress = await commonService.updateData(VendorAssessments, { progress: progress }, { id: via_id }, dbTrans);
    if (!updateProgress) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'PROGRESS_FETCHED', data: { totalControls, answeredControls, progress } }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
  }
};

exports.getCategories = async (req, res) => {
  try {
    const { ViaCategory, ViaCollaborator, VendorAssessments, ViaCustomerControls, ReviewVIA } = db.models;
    const { page, size, sort_by = 'id', sort_order = 'ASC' } = req.query;
    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];

    let viaLevel = req.params.via_level;
    const via_id = req.query.via_id;
    viaLevel = viaLevel.charAt(0).toUpperCase() + viaLevel.slice(1);

    let categoryCondition = { via_level: viaLevel };
    let conditions = [];

    const via = await commonService.findByCondition(VendorAssessments, { id: via_id }, ['status', 'assigned_to', 'approver']);
    if (!via) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (via.assigned_to !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2] && via.approver !== req.data.userId) {
      const collaborators = await commonService.getList(ViaCollaborator, { via_id: via_id, user_id: req.data.userId }, ['category_id']);
      if (!collaborators) {
        return response.error(req, res, { msgCode: 'VIA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED);
      }
      // categoryCondition.id = { [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) };
      conditions.push({ [Op.in]: collaborators?.rows?.map(collaborator => collaborator.category_id) });
    }

    if (via.status === constant.status.CHANGES_REQUESTED) {
      const changeReqCategories = await commonService.getListAssociateWithAlias(ViaCustomerControls, ReviewVIA, 'ReviewVIA', { via_id: via_id }, { accurate_information: 0 }, ['category_id']);
      if (!changeReqCategories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
      // categoryCondition.id = { [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) };
      conditions.push({ [Op.in]: changeReqCategories?.map(changeReqCategory => changeReqCategory.category_id) });
    } else {
      categoryCondition.customer_id = req.data.customer_id;
    }

    if (conditions.length > 0) {
      categoryCondition.id = {
        [Op.and]: conditions
      };
    }

    let categories = await commonService.getList(ViaCategory, categoryCondition, ['id', 'name'], limit, offset, order);
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (categories.count == 0) {
      delete categoryCondition.customer_id;
      categories = await commonService.getList(ViaCategory, categoryCondition, ['id', 'name'], limit, offset, order);
      if (!categories) {
        return response.error(req, res, { msgCode: 'CATEGORIES_NOT_FOUND' }, httpStatus.NOT_FOUND);
      }
    }

    return response.success(req, res, { msgCode: 'CATEGORIES_FETCHED', data: categories }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getControls = async (req, res) => {
  try {
    const { ViaControls, ViaCustomerControls, ViaAnswers, User, VendorAssessments, ViaCollaborator, ReviewVIA } = db.models;
    const via_id = req.params.via_id;
    const category_id = req.query.category_id;

    const via = await commonService.findByCondition(VendorAssessments, { id: via_id }, ['status', 'assigned_to', 'approver']);
    if (!via) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (via.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'VIA_NOT_STARTED' }, httpStatus.BAD_REQUEST);
    }

    if (via.assigned_to !== req.data.userId && via.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      const collaborator = await commonService.findByCondition(ViaCollaborator, { via_id: via_id, user_id: req.data.userId, category_id: category_id }, ['id']);
      if (!collaborator) {
        return response.error(req, res, { msgCode: 'VIA_NOT_ASSIGNED' }, httpStatus.UNAUTHORIZED);
      }
    }

    let controls;
    const controlsAttributes = [
      [sequelize.literal(`"ViaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."title" ELSE "ViaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."description" ELSE "ViaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN CAST("ViaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("ViaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."is_attachment" ELSE "ViaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."question" ELSE "ViaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."fields" ELSE "ViaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."extra_input" ELSE "ViaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN CAST("ViaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("ViaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."extra_input_fields" ELSE "ViaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."question_id" IS NOT NULL THEN "ViaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    if (via.status === constant.status.UNDER_REVIEW || via.status === constant.status.CHANGES_REQUESTED || via.status === constant.status.COMPLETED) {
      if (via.status === constant.status.UNDER_REVIEW && via.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }
      controls = await viaService.getControlsWithReview(
        ViaCustomerControls,
        ViaControls,
        ViaAnswers,
        User,
        ReviewVIA,
        { via_id: via_id, category_id: category_id },
        {},
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        ['id', 'accurate_information', 'comments'],
        [['question_id', 'ASC']]
      );
    } else {
      controls = await viaService.getControls(
        ViaCustomerControls,
        ViaControls,
        ViaAnswers,
        User,
        { via_id: via_id, category_id: category_id },
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        [['question_id', 'ASC']]
      );
    }

    if (!controls) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    for (let control of controls) {
      control.Answer = control.ViaAnswer;
      delete control.ViaAnswer;
      control.Review = control.ReviewVIA;
      delete control.ReviewVIA;
      if (control.Answer) {
        control.answered = true;
        if (control.Answer.extra_answer) {
          control.Answer.extra_answered = true;
        } else {
          control.Answer.extra_answered = false;
        }
      } else {
        control.answered = false;
      }

      if (control.Review) {
        control.reviewed = true;
      } else {
        control.reviewed = false;
      }

      if (via.assigned_to !== req.data.userId && via.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
        control.is_collaborator = true;
      } else {
        control.is_collaborator = false;
      }
    }

    let parents = controls?.filter(control => control.parent_id === null);
    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    if (via.status === constant.status.CHANGES_REQUESTED) {
      parents = parents.filter(parent => parent.Review?.accurate_information === 0);
    }

    return response.success(req, res, { msgCode: 'CONTROLS_FETCHED', data: { status: via.status, controls: parents } }, httpStatus.OK);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.getArtifactTypes = async (req, res) => {
  try {
    const { ViaControls } = db.models;
    const artifactTypes = ViaControls.rawAttributes.artifact_type.values;
    if (!artifactTypes) {
      return response.error(req, res, { msgCode: 'ARTIFACT_TYPES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    return response.success(req, res, { msgCode: 'ARTIFACT_TYPES_FETCHED', data: artifactTypes }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaCustomerControls,VendorAssessments, Departments, Processes, AuditLog, User} = db.models;
    req.body.is_custom = true;
    const viaDetails = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.via_id },
      {},
      {},
      {},
      ['id', 'assessment_name', 'vendor_id', 'department_id'],
      {},
      {},
      []
    );

    if (!viaDetails) {
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }

    const addedControls = await commonService.addDetail(ViaCustomerControls, req.body, dbTrans);
    if (!addedControls) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }
    
    // Determine VIA name for audit log
    let viaName = null;
    let dept_id = null;
    
    if (viaDetails.Department) {
      viaName = viaDetails.Department.name;
      dept_id = viaDetails.Department.id;
    } else if (viaDetails.Process) {
      viaName = viaDetails.Process.name;
      dept_id = viaDetails.Process.Department.id;
    }
    
    const controlTitle = req.body.title || 'Custom Control';
    const auditAction = `${user.firstName} ${user.lastName} added custom control "${controlTitle}" to ${viaName} VIA`;
    
    // Create audit log
    const auditLog = await commonService.addDetail(AuditLog, {
      type: 'VIA',
      type_id: req.body.via_id,
      action: auditAction,
      action_by_id: req.data.userId,
      dept_id: dept_id,
      customer_id: req.data.customer_id
    }, dbTrans);
    
    if (!auditLog) {
      return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_CREATED', data: addedControls }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaControls, ViaCustomerControls,VendorAssessments, Departments, Processes, AuditLog, User  } = db.models;
    const { title, description, artifact_type, question, fields, is_attachment } = req.body;

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    let raw_question = null;
    const originalQuestion = await commonService.getDataAssociate(ViaControls, ViaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    if (originalQuestion) {
      raw_question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(ViaCustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      raw_question = customQuestion;
    }
    
    const viaDetails = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: raw_question.via_id || originalQuestion.ViaCustomerControls[0].via_id },
      {},
      {},
      {},
      ['id', 'assessment_name', 'vendor_id', 'department_id'],
      {},
      {},
      []
    );
    
    if (!viaDetails) {
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }

    const updatedValues = {
      title: title || raw_question?.title,
      description: description || raw_question?.description,
      artifact_type: artifact_type || raw_question?.artifact_type,
      is_attachment: is_attachment || raw_question?.is_attachment,
      question: question || raw_question?.question,
      fields: fields || raw_question?.fields,
      is_custom: true
    };

    const updatedControls = await commonService.updateData(ViaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }
    
    // Determine VIA name for audit log
    let viaName = null;
    let dept_id = null;
    
    if (viaDetails.Department) {
      viaName = viaDetails.Department.name;
      dept_id = viaDetails.Department.id;
    } else if (viaDetails.Process) {
      viaName = viaDetails.Process.name;
      dept_id = viaDetails.Process.Department.id;
    }
    
    const controlTitle = raw_question.title || 'Control';
    const auditAction = `${user.firstName} ${user.lastName} updated control "${controlTitle}" in ${viaName} VIA`;
    
    // Create audit log
    const auditLog = await commonService.addDetail(AuditLog, {
      type: 'VIA',
      type_id: raw_question.via_id || originalQuestion.ViaCustomerControls[0].via_id,
      action: auditAction,
      action_by_id: req.data.userId,
      dept_id: dept_id,
      customer_id: req.data.customer_id
    }, dbTrans);
    
    if (!auditLog) {
      return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log(err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.updateFields = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaControls, ViaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    const { fields } = req.body;
    let question = null;

    const originalQuestion = await commonService.getDataAssociate(ViaControls, ViaCustomerControls, {}, { id: req.params.customer_control_id }, {}, {});
    if (originalQuestion) {
      question = originalQuestion;
    } else {
      const customQuestion = await commonService.findByCondition(ViaCustomerControls, { id: req.params.customer_control_id }, {});
      if (!customQuestion) {
        return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
      }
      question = customQuestion;
    }
    // console.log("=======>>>>>>>",question);
    const viaId = question.via_id || question.ViaCustomerControls?.[0]?.via_id;
    const viaDetails = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: viaId },
      {},
      {},
      {},
      ['id', 'assessment_name', 'vendor_id', 'department_id'],
      {},
      {},
      []
    );
    
    if (!viaDetails) {
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }
    
    const updatedValues = {
      title: question.title,
      description: question.description,
      artifact_type: question.artifact_type,
      is_attachment: question.is_attachment,
      question: question.question,
      fields: fields || question.fields,
      is_custom: true
    };
    const updatedControls = await commonService.updateData(ViaCustomerControls, updatedValues, { id: req.params.customer_control_id }, dbTrans);
    if (!updatedControls[1]) {
      return response.error(req, res, { msgCode: 'ERROR_UPDATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }
    
    // Determine VIA name for audit log
    let viaName = null;
    let dept_id = null;
    
    if (viaDetails.Department) {
      viaName = viaDetails.Department.name;
      dept_id = viaDetails.Department.id;
    } else if (viaDetails.Process) {
      viaName = viaDetails.Process.name;
      dept_id = viaDetails.Process.Department.id;
    }
    
    const controlTitle = question.title || 'Control';
    const auditAction = `${user.firstName} ${user.lastName} updated fields for control "${controlTitle}" in ${viaName} VIA`;
    
    // Create audit log
    const auditLog = await commonService.addDetail(AuditLog, {
      type: 'VIA',
      type_id: viaId,
      action: auditAction,
      action_by_id: req.data.userId,
      dept_id: dept_id,
      customer_id: req.data.customer_id
    }, dbTrans);
    
    if (!auditLog) {
      return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_UPDATED', data: updatedControls[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.deleteCustomControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;

    const control = await commonService.findByCondition(ViaCustomerControls, { 
      id: req.params.customer_control_id, 
      is_custom: true, 
      question_id: null 
    }, {});
    
    if (!control) {
      return response.error(req, res, { msgCode: 'CONTROLS_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const viaDetails = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: control.via_id },
      {},
      {},
      {},
      ['id', 'assessment_name', 'vendor_id', 'department_id'],
      {},
      {},
      []
    );
    
    if (!viaDetails) {
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }

    const deletedControls = await commonService.deleteQuery(ViaCustomerControls, { id: req.params.customer_control_id, is_custom: true, question_id: null }, dbTrans);
    if (!deletedControls) {
      return response.error(req, res, { msgCode: 'ERROR_DELETING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!user) {
      return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }
    
    // Determine VIA name for audit log
    let viaName = null;
    let dept_id = null;
    
    if (viaDetails.Department) {
      viaName = viaDetails.Department.name;
      dept_id = viaDetails.Department.id;
    } else if (viaDetails.Process) {
      viaName = viaDetails.Process.name;
      dept_id = viaDetails.Process.Department.id;
    }
    
    const controlTitle = control.title || 'Custom Control';
    const auditAction = `${user.firstName} ${user.lastName} deleted custom control "${controlTitle}" from ${viaName} VIA`;
    
    // Create audit log
    const auditLog = await commonService.addDetail(AuditLog, {
      type: 'VIA',
      type_id: control.via_id,
      action: auditAction,
      action_by_id: req.data.userId,
      dept_id: dept_id,
      customer_id: req.data.customer_id
    }, dbTrans);
    
    if (!auditLog) {
      return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'CONTROL_DELETED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getviaCollaborators = async (req, res) => {
  try {
    const { ViaCollaborator, User, VendorAssessments, ViaCategory } = db.models;
    const via_id = req.params.via_id;

    const via = await viaService.getVIAWithAssignee(VendorAssessments, User, { id: via_id }, {}, ['status', 'assigned_to', 'approver'], ['firstName', 'lastName']);
    if (!via) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    if (via.assigned_to !== req.data.userId && via.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }
    const collaborators = await viaService.getviaCollaborators(ViaCategory, ViaCollaborator, User, {}, { via_id: via_id }, {}, ['id', 'name'], ['id', 'user_id'], ['id', 'firstName', 'lastName', 'email']);
    if (!collaborators) {
      return response.error(req, res, { msgCode: 'COLLABORATORS_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    collaborators?.forEach(collaborator => {
      collaborator.Collaborators = collaborator.ViaCollaborators;
      delete collaborator.ViaCollaborators;
    });

    const assignee = `${via.AssignedTo?.firstName} ${via.AssignedTo?.lastName}`;

    return response.success(req, res, { msgCode: 'COLLABORATORS_FETCHED', data: { assignee: assignee, collaborators } }, httpStatus.OK);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.addviaCollaborator = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaCollaborator, AuditLog, VendorAssessments, Departments, Processes, User, ViaCategory } = db.models;

    const checkVIA = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.body.via_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName']
    );
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVIA.assigned_to !== req.data.userId && checkVIA.approver !== req.data.userId && req.data.roleName !== authConstant.USER_ROLE[2]) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }

    const userList = req.body.collaborators?.flatMap(collaborator => collaborator.users?.map(user => user.id));

    const users = await commonService.getListWithoutCount(
      User,
      {
        id: { [Op.in]: userList }
      },
      ['id', 'firstName', 'lastName', 'email']
    );
    if (!users) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const categories = await commonService.getListWithoutCount(
      ViaCategory,
      {
        id: { [Op.in]: req.body.collaborators.map(collaborator => collaborator.category_id) }
      },
      ['id', 'name']
    );
    if (!categories) {
      return response.error(req, res, { msgCode: 'CATEGORY_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const invitee = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!invitee) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    let collaboratorData = [];
    const auditData = [];
    let viaName = null;
    let dept_id = null;
    if (checkVIA.Department) {
      viaName = checkVIA.Department.name;
      dept_id = checkVIA.Department.id;
    }
    // else if (checkVIA.Process) {
    //     viaName = checkVIA.Process.name;
    //     dept_id = checkVIA.Process.Department.id;
    // }

    // const names = [];
    for (let collaborator of req.body.collaborators) {
      const categoryName = categories?.find(category => category.id == collaborator.category_id)?.name;

      for (let collaboratingUser of collaborator.users) {
        const user = users?.find(user => user.id === collaboratingUser.id);
        let userName = `${user?.firstName} ${user?.lastName}`;
        // names.push(userName);

        if (collaboratingUser.action === 'add') {
          collaboratorData.push({
            via_id: req.body.via_id,
            user_id: collaboratingUser.id,
            category_id: collaborator.category_id
          });

          auditData.push({
            type: 'VIA',
            type_id: req.body.via_id,
            action: `Added ${userName} as a collaborator for ${viaName} VIA under ${categoryName} category`,
            action_by_id: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id
          });

          //send mail
          const subject = `Collaboration Request: Assistance Needed with VIA in ${categoryName}`;
          const textTemplate = 'via_collaborator.ejs';
          const sendData = {
            collaboratorName: userName,
            inviteeName: `${invitee.firstName} ${invitee.lastName}`,
            viaName: viaName,
            categoryName: categoryName,
            url: `${process.env.SERVER_IP}/privacy/via/`
          };

          sendMail(user.email, sendData, subject, textTemplate);
        } else if (collaboratingUser.action === 'remove') {
          const oldviaCollaborator = await commonService.deleteQuery(ViaCollaborator, { via_id: req.body.via_id, user_id: collaboratingUser.id, category_id: collaborator.category_id }, dbTrans, true);
          if (!oldviaCollaborator) {
            return response.error(req, res, { msgCode: 'ERROR_DELETING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
          }

          auditData.push({
            type: 'VIA',
            type_id: req.body.via_id,
            action: `Removed ${userName} as a collaborator for ${viaName} VIA under ${categoryName} category`,
            action_by_id: req.data.userId,
            dept_id: dept_id,
            customer_id: req.data.customer_id
          });
        }
      }
    }

    const newviaCollaborators = await commonService.bulkAdd(ViaCollaborator, collaboratorData, dbTrans);
    if (!newviaCollaborators) {
      return response.error(req, res, { msgCode: 'ERROR_ADDING_COLLABORATOR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const auditLog = await commonService.bulkAdd(AuditLog, auditData, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'COLLABORATOR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.createOrUpdateAnswers = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaAnswers, VendorAssessments, Departments, Processes, AuditLog, User } = db.models;
    const answers = req.body.answers;
    const via_id = req.body.via_id;

    const checkVIA = await commonService.findByCondition(VendorAssessments, { id: via_id }, ['status']);
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVIA.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'VIA_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VIA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VIA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    // Separate answers into two arrays based on the type
    const addAnswers = answers?.filter(answer => answer.type === 'add');
    const updateAnswers = answers?.filter(answer => answer.type === 'update');

    // Add 'answered_by' field to all answers
    addAnswers?.forEach(answer => (answer.answered_by = req.data.userId));
    updateAnswers?.forEach(answer => (answer.answered_by = req.data.userId));

    const viaDetails = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: via_id },
      {},
      {},
      {},
      ['id', 'assessment_name', 'vendor_id', 'department_id'],
      {},
      {},
      []
    );
    
    if (!viaDetails) {
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }
    // Bulk add or update answers
    if (addAnswers.length > 0) {
      const addNewAnswers = await commonService.bulkAdd(ViaAnswers, addAnswers, dbTrans);
      if (!addNewAnswers) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateAnswers.length > 0) {
      for (let answer of updateAnswers) {
        const updateAnswers = await commonService.updateData(ViaAnswers, answer, { customer_question_id: answer.customer_question_id }, dbTrans);
        if (!updateAnswers[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    if (addAnswers.length > 0 || updateAnswers.length > 0) {
      // Get user info for audit log
      const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
      if (!user) {
        return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
      }
      
      // Determine VIA name for audit log
      let viaName = null;
      let dept_id = null;
      
      if (viaDetails.Department) {
        viaName = viaDetails.Department.name;
        dept_id = viaDetails.Department.id;
      } else if (viaDetails.Process) {
        viaName = viaDetails.Process.name;
        dept_id = viaDetails.Process.Department.id;
      }
      
      const auditAction = `${user.firstName} ${user.lastName} answered ${addAnswers.length + updateAnswers.length} control(s) for ${viaName} VIA`;
      
      // Create audit log
      const auditLog = await commonService.addDetail(AuditLog, {
        type: 'VIA',
        type_id: via_id,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      }, dbTrans);
      
      if (!auditLog) {
        return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }

    return response.success(req, res, { msgCode: 'ANSWER_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitVIA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorAssessments, AuditLog, Departments, Processes, User, ViaAnswers, ViaCustomerControls } = db.models;
    const checkVIA = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.params.via_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'start_date', 'end_date', 'risks', 'progress', 'status'],
      {},
      {},
      ['firstName', 'lastName', 'email']
    );
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVIA.status === constant.status.YET_TO_START) {
      return response.error(req, res, { msgCode: 'VIA_NOT_STARTED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VIA_COMPLETED' }, httpStatus.BAD_REQUEST, dbTrans);
    } else if (checkVIA.status === constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VIA_UNDER_REVIEW' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const checkAnswerStatus = await commonService.getListAssociateWithoutCount(ViaCustomerControls, ViaAnswers, { via_id: req.params.via_id }, {}, [[sequelize.literal(`"ViaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'], {});

    const parents = checkAnswerStatus?.filter(control => control.parent_id === null);
    const childrenMap = checkAnswerStatus?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const unansweredQuestions = parents?.reduce((acc, parent) => {
      if (parent.children.length > 0) {
        parent.children.forEach(child => {
          if (child.ViaAnswer === null) {
            acc.push({ customer_question_id: child.customer_question_id });
          }
        });
      } else if (parent.ViaAnswer === null) {
        acc.push({ customer_question_id: parent.customer_question_id });
      }
      return acc;
    }, []);

    if (unansweredQuestions.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_ANSWERED', data: unansweredQuestions }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const via = await commonService.updateData(VendorAssessments, { status: constant.status.UNDER_REVIEW }, { id: req.params.via_id }, dbTrans);
    if (!via[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let viaName = null;
    let dept_id = null;

    if (checkVIA.Department) {
      viaName = checkVIA.Department.name;
      dept_id = checkVIA.Department.id;
    }
    // else if (checkVIA.Process) {
    //     viaName = checkVIA.Process.name;
    //     dept_id = checkVIA.Process.Department.id;
    // }

    const submitter = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
    if (!submitter) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    const submitterName = `${submitter.firstName} ${submitter.lastName}`;

    const subject = `Request for Review: Vendor Internal Assessment (VIA) - ${viaName} Submission by ${submitterName}`;
    const textTemplate = 'via_submit.ejs';
    const sendData = {
      assignee: submitterName,
      viaName: viaName,
      reviewer: `${checkVIA.Approver?.firstName} ${checkVIA.Approver?.lastName}`,
      url: `${process.env.SERVER_IP}/privacy/via/`
    };
    console.log('=================================================', checkVIA);
    await sendMail(checkVIA.Approver.email, sendData, subject, textTemplate);

    const auditAction = `Submitted ${viaName} VIA for review`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VIA', type_id: req.params.via_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VIA_SUBMITTED', data: via[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.reviewVIA = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ReviewVIA, VendorDetail, ViaCustomerControls, VendorAssessments, Departments, Processes, AuditLog, User  } = db.models;
    const reviews = req.body.reviews;

    const findVia = await commonService.findByCondition(ViaCustomerControls,{id:reviews[0].customer_question_id},['via_id']);
    if(!findVia){
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);

    }
    // Separate answers into two arrays based on the type
    const addReviews = reviews?.filter(answer => answer.type === 'add');
    const updateReviews = reviews?.filter(answer => answer.type === 'update');

    // Add 'areviewer_id' field to all answers
    addReviews?.forEach(review => (review.reviewer_id = req.data.userId));
    updateReviews?.forEach(review => (review.reviewer_id = req.data.userId));

    const viaId = findVia.via_id;
    const viaDetails = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: viaId },
      {},
      {},
      {},
      ['id', 'assessment_name', 'vendor_id', 'department_id'],
      {},
      {},
      []
    );
    
    if (!viaDetails) {
      return response.error(req, res, { msgCode: "VIA_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
    }

    // Bulk add or update reviews
    if (addReviews.length > 0) {
      const addNewReviews = await commonService.bulkAdd(ReviewVIA, addReviews, dbTrans);
      if (!addNewReviews) {
        return response.error(req, res, { msgCode: 'ERROR_CREATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    if (updateReviews.length > 0) {
      for (let review of updateReviews) {
        const updateReviews = await commonService.updateData(ReviewVIA, review, { customer_question_id: review.customer_question_id }, dbTrans);
        if (!updateReviews[1]) {
          return response.error(req, res, { msgCode: 'ERROR_UPDATING_ANSWER' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      }
    }

    if (addReviews.length > 0 || updateReviews.length > 0) {
      // Get user info for audit log
      const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName']);
      if (!user) {
        return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND, dbTrans);
      }
      
      // Determine VIA name for audit log
      let viaName = null;
      let dept_id = null;
      
      if (viaDetails.Department) {
        viaName = viaDetails.Department.name;
        dept_id = viaDetails.Department.id;
      } else if (viaDetails.Process) {
        viaName = viaDetails.Process.name;
        dept_id = viaDetails.Process.Department.id;
      }
      
      const auditAction = `${user.firstName} ${user.lastName} added review comments to ${addReviews.length + updateReviews.length} control(s) for ${viaName} VIA`;
      
      // Create audit log
      const auditLog = await commonService.addDetail(AuditLog, {
        type: 'VIA',
        type_id: viaId,
        action: auditAction,
        action_by_id: req.data.userId,
        dept_id: dept_id,
        customer_id: req.data.customer_id
      }, dbTrans);
      
      if (!auditLog) {
        return response.error(req, res, { msgCode: "ERROR_CREATING_AUDIT_LOG" }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    
    return response.success(req, res, { msgCode: 'REVIEW_CREATED_OR_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.submitReview = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { VendorsMapping, VendorAssessments, ViaCustomerControls, ViaControls, ViaAnswers, AuditLog, Customer, Departments, Processes, User, ReviewVIA, VendorDetail } = db.models;
    const checkVIA = await viaService.getVIA(
      VendorAssessments,
      Departments,
      Processes,
      User,
      { id: req.params.via_id },
      {},
      {},
      {},
      ['id', 'assessment_type', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'approver', 'assigned_to', 'approver', 'start_date', 'end_date', 'risks', 'progress', 'status', 'template_id', 'entity_id', 'owner_id'],
      {},
      {},
      ['firstName', 'lastName', 'email']
    );
    if (!checkVIA) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND, dbTrans);
    }

    if (checkVIA.status !== constant.status.UNDER_REVIEW) {
      return response.error(req, res, { msgCode: 'VIA_NOT_SUBMITTED' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    let viaName = null;
    let dept_id = null;

    if (checkVIA.Department) {
      viaName = checkVIA.Department.name;
      dept_id = checkVIA.Department.id;
    }
    // else if (checkVIA.Process) {
    //     viaName = checkVIA.Process.name;
    //     dept_id = checkVIA.Process.Department.id;
    // }

    let status = constant.status.COMPLETED;
    let end_date = Date();
    const checkReviewStatus = await commonService.getListAssociateWithoutCountWithAlias(
      ViaCustomerControls,
      ReviewVIA,
      'ReviewVIA',
      { via_id: req.params.via_id },
      {},
      [[sequelize.literal(`"ViaCustomerControls"."id"`), 'customer_question_id'], 'parent_id'],
      ['accurate_information']
    );

    const unreviewedControls = checkReviewStatus?.filter(review => review.ReviewVIA === null && review.parent_id === null);
    if (unreviewedControls.length > 0) {
      return response.error(req, res, { msgCode: 'ALL_NOT_REVIEWED', data: unreviewedControls }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const unapprovedControls = checkReviewStatus?.filter(review => review.ReviewVIA?.accurate_information === 0);
    if (unapprovedControls.length > 0) {
      status = constant.status.CHANGES_REQUESTED;
      end_date = null;
    }

    let riskLevel = null;

    if (status === constant.status.COMPLETED) {
      // risk assessment logic
      const controlsAttributes = [[sequelize.literal(`"ViaCustomerControls"."id"`), 'customer_question_id'], 'question_id', 'category_id', 'parent_id', 'is_custom'];
      let yesCount = 0;
      let noCount = 0;
      controls = await viaService.getControls(
        ViaCustomerControls,
        ViaControls,
        ViaAnswers,
        User,
        { via_id: req.params.via_id },
        {},
        {},
        {},
        controlsAttributes,
        [],
        ['id', 'answer', 'extra_answer'],
        ['id', 'firstName', 'lastName'],
        [['question_id', 'ASC']]
      );
      for (let control of controls) {
        if (control.ViaAnswer.answer[0] === '0') {
          ++yesCount;
        } else {
          ++noCount;
        }
      }
      const riskPercent = (yesCount * 100) / (yesCount + noCount);
      if (riskPercent > 70) {
        riskLevel = 'High';
      } else if (riskPercent >= 41 && riskPercent <= 70) {
        riskLevel = 'Medium';
      } else {
        riskLevel = 'Low';
      }

      // update via status
      const stageUpdate = await commonService.updateData(VendorDetail, { stage: 'VENDOR_ASSESSMENT' }, { vendor_id: checkVIA.vendor_id }, dbTrans);
      if (!stageUpdate[1]) {
        return response.error(req, res, { msgCode: 'UPDATE_VIA_FAILED' }, httpStatus.NOT_FOUND, dbTrans);
      }

      const subject = `VIA Completed: Review Suggested Risks for Comptiance Enhancement`;
      const textTemplate = 'via_review_submit.ejs';
      const sendDataAssignedTo = {
        assignee: `${checkVIA.AssignedTo.firstName} ${checkVIA.AssignedTo.lastName}`,
        viaName: viaName,
        status: checkVIA.status,
        reviewer: `${checkVIA.Approver.firstName} ${checkVIA.Approver.lastName}`,
        url: `${process.env.SERVER_IP}/privacy/via/`
      };
      console.log('sendDataAssignedTo====>>>>', sendDataAssignedTo);

      sendMail(checkVIA.AssignedTo.email, sendDataAssignedTo, subject, textTemplate);

      const sendDataApprover = {
        assignee: `${checkVIA.Approver.firstName} ${checkVIA.Approver.lastName}`,
        viaName: viaName,
        status: checkVIA.status,
        reviewer: `${checkVIA.Approver.firstName} ${checkVIA.Approver.lastName}`,
        url: `${process.env.SERVER_IP}/privacy/via/`
      };
      console.log('sendDataApprover====>>>>', sendDataApprover);
      sendMail(checkVIA.Approver.email, sendDataApprover, subject, textTemplate);

      const vendorMapping = await commonService.findByCondition(VendorsMapping, { id: checkVIA.vendor_id }, ['vendor_id']);
      if (!vendorMapping) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      const user = await commonService.getListAssociateWithoutCount(Customer, User, { id: vendorMapping.vendor_id }, {}, ['id', 'name'], ['id', 'firstName']);
      if (!user) {
        return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }

      //Entry in Vendor Assessment
      const data = {
        assessment_type: 'vea',
        assessment_name: 'Vendor External Assessment',
        status: 'Yet to Start',
        customer_id: checkVIA.customer_id,
        vendor_id: checkVIA.vendor_id,
        approver: checkVIA.approver,
        assigned_to: user[0].Users[0].id,
        department_id: checkVIA.department_id,
        template_id: checkVIA.template_id,
        entity_id: checkVIA.entity_id,
        owner_id: checkVIA.owner_id
      };

      const vendorAssessment = await commonService.addDetail(VendorAssessments, data, dbTrans);
      if (!vendorAssessment) {
        return response.error(req, res, { msgCode: 'INTERNAL_ASSESSMENT_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
      }
    }
    const via = await commonService.updateData(VendorAssessments, { status: status, end_date: end_date, risks: riskLevel }, { id: req.params.via_id }, dbTrans);
    if (!via[1]) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    const auditAction = `Submitted review for ${viaName} VIA with status '${status}'`;

    const auditLog = await commonService.addDetail(AuditLog, { type: 'VIA', type_id: req.params.via_id, action: auditAction, action_by_id: req.data.userId, dept_id: dept_id, customer_id: req.data.customer_id }, dbTrans);
    if (!auditLog) {
      return response.error(req, res, { msgCode: 'ERROR_CREATING_AUDIT_LOG' }, httpStatus.BAD_REQUEST, dbTrans);
    }

    return response.success(req, res, { msgCode: 'VIA_REVIEWED', data: via[1] }, httpStatus.OK, dbTrans);
  } catch (err) {
    console.log('error', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getAuditLog = async (req, res) => {
  try {
    const { AuditLog, User, Departments } = db.models;

    const { via_id, page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

    const { limit, offset } = getPagination(page, size);
    const order = [[sort_by, sort_order]];
    const auditCondition = { type: 'VIA', customer_id: req.data.customer_id };
    const userCondition = {};

    if (search) {
      userCondition[Op.or] = [{ firstName: { [Op.iLike]: `%${search}%` } }, { lastName: { [Op.iLike]: `%${search}%` } }, { email: { [Op.iLike]: `%${search}%` } }];
    }

    if (req.data.roleName !== authConstant.USER_ROLE[2]) {
      const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
      if (!deptHead) {
        return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
      }

      const deptIds = deptHead.rows?.map(dept => dept.id);
      auditCondition.dept_id = { [Op.in]: deptIds };
    }

    if (via_id) {
      auditCondition.type_id = via_id;
    }

    const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], limit, offset, order);

    if (!auditData) {
      return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    // getting name initials and added to the audit data
    auditData.rows?.map(row => {
      const name = row?.User?.firstName + ' ' + row?.User?.lastName;
      row.name = name;
      const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
      row.initials = initials;
      delete row.User;
    });

    return response.success(req, res, { msgCode: 'AUDIT_LOG_FETCHED', data: auditData }, httpStatus.OK);
  } catch (error) {
    console.log('error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

// exports.downloadAuditLog = async (req, res) => {
//     try {
//         const { AuditLog, User, Departments,Customer,VendorAssessments, VendorList } = db.models;

//         const  via_id  = req.params.req_id;
//         const sort_by = 'createdAt', sort_order = 'DESC'

//         const auditCondition = { type: 'VIA', customer_id: req.data.customer_id };
//         const userCondition = {};
//         const order = [[sort_by, sort_order]];

//         if (req.data.roleName !== authConstant.USER_ROLE[2]) {
//             const deptHead = await commonService.getList(Departments, { spoc_id: req.data.userId }, ['id']);
//             if (!deptHead) {
//                 return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
//             }

//             const deptIds = deptHead.rows?.map(dept => dept.id);
//             auditCondition.dept_id = { [Op.in]: deptIds };
//         }

//         if (via_id) {
//             auditCondition.type_id = via_id;
//         }

//         const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id });
//         if (!customer) {
//             return response.error(req, res, { msgCode: "CUSTOMER_NOT_FOUND" }, httpStatus.NOT_FOUND);
//         }

//         const auditData = await commonService.getListAssociateWithCount(AuditLog, User, auditCondition, userCondition, ['id', 'action', 'action_by_id', 'createdAt', 'type_id'], ['firstName', 'lastName'],undefined, undefined, order);

//         if (!auditData) {
//             return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
//         }

//         // getting name initials and added to the audit data
//         await Promise.all(auditData.rows?.map(async (row) => {
//             const name = row?.User?.firstName + ' ' + row?.User?.lastName;
//             row.name = name;
//             const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
//             row.initials = initials;

//             const vendorAssessments = await commonService.findByCondition(VendorAssessments, { id: row.type_id });
//             if (!vendorAssessments) {
//                 return response.error(req, res, { msgCode: "VENDOR_ASSESSMENTS_NOT_FOUND" }, httpStatus.NOT_FOUND);
//             }

//             const vendorList = await commonService.findByCondition(VendorList, { id: vendorAssessments.vendor_id });
//             if (!vendorList) {
//                 return response.error(req, res, { msgCode: "VENDOR_LIST_NOT_FOUND" }, httpStatus.NOT_FOUND);
//             }

//             row.typeName = vendorList?.name

//           delete row.User;
//       }));

//         const excelData = auditData?.rows;
//         const typeName = auditData?.rows[0]?.typeName ? auditData.rows[0].typeName : 'Unknown Type'; // get the vendor name

//         // remove the unnecessary fields from the excel data using map
//         excelData?.map(row => {
//             const istTime = moment(row.createdAt).tz('Asia/Kolkata');
//             const date = istTime.format('YYYY-MM-DD');
//             const time = istTime.format('HH:mm:ss.SSSZ');
//             row.date = date;
//             row.time = time;
//             delete row.createdAt;
//             delete row.id;
//             delete row.initials;
//             delete row.action_by_id;
//             delete row.typeName
//         });
//         // console.log('excelData', excelData);

//         const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

//         const excelFile = await generateExcelForAuditData(excelData, date, customer?.name, via_id,'VIA', typeName );

//         const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
//         if (!user) {
//             return response.error(req, res, { msgCode: "USER_NOT_FOUND" }, httpStatus.NOT_FOUND);
//         }

//         const mailData = {
//             name: `${user?.firstName} ${user?.lastName}`,
//             type: 'VIA',
//         }

//         await sendMailWithAttach(
//             req.data.email,
//             mailData,
//             'Your Copy of VIA Audit Log file made on GoTrust',
//             'audit_log_download.ejs',
//             excelFile
//         );

//         return response.success(req, res, { msgCode: 'AUDIT_DOWNLOADED'}, httpStatus.OK );
//     } catch (error) {
//         console.log('error', error);
//         return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
//     }
// }

exports.uploadControls = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { ViaControls, ViaCategory } = db.models;
    const controls = [];
    let parentId = null;
    const childControlsData = [];
    const uniqueCategories = new Map(); // Map to store unique categories
    const artifactTypeMapping = {
      radio: 'radio',
      dropdown: 'select',
      table: 'table',
      'text box': 'textarea',
      'upload attachment': 'attachment'
    };

    const requiredHeaders = ['VIA Level', 'Category', 'Title', 'Explanation', 'Artifact Type', 'Question', 'Fields', 'Has Attachment', 'Extra Input Required', 'Extra Input Type', 'Extra Input Fields'];
    const { isValid, missingHeader } = await viaService.validateHeaders(req.files[0].path, requiredHeaders);
    if (!isValid) {
      deleteFile(req.files[0].path);
      return response.error(req, res, { msgCode: 'INVALID_HEADER', data: `${missingHeader} is required` }, httpStatus.BAD_REQUEST, dbTrans);
    }

    fs.createReadStream(req.files[0].path)
      .pipe(csv())
      .on('data', async row => {
        const controlData = {
          VIALevel: row['VIA Level'],
          viaCategory: row['Category'],
          title: row['Title'],
          description: row['Explanation'],
          customer_id: req.data.customer_id,
          artifact_type: row['Artifact Type'],
          question: row['Question'],
          fields: row['Fields'],
          is_attachment: row['Has Attachment'],
          extra_input: row['Extra Input Required'],
          extra_input_type: row['Extra Input Type'],
          extra_input_fields: row['Extra Input Fields'] === 'Custom Fields' ? null : row['Extra Input Fields']
        };

        // Check if all properties of controlData are empty
        if (!Object.values(controlData).every(x => x === '')) {
          if (controlData.title === '' && controlData.question === '') {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          controls.push(controlData);
        }
      })
      .on('end', async () => {
        // Insert the data into the database
        const categoryTitleQuestionSet = new Set();
        for (let row of controls) {
          if (!row['VIALevel'] || !row['viaCategory']) {
            await deleteFile(req.files[0].path);
            return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
          }
          const key = `${row['VIALevel']}_${row['viaCategory']}`;
          if (row['title']) {
            // Parent question
            const titleText = row['title']?.trim();
            const duplicateKey = `${key}_${titleText}`;

            if (categoryTitleQuestionSet.has(duplicateKey)) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'DUPLICATE_QUESTION_TITLE', data: `Duplicate control found in category [${key}] with title [${titleText}]` }, httpStatus.BAD_REQUEST, dbTrans);
            }

            categoryTitleQuestionSet.add(duplicateKey);

            if (!uniqueCategories.has(key)) {
              // Create or retrieve category and store in the map
              const [via_level, name] = key.split('_');
              let category = await commonService.findByCondition(ViaCategory, { via_level, name, customer_id: req.data.customer_id }, {});
              if (!category) {
                category = await ViaCategory.create({ name, via_level, customer_id: req.data.customer_id }, { transaction: dbTrans });
              }
              uniqueCategories.set(key, category);
            }

            let artifactType = null;
            if (row['artifact_type'] !== '') {
              artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
              if (!artifactType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            let extraInputType = null;
            if (row['extra_input_type'] !== '') {
              extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
              if (!extraInputType) {
                await deleteFile(req.files[0].path);
                return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
              }
            }

            // Create parent control
            const control = await commonService.addDetail(
              ViaControls,
              {
                title: row['title'],
                description: row['description'],
                artifact_type: artifactType,
                question: row['question'],
                customer_id: req.data.customer_id,
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: null,
                industry_vertical_id: req.body.industry_vertical_id
              },
              dbTrans
            );

            if (!control) {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
            }

            // Update parent ID for potential child questions
            parentId = control.id;
          } else {
            // Child question
            if (parentId) {
              // Create child control
              let artifactType = null;
              if (row['artifact_type'] !== '') {
                artifactType = artifactTypeMapping[row['artifact_type'].toLowerCase()];
                if (!artifactType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_ARTIFACT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              let extraInputType = null;
              if (row['extra_input_type'] !== '') {
                extraInputType = artifactTypeMapping[row['extra_input_type'].toLowerCase()];
                if (!extraInputType) {
                  await deleteFile(req.files[0].path);
                  return response.error(req, res, { msgCode: 'INVALID_EXTRA_INPUT_TYPE' }, httpStatus.BAD_REQUEST, dbTrans);
                }
              }

              childControlsData.push({
                title: null,
                description: null,
                artifact_type: artifactType,
                customer_id: req.data.customer_id,
                question: row['question'],
                fields: row['fields']
                  ? row['fields']
                      .split('\n')
                      .map(line => line.replace('\r', ''))
                      .map((name, id) => ({ id, name }))
                  : null,
                is_attachment: row['is_attachment'] === 'Yes',
                extra_input: row['extra_input'] === 'Yes',
                extra_input_type: extraInputType,
                extra_input_fields: row['extra_input_fields'] ? row['extra_input_fields'].split('\n').map((name, id) => ({ id, name })) : null,
                category_id: uniqueCategories.get(key).id,
                parent_id: parentId,
                industry_vertical_id: req.body.industry_vertical_id
              });
            } else {
              await deleteFile(req.files[0].path);
              return response.error(req, res, { msgCode: 'INVALID_DATA' }, httpStatus.BAD_REQUEST, dbTrans);
            }
          }
        }
        // Batch create child controls
        const childControls = await commonService.bulkAdd(ViaControls, childControlsData, dbTrans);
        if (!childControls) {
          await deleteFile(req.files[0].path);
          return response.error(req, res, { msgCode: 'ERROR_CREATING_CONTROL' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        await deleteFile(req.files[0].path);

        return response.success(req, res, { msgCode: 'CONTROLS_UPLOADED' }, httpStatus.OK, dbTrans);
      });
  } catch (err) {
    console.log('error', err);
    await deleteFile(req.files[0].path);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.getViaData = async (req, res) => {
  try {
    const { ViaControls, ViaCustomerControls, ViaAnswers, User, VendorAssessments, ViaCategory, Customer, Departments, Processes } = db.models;
    const via_id = req.params.via_id;

    const via = await commonService.findByCondition(VendorAssessments, { id: req.params.via_id }, ['status', 'assigned_to', 'approver', 'department_id', 'risks']);
    if (!via) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    const user = await commonService.findByCondition(User, { id: req.data.userId }, ['firstName', 'lastName', 'email']);
    if (!user) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    if (via.status !== constant.status.COMPLETED) {
      return response.error(req, res, { msgCode: 'VIA_NOT_COMPLETED' }, httpStatus.BAD_REQUEST);
    }

    const controlsAttributes = [
      [sequelize.literal(`"ViaCustomerControls"."id"`), 'customer_question_id'],
      'question_id',
      'category_id',
      'parent_id',
      'is_custom',
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."title" ELSE "ViaControl"."title" END`), 'title'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."description" ELSE "ViaControl"."description" END`), 'description'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN CAST("ViaCustomerControls"."artifact_type" AS TEXT) ELSE CAST("ViaControl"."artifact_type" AS TEXT) END`), 'artifact_type'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."is_attachment" ELSE "ViaControl"."is_attachment" END`), 'is_attachment'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."question" ELSE "ViaControl"."question" END`), 'question'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."fields" ELSE "ViaControl"."fields" END`), 'fields'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."extra_input" ELSE "ViaControl"."extra_input" END`), 'extra_input'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN CAST("ViaCustomerControls"."extra_input_type" AS TEXT) ELSE CAST("ViaControl"."extra_input_type" AS TEXT) END`), 'extra_input_type'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."is_custom" THEN "ViaCustomerControls"."extra_input_fields" ELSE "ViaControl"."extra_input_fields" END`), 'extra_input_fields'],
      [sequelize.literal(`CASE WHEN "ViaCustomerControls"."question_id" IS NOT NULL THEN "ViaControl"."endpoint" ELSE NULL END`), 'endpoint']
    ];

    const controls = await assessmentService.getControlsWithCategory(
      ViaCustomerControls,
      ViaControls,
      ViaAnswers,
      User,
      ViaCategory,
      { via_id: via_id },
      {},
      {},
      {},
      {},
      controlsAttributes,
      [],
      ['id', 'answer', 'attachment_link', 'raw_url', 'extra_answer'],
      ['id', 'firstName', 'lastName'],
      ['id', 'name'],
      [['question_id', 'ASC']]
    );
    for (let control of controls) {
      control.Answer = control.ViaAnswer;
      delete control.ViaAnswer;
      control.Category = control.ViaCategory;
      delete control.ViaCategory;
    }
    let parents = controls?.filter(control => control.parent_id === null);

    const childrenMap = controls?.reduce((map, control) => {
      if (control.parent_id !== null) {
        if (!map[control.parent_id]) {
          map[control.parent_id] = [];
        }
        map[control.parent_id].push(control);
      }
      return map;
    }, {});

    parents?.forEach(parent => {
      parent.children = childrenMap[parent.customer_question_id] || [];
    });

    const excelData = transformData(parents);

    const date = new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

    const customer = await commonService.findByCondition(Customer, { id: req.data.customer_id }, ['name']);
    if (!customer) {
      return response.error(req, res, { msgCode: 'CUSTOMER_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    let deptName = '';
    let procName = '';

    if (via.department_id) {
      const dept = await commonService.findByCondition(Departments, { id: via.department_id }, ['name']);
      deptName = dept?.name;
    }
    // else if (via.process_id) {
    //     const proc = await commonService.getDataAssociate(Processes, Departments, { id: via.process_id }, {}, ['name'], ['name']);
    //     procName = proc?.name;
    //     deptName = proc?.Department?.name;
    // }

    const excelFile = await createAssessmentExcelFile(excelData, 'VIA (Vendor Internal Assessment)', date, customer?.name, deptName, procName);

    await sendMailWithAttach(req.data.email, { name: `${user?.firstName} ${user?.lastName}` }, 'Your Copy of VIA (Vendor Internal Assessment) file made on GoTrust', 'via_download.ejs', excelFile);

    return response.success(req, res, { msgCode: 'VIA_DOWNLOADED', data: 'VIA data sent via E-mail' }, httpStatus.OK);
  } catch (err) {
    console.log('assessmentDownloadError', err);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
exports.collaboratorProgress = async (req, res) => {
  try {
    const { ViaCategory, ViaCustomerControls, ViaAnswers, VendorAssessments, ReviewVIA, ViaCollaborator, User } = db.models;
    const viaId = req.params.via_id;

    const via = await commonService.findByCondition(VendorAssessments, { id: viaId }, ['status']);
    if (!via) {
      return response.error(req, res, { msgCode: 'VIA_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    const findCategoryAndDetails = await commonService.getDistinct(ViaCustomerControls, { via_id: viaId }, ['category_id'], ['category_id']);
    // console.log('------------------------------', findCategoryAndDetails);
    findCategoryAndDetails.sort((a, b) => a.category_id - b.category_id);
    if (!findCategoryAndDetails) {
      return response.error(req, res, { msgCode: 'DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
    }

    let categoryProgress = [];

    for (let category of findCategoryAndDetails) {
      const category_id = category.category_id;

      let totalControls = 0;
      let answeredControls = 0;
      let childControls = [];
      let progress = 0;

      let controls = null;
      const condition = { via_id: viaId, category_id };

      if (via.status === constant.status.STARTED || via.status === constant.status.COMPLETED || via.status === constant.status.UNDER_REVIEW) {
        controls = await commonService.getListAssociateWithoutCount(ViaCustomerControls, ViaAnswers, condition, {}, ['id', 'parent_id'], ['id']);
        controls?.forEach(control => {
          if (control.parent_id === null) {
            totalControls++;
            if (control.ViaAnswer) answeredControls++;
          } else {
            childControls.push(control);
          }
        });
      } else if (via.status === constant.status.CHANGES_REQUESTED) {
        controls = await viaService.getControlsWithAnswersAndReviews(ViaCustomerControls, ViaAnswers, ReviewVIA, condition, {}, {}, ['id', 'parent_id'], ['updatedAt'], ['accurate_information', 'updatedAt']);

        const parentControls = controls.rows?.filter(
          control => control?.parent_id === null && control?.ReviewVIA // only where review marked answer as accurate
        );
        parentControls?.forEach(control => {
          totalControls++;
          if (control.ViaAnswer?.updatedAt > control.ReviewVIA?.updatedAt || (control.ViaAnswer && control?.ReviewVIA?.accurate_information === 1)) {
            answeredControls++;
          }
        });

        childControls = controls.rows?.filter(
          ctrl => ctrl.parent_id !== null && ctrl.ViaAnswer // only include reviewed-accurate child controls
        );
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.entries(childControlsByParent)?.forEach(([parentId, childList]) => {
          const parentControl = parentControls?.find(control => control.id == parentId);
          if (parentControl && childList.every(ctrl => ctrl.ViaAnswer?.updatedAt > parentControl?.ReviewVIA?.updatedAt || (ctrl?.ViaAnswer && ctrl?.ReviewVIA?.accurate_information === 1))) {
            answeredControls += 1;
          }
        });
      }

      if (via.status !== constant.status.CHANGES_REQUESTED) {
        const childControlsByParent = childControls?.reduce((acc, control) => {
          if (!acc[control.parent_id]) acc[control.parent_id] = [];
          acc[control.parent_id].push(control);
          return acc;
        }, {});

        Object.values(childControlsByParent)?.forEach(childList => {
          if (childList.every(control => control.ViaAnswer)) {
            answeredControls += 1;
          }
        });
      }

      progress = totalControls ? parseFloat(((answeredControls / totalControls) * 100).toFixed(2)) : 0;

      const details = await commonService.getListWith3Models(ViaCategory, ViaCollaborator, User, { id: category_id }, { via_id: viaId, category_id }, {}, ['id', 'name'], {}, ['id', 'firstName', 'lastName', 'email']);
      const categoryDetails = details[0] || {};
      const collaborators =
        categoryDetails?.ViaCollaborators?.map(collab => ({
          id: collab.User?.id,
          name: `${collab.User?.firstName} ${collab.User?.lastName}`
        })) || [];

      categoryProgress.push({
        category_id,
        category_name: categoryDetails?.name || 'Unknown',
        progress,
        collaborators: collaborators.length ? collaborators : null
      });
    }

    return response.success(req, res, { msgCode: 'DATA_FETCHED', data: categoryProgress }, httpStatus.OK);
  } catch (error) {
    console.log('Error', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};
