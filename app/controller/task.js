const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { getPagination } = require('../config/helper');
const { request } = require('https');
const moment = require('moment');



exports.getAllTask = async (req, res) => {
    try {
        const { RequestTask, RequestTypeStages, Customer, User } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
        let order = [[sort_by, sort_order]];
        let reqCondition = { stage_id: req.params.stage_id };
        
        order = [  
            ['id', 'ASC'],
            ['stage_id', 'ASC']
        ]
        const checkStep = await commonService.findByCondition(RequestTypeStages, { id: req.params.stage_id});
        if (!checkStep) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
        }

        
     
        //const getAllTask = await dsrService.getAllRecord(RequestTask, reqCondition, ['id' ,'stage_id', 'title', 'guidance_text', 'activepieces_automation_id']);   
        const getAllTask = await dsrService.getAllRecord(RequestTask, reqCondition, {}, null ,null, order);   
       
        if(getAllTask.length > 0){
            for(let key in getAllTask) {                
                if(getAllTask[key].assignee_id){
                    // Since assignee_id is an array, we need to get all users
                    const allUser = await dsrService.getAllRecord(User, {
                        id: getAllTask[key].assignee_id
                    }, ['firstName', 'lastName']); 
                    
                    getAllTask[key].users = allUser;
                } else {
                    getAllTask[key].users = [];             
                }                                              
            }
        }     
        
        return response.success(req, res, { msgCode: "TASK_FETCHED", data: getAllTask }, httpStatus.OK);
        
    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.getTaskById = async (req, res) => {
    try {
        const { RequestTask, RequestTypeStages, TaskDocument } = await db.models;
        const { page, size, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        const { limit, offset } = getPagination(page, size);
       // let order = [[sort_by, sort_order]];
        let order = [[RequestTypeStages, 'order', 'ASC']];

        let reqCondition = { id: req.params.task_id };

        //const check = await commonService.findByCondition(RequestTask, { id: req.params.task_id});
        const check = await dsrService.getSingleLeftJoinData(RequestTask, TaskDocument, { id: req.params.task_id}, {});
        
        if (!check) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST);
        }
                
        return response.success(req, res, { msgCode: "WORKFLOW_FETCHED", data: check }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.createTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTask, RequestTypeStages } = db.models;
                 
        const checkStep = await commonService.findByCondition(RequestTypeStages, { id: req.body.stage_id});
        if (!checkStep) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const check = await commonService.findByCondition(RequestTask, { title: req.body.title, stage_id : req.body.stage_id });
       
        if (check) {
            return response.error(req, res, { msgCode: 'TASK_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        req.body.created_by = req.data.userId;
        req.body.customer_id = req.data.customer_id;
        //req.body.due_date = moment(req.body.start_date).add(parseInt(req.body.due_days, 10), 'days').toISOString();
        const task = await commonService.addDetail(RequestTask, req.body, dbTrans);
         
         if (!task) {
             return response.error(req, res, { msgCode: 'ERROR_CREATING_TASK' }, httpStatus.BAD_REQUEST, dbTrans);
         }

         //insert documents
         //adjusting the name of the file to store in database
        const bulkData = [];
        if(req.body?.documents){
            if(req.body?.documents.length > 0){
                req.body?.documents.forEach(data => {
                    bulkData.push({
                        original_name: data.original_name,
                        url: data.url,
                        task_id: task.id,
                        dsr_request_id: req.body.request_id
                    })
                })

                //adding to the database
                const documentUpload = await commonService.bulkAdd(TaskDocument, bulkData, dbTrans);
                if (!documentUpload) {
                    return response.error(req, res, { msgCode: 'ERROR_IN_DOCUMENT_UPLOAD' }, httpStatus.BAD_REQUEST, dbTrans);
                }
            }
        }

         return response.success(req, res, { msgCode: "TASK_CREATED", data: task }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTask } = db.models;
     
        let id = req.params.id
        
        const check = await commonService.findByCondition(RequestTask, { id: id});
        if (!check) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        let obj = {
            title : req.body.title,
            guidance_text : req.body.guidance_text
        }

        const update = await commonService.updateData(RequestTask, req.body, { id: id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        return response.success(req, res, { msgCode: "TASK_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.updateTaskActivePiecesInfo = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTask } = db.models;
     
        let id = req.params.id
        
        const check = await commonService.findByCondition(RequestTask, { id: id});
        if (!check) {
            return response.error(req, res, { msgCode: 'TASK_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const update = await commonService.updateData(RequestTask, req.body, { id: id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        return response.success(req, res, { msgCode: "TASK_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.deleteTask = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTask } = db.models;

        const deletedRec = await commonService.deleteQuery(RequestTask, { id: req.params.task_id }, dbTrans);
        if (!deletedRec) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_TASK" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "TASK_DELETED_SUCC" }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


