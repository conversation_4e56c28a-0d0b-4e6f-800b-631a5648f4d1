const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const dayjs = require('dayjs');
const commonService = require('../services/common');
const policyService = require('../services/policy');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');

exports.createImprovement =async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const {Improvement} = db.models;
        req.body.customer_id = req.data.customer_id;
        req.body.owner_id = req.data.userId;
        const createImprovementData = await commonService.addDetail(Improvement, req.body, dbTrans);
        if(!createImprovementData ){
            return response.error(req, res, { msgCode: 'ERROR_CREATING_IMPROVEMENT' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        return response.success(req,res, {msgCode:'IMPROVEMENT_CREATED',data:createImprovementData}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error in creating improvement",error);

        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);

    }
}
exports.updateImprovement = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{

        const {Improvement}=db.models;
    
        const check = await commonService.findByCondition(Improvement,{customer_id:req.data.customer_id,id:req.params.id});

        if(!check){
            return response.error(req,res,{msgCode:'IMPROVEMENT_NOT_FOUND'},httpStatus.BAD_REQUEST,dbTrans);
        }

        const updateData = await commonService.updateData(Improvement,req.body,{id:req.params.id},dbTrans);

        if(!updateData[1]){
            return response.error(req,res,{msgCode:'UPDATE_ERROR'},httpStatus.BAD_REQUEST,dbTrans);
        }

        return response.success(req,res, {msgCode:'IMPROVEMENT_UPDATED',data:updateData[1]}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }

}
exports.detailImprovement = async(req,res)=>{
    try{
        const {Improvement,User}=db.models;
       
        const detailImprovement = await commonService.findByCondition(Improvement,{id:req.params.id,customer_id:req.data.customer_id});
        if(!detailImprovement){
            return response.error(req,res,{msgCode:'IMPROVEMENT_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }
        if (detailImprovement.assignee_id && Array.isArray(detailImprovement.assignee_id)) {
            const assignees = await commonService.getListWithoutCount(User,{id: {[Op.in]: detailImprovement.assignee_id}},['id', 'firstName', 'lastName'])
            // Format the assignees into full names
            detailImprovement.assignees = assignees.map(assignee => ({
                id: assignee.id,
                fullName: `${assignee.firstName} ${assignee.lastName}`
            }));
        }

        if (detailImprovement.owner_id) {
            const owner = await commonService.findByCondition(User,{ id: detailImprovement.owner_id },['id', 'firstName', 'lastName'])
            if (owner) {
                detailImprovement.Owner = {
                    id: owner.id,
                    fullName: `${owner.firstName} ${owner.lastName}`
                };
            }
        }

        // Clean up the response
        delete detailImprovement.assignee_id; // Remove the original array if needed
        delete detailImprovement.owner_id; 

        

         return response.success(req,res, {msgCode:'API_SUCCESS',data:detailImprovement}, httpStatus.OK);

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.listImprovement = async(req,res)=>{
    try{
        const {Improvement,User}=db.models;
        const { page, size, start_date, end_date, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const improvementCondition = {
            customer_id:req.data.customer_id,
            entity_id: req.query.entity_id
        }

        if(req.query.status === 'INPROGRESS'||req.query.status === 'COMPLETED'){
            improvementCondition[Op.and]  = [
                {status:req.query.status}
            ];
        }
        
        if (search) {
            improvementCondition[Op.or] = [
                { title: { [Op.iLike]: `%${search}%` } }
            ];
        }
        if (start_date) {
            improvementCondition = {
                createdAt: {
                    [Op.gte]: req.query.start_date
                }
            }
        }
        if (end_date) {
            improvementCondition = {
                createdAt: {
                    [Op.lte]: req.query.end_date
                }
            }
        }

        if (start_date && end_date) {
            improvementCondition = { createdAt: { [Op.between]: [start_date, end_date] } }
        }
        const { limit, offset } = getPagination(page, size);

        let order = [[sort_by, sort_order]];
        const listImprovement= await commonService.getList(Improvement,improvementCondition,{},limit, offset, order);
        if(!listImprovement){
            return response.error(req,res,{msgCode:'IMPROVEMENT_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }
        
        const currentDate = dayjs().format('YYYY-MM-DD');

        listImprovement.rows?.map(item=>{
            if(item?.due_date<currentDate &&item?.status==='INPROGRESS'){
                item.due_status='OVERDUE'
            }
            return item;
        });
        

        return response.success(req,res, {msgCode:'API_SUCCESS',data:listImprovement}, httpStatus.OK); 

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.countImprovement = async(req,res)=>{
    try{
        const {Improvement}=db.models;
        const Data = await commonService.getListGroupBy(
            Improvement, 
            { customer_id: req.data.customer_id ,entity_id: req.query.entity_id}, 
            ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
            ['status']
        );

        let totalCount = 0;
        let activeCount = 0;
        let archiveCount = 0;
        

        Data.forEach(row => {
            const count = parseInt(row.count || 0);
            totalCount += count;

            switch(row.status) {
                case 'INPROGRESS':
                    activeCount = count;
                    break;
                case 'COMPLETED':
                    archiveCount = count;
                    break;
            }
        });

        const data = [
            { 
                improvement: 'TOTAL', 
                count: String(totalCount)
            },
            { 
                improvement: 'INPROGRESS', 
                count: String(activeCount)
            },
            { 
                improvement: 'ARCHIVE', 
                count: String(archiveCount)
            }
        ];
        return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);

    }
}

