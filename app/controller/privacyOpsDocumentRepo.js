const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const policyService = require('../services/policy');
const authConstant = require('../constant/auth');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');

 exports.getList = async (req, res) => {
    try {
        const { Policy,  PolicyCategory,  PolicyDocument,User } = db.models;
        const { page, size, status, start_date, end_date, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;
        let policyCondition = { customer_id: req.data.customer_id };
        // implementation of searching and the filteration of data
        if (search) {
            policyCondition[Op.or] = [
                { name: { [Op.iLike]: `%${search}%` } }
            ];
        }
        if (status) {
            policyCondition.status = status
        }

        if (start_date) {
            policyCondition = {
                review_date: {
                    [Op.gte]: req.query.start_date
                }
            }
        }
        if (end_date) {
            policyCondition = {
                review_date: {
                    [Op.lte]: req.query.end_date
                }
            }
        }

        if (start_date && end_date) {
            policyCondition = { review_date: { [Op.between]: [start_date, end_date] } }
        }

        const { limit, offset } = getPagination(page, size);

        let order = [[sort_by, sort_order]];
        // Add sorting based on department name and policy category name and also for Group name
        if (sort_by === 'policyCategoryName') {
            order = [[{ model: PolicyCategory, as: 'policyCategory' }, 'name', sort_order]];
        } 
        // // fetching the role name
        // const { role_name } = await commonService.findByCondition(Role, { id: req.data.roleId });
        // req.data.roleName = role_name;
        const policyList = await policyService.getListWithMultipleAssociates2(Policy,User, PolicyCategory,  PolicyDocument, {},'Author',{},{},policyCondition, {}, {}, {},  ['id', 'name', 'description','createdAt','author_id'], ['firstName','lastName'], ['name'],  ['id', 'original_name', 'url'], limit, offset, order);
        
        if (!policyList) {
            return response.error(req, res, { msgCode: "ERROR_IN_POLICYLIST" }, httpStatus.NOT_FOUND);
        }

        policyList?.rows.map(item=>{
            item.Author.Name = item.Author.firstName+' '+item.Author.lastName
            item.Author.intials = item.Author.firstName.charAt(0).toUpperCase()+item.Author.lastName.charAt(0).toUpperCase();
            delete item.Author.firstName;
            delete item.Author.lastName;
            return item;
        })
        return response.success(req, res, { msgCode: "POLICY_FETCHED", data: policyList }, httpStatus.OK);

    } catch (error) {
        console.log('error', error);
        response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
 };


exports.getCount = async (req, res) => {
    try {
        const { Policy } = db.models;
        const policyData = await commonService.getListGroupBy(
            Policy, 
            { customer_id: req.data.customer_id }, 
            ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
            ['status']
        );
        if (!policyData) {
            return response.error(req, res, { msgCode: 'DASHBOARD_DATA_NOT_FOUND' }, httpStatus.BAD_REQUEST);
        }
        let totalCount = 0;
        let creationCount = 0;
        let inProgressCount = 0;
        let completedCount = 0;

        // Calculate counts from the grouped data
        policyData.forEach(row => {
            const count = parseInt(row.count || 0);
            totalCount += count;

            switch(row.status) {
                case 'CREATION_OF_POLICY':
                    creationCount = count;
                    break;
                case 'APPROVAL_OF_POLICY':
                case 'REVIEW_OF_POLICY':
                    inProgressCount += count;
                    break;
                case 'POLICY_IN_USE':
                    completedCount = count;
                    break;
            }
        });

        const data = [
            { 
                policy: 'TOTAL', 
                count: String(totalCount)
            },
            { 
                policy: 'CREATION', 
                count: String(creationCount)
            },
            { 
                policy: 'IN_PROGRESS', 
                count: String(inProgressCount)
            },
            { 
                policy: 'IN_USE', 
                count: String(completedCount)
            }
        ];



        return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);
    } catch (err) {
        console.log(err);
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};