const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const sequelize = require('sequelize');
const commonService = require('../services/common');
const dsrService = require('../services/dsr');
const { getPagination } = require('../config/helper');
const { request } = require('https');




exports.createWorkflowSteps = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { DsrRequestType, RequestTypeStages } = db.models;

        const checkWorkflow = await commonService.findByCondition(DsrRequestType, { id: req.body.type_id});
        if (!checkWorkflow) {
            return response.error(req, res, { msgCode: 'WORKFLOW_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

                 
        // Create workflow
        const check = await commonService.findByCondition(RequestTypeStages, { step_title: req.body.step_title, type_id : req.body.type_id });
       
        if (check) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_NAME_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        req.body.created_by = req.data.userId;

        //get last inserted order
        const order = [['order', 'DESC']];

        const getLastRecord = await dsrService.getOneRecord(RequestTypeStages, { created_by: req.data.userId, type_id : req.body.type_id }, ['id', 'order'], 1, 0, order);
        
        req.body.order = getLastRecord?.order+1;

        const createRecord = await commonService.addDetail(RequestTypeStages, req.body, dbTrans);
         
         if (!createRecord) {
             return response.error(req, res, { msgCode: 'ERROR_CREATING_WORKFLOW_STEP' }, httpStatus.BAD_REQUEST, dbTrans);
         }
         
         return response.success(req, res, { msgCode: "WORKFLOW_STEP_CREATED", data: createRecord }, httpStatus.CREATED, dbTrans);
    } catch (error) {
        console.error('Error creating workflow:', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.updateWorkflowSteps = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTypeStages } = db.models;
        // Check department
        let id = req.params.workflow_step_id
        
        const check = await commonService.findByCondition(RequestTypeStages, { id: id});
        if (!check) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

      
        let obj = {
            step_title : req.body.step_title,
        }

        const update = await commonService.updateData(RequestTypeStages, req.body, { id: id }, dbTrans);
        
        if (!update) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        

        return response.success(req, res, { msgCode: "WORKFLOW_STEP_UPDATED", data: update[1] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.deleteWorkflowStepsBackup = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
        const { RequestTypeStages } = db.models;

        const deletedRec = await commonService.deleteQuery(RequestTypeStages, { id: req.params.workflow_step_id }, dbTrans);
        if (!deletedRec) {
            return response.error(req, res, { msgCode: "ERROR_DELETING_WORKFLOW_STEP" }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req, res, { msgCode: "WORKFLOW_STEP_DELETED_SUCC" }, httpStatus.OK, dbTrans);
    } catch (err) {
        return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
}


exports.deleteWorkflowSteps = async (req, res) => {
    const dbTrans = await db.transaction();
    try {
      const { RequestTypeStages, RequestTask } = db.models;
  
      let id = req.params.workflow_step_id;
  
      const deletedRec = await commonService.findByCondition(RequestTypeStages, { id: id });
      if (!deletedRec) {
        return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
      }
  
      const deletedOrder = deletedRec.order;
      const typeId = deletedRec.type_id;
  
      // Perform the deletion
      await commonService.deleteQuery(RequestTypeStages, { id: id }, dbTrans);
  
      // Log the condition to make sure we are targeting the right records
      //console.log("Updating records with condition:", { order: { [sequelize.Op.gt]: deletedOrder }, type_id: typeId });
  
      // Update the order for all records with order > deletedOrder
      await dsrService.bulkUpdate(RequestTypeStages, 
        { 
          order: { [sequelize.Op.gt]: deletedOrder },  // Correct condition for 'order' field
          type_id: typeId
        },
        { 
          order: sequelize.literal('"order" - 1') // Decrement the order of all records by 1 (PostgreSQL syntax)
        },
        dbTrans);

        //delete task of perticuler step.
        await commonService.deleteQuery(RequestTask, { stage_id: id }, dbTrans);
  
    
      return response.success(req, res, { msgCode: "WORKFLOW_STEP_DELETED_SUCC", data: [] }, httpStatus.OK, dbTrans);
  
    } catch (err) {
      console.log("Error during deletion process:", err);
      return response.error(req, res, { msgCode: "INTERNAL_SERVER_ERROR" }, httpStatus.INTERNAL_SERVER_ERROR , dbTrans);
    }
  };
  

exports.moveUpSteps = async (req, res) => {

    const dbTrans = await db.transaction();
    try {
        const { RequestTypeStages } = db.models;
        // Check department
        let id = req.params.workflow_step_id
        
        const check = await commonService.findByCondition(RequestTypeStages, { id: id});
        if (!check) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        if (check.order === 1) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_ALREADY_TOP' }, httpStatus.BAD_REQUEST, dbTrans);
        }
        
        // Find the category that is right above the current one
        const upperStep = await commonService.findByCondition(RequestTypeStages, { type_id : check.type_id, order : check.order -1});

        if (! upperStep) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);            
        }

        // Swap the positions
        let upperStepObj = {
            order : upperStep.order,
        }

        let currentStepObj = {
            order : check.order,
        }

        const updateCurrent = await commonService.updateData(RequestTypeStages, upperStepObj, { id: id }, dbTrans);
        if (!updateCurrent) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const updateUpper = await commonService.updateData(RequestTypeStages, currentStepObj, { id: upperStep.id }, dbTrans);
       
        if (!updateUpper) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      
        return response.success(req, res, { msgCode: "WORKFLOW_STEP_UPDATED", data: [] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};

exports.moveDownSteps = async (req, res) => {

    const dbTrans = await db.transaction();
    try {
        const { RequestTypeStages } = db.models;
        // Check department
        let id = req.params.workflow_step_id
        
        const check = await commonService.findByCondition(RequestTypeStages, { id: id});
        if (!check) {
            return response.error(req, res, { msgCode: 'WORKFLOW_STEP_DOES_NOT_EXIST' }, httpStatus.BAD_REQUEST, dbTrans);
        }
 
        // Find the category that is right below the current one
        const lowerStep = await commonService.findByCondition(RequestTypeStages, { type_id : check.type_id, order : check.order + 1});

        if (! lowerStep) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);            
        }

        // Swap the positions
        let lowerStepObj = {
            order : lowerStep.order,
        }

        let currentStepObj = {
            order : check.order,
        }

        const updateCurrent = await commonService.updateData(RequestTypeStages, lowerStepObj, { id: id }, dbTrans);
        if (!updateCurrent) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        const updateLower = await commonService.updateData(RequestTypeStages, currentStepObj, { id: lowerStep.id }, dbTrans);
       
        if (!updateLower) {
            return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.BAD_REQUEST, dbTrans);
        }
      
        return response.success(req, res, { msgCode: "WORKFLOW_STEP_UPDATED", data: [] }, httpStatus.OK, dbTrans);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
};


