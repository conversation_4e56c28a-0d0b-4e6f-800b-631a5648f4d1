const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const { getPagination } = require('../config/helper');


exports.getInvoices = async (req, res) => {
    try {
        const { Invoices } = await db.models;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);
        // get Invoices

        const getInvoice = await commonService.getList(Invoices, { customer_id: req.data.customer_id }, {}, limit, offset);
        return response.success(req, res, { msgCode: "INVOICES_FETCHED", data: getInvoice }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.invoice = async (req, res) => {
    try {
        const { Invoices } = await db.models;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);
        // get Invoices

        const getInvoice = await commonService.getList(Invoices, { id: req.params.id }, {}, limit, offset);
        return response.success(req, res, { msgCode: "INVOICES_FETCHED", data: getInvoice }, httpStatus.OK);

    } catch (err) {
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};