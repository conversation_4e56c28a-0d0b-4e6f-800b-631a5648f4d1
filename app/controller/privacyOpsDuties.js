const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const dayjs = require('dayjs');
const commonService = require('../services/common');
const policyService = require('../services/policy');
const policyConstant = require('../constant/policy');
const sequelize = require('sequelize');
const { Op, Sequelize } = require('sequelize');
const { getPagination } = require('../config/helper');


exports.createDuty= async(req,res)=>{
    const dbTrans = await db.transaction();
    try{
        const {Duties}=db.models;
        req.body.customer_id = req.data.customer_id;
        req.body.owner_id = req.data.userId;
        const createDuty = await commonService.addDetail(Duties, req.body, dbTrans);
        
        if(!createDuty){
            return response.error(req, res, { msgCode: 'ERROR_CREATING_DUTY' }, httpStatus.BAD_REQUEST, dbTrans);
        }

        return response.success(req,res, {msgCode:'DUTY_CREATED',data:createDuty}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error in creating duty",error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
}

exports.updateDuty = async(req,res)=>{
    const dbTrans = await db.transaction();
    try{

        const {Duties}=db.models;
    
        const check = await commonService.findByCondition(Duties,{customer_id:req.data.customer_id,id:req.params.id});

        if(!check){
            return response.error(req,res,{msgCode:'DUTY_NOT_FOUND'},httpStatus.BAD_REQUEST,dbTrans);
        }

        const updateData = await commonService.updateData(Duties,req.body,{id:req.params.id},dbTrans);

        if(!updateData[1]){
            return response.error(req,res,{msgCode:'UPDATE_ERROR'},httpStatus.BAD_REQUEST,dbTrans);
        }

        return response.success(req,res, {msgCode:'DUTY_UPDATED',data:updateData[1]}, httpStatus.OK, dbTrans);
    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR,dbTrans);
    }

}
exports.detailDuty = async(req,res)=>{
    try{
        const {Duties,User}=db.models;
        const dutyCondition = {
            id:req.params.id,
            customer_id:req.data.customer_id
        }
        const detailDuty = await commonService.findByCondition(Duties,{id:req.params.id,customer_id:req.data.customer_id});
        if(!detailDuty){
            return response.error(req,res,{msgCode:'DUTY_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }
        if (detailDuty.assignee_id && Array.isArray(detailDuty.assignee_id)) {
            const assignees = await commonService.getListWithoutCount(User,{id: {[Op.in]: detailDuty.assignee_id}},['id', 'firstName', 'lastName'])
            // Format the assignees into full names
            detailDuty.assignees = assignees.map(assignee => ({
                id: assignee.id,
                fullName: `${assignee.firstName} ${assignee.lastName}`
            }));
        }

        if (detailDuty.owner_id) {
            const owner = await commonService.findByCondition(User,{ id: detailDuty.owner_id },['id', 'firstName', 'lastName'])
            if (owner) {
                detailDuty.Owner = {
                    id: owner.id,
                    fullName: `${owner.firstName} ${owner.lastName}`
                };
            }
        }

        // Clean up the response
        delete detailDuty.assignee_id; // Remove the original array if needed
        delete detailDuty.owner_id; 

         return response.success(req,res, {msgCode:'API_SUCCESS',data:detailDuty}, httpStatus.OK);

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.listduties = async(req,res)=>{
    try{
        const {Duties,User}=db.models;
        const { page, size, start_date, end_date, search, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const dutyCondition = {
            customer_id:req.data.customer_id,
            entity_id: req.query.entity_id
        }

        if(req.query.status === 'OPEN'||req.query.status === 'COMPLETED'){
            dutyCondition[Op.and]  = [
                {status:req.query.status}
            ];
        }
        
        if (search) {
            dutyCondition[Op.or] = [
                { title: { [Op.iLike]: `%${search}%` } }
            ];
        }
        if (start_date) {
            dutyCondition = {
                createdAt: {
                    [Op.gte]: req.query.start_date
                }
            }
        }
        if (end_date) {
            dutyCondition = {
                createdAt: {
                    [Op.lte]: req.query.end_date
                }
            }
        }

        if (start_date && end_date) {
            dutyCondition = { createdAt: { [Op.between]: [start_date, end_date] } }
        }
        const { limit, offset } = getPagination(page, size);

        let order = [[sort_by, sort_order]];
        const listDuties = await commonService.getList(Duties,dutyCondition,{},limit, offset, order);
        if(!listDuties){
            return response.error(req,res,{msgCode:'DUTY_NOT_FOUND'},httpStatus.BAD_REQUEST);
        }
        
        const currentDate = dayjs().format('YYYY-MM-DD');

        listDuties.rows?.map(item=>{
            if(item?.due_date<currentDate &&item?.status==='OPEN'){
                item.due_status='OVERDUE'
            }
            return item;
        });
        

        return response.success(req,res, {msgCode:'API_SUCCESS',data:listDuties}, httpStatus.OK); 

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.countDuties = async(req,res)=>{
    try{
        const {Duties}=db.models;
        const Data = await commonService.getListGroupBy(
            Duties, 
            { customer_id: req.data.customer_id, entity_id: req.query.entity_id }, 
            ['status', [sequelize.fn('COUNT', 'id'), 'count']], 
            ['status']
        );

        let totalCount = 0;
        let activeCount = 0;
        let archiveCount = 0;
        

        Data.forEach(row => {
            const count = parseInt(row.count || 0);
            totalCount += count;

            switch(row.status) {
                case 'OPEN':
                    activeCount = count;
                    break;
                case 'COMPLETED':
                    archiveCount = count;
                    break;
            }
        });

        const data = [
            { 
                duty: 'TOTAL', 
                count: String(totalCount)
            },
            { 
                duty: 'OPEN', 
                count: String(activeCount)
            },
            { 
                duty: 'ARCHIVE', 
                count: String(archiveCount)
            }
        ];
        return response.success(req, res, { msgCode: "API_SUCCESS", data: data }, httpStatus.OK);

    }
    catch(error){
        console.log("error",error);
        return response.error(req,res,{msgCode:'INTERNAL_SERVER_ERROR'},httpStatus.INTERNAL_SERVER_ERROR);

    }
}