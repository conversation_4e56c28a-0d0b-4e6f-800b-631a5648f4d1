const response = require('../response');
const httpStatus = require('http-status');
const db = require('../models/index').sequelize;
const commonService = require('../services/common');
const policyService = require('../services/policy');
const { getPagination } = require('../config/helper');
const { Op } = require('sequelize');


exports.auditPolicy = async (req, res) => {
    try {
        const { AuditLog, User, PolicyComments } = db.models;
        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        // const auditData = await commonService.getListAssociateWithCount(AuditLog, User , { type:'POLICY' , type_id: req.params.id }, {}, ['id', 'action','flag' ,'action_by_id', 'createdAt'],['firstName' , 'lastName'] , ['comment'] , limit, offset, order);
        auditData = await policyService.getListWithMultiAssociate(AuditLog, User, PolicyComments, { type: 'POLICY', type_id: req.params.id }, {}, { policy_id: req.params.id }, ['id', 'action', 'flag', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], ['comment'], limit, offset, order);

        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // getting name initials and also check flag for comment and delete it if flag is false and added to the audit data
        auditData.rows?.forEach(row => {
            const name = row?.User?.firstName + ' ' + row?.User?.lastName;
            row.name = name;
            const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
            row.initials = initials;
            delete row.User;
            if (!row.flag) {
                delete row.PolicyComment;


            }
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.auditLogs = async (req, res) => {
    try {
        const { AuditLog, User, PolicyComments } = db.models;

        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        auditData = await policyService.getListWithMultiAssociate(AuditLog, User, PolicyComments, { type: 'POLICY', customer_id: req.data.customer_id }, {}, {}, ['id', 'action', 'flag', 'action_by_id', 'createdAt'], ['firstName', 'lastName'], ['comment'], limit, offset, order);

        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // getting name initials and also check flag for comment and delete it if flag is false and added to the audit data
        auditData.rows?.forEach(row => {
            const name = row?.User?.firstName + ' ' + row?.User?.lastName;
            row.name = name;
            const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
            row.initials = initials;
            delete row.User;
            if (!row.flag) {
                delete row.PolicyComment;
            }
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.auditAssessments = async (req, res) => {
    try {
        const { AuditLog, User } = db.models;

        const { page, size, sort_by = 'createdAt', sort_order = 'DESC' } = req.query;

        const { limit, offset } = getPagination(page, size);
        const order = [[sort_by, sort_order]];

        // const auditData = await commonService.getListAssociateWithCount(AuditLog, User , { type:'POLICY' , type_id: req.params.id }, {}, ['id', 'action','flag' ,'action_by_id', 'createdAt'],['firstName' , 'lastName'] , ['comment'] , limit, offset, order);
        auditData = await commonService.getList2(AuditLog, User, { customer_id: req.data.customer_id ,type: { [Op.in]: ['ASSESSMENT'] }}, {}, ['id', 'action', 'type', 'action_by_id', 'createdAt' ,'customer_id'], ['firstName', 'lastName'], limit, offset, order);
        if (!auditData) {
            return response.error(req, res, { msgCode: 'AUDIT_DATA_NOT_FOUND' }, httpStatus.NOT_FOUND);
        }
        // getting name initials and also check flag for comment and delete it if flag is false and added to the audit data
        auditData.rows?.forEach(row => {
            const name = row?.User?.firstName + ' ' + row?.User?.lastName;
            row.name = name;
            const initials = row?.User?.firstName.charAt(0).toUpperCase() + row?.User?.lastName.charAt(0).toUpperCase();
            row.initials = initials;
            delete row.User;
        });

        return response.success(req, res, { msgCode: "AUDIT_LOG_FETCHED", data: auditData }, httpStatus.OK);
    } catch (error) {
        console.log('error', error);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
