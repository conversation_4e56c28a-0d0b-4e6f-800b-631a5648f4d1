const response = require('../response');
const httpStatus = require('http-status');
const uploadDocument = require('../utils/s3-bucket');
const { deleteFile } = require('../utils/delete-files');

exports.uploadDocuments = async (req, res, next) => {
    try {
        if (!req.body.raw_url) {
            if (!req.files.length) {
                return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST);
            }
            const imagesArrayURL = req.files.map((file) => file.path)

            // upload documents to s3
            const uploadDocuemntToS3 = await uploadDocument.uploadToS3(`${imagesArrayURL[0]}`);
            if (uploadDocuemntToS3.status != true) {
                // delete file from local
                if (req.files[0]) {
                    // delete file from local
                    await deleteFile(req.files[0].path)
                }
                console.log('S3 Error', uploadDocuemntToS3)
                return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
            }
            if (req.files[0]) {
                // delete file from local
                await deleteFile(req.files[0].path)
            }
            req.body.originalName = req.files[0].originalname;
            req.body.url = uploadDocuemntToS3.data.Location;

        } else {
            req.body.originalName = 'Web Document';
            req.body.url = req.body.raw_url;
        }

        next();

    } catch (err) {
        console.log('uploadDocuments', err);
        if (req.files[0]) {
            // delete file from local
            await deleteFile(req.files[0].path)
        }
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.uploadDocumentsV2 = async (req, res, next) => {
    try {
        if (!req.body.raw_url) {
            if (!req.files.length) {
                return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST);
            }
            const imagesArrayURL = req.files.map((file) => file.path)

            // upload documents to s3
            const uploadDocuemntToS3 = await uploadDocument.uploadToS3(`${imagesArrayURL[0]}`);
            if (uploadDocuemntToS3.status != true) {
                // delete file from local
                if (req.files[0]) {
                    // delete file from local
                    await deleteFile(req.files[0].path)
                }
                console.log('S3 Error', uploadDocuemntToS3)
                return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
            }
            // if (req.files[0]) {
            //     // delete file from local
            //     await deleteFile(req.files[0].path)
            // }
            req.body.originalName = req.files[0].originalname;
            req.body.url = uploadDocuemntToS3.data.Location;

        }

        next();

    } catch (err) {
        console.log('uploadDocuments', err);
        if (req.files[0]) {
            // delete file from local
            await deleteFile(req.files[0].path)
        }
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.deleteDocuments = async (req, res, next) => {
    try {
        if (!req.body.raw_url) {
            // delete file from s3
            const deleteDocumentFromS3 = await uploadDocument.deleteFromS3(req.body.url);
            if (deleteDocumentFromS3.status != true) {
                console.log('S3 Error', deleteDocumentFromS3)
                return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
            }
        } else {
            req.body.url = req.body.raw_url;
        }

        next();

    } catch (err) {
        console.log('deleteDocuments', err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
}

exports.uploadMultipleDocuments = async (req, res, next) => {
    try {
        const uploadedFiles = [];

        if (!req.files && !req.body.raw_url) {
            return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST);
        }

        else if (req.files.length ) {
            for (const file of req.files) {
                const uploadResult = await uploadDocument.uploadToS3(file.path);
                if (uploadResult.status !== true) {
                    // Delete all successfully uploaded files from local 
                    if (file) {
                        // delete file from local
                        await deleteFile(file.path)
                    }
                    console.log('S3 Error', uploadResult);
                    return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
                }
                if (file) {
                    // delete file from local
                    await deleteFile(file.path)
                }
                uploadedFiles.push({
                    originalName: file.originalname,
                    url: uploadResult.data.Location
                })
            }
        }
                
        if(req.body.raw_url){
            for (const raw_url of req.body.raw_url) {
                uploadedFiles.push({
                    originalName: 'Web Document',
                    url: raw_url
                })
            }
        }
        req.body.uploadData = uploadedFiles;

        next();

    } catch (err) {
        console.log('uploadDocuments', err);
        for (const file of req.files) {
            if (file) {
                // delete file from local
                await deleteFile(file.path)
            }
        }

        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};