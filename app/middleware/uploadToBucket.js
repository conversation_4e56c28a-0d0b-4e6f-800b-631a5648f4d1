const response = require('../response');
const httpStatus = require('http-status');
const { uploadToBucket, deleteFromBucket } = require('../utils/minIO');
const { deleteFile } = require('../utils/delete-files');

exports.uploadDocuments = async (req, res, next) => {
    try {
        if (!req.body.raw_url) {
            if (!req.files || req.files.length === 0) {
                return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST);
            }

            // Extract the file path from the uploaded file
            const filePath = req.files[0].path;

            // Upload the document to S3 or MinIO
            const uploadDocumentResult = await uploadToBucket(filePath);
            if (uploadDocumentResult.status !== true) {
                // Delete the file from local storage if upload fails
                await deleteFile(filePath);
                console.log('Error uploading to S3/MinIO:', uploadDocumentResult);
                return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
            }

            // Delete the file from local storage after successful upload
            await deleteFile(filePath);

            req.body.originalName = req.files[0].originalname;
            req.body.url = uploadDocumentResult.data.Location;
        } else {
            req.body.originalName = 'Web Document';
            req.body.url = req.body.raw_url;
        }

        next();
    } catch (err) {
        console.log('uploadDocuments Error:', err);
        if (req.files && req.files[0]) {
            // Delete the file from local storage in case of error
            await deleteFile(req.files[0].path);
        }
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.uploadDocumentsV2 = async (req, res, next) => {
    try {
        if (!req.body.raw_url) {
            if (!req.files || req.files.length === 0) {
                return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST);
            }

            // Extract the file path from the uploaded file
            const filePath = req.files[0].path;

            // Upload the document to S3 or MinIO
            const uploadDocumentResult = await uploadToBucket(filePath);
            if (uploadDocumentResult.status !== true) {
                // Delete the file from local storage if upload fails
                await deleteFile(filePath);
                console.log('Error uploading to S3/MinIO:', uploadDocumentResult);
                return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
            }

            // Delete the file from local storage after successful upload
            // await deleteFile(filePath);

            req.body.originalName = req.files[0].originalname;
            req.body.url = uploadDocumentResult.data.Location;
        }

        next();
    } catch (err) {
        console.log('uploadDocumentsV2 Error:', err);
        if (req.files && req.files[0]) {
            // Delete the file from local storage in case of error
            await deleteFile(req.files[0].path);
        }
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.deleteDocuments = async (req, res, next) => {
    try {
        if (!req.body.raw_url) {
            // Delete the file from S3 or MinIO
            const deleteDocumentResult = await deleteFromBucket(req.body.url);
            console.log('deleteDocumentResult', deleteDocumentResult);
            if (deleteDocumentResult.status !== true) {
                console.log('Error deleting from S3/MinIO:', deleteDocumentResult);
                return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
            }
        } else {
            req.body.url = req.body.raw_url;
        }

        next();
    } catch (err) {
        console.log('deleteDocuments Error:', err);
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};

exports.uploadMultipleDocuments = async (req, res, next) => {
    try {
        const uploadedFiles = [];

        if (!req.files && !req.body.raw_url) {
            return response.error(req, res, { msgCode: 'UPLOAD_FILE' }, httpStatus.BAD_REQUEST);
        }

        // Handle uploading files
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                const filePath = file.path;
                const uploadResult = await uploadToBucket(filePath);
                if (uploadResult.status !== true) {
                    // Delete all successfully uploaded files from local storage if upload fails
                    await deleteFile(filePath);
                    console.log('Error uploading to S3/MinIO:', uploadResult);
                    return response.error(req, res, { msgCode: 'S3_BUCKET_ERROR' }, httpStatus.BAD_REQUEST);
                }

                // Delete the file from local storage after successful upload
                await deleteFile(filePath);

                uploadedFiles.push({
                    originalName: file.originalname,
                    url: uploadResult.data.Location
                });
            }
        }

        // Handle raw URLs
        if (req.body.raw_url && req.body.raw_url.length > 0) {
            for (const raw_url of req.body.raw_url) {
                uploadedFiles.push({
                    originalName: 'Web Document',
                    url: raw_url
                });
            }
        }

        req.body.uploadData = uploadedFiles;
        next();
    } catch (err) {
        console.log('uploadMultipleDocuments Error:', err);
        if (req.files && req.files.length > 0) {
            // Delete the files from local storage in case of error
            for (const file of req.files) {
                await deleteFile(file.path);
            }
        }
        return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
    }
};
