const db = require('../models/index').sequelize;
const response = require('../response/index');
const httpStatus = require('http-status');
const commonService = require('../services/common');

const endpointToResource = {
    'user': 'User Management',
    'role': 'Role Management',
    'customer': 'Customer Management',
    'groups': 'Company Structure',
    'policy': 'Policy',
    'policy': 'Task Overview',
    'ropa': 'Task Overview',
    'ropa-dashboard': 'ROPA Dashboard',
    'policy-dashboard': 'Policy Dashboard',
    'ticket-dashboard': 'Support Dashboard',
    'validate-user': 'Awareness Program',
    'ropa-dashboard': 'Dashboard',
    'policy-dashboard': 'Dashboard',
    'assessments': 'Assessment Management',
    'assessments-dashboard': 'Dashboard',
    'lia': 'Legitimate Interests Assessment',
    'tia': 'Transfer Impact Assessment',
    'pia': 'Privacy Impact Assessment',
    'pda': 'Privacy By Design Assessment',
    'vrm': 'Vendor Risk Management',
    'vrm-dashboard': 'Dashboard',
    'workflow': 'Data Subject Rights Management',
    'workflowsteps': 'Data Subject Rights Management',
    'dsr': 'Data Subject Rights Management',
    'dsr-dashboard': 'Dashboard',
    'privacy-ops': 'Privacy Ops',
    'data-breach-management': 'Data Breach Management',
    'data-breach-dashboard': 'Dashboard',
    'grafana-dashboard': 'Data Visualization'
};

exports.checkRoleAccess = async (req, res, next) => {
    const userRole = req.data.roleId;
    const { UserRolePrivileges, Resources, Role } = db.models;

    // Query the database to get the resources accessible by the user's role
    const resources = await commonService.getListAssociateWithoutCount(UserRolePrivileges, Resources, { role_id: userRole }, {}, ['resource_id'], ['resource_name']);
    if (!resources) {
        return response.error(req, res, { msgCode: 'RESOURCES_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }
    // Map the resources to their names
    const resourceNames = resources?.map(resource => resource?.Resource?.resource_name);
    // Check if the requested endpoint corresponds to one of the accessible resources
    const requestedResource = endpointToResource[req.path.split('/')[1]];
    // console.log(requestedResource);
    // console.log(resourceNames);
    if (!resourceNames.includes(requestedResource)) {
        return response.error(req, res, { msgCode: 'FORBIDDEN' }, httpStatus.FORBIDDEN);
    }

    const role = await commonService.findByCondition(Role, { id: userRole });
    if (!role) {
        return response.error(req, res, { msgCode: 'ROLE_NOT_FOUND' }, httpStatus.NOT_FOUND);
    }

    req.data.is_module_head = role.is_module_head;

    // Attach the role name to the request object
    if (req.data.is_module_head) {
        req.data.roleName = 'Data Protection Officer';
    }else{
        req.data.roleName = role.role_name;
    }
    req.data.role_level = role.role_name;
    next();
};
