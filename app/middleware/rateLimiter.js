const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour in milliseconds
  max: 20, // limit each IP to 10 requests per windowMs
  handler: (req, res) => {
    const response = {
      success: false,
      status_code: 405,
      message: 'Too many requests, please try again later.',
      result: {
        error: 'error'
      },
      time: Date.now()
    };
    res.status(429).json(response); // 429 Too Many Requests
  }
});

module.exports = limiter;
