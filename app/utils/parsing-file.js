const XLSX = require('xlsx');
const pdf = require('pdf-parse');
const fs = require('fs');
const csv = require('csv-parser');
const ollamaService = require('./ollama');
const embeddingService = require('./embeddings');

exports.parseFile = async (filePath, mimeType) => {
  if (mimeType.includes('text/csv')) {
    console.log('---->>>>parsing csv file ');
    return await parseCSV(filePath);
  } else if (mimeType.includes('spreadsheetml') || mimeType.includes('excel')) {
    console.log('---->>>>parsing excel file ');
    return parseExcel(filePath);
  } else if (mimeType.includes('pdf')) {
    console.log('---->>>>parsing pdf file ');
    return parsePDF(filePath);
  } else if (mimeType.includes('text/plain')) {
    console.log('---->>>>parsing text file ');
    return parseText(filePath);
  } else {
    throw new Error('Unsupported file type');
  }
};

async function parseCSV(filePath) {
  const rows = [];
  const headersSet = new Set();

  return new Promise((resolve, reject) => {
    try {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('headers', headers => headers.forEach(h => headersSet.add(h)))
        .on('data', row => rows.push(row))
        .on('end', async () => {
          try {
            let mapping = {} ;
            const headers = Array.from(headersSet);
            const hasRequiredHeaders = ['Title', 'Explanation', 'Question'].every(h => headers.includes(h));
            if (hasRequiredHeaders) {
              mapping.question = 'Title';
              mapping.answer = 'Answer'; // Make sure 'Answer' exists in headers too if needed
            } else {
              mapping = await detectColumns(headers);
            }
            const controlHeader = mapping['question']?.trim().toLowerCase();
            const answerHeader = mapping['answer']?.trim().toLowerCase();

            if (!controlHeader || !answerHeader) {
              return reject(new Error('Invalid header mapping returned by detectColumns'));
            }

            const extractedData = await Promise.all(
              rows.map(async row => {
                try {
                  const normalizedRow = Object.entries(row).reduce((acc, [key, value]) => {
                    acc[key.trim().toLowerCase()] = value;
                    return acc;
                  }, {});

                  const question = normalizedRow[controlHeader];
                  const answer = normalizedRow[answerHeader];
                  if (question === undefined || question === '') return null; // skip empty rows
                  const embedding = await embeddingService.generateEmbedding(question || '');
                  
                  return {
                    question,
                    answer,
                    embedding
                  };
                } catch (innerErr) {
                  console.error('Error processing row:', row, innerErr);
                  return null; // skip problematic rows
                }
              })
            );

            resolve(extractedData.filter(Boolean)); // filter out any nulls from failed rows
          } catch (err) {
            console.error('Error during end processing:', err);
            reject(err);
          }
        })
        .on('error', err => {
          console.error('CSV stream error:', err);
          reject(err);
        });
    } catch (err) {
      console.error('Unexpected error:', err);
      reject(err);
    }
  });
}

async function parseExcel(filePath) {
  try {
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const rows = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);
    const headers = Object.keys(rows[0] || {});

    let mapping = {};
    const hasRequiredHeaders = ['Title', 'Explanation', 'Question'].every(h => headers.includes(h));
    if (hasRequiredHeaders) {
      mapping.question = 'Title';
      mapping.answer = 'Answer'; // Make sure 'Answer' exists in headers too if needed
    } else {
      mapping = await detectColumns(headers);
    }
    const controlHeader = mapping['question']?.trim().toLowerCase();
    const answerHeader = mapping['answer']?.trim().toLowerCase();

    if (!controlHeader || !answerHeader) {
      throw new Error('Invalid header mapping returned by detectColumns');
    }

    const extractedData = await Promise.all(
      rows.map(async row => {
        try {
          const normalizedRow = Object.entries(row).reduce((acc, [key, value]) => {
            acc[key.trim().toLowerCase()] = value;
            return acc;
          }, {});

          const question = normalizedRow[controlHeader];
          const answer = normalizedRow[answerHeader];
          if (question === undefined || question === '') return null; // skip empty rows
          const embedding = await embeddingService.generateEmbedding(question || '');
          
          return {
            question,
            answer,
            embedding
          };
        } catch (innerErr) {
          console.error('Error processing row:', row, innerErr);
          return null; // skip problematic rows
        }
      })
    );

    return extractedData.filter(Boolean); // Filter out null values
  } catch (err) {
    console.error('Error parsing Excel file:', err);
    throw err;
  }
}

async function parsePDF(filePath) {
  const dataBuffer = fs.readFileSync(filePath);
  const data = await pdf(dataBuffer);
  const extractedPairs = await generateQuestionsAndAnswersFromPDF(data);
  console.log('extractedPairs', extractedPairs);
  const extractedData = await Promise.all(
    extractedPairs.map(async line => ({
      question: line.question,
      answer: line.answer,
      embedding: await embeddingService.generateEmbedding(line.question)
    }))
  );
  return extractedData;
}

async function parseText(filePath) {
  try {
    const text = fs.readFileSync(filePath, 'utf8');
    const extractedPairs = await extractQuestionsAndAnswers(text);

    // Generate embeddings for each question
    const extractedData = await Promise.all(
      extractedPairs.map(async pair => ({
        question: pair.question,
        answer: pair.answer,
        embedding: await embeddingService.generateEmbedding(pair.question)
      }))
    );

    return extractedData;
  } catch (err) {
    console.error('Error parsing text file:', err);
    throw err;
  }
}

async function detectColumns(headers) {
  // Create a sample of the data to help with detection

  const prompt = `
You will be given a list of CSV headers.
 
Task:
- Identify the most likely column for Control Question.
- Identify the most likely column for Answer.
 
Only output a JSON object in the format shown below.
Do NOT add any text, explanation, or markdown.

Headers:
${headers.join(', ')}

Respond ONLY with valid JSON like this:

{
  "question": "<exact header name for control question>",
  "answer": "<exact header name for answer>"
}
`;

  const response = await ollamaService.simplePrompt(prompt);
  const jsonMatch = response.match(/\{[\s\S]*?\}/);
  return JSON.parse(jsonMatch[0]);
}

async function extractQuestionsAndAnswers(text) {
  const prompt = `
Extract all question-answer pairs from the following text.
If a question doesn't have a clear answer, use "Not specified" as the answer.
Format your response as a JSON array of objects with "question" and "answer" properties.

Text:
${text}

Expected output format:
[
  {"question": "Question 1?", "answer": "Answer 1"},
  {"question": "Question 2?", "answer": "Answer 2"}
]
`;

  try {
    const response = await ollamaService.simplePrompt(prompt);
    const jsonMatch = response.match(/\[\s*\{[\s\S]*\}\s*\]/);
    if (!jsonMatch) {
      console.error('Failed to extract JSON from LLM response');
      return [];
    }

    const extractedPairs = JSON.parse(jsonMatch[0]);

    // Generate embeddings for each question
    // const extractedData = await Promise.all(
    //   extractedPairs.map(async pair => ({
    //     question: pair.question,
    //     answer: pair.answer,
    //     embedding: await embeddingService.generateEmbedding(pair.question)
    //   }))
    // );

    return extractedPairs;
  } catch (err) {
    console.error('Failed to extract Q&A pairs:', err);
    return [];
  }
}
async function generateQuestionsAndAnswersFromPDF(text) {
  // Process the text in chunks to handle larger documents

  // console.log('Processing PDF text in chunks...', text);
  const chunkSize = 8000;
  const chunks = [];

  // Split text into manageable chunks with some overlap
  for (let i = 0; i < text.text.length; i += chunkSize - 500) {
    chunks.push(text.text.substring(i, i + chunkSize));
  }

  // Process each chunk and collect results
  const allResults = [];

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];

    const prompt = `
Generate meaningful question-answer pairs from this unstructured PDF text chunk.
This is chunk ${i + 1} of ${chunks.length}.

Task:
1. Identify key information, facts, statements, and data points in the text.
2. For each important piece of information, formulate a clear, specific question that would have this information as its answer.
3. Extract or summarize the corresponding answer from the text.

Guidelines:
- Create questions that are specific and directly answerable from the text
- Questions should cover the main topics, facts, figures, and important details
- Ensure questions are diverse and cover different aspects of the content
- Formulate questions as if they would be used in a knowledge base or FAQ
- Make sure answers are accurate and directly supported by the text

Text chunk:
${chunk}

Return ONLY a JSON array with generated question-answer pairs in this format:
[
  {"question": "What is the main purpose of this document?", "answer": "The document outlines the company's data protection policy."},
  {"question": "Who is responsible for data security according to the text?", "answer": "The Chief Information Security Officer (CISO) is responsible for data security."}
]

Generate at least 10-15 question-answer pairs if the text contains sufficient information.
`;
    const response = await ollamaService.simplePrompt(prompt);
    // Extract JSON from the response
    const jsonMatch = response.match(/\[\s*\{[\s\S]*\}\s*\]/);
    if (jsonMatch) {
      try {
        const chunkResults = JSON.parse(jsonMatch[0]);
        allResults.push(...chunkResults);
      } catch (parseError) {
        console.error(`Failed to parse JSON from chunk ${i + 1}:`, parseError);
      }
    } else {
      console.error(`No valid JSON found in response for chunk ${i + 1}`);
    }
  }

  // Remove duplicates and similar questions
  const uniqueResults = [];
  const seenQuestions = new Map(); // Map question to its index in uniqueResults

  for (const item of allResults) {
    // Skip empty or invalid entries
    if (!item.question || !item.answer) continue;

    // Normalize question text for comparison
    const normalizedQuestion = item.question.toLowerCase().replace(/\s+/g, ' ').trim();

    // Check for duplicates or very similar questions
    let isDuplicate = false;
    let similarQuestionIndex = -1;

    for (const [existingQuestion, index] of seenQuestions.entries()) {
      // Check if questions are similar (simple check - could be improved)
      if (existingQuestion.includes(normalizedQuestion) || normalizedQuestion.includes(existingQuestion) || calculateSimilarity(existingQuestion, normalizedQuestion) > 0.7) {
        isDuplicate = true;
        similarQuestionIndex = index;
        break;
      }
    }

    if (isDuplicate && similarQuestionIndex >= 0) {
      // If the new answer is more detailed, update the existing entry
      const existingAnswer = uniqueResults[similarQuestionIndex].answer;
      if (item.answer.length > existingAnswer.length * 1.5) {
        uniqueResults[similarQuestionIndex].answer = item.answer;
      }
    } else {
      // Add new unique question
      seenQuestions.set(normalizedQuestion, uniqueResults.length);
      uniqueResults.push(item);
    }
  }

  return uniqueResults;
}
// Simple function to calculate text similarity
function calculateSimilarity(str1, str2) {
  const words1 = str1.split(' ');
  const words2 = str2.split(' ');

  const set1 = new Set(words1);
  const set2 = new Set(words2);

  const intersection = new Set([...set1].filter(x => set2.has(x)));
  const union = new Set([...set1, ...set2]);

  return intersection.size / union.size;
}
