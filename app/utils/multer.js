const multer = require('multer');
const path = require('path');

const MAX_FILE_SIZE_MB = process.env.MAX_FILE_SIZE ? parseInt(process.env.MAX_FILE_SIZE, 10) : 100; // Default 100MB
const MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024; // Convert MB to bytes
// Define storage settings for multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/'); // Specify the destination folder where uploaded files will be stored
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, uniqueSuffix + path.extname(file.originalname)); // Generate unique filenames
  }
});

// Define file type filter
const fileFilter = function (req, file, cb) {
  console.log('sss', file);
  // Define accepted mime types and corresponding file extensions
  const acceptedTypes = [
    'image/jpeg', // JPEG
    'image/png', // PNG
    'image/heic', // HEIC
    'application/pdf', // PDF
    'application/msword', // DOC
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
    'application/vnd.ms-excel', // XLS
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
    'text/csv', //CSV
    'application/zip', //Zip file
    'application/javascript',
    'text/plain' // JavaScript file
  ];

  // Check if the uploaded file's MIME type is in the accepted types array
  if (acceptedTypes.includes(file.mimetype)) {
    cb(null, true); // Accept file
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, HEIC, PDF, DOC, DOCX, XLS, XLSX, JS and CSV files are allowed.'), false); // Reject file
  }
};

// Initialize multer instance with storage and file type filter
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE // Use the converted file size in bytes
  }
});

// console.log('-----', upload);

module.exports = upload;
