const response = require('../response');
const httpStatus = require('http-status');
const passwordHash = require('../utils/password');
const db = require('../models/index').sequelize;
const { env } = require('../constant/environment');
const commonService = require('../services/common');
const axios = require('axios');
const dsrService = require('../services/dsr');

exports.automationOnCreateRequest = async data => {
  const { DsrRequest, DataSubject, CustomRequestTask } = db.models;

  // Automation start here
  let taskLimit = 1;
  const order = [
    ['stage_id', 'ASC'], // First, order by stage_id ascending
    ['id', 'ASC'] // Then, order by id ascending within each stage
  ];

  const getTasks = await commonService.getList(CustomRequestTask, { request_id: data.request_id }, {}, taskLimit, null, order);
  const dataSubjectData = await commonService.getDataAssociate(DsrRequest, DataSubject, { id: data.request_id }, {}, ['id', 'dsr_id'], ['id', 'email', 'first_name', 'last_name', 'phone_no']);
  let dataSubjectRespo = await dsrService.getOneRecord(DataSubject, { id: dataSubjectData.DataSubject.id }, ['id', 'email', 'first_name', 'last_name', 'unique_identification_type', 'unique_identification_number', 'phone_no', 'dob']);

  const automation_id = [];

  for (let task of getTasks.rows) {
    if (task.progress !== 'COMPLETED' && task.activepieces_automation_id) {
      const task_data = {
        task_id: task.id,
        activepieces_automation_id: task.activepieces_automation_id,
        dsr_request_id: dataSubjectData.id,
        data_subject_id: dataSubjectData.DataSubject.id,
        phone_number: dataSubjectData.DataSubject?.phone_no
      };
      automation_id.push(task_data);
      break;
    } else {
      break;
    }
  }

  console.log(automation_id);
  if (automation_id && automation_id[0]) {
    const activepiecesObj = automation_id[0];
    await callActivepiecesWebhook(activepiecesObj, dataSubjectRespo);
  }

  return true;
};

exports.automationOnCompleteTask = async data => {
  const { CustomRequestTask, DSRAuditLog, User, TaskDocument, DsrRequest, DataSubject } = db.models;

  const dataSubjectData = await commonService.getDataAssociate(DsrRequest, DataSubject, { id: data.request_id }, {}, ['id', 'dsr_id'], ['id', 'email', 'first_name', 'last_name', 'phone_no']);
  let dataSubjectRespo = await dsrService.getOneRecord(DataSubject, { id: dataSubjectData.DataSubject.id }, ['id', 'email', 'first_name', 'last_name', 'unique_identification_type', 'unique_identification_number', 'phone_no', 'dob']);

  const order = [
    ['stage_id', 'ASC'], // First, order by stage_id ascending
    ['id', 'ASC'] // Then, order by id ascending within each stage
  ];

  const getTasks = await commonService.getList(CustomRequestTask, { id: { [db.Sequelize.Op.gt]: data.completed_task_id }, request_id: data.request_id }, {}, null, null, order);

  const automation_id = [];
  for (let task of getTasks.rows) {
    const checkPreviousTask = await commonService.getList(
      CustomRequestTask,
      { id: { [db.Sequelize.Op.lt]: task.id }, request_id: data.request_id, progress: { [db.Sequelize.Op.in]: ['NOT_STARTED', 'IN_PROGRESS', 'REJECTED'] } },
      {},
      null,
      null,
      order
    );

    if (checkPreviousTask.count === 0) {
      if (task.progress !== 'COMPLETED' && task.activepieces_automation_id) {
        const task_data = {
          task_id: task.id,
          activepieces_automation_id: task.activepieces_automation_id,
          dsr_request_id: dataSubjectData.id,
          data_subject_id: dataSubjectData.DataSubject.id,
          phone_number: dataSubjectData.DataSubject?.phone_no
        };
        automation_id.push(task_data);
        break;
      } else {
        // break;
        console.log('else');
      }
    } else {
      break;
    }
  }

  console.log(automation_id);
  if (automation_id && automation_id[0]) {
    const activepiecesObj = automation_id[0];
    await callActivepiecesWebhook(activepiecesObj, dataSubjectRespo);
  }

  return true;
};

const callActivepiecesWebhook = async (activepiecesObj, dataSubjectRespo) => {
  const activepiecesWebhookUrl = process.env.ACTIVEPIECES_WEBHOOK_URL ? process.env.ACTIVEPIECES_WEBHOOK_URL : 'https://workflow-dev.gotrust.tech';

  let webhookurl = `${activepiecesWebhookUrl}/api/v1/webhooks/${activepiecesObj.activepieces_automation_id}`;
  console.log(webhookurl);

  let paramData = {
    task_id: activepiecesObj.task_id,
    activepieces_automation_id: activepiecesObj.activepieces_automation_id,
    phone_number: activepiecesObj.phone_number,
    dsr_request_id: activepiecesObj.dsr_request_id,
    data_subject_id: activepiecesObj.data_subject_id
  };

  let jsonData = JSON.stringify(paramData);
  console.log(jsonData);

  // Add task_id to the response
  dataSubjectRespo['task_id'] = activepiecesObj.task_id;

  if (webhookurl) {
    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: webhookurl,
      headers: {},
      data: dataSubjectRespo
    };

    console.log('---', config);
    let x = await axios.request(config);
    console.log(x);
    console.log('=== webhook is called');
  }
};
