const ollamaService = require('../utils/ollama');
exports.generateCombinedPrivacyNotice = async (data,companyName,regulations) => {
    try {
      console.log('Generating privacy notices for multiple departments');
      const departmentNotices = [];
      console.log(companyName,regulations)
      
      // Handle the case where data is wrapped in a qaData object
      let departmentsData = data;
      if (data && data.qaData && typeof data.qaData === 'object') {
        departmentsData = data.qaData;
      }
      
      // If departmentsData is an object with department names as keys
      if (departmentsData && typeof departmentsData === 'object' && !Array.isArray(departmentsData)) {
        console.log('Processing object with department keys');
        // Process each department
        for (const [deptName, qaItems] of Object.entries(departmentsData)) {
          console.log(`Processing department: ${deptName} with ${Array.isArray(qaItems) ? qaItems.length : 0} QA pairs`);
          
          // Skip if no QA items
          if (!Array.isArray(qaItems) || qaItems.length === 0) {
            console.log(`Skipping department ${deptName} - no QA data`);
            continue;
          }
          
          // Generate department-specific privacy notice
          const deptNotice = await this.generateStructuredPrivacyNotice(qaItems);
          
          if (deptNotice) {
            departmentNotices.push({
              name: deptName,
              notice: deptNotice
            });
          }
          
          // Add a small delay to prevent rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } else if (Array.isArray(departmentsData)) {
        // Handle array format (previous implementation)
        console.log(`Processing array of ${departmentsData.length} departments`);
        
        for (const deptData of departmentsData) {
          const deptName = deptData.name || 'Unnamed Department';
          let qaData = deptData.qaData || deptData.questions || [];
          
          // Process qaData as before...
          if (!Array.isArray(qaData) && qaData.items && Array.isArray(qaData.items)) {
            qaData = qaData.items;
          }
          
          // Convert to standard format if needed
          if (Array.isArray(qaData)) {
            qaData = qaData.map(item => {
              if (item.question !== undefined && item.answer !== undefined) {
                return item;
              }
              return {
                question: item.question || item.q || item.title || 'Unknown Question',
                answer: item.answer || item.a || item.content || 'No answer provided'
              };
            });
          }
          
          console.log(`Processing department: ${deptName} with ${qaData.length} QA pairs`);
          
          if (qaData.length === 0) {
            console.log(`Skipping department ${deptName} - no QA data`);
            continue;
          }
          
          // Generate department-specific privacy notice
          const deptNotice = await this.generateStructuredPrivacyNotice(qaData);
          
          if (deptNotice) {
            departmentNotices.push({
              name: deptName,
              notice: deptNotice
            });
          }
          
          // Add a small delay to prevent rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } else {
        console.error('Invalid data format for privacy notice generation');
        return false;
      }
      
      // If no valid notices were generated, return false
      if (departmentNotices.length === 0) {
        console.error('No valid privacy notices could be generated');
        return false;
      }
      
      // Combine all department notices into a single comprehensive notice
      const combinedNotice = await this.mergePrivacyNotices(departmentNotices, companyName, regulations);
      
      // Return both individual department notices and the combined notice
      return {
        combinedNotice
      };
      
    } catch (err) {
      console.error('Failed to generate combined privacy notice:', err);
      return false;
    }
  };

  exports.mergePrivacyNotices = async (departmentNotices,companyName,regulations) => {
    try {
        
      console.log('Merging privacy notices from multiple departments');
      
      // Extract all sections from all department notices
      const allSections = {};
      
      // Define the section order for the final document
      const sectionOrder = [
        "Introduction",
        "Information We Collect",
        "How We Collect Information",
        "How We Use Information",
        "How We Share Information",
        "Your Privacy Rights",
        "Data Security",
        "International Data Transfers",
        "Data Retention",
        "Children's Privacy",
        "Changes to This Privacy Notice",
        "Contact Information"
      ];
      
      // Initialize sections map
      sectionOrder.forEach(section => {
        allSections[section] = [];
      });
      
      // Extract sections from each department notice
      for (const deptNotice of departmentNotices) {
        const deptName = deptNotice.name;
        const noticeHtml = deptNotice.notice;
        
        // Extract sections using regex
        for (const sectionTitle of sectionOrder) {
          const sectionRegex = new RegExp(`<h2>${sectionTitle}<\\/h2>([\\s\\S]*?)(?=<h2>|$)`, 'i');
          const match = noticeHtml.match(sectionRegex);
          
          if (match && match[1]) {
            allSections[sectionTitle].push({
              department: deptName,
              content: match[1].trim()
            });
          }
        }
      }
      
      // Merge sections from all departments
      const mergedSections = {};
      
      for (const sectionTitle of sectionOrder) {
        if (allSections[sectionTitle].length > 0) {
          // Use OpenAI to merge the sections
          const sectionContents = allSections[sectionTitle].map(s => 
            `DEPARTMENT: ${s.department}\nCONTENT: ${s.content}`
          ).join('\n\n');
          
          let mergePrompt = `
  Merge the following department-specific sections of a privacy notice into a single cohesive "${sectionTitle}" section.
  Remove redundancies, combine similar points, and ensure the merged content is comprehensive but concise.
  
  ${sectionContents}

   IMPORTANT INSTRUCTIONS:
   - Use ONLY the information provided in the DEPARTMENT-SPECIFIC CONTENT section. Do NOT add any information that is not explicitly mentioned.
   - Remove redundancies and avoid repeating the same information across departments.
   - Combine similar points into a unified statement where possible.
   - If departments have different practices, clearly specify which practice applies to which department.
   - Ensure the content is legally compliant and uses formal, professional language.

  
  FORMAT REQUIREMENTS:
  - Start with <h2>${sectionTitle}</h2>
  - Use <h3> for subheadings
  - Use <p> for paragraphs
  - Use <ul> and <li> for lists
  - Use <strong> for important terms
  - Be comprehensive but concise
  - Use formal, professional language
  - Be legally compliant
  - Maintain all important information from each department
  - Where departments have different practices, specify which practice applies to which department
  
  `;
  
  if (sectionTitle === "Introduction") {
    mergePrompt += `
ADDITIONAL CONTEXT:
- Company Name: ${companyName}
- Regulations: ${regulations}
Include this information in the "Introduction" section.`;
  }
  mergePrompt+=`
  Return ONLY the HTML for this merged section.`;

          // Use the same service as for generating individual sections
          const mergedSection = await ollamaService.simplePrompt(mergePrompt);
          mergedSections[sectionTitle] = mergedSection;
          
          // Add a small delay to prevent rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      // Combine all merged sections in the correct order
      const combinedContent = sectionOrder
        .filter(title => mergedSections[title])
        .map(title => mergedSections[title])
        .join('\n\n');
      
      // Clean up the response
      let cleanedResponse = combinedContent
        .replace(/<\!DOCTYPE[^>]*>/gi, '')
        .replace(/<html[^>]*>|<\/html>/gi, '')
        .replace(/<head>[\s\S]*?<\/head>/gi, '')
        .replace(/<body[^>]*>|<\/body>/gi, '');
      
      // Add a table of contents
  //     const tableOfContents = `
  // <div class="toc">
  //   <h2>Table of Contents</h2>
  //   <ul>
  //     ${sectionOrder
  //       .filter(title => mergedSections[title])
  //       .map(title => `<li><a href="#${title.toLowerCase().replace(/\s+/g, '-')}">${title}</a></li>`)
  //       .join('\n    ')}
  //   </ul>
  // </div>`;
      
      // Add IDs to section headings for TOC links
      cleanedResponse = cleanedResponse.replace(
        /<h2>(.*?)<\/h2>/gi, 
        (match, title) => `<h2 id="${title.toLowerCase().replace(/\s+/g, '-')}">${title}</h2>`
      );
      
      // Wrap in privacy-notice div with combined class
      return `<div class="privacy-notice combined-notice">
  ${cleanedResponse}
  </div>`;
      
    } catch (err) {
      console.error('Failed to merge privacy notices:', err);
      return false;
    }
  }
exports.generateStructuredPrivacyNotice = async(qaData)=> {
    try {
      // Define the sections we need to generate
      const sections = [
        { title: "Introduction", description: "Company identity, scope, definitions" },
        { title: "Information We Collect", description: "Categories of personal data collected" },
        { title: "How We Collect Information", description: "Collection methods, sources" },
        { title: "How We Use Information", description: "Processing purposes, legal bases" },
        { title: "How We Share Information", description: "Recipients, sharing circumstances" },
        { title: "Your Privacy Rights", description: "User rights, exercise procedures" },
        { title: "Data Security", description: "Security measures, breach procedures" },
        { title: "International Data Transfers", description: "Cross-border mechanisms" },
        { title: "Data Retention", description: "Retention periods, criteria" },
        { title: "Children's Privacy", description: "Minor protections, age restrictions" },
        { title: "Changes to This Privacy Notice", description: "Update procedures" },
        { title: "Contact Information", description: "Privacy inquiries contact details" }
      ];
      
      // Generate each section individually with minimal context
      console.log('Generating privacy notice section by section');
      const sectionContents = [];
      
      // Extract essential company information for context
      const companyInfo = extractMinimalCompanyInfo(qaData);
      
      for (const section of sections) {
        // Select only the most relevant Q&A pairs for this section (max 10)
        const relevantQA = selectRelevantQAPairs(qaData, section.title, 10);
        
        // Create a minimal prompt for this section
        const sectionPrompt = `
  Generate the "${section.title}" section of a privacy notice based on this information:
  
  COMPANY INFO: ${companyInfo}
  
  RELEVANT Q&A:
  ${relevantQA.map(qa => `Q: ${qa.question}\nA: ${qa.answer}`).join('\n\n')}
  
  SECTION DESCRIPTION: ${section.description}
  
  FORMAT REQUIREMENTS:
  - Start with <h2>${section.title}</h2>
  - Use <h3> for subheadings
  - Use <p> for paragraphs
  - Use <ul> and <li> for lists
  - Use <strong> for important terms
  - Be comprehensive but concise (250-350 words)
  - Use formal, professional language
  - Be legally compliant
  - Use ONLY the information provided in the RELEVANT Q&A section. Do NOT add any information that is not explicitly mentioned in the Q&A.
  
  Return ONLY the HTML for this section.`;



  
        // Generate the section content
        const sectionContent = await ollamaService.simplePrompt(sectionPrompt);
        sectionContents.push(sectionContent);
        
        // Add a small delay to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // Combine all sections
      const fullNotice = sectionContents.join('\n\n');
      
      // Clean up the response - remove any HTML, head, body tags
      let cleanedResponse = fullNotice
        .replace(/<\!DOCTYPE[^>]*>/gi, '')
        .replace(/<html[^>]*>|<\/html>/gi, '')
        .replace(/<head>[\s\S]*?<\/head>/gi, '')
        .replace(/<body[^>]*>|<\/body>/gi, '');
      
      // Wrap in privacy-notice div
      return `<div class="privacy-notice">${cleanedResponse}</div>`;
    } catch (err) {
      console.error('Failed to generate structured privacy notice:', err);
      return false;
    }
  };
  
  // Helper function to extract minimal company information
  function extractMinimalCompanyInfo(qaData) {
    // Look for company name
    const companyNameQA = qaData.find(qa => 
      qa.question.toLowerCase().includes("company name") || 
      qa.question.toLowerCase().includes("organization name") ||
      qa.question.toLowerCase().includes("business name")
    );
    
    // Look for business type/industry
    const businessTypeQA = qaData.find(qa => 
      qa.question.toLowerCase().includes("industry") || 
      qa.question.toLowerCase().includes("business type") ||
      qa.question.toLowerCase().includes("sector")
    );
    
    // Look for location/country
    const locationQA = qaData.find(qa => 
      qa.question.toLowerCase().includes("country") || 
      qa.question.toLowerCase().includes("location") ||
      qa.question.toLowerCase().includes("jurisdiction")
    );
    
    let companyInfo = "Company information: ";
    
    if (companyNameQA) {
      companyInfo += `Name: ${companyNameQA.answer.substring(0, 50)}; `;
    }
    
    if (businessTypeQA) {
      companyInfo += `Business type: ${businessTypeQA.answer.substring(0, 50)}; `;
    }
    
    if (locationQA) {
      companyInfo += `Location: ${locationQA.answer.substring(0, 50)}; `;
    }
    
    return companyInfo;
  }
  
  // Helper function to select relevant Q&A pairs for a section
  function selectRelevantQAPairs(qaData, sectionTitle, maxPairs) {
    // Keywords for matching Q&A pairs to sections
    const keywordMap = {
      "Introduction": ["company", "business", "organization", "about", "who", "what is", "introduction"],
      "Information We Collect": ["collect", "information", "data", "personal", "gather", "obtain", "categories"],
      "How We Collect Information": ["how collect", "sources", "cookies", "tracking", "forms", "submission"],
      "How We Use Information": ["use", "purpose", "why", "process", "processing", "reason"],
      "How We Share Information": ["share", "disclose", "third party", "third-party", "recipient", "transfer"],
      "Your Privacy Rights": ["rights", "access", "delete", "correct", "opt-out", "opt out", "control"],
      "Data Security": ["security", "protect", "safeguard", "breach", "incident", "secure"],
      "International Data Transfers": ["international", "cross-border", "transfer", "country", "region"],
      "Data Retention": ["retention", "store", "keep", "period", "duration", "long"],
      "Children's Privacy": ["children", "minor", "kid", "young", "age", "under 13", "under 16"],
      "Changes to This Privacy Notice": ["change", "update", "modify", "revise", "amendment"],
      "Contact Information": ["contact", "reach", "email", "phone", "address", "inquiry", "question"]
    };
    
    // Get keywords for this section
    const keywords = keywordMap[sectionTitle] || [];
    
    // Score each Q&A pair based on keyword matches
    const scoredPairs = qaData.map(qa => {
      const combinedText = (qa.question + " " + qa.answer).toLowerCase();
      let score = 0;
      
      for (const keyword of keywords) {
        if (combinedText.includes(keyword.toLowerCase())) {
          score += 1;
        }
      }
      
      return { qa, score };
    });
    
    // Sort by score (descending) and take top N
    const topPairs = scoredPairs
      .sort((a, b) => b.score - a.score)
      .slice(0, maxPairs)
      .map(item => item.qa);
    
    // If no matches found, return some general Q&A pairs
    if (topPairs.length === 0) {
      return qaData.slice(0, 2); // Just take first two Q&A pairs as fallback
    }
    
    return topPairs;
  }