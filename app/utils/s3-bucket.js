const AWS = require("aws-sdk");
const fs = require("fs");
const AWSCredentials = {
  accessKey: process.env.S3_ACCESSKEYID,
  secret: process.env.S3_SECRETKEY,
  bucketName: process.env.BUCKET_NAME,
};

const s3 = new AWS.S3({
  accessKeyId: AWSCredentials.accessKey,
  secretAccessKey: AWSCredentials.secret,
});

const uploadToS3 = (fileName) => {

  console.log("fileName", fileName)
  const fileContent = fs.readFileSync(fileName);

  return new Promise((resolve, reject) => {

    const params = {
      Bucket: AWSCredentials.bucketName,
      Key: fileName,
      Body: fileContent,
    };
    console.log("params", params.Key);

    s3.upload(params, function (err, data) {
      if (err) {
        reject({ status: false, data: err });
      }
      console.log(`File uploaded successfully. ${data}`);
      resolve({ status: true, data: data });
    });
  });
};

const uploadBufferToS3 = (buffer, fileName) => {

  console.log("fileName", fileName)

  return new Promise((resolve, reject) => {

    const params = {
      Bucket: AWSCredentials.bucketName,
      Key: fileName,
      Body: buffer,
    };
    console.log("params", params.Key);

    s3.upload(params, function (err, data) {
      if (err) {
        reject({ status: false, data: err });
      }
      console.log(`File uploaded successfully. ${data}`);
      resolve({ status: true, data: data });
    });
  });
};

const deleteFromS3 = (url) => {
  return new Promise((resolve, reject) => {
    const key = url.split('.com/')[1];
    const params = {
      Bucket: AWSCredentials.bucketName,
      Key: key,
    };
    console.log("params", params);
    s3.deleteObject(params, function (err, data) {
      if (err) {
        reject({ status: false, data: err });
      }
      console.log(`File deleted successfully. ${data}`);
      resolve({ status: true, data: data });
    });
  });
}

const downloadFile = (fileKey, filePath) => {
  console.log("fileKey", fileKey);
  console.log("filePath", filePath);
  const decodedUrl = decodeURIComponent(fileKey);
  const s3Key = decodedUrl.split('.com/')[1]; // Convert backslashes to forward slashes
  return new Promise((resolve, reject) => {
    const params = {
      Bucket: AWSCredentials.bucketName, // Ensure AWSCredentials is properly defined
      Key: s3Key,
    };

    s3.getObject(params, (err, data) => {
      if (err) {
        console.error("Error fetching object from S3:", err);
        reject(err);
        return;
      }
      const fileData = data.Body;

      fs.writeFile(filePath, fileData, (err) => {
        if (err) {
          console.error("Error writing file to local system:", err);
          reject(err);
          return;
        }
        resolve(true);
      });
    });
  });
};

module.exports = {
  uploadToS3,
  uploadBufferToS3,
  deleteFromS3,
  downloadFile
};