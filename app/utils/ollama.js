const axios = require('axios');

// exports.simplePrompt = async prompt => {
//   console.log('inside the ollama service');
//   const response = await axios.post('http://localhost:11434/api/generate', {
//     model: 'mistral',
//     prompt,
//     options: { temperature: 0.2 }
//   });
//   console.log('inside the ollama service', response.data);

//   return response.data.trim();
// };

exports.simplePrompt = async prompt => {
  console.log('inside the ollama service');

  try {
    const response = await axios({
      method: 'post',
      url: `${process.env.API_BASE_URL}/api/generate`,
      data: {
        model: `${process.env.PROMPT_MODEL}`,
        prompt,
        options: { temperature: 0.2 }
      },
      responseType: 'stream'
    });

    let finalOutput = '';

    // Read streamed chunks line by line
    for await (const chunk of response.data) {
      const lines = chunk.toString().split('\n');
      // console.log('lines', lines);
      for (const line of lines) {
        if (!line.trim()) continue;
        try {
          const parsed = JSON.parse(line);
          if (parsed.response) {
            finalOutput += parsed.response;
          }
        } catch (err) {
          console.error('⚠️ Failed to parse line:', line);
        }
      }
    }

    // console.log('✅ Final LLM Response:', finalOutput);
    return finalOutput.trim();
  } catch (err) {
    console.error('❌ Ollama API Error:', err.message);
    return err;
  }
};
