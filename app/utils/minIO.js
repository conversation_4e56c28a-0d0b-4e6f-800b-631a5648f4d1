const AWS = require('aws-sdk');
const Minio = require('minio');
const fs = require('fs');

const USE_MINIO = process.env.USE_MINIO === 'true';
const BUCKET_NAME = process.env.BUCKET_NAME;

let client;

// Initialize the client dynamically based on the configuration
if (USE_MINIO) {
  client = new Minio.Client({
    endPoint: process.env.S3_ENDPOINT,
    port: parseInt(process.env.S3_PORT, 10),
    useSSL: process.env.S3_ENDPOINT.startsWith('https'),
    accessKey: process.env.S3_ACCESSKEYID,
    secretKey: process.env.S3_SECRETKEY
  });
  async function ensureBucketExists() {
    try {
      const bucketExists = await client.bucketExists(BUCKET_NAME);
      if (!bucketExists) {
        console.log(`Bucket ${BUCKET_NAME} does not exist. Creating...`);
        await client.makeBucket(BUCKET_NAME);
        console.log(`Bucket ${BUCKET_NAME} created successfully.`);

        // Define public read policy
        const policy = {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Principal: '*',
              Action: ['s3:GetObject'],
              Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`]
            }
          ]
        };

        await client.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));
        console.log(`Bucket ${BUCKET_NAME} is now public.`);
      } else {
        console.log(`Bucket ${BUCKET_NAME} already exists.`);
      }
    } catch (error) {
      console.error('Error ensuring bucket exists:', error);
    }
  }

  ensureBucketExists();
} else {
  client = new AWS.S3({
    accessKeyId: process.env.S3_ACCESSKEYID,
    secretAccessKey: process.env.S3_SECRETKEY
  });
}

async function getDownloadUrl(bucketName, objectName, expiry = 24 * 60 * 60) {
  try {
    const url = await client.presignedGetObject(bucketName, objectName, expiry);
    return url;
  } catch (error) {
    console.error('Error generating download URL:', error);
    throw error;
  }
}

// Upload a file to the bucket
const uploadToBucket = fileName => {
  console.log('fileName', fileName);
  const fileContent = fs.readFileSync(fileName);

  if (USE_MINIO) {
    return new Promise((resolve, reject) => {
      // Normalize the file path to use forward slashes
      const normalizedFileName = fileName.replace(/\\/g, '/');
      console.log('normalizedFileName---', client.host, '-', client.port, '-', normalizedFileName);
      client.putObject(BUCKET_NAME, normalizedFileName, fileContent, async (err, etag) => {
        if (err) {
          if (err.code === 'ENOSPC') {
            console.error('Disk is full on MinIO server.');
          } else {
            console.error('Error uploading to MinIO:', err);
          }
          reject({ status: false, data: err });
        } else {
          // Generate Download URL after successful upload
          // const downloadUrl = await getDownloadUrl(BUCKET_NAME, normalizedFileName);
          const fileLocation = `${process.env.STORAGE_DOMAIN}/api/v1/buckets/${BUCKET_NAME}/objects/download?prefix=${normalizedFileName}`;
          console.log('File uploaded successfully to MinIO. Location:', fileLocation);
          resolve({ status: true, data: { Location: fileLocation, ETag: etag } });
        }
      });
    });
  } else {
    const params = {
      Bucket: BUCKET_NAME,
      Key: fileName,
      Body: fileContent
    };
    return new Promise((resolve, reject) => {
      client.upload(params, function (err, data) {
        if (err) {
          reject({ status: false, data: err });
        }
        console.log('File uploaded successfully.', data);
        resolve({ status: true, data: data });
      });
    });
  }
};

// Upload buffer to S3 or MinIO
const uploadBufferToBucket = (buffer, fileName) => {
  console.log('fileName', fileName);

  return new Promise((resolve, reject) => {
    const params = {
      Bucket: BUCKET_NAME,
      Key: fileName,
      Body: buffer
    };
    console.log('params', params.Key);

    // Use appropriate upload method based on S3 or MinIO
    if (USE_MINIO) {
      const normalizedFileName = fileName.replace(/\\/g, '/');
      client.putObject(BUCKET_NAME, fileName, buffer, (err, etag) => {
        if (err) {
          if (err.code === 'ENOSPC') {
            console.error('Disk is full on MinIO server.');
          } else {
            console.error('Error uploading to MinIO:', err);
          }
          reject({ status: false, data: err });
        } else {
          resolve({ status: true, data: { Location: `${process.env.STORAGE_DOMAIN}/api/v1/buckets/${BUCKET_NAME}/objects/download?prefix=${normalizedFileName}` } });
        }
      });
    } else {
      client.upload(params, function (err, data) {
        if (err) {
          reject({ status: false, data: err });
        }
        resolve({ status: true, data: data });
      });
    }
  });
};

// Delete a file from the bucket
const deleteFromBucket = url => {
  let key;
  if (USE_MINIO) {
    // MinIO-specific parsing logic
    const baseUrl = `${process.env.STORAGE_DOMAIN}/api/v1/buckets/${BUCKET_NAME}/`;
    key = url.replace(baseUrl, '');
  } else {
    // AWS S3-specific parsing logic
    const keyParts = url.split(`${BUCKET_NAME}/`);
    key = keyParts[1] || ''; // Extract everything after the bucket name
  }

  if (USE_MINIO) {
    return new Promise((resolve, reject) => {
      client.removeObject(BUCKET_NAME, key, err => {
        if (err) {
          reject({ status: false, data: err });
        }
        console.log('File deleted successfully.');
        resolve({ status: true });
      });
    });
  } else {
    const params = {
      Bucket: BUCKET_NAME,
      Key: key
    };
    return new Promise((resolve, reject) => {
      client.deleteObject(params, function (err, data) {
        if (err) {
          reject({ status: false, data: err });
        }
        console.log('File deleted successfully.', data);
        resolve({ status: true, data: data });
      });
    });
  }
};

// Download file from S3 or MinIO
const downloadFile = (fileKey, filePath) => {
  console.log('fileKey', fileKey);
  console.log('filePath', filePath);

  const decodedUrl = decodeURIComponent(fileKey);
  const key = decodedUrl.split('.com/')[1]; // Extract key from URL

  return new Promise((resolve, reject) => {
    const params = {
      Bucket: BUCKET_NAME,
      Key: key
    };

    // Use appropriate download method based on S3 or MinIO
    if (USE_MINIO) {
      client.getObject(params.Bucket, params.Key, (err, data) => {
        if (err) {
          reject(err);
          return;
        }
        fs.writeFile(filePath, data, err => {
          if (err) {
            reject(err);
          }
          resolve(true);
        });
      });
    } else {
      client.getObject(params, (err, data) => {
        if (err) {
          reject(err);
          return;
        }
        fs.writeFile(filePath, data.Body, err => {
          if (err) {
            reject(err);
          }
          resolve(true);
        });
      });
    }
  });
};

// Export the functions
module.exports = {
  uploadToBucket,
  uploadBufferToBucket,
  deleteFromBucket,
  downloadFile,
  client
};
