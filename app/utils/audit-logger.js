const { models } = require('../models/index').sequelize;
const commonService = require('../services/common');

/**
 * Creates a detailed audit log entry for DSR actions
 * 
 * @param {Object} options - Configuration for the audit log
 * @param {number} options.dsrId - The DSR request ID
 * @param {number} options.stepId - The workflow step ID
 * @param {number} options.actorId - The user ID of the person performing the action
 * @param {string} options.actionType - Type of action (ASSIGN, STATUS_CHANGE, TASK_CREATE, etc.)
 * @param {Object} options.before - State before the change
 * @param {Object} options.after - State after the change
 * @param {Object} options.metadata - Additional contextual information
 * @param {Object} req - Express request object
 * @param {Object} transaction - Database transaction
 * @returns {Promise<Object>} - The created audit log entry
 */
exports.createDetailedAuditLog = async (options, req, transaction = null) => {
  try {
    const { DSRAuditLog, User, DsrRequest, DsrRequestType } = models;
    const { dsrId, stepId, actorId, actionType, before = {}, after = {}, metadata = {} } = options;
    
    // Get actor information using commonService
    const actor = await commonService.findByCondition(User, 
      { id: actorId }, 
      ['id', 'firstName', 'lastName', 'email']
    );
    if(!actor){
      return false;
    }
    // Get DSR request information using commonService
    const dsrRequest = await commonService.getDataAssociate(
      DsrRequest,
      DsrRequestType,
      { id: dsrId },
      {},
      ['id', 'dsr_id', 'request_type'],
      ['id', 'flowtype'],
      null,
      null,
      null
    );
    if(!dsrRequest){
      return false;
    }
    // Generate detailed description based on action type
    const description = await generateDetailedDescription({
      actionType,
      actor,
      dsrRequest,
      before,
      after,
      metadata
    });
    if(!description){
      return false;
    }

    // Create the audit log entry using commonService
    const auditLog = await commonService.addDetail(
      DSRAuditLog, 
      {
        type: 'DSR',
        type_id: dsrId,
        step: stepId,
        action: description,
        action_by_id: actorId,
        customer_id: req.data.customer_id,
        chat: metadata.isChat || false
      }, 
      transaction
    );
    return auditLog;
  } catch (error) {
    console.log('error', error);
    return error;
  }
};

/**
 * Generates a detailed description for the audit log based on action type
 * 
 * @param {Object} options - Options for generating the description
 * @returns {Promise<string>} - The detailed description
 */
async function generateDetailedDescription(options) {
  try{
    // Destructure options}
  const { actionType, actor, dsrRequest, before, after, metadata } = options;
  const { User } = models;
  
  // Format actor name
  const actorName = actor ? `${actor.firstName} ${actor.lastName}` : "Unknown user";
  
  // Format DSR information
  const dsrInfo = dsrRequest ? 
    `DSR #${dsrRequest.dsr_id} (${dsrRequest.DsrRequestType?.flowtype || 'Unknown type'})` : 
    "Unknown DSR";
  
  // Base description starts with actor and DSR info
  let description = `${actorName} performed an action on ${dsrInfo}`;
  
  // Add detailed information based on action type
  switch (actionType) {
    case 'ASSIGN': {
      description = `${actorName} assigned ${dsrInfo} to `;
      
      // Get assignee information if available
      if (after.assignee_id) {
        const assignee = await commonService.findByCondition(
          User, 
          { id: after.assignee_id }, 
          ['firstName', 'lastName']
        );
        
        if (assignee) {
          description += `${assignee.firstName} ${assignee.lastName}`;
        } else {
          description += `user ID ${after.assignee_id}`;
        }
      } else {
        description += "no one (unassigned)";
      }
      
      // Add previous assignee information if available
      if (before.assignee_id) {
        const prevAssignee = await commonService.findByCondition(
          User, 
          { id: before.assignee_id }, 
          ['firstName', 'lastName']
        );
        
        if (prevAssignee) {
          description += ` (previously assigned to ${prevAssignee.firstName} ${prevAssignee.lastName})`;
        }
      }
      
      break;
    }
    
    case 'STATUS_CHANGE': {
      const fromStatus = before.status || 'Unknown';
      const toStatus = after.status || 'Unknown';
      
      description = `${actorName} changed the status of ${dsrInfo} from ${fromStatus} to ${toStatus}`;
      
      // Add reason if available
      if (after.reject_reason) {
        description += ` with reason: "${after.reject_reason}"`;
      }
      
      break;
    }
    
    case 'TASK_CREATE': {
      description = `${actorName} created a new task ${metadata.taskName || 'Unnamed task'} for ${dsrInfo}`;
      
      // Add assignee information if available
      if (metadata.assigneeIds && metadata.assigneeIds.length > 0) {
        const assignees = await commonService.getList(
          User, 
          { id: metadata.assigneeIds }, 
          ['firstName', 'lastName'],
          null,
          null,
          null
        );
        
        if (assignees && assignees.rows && assignees.rows.length > 0) {
          const assigneeNames = assignees.rows.map(a => `${a.firstName} ${a.lastName}`).join(', ');
          description += ` and assigned it to ${assigneeNames}`;
        }
      }
      
      // Add due date if available
      if (metadata.dueDate) {
        const date = new Date(metadata.dueDate);
        const day = String(date.getUTCDate()).padStart(2, '0');
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const year = date.getUTCFullYear();
        const formattedDate = `${day}/${month}/${year}`;
        
        description += ` with due date ${formattedDate}`;
      }
      
      break;
    }
    
    case 'TASK_UPDATE': {
      description = `${actorName} updated task ${metadata.taskName || 'Unnamed task'} for ${dsrInfo}`;
      
      // Add what was changed
      const changes = [];
      
      if (before.title !== after.title && before.title && after.title) {
        changes.push(`renamed from ${before.title} to ${after.title}`);
      }
      
      if (before.status !== after.status && before.status && after.status) {
        changes.push(`changed status from ${before.status} to ${after.status}`);
      }
      
      if (JSON.stringify(before.assignee_id) !== JSON.stringify(after.assignee_id) && before.assignee_id && after.assignee_id) {
        const beforeAssignees = Array.isArray(before.assignee_id) ? before.assignee_id : [before.assignee_id];
        const afterAssignees = Array.isArray(after.assignee_id) ? after.assignee_id : [after.assignee_id];
        
        // Fetch assignee names
        const assigneeUsers = await commonService.getListWithoutCount(
          User, 
          { id: [...beforeAssignees, ...afterAssignees] }, 
          ['id', 'firstName', 'lastName']
        );
        
        // Format before assignees
        const beforeNames = beforeAssignees.map(id => {
          const user = assigneeUsers.find(u => u.id === id);
          return user ? `${user.firstName} ${user.lastName}` : `User ID ${id}`;
        }).join(', ');
        
        // Format after assignees
        const afterNames = afterAssignees.map(id => {
          const user = assigneeUsers.find(u => u.id === id);
          return user ? `${user.firstName} ${user.lastName}` : `User ID ${id}`;
        }).join(', ');
        
        changes.push(`updated assignees from ${beforeNames} to ${afterNames}`);
      }
      
      if (before.due_date && after.due_date) {
        const beforeDate = new Date(before.due_date);
        const afterDate = new Date(after.due_date);
        
        const beforeFormatted = `${String(beforeDate.getUTCDate()).padStart(2, '0')}/${String(beforeDate.getUTCMonth() + 1).padStart(2, '0')}/${beforeDate.getUTCFullYear()}`;
        const afterFormatted = `${String(afterDate.getUTCDate()).padStart(2, '0')}/${String(afterDate.getUTCMonth() + 1).padStart(2, '0')}/${afterDate.getUTCFullYear()}`;
        
        if (beforeFormatted !== afterFormatted) {
          changes.push(`changed due date from ${beforeFormatted} to ${afterFormatted}`);
        }
      }
      
      if(before.progress !== after.progress && before.progress && after.progress){
        changes.push(`changed progress from ${before.progress} to ${after.progress}`);
      }
      
      if (changes.length > 0) {
        description += `: ${changes.join(', ')}`;
      }
      
      break;
    }
    
    case 'TASK_COMPLETE': {
      description = `${actorName} marked task "${metadata.taskName || 'Unnamed task'}" as complete for ${dsrInfo}`;
      
      // Add completion date if available
      if (metadata.completionDate) {
        const date = new Date(metadata.completionDate);
        const day = String(date.getUTCDate()).padStart(2, '0');
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const year = date.getUTCFullYear();
        const formattedDate = `${day}/${month}/${year}`;
        
        description += ` on ${formattedDate}`;
      }
      
      break;
    }
    
    case 'COMMENT_ADD': {
      description = `${actorName} added a comment to ${dsrInfo}`;
      
      // Add comment text if available
      if (metadata.commentText) {
        // Truncate long comments
        const truncatedComment = metadata.commentText.length > 100 
          ? metadata.commentText.substring(0, 100) + '...' 
          : metadata.commentText;
        
        description += `: "${truncatedComment}"`;
      }
      
      break;
    }
    
    case 'DOCUMENT_UPLOAD': {
      description = `${actorName} uploaded ${metadata.documentCount || 'a'} document(s) to ${dsrInfo}`;
      
      // Add document names if available
      if (metadata.documentNames && metadata.documentNames.length > 0) {
        description += `: ${metadata.documentNames.join(', ')}`;
      }
      
      break;
    }
    
    case 'DEADLINE_EXTENSION': {
      let fromDate = 'Unknown';
      let toDate = 'Unknown';
      
      if (before.deadline_date) {
        const date = new Date(before.deadline_date);
        const day = String(date.getUTCDate()).padStart(2, '0');
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const year = date.getUTCFullYear();
        fromDate = `${day}/${month}/${year}`;
      }
      
      if (after.deadline_date) {
        const date = new Date(after.deadline_date);
        const day = String(date.getUTCDate()).padStart(2, '0');
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const year = date.getUTCFullYear();
        toDate = `${day}/${month}/${year}`;
      }
      
      description = `${actorName} extended the deadline for ${dsrInfo} from ${fromDate} to ${toDate}`;
      
      // Add reason if available
      if (metadata.reason) {
        description += ` with reason: "${metadata.reason}"`;
      }
      
      break;
    }
    
    case 'VERIFICATION': {
      description = `${actorName} `;
      
      if (after.first_verification !== before.first_verification) {
        description += after.first_verification ? 
          `verified the first verification step for ${dsrInfo}` : 
          `unmarked the first verification step for ${dsrInfo}`;
      } else if (after.second_verification !== before.second_verification) {
        description += after.second_verification ? 
          `verified the second verification step for ${dsrInfo}` : 
          `unmarked the second verification step for ${dsrInfo}`;
      } else {
        description += `updated verification status for ${dsrInfo}`;
      }
      
      break;
    }
    
    case 'MESSAGE_SEND': {
      const messageType = metadata.messageType === 'EXTERNAL' ? 'external' : 'internal';
      description = `${actorName} sent an ${messageType} message regarding ${dsrInfo}`;
      
      // Add subject if available
      if (metadata.subject) {
        description += ` with subject ${metadata.subject}`;
      }
      
      // Add recipients if available
      if (metadata.recipients && metadata.recipients.length > 0) {
        description += ` to ${metadata.recipients.join(', ')}`;
      }
      
      break;
    }
    
    case 'DATA_DISCOVERY': {
      description = `${actorName} updated data discovery information for ${dsrInfo}`;
      
      // Add specific changes if available
      if (metadata.discoveryDetails) {
        description += `: ${metadata.discoveryDetails}`;
      }
      
      break;
    }
    
    default:
      // Generic description for unknown action types
      description = `${actorName} performed action ${actionType} on ${dsrInfo}`;
      
      // Add any available metadata
      const metadataStr = Object.entries(metadata)
        .filter(([key]) => !['isChat'].includes(key)) // Exclude certain fields
        .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
        .join(', ');
      
      if (metadataStr) {
        description += ` with details: ${metadataStr}`;
      }
  }
  
  // Add timestamp in dd/mm/yyyy format
  const now = new Date();
  const day = String(now.getUTCDate()).padStart(2, '0');
  const month = String(now.getUTCMonth() + 1).padStart(2, '0');
  const year = now.getUTCFullYear();
  const formattedDate = `${day}/${month}/${year}`;
  
  // Also include the ISO string for potential programmatic use
  const timestamp = formattedDate;
  description += ` [${timestamp}]`;
  
  return description;
}
catch(error){
  console.log(error);
  throw error;
}
}

/**
 * Creates a general audit log entry for assessment actions
 * 
 * @param {Object|Object[]} options - Single audit log config object or an array of such objects.
 * @param {string} options.type - Type of the audit log
 * @param {number} options.type_id - Type id
 * @param {number} options.dept_id - Department id
 * @param {number} options.customer_id - Customer Id
 * @param {number} options.actorId - The user ID of the person performing the action
 * @param {string} options.actionType -  Type of the action (START, SUBMIT_WITHOUT_STATUS, SUBMIT_WITH_STATUS, ASSIGN, ADD_REVIEWER, etc.)
 * @param {Object} options.metadata - Contextual information to genetrate the description
 * @param {Object} req - Express request object
 * @param {Object} transaction - Database transaction
 * @returns {Promise<Object>} - The created audit log entry
 */
exports.createGeneralAuditLog = async (options, req, transaction = null) => {
  try {
    const { AuditLog, User } = models;

    // Normalize input to an array
    const auditEntries = Array.isArray(options) ? options : [options];
    const auditData = [];


    for (const entry of auditEntries) {
      const { type, type_id, dept_id,customer_id, actorId, actionType, metadata } = entry;


      // Get actor
      const actor = await commonService.findByCondition(
        User,
        { id: actorId },
        ['id', 'firstName', 'lastName', 'email']
      );
      if (!actor) return false;

      // Generate description
      const description = await generateGeneralizedDescription({
        type,
        actionType,
        actor,
        metadata
      });
      if (!description) return false;

      // Build audit log entry
      auditData.push({
        type,
        type_id,
        action: description,
        action_by_id: actorId,
        customer_id,
        dept_id
      });
    }

    // If there's nothing to insert, return null
    if (auditData.length === 0) return null;

    // Perform bulk insert
    const auditLogs = await commonService.bulkAdd(AuditLog, auditData, transaction);

    // Return a single log if only one entry, otherwise return array
    return auditLogs.length === 1 ? auditLogs[0] : auditLogs;

  } catch (error) {
    console.error('Error creating general audit logs:', error);
    return error;
  }
};


/**
 * Generates a generalized description for the audit log based on action type
 * 
 * @param {Object} options - Options for generating the description
 * @returns {Promise<string>} - The detailed description
 */
async function generateGeneralizedDescription(options) {
  try{
  // Destructure options
  const {type, actionType, actor, metadata } = options;
  
  // Format actor name
  const actorName = actor ? `${actor.firstName} ${actor.lastName}` : "Unknown user";
  
  // Base description starts with actor and DSR info
  let description = `${actorName} performed an action on ${type}`;
  
  // Add detailed information based on action type
  switch (actionType) {

    case 'START': {
      description = `${actorName || 'Someone'} started the ${metadata?.assessmentName || 'Unnamed Assessment'} ${metadata?.key || 'No Key'} `;
      break;
    }

    case 'SUBMIT_ASSESSMENT': {
      description = `${actorName || 'Someone'} submitted the ${metadata?.assessmentName || 'Unnamed Assessment'} ${metadata?.assessment_name} for review `;
      break;
    }

    case 'SUBMIT_REVIEW': {
      description = `${actorName || 'Someone'} submitted a review for ${metadata?.assessmentName || 'Unnamed Assessment'} (${metadata?.key || 'No Key'}) with status '${metadata?.status || 'Unknown'}'.`;
      break;
    }
    case 'ASSIGN': {
      description = `${actorName || 'Someone'} assigned the assessment ${metadata?.assessmentName || 'Unnamed Assessment' } to ${metadata?.assignedToName || 'Unknown'}.`;
      break;
    }
    
    case 'ADD_REVIEWER': {
      description = `${actorName || 'Someone'} added ${metadata?.reviewerName || 'a reviewer'} as a reviewer to the assessment ${metadata?.assessmentName || 'Unnamed Assessment'}.`;
      break;
    }

    case 'ADD_COLLABORATOR': {
      description = `${actorName || 'Someone'} added ${metadata?.userName || 'a user'} as a collaborator for the assessment ${metadata?.assessmentName || 'Unnamed Assessment'} under the ${metadata?.categoryName || 'Unspecified'} category.`;
      break;
    }
    case 'REMOVE_COLLABORATOR': {
      description = `${actorName || 'Someone'} removed ${metadata?.userName || 'a user'} as a collaborator from the assessment ${metadata?.assessmentName || 'Unnamed Assessment'} under the ${metadata?.categoryName || 'Unspecified'} category.`;
      break;
    }
    case 'CREATE_ASSESSMENT': {
      description = `${actorName || 'Someone'} created a new assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'}) of type ${metadata?.assessmentType || 'Standard'}`;
      break;
    }

    case 'ADD_CUSTOM_CONTROL': {
      description = `${actorName || 'Someone'} added a custom control "${metadata?.controlTitle || 'Custom Control'}" to assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      break;
    }
    case 'UPDATE_CONTROL_FIELDS': {
      description = `${actorName || 'Someone'} updated fields for control "${metadata?.controlTitle || 'Control'}" in assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      break;
    }

    case 'DELETE_CUSTOM_CONTROL': {
      description = `${actorName || 'Someone'} deleted custom control "${metadata?.controlTitle || 'Custom Control'}" from assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      break;
    }

    case 'SAVE_ANSWERS': {
      description = `${actorName || 'Someone'} saved ${metadata?.answerCount || 'multiple'} answers (${metadata?.addedCount || 0} new, ${metadata?.updatedCount || 0} updated) in assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      break;
    }

    case 'ADD_REVIEW_COMMENTS': {
      description = `${actorName || 'Someone'} added ${metadata?.reviewCount || 'multiple'} review  (${metadata?.addedCount || 0} new, ${metadata?.updatedCount || 0} updated) to assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      break;
    }

    case 'UPDATE_CONTROL': {
      description = `${actorName || 'Someone'} updated control "${metadata?.controlTitle || 'Control'}" in assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      
      // Add details about what was changed if available
      if (metadata?.changes) {
        const changedFields = [];
        if (metadata.changes.title) changedFields.push('title');
        if (metadata.changes.description) changedFields.push('description');
        if (metadata.changes.artifact_type) changedFields.push('artifact type');
        if (metadata.changes.question) changedFields.push('question');
        if (metadata.changes.fields) changedFields.push('fields');
        
        if (changedFields.length > 0) {
          description += ` (changed: ${changedFields.join(', ')})`;
        }
      }
      break;
    }
    case 'UPDATE_ASSESSMENT': {
      description = `${actorName || 'Someone'} updated assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'})`;
      
      // Add details about what was changed if available
      if (metadata?.changes) {
        const changedFields = Object.keys(metadata.changes).filter(key => metadata.changes[key]);
        
        if (changedFields.length > 0) {
          const readableFields = changedFields.map(field => {
            // Convert camelCase or snake_case to readable format
            return field
              .replace(/_/g, ' ')
              .replace(/([A-Z])/g, ' $1')
              .toLowerCase()
              .trim();
          });
          
          description += ` (changed: ${readableFields.join(', ')})`;
        }
      }
      break;
    }
    case 'CREATE_ASSESSMENT_TYPE': {
      description = `${actorName || 'Someone'} created a new assessment type "${metadata?.assessmentName || 'Unnamed Assessment'}" with key "${metadata?.key || 'No Key'}" and type "${metadata?.type || 'Unknown'}"`;
      break;
    }
    case 'UPLOAD_CONTROLS': {
      description = `${actorName || 'Someone'} uploaded controls to assessment "${metadata?.assessmentName || 'Unnamed Assessment'}" (${metadata?.key || 'No Key'}) with template "${metadata?.templateName || 'Unnamed Template'}"`;
      break;
    }
    default:
      // Generic description for unknown action types
      description = `${actorName} performed action ${actionType} on ${type}`;
  }
  
  // Add timestamp in dd/mm/yyyy format
  const now = new Date();
  const day = String(now.getUTCDate()).padStart(2, '0');
  const month = String(now.getUTCMonth() + 1).padStart(2, '0');
  const year = now.getUTCFullYear();
  const formattedDate = `${day}/${month}/${year}`;
  
  // Also include the ISO string for potential programmatic use
  const timestamp = formattedDate;
  description += ` [${timestamp}]`;
  
  return description;
  }
  catch(error){
    console.log(error);
    throw error;
  }
}