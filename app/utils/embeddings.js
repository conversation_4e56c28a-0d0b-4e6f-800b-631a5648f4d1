const axios = require('axios');

exports.generateEmbedding = async text => {
  try {
    const response = await axios.post(`${process.env.API_BASE_URL}/api/embeddings`, {
      model: `${process.env.EMBEDDING_MODEL}`,
      prompt: text
    });

    return response.data.embedding; // returns [float, float, ...]
  } catch (err) {
    console.error('Embedding generation failed:', err.message);
    return null;
  }
};
