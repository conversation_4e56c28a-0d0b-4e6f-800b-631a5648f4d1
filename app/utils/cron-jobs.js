const db = require('../models/index').sequelize;
const moment = require('moment');
const policyService = require('../services/policy');
const { sendMail } = require('../config/email');
const ropaService = require('../services/ropa');
const constant = require('../constant/ROPA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const assessmentService = require('../services/assessment');
const { Op, Sequelize } = require('sequelize');
const dsrService = require('../services/dsr');

exports.checkPolicyExpiryAndSendEmail = async () => {
    try {
        const { Policy, User } = db.models;
        const policies = await policyService.getListAssociateWithoutCount(Policy, User, 'Author', {}, {}, ['id', 'name', 'author_id', 'reviewer_id', 'approver_id', 'renewal_date'], ['id', 'email']);
        if (!policies) {
            throw new Error('Error in fetching policies');
        }
        const currentDate = moment().tz('Asia/Kolkata');

        const emailPromises = policies?.map(policy => {
            const expiryDate = moment(policy?.renewal_date);
            const daysUntilExpiry = expiryDate.diff(currentDate, 'days');

            if (daysUntilExpiry == 45 || daysUntilExpiry == 30 || daysUntilExpiry == 15) {
                const subject = `${policy?.name} Expiring in ${daysUntilExpiry} Days: Hurry up, Renew the Policy!`;
                const textTemplate = "policy_expire_mail_template.ejs";
                const sendData = {
                    policyName: `${policy?.name}`,
                    url: `${process.env.SERVER_IP}/login/${policy?.id}`
                };
                return sendMail(
                    policy?.Author?.email,
                    sendData,
                    subject,
                    textTemplate,
                );
            }
            else if (daysUntilExpiry == 0) {
                const subject = `${policy.name} Expiring today : Hurry up, Renew the Policy!`;
                const textTemplate = "policy_expire_mail_template.ejs";
                const sendData = {
                    policyName: `${policy?.name}`,
                    url: `${process.env.SERVER_IP}/login/${policy?.id}`
                };
                return sendMail(
                    policy?.Author?.email,
                    sendData,
                    subject,
                    textTemplate,
                );
            }
        });
        await Promise.all(emailPromises);
    } catch (error) {
        console.log(error);
    }

};



exports.checkRopaTentativeCompletionDateAndSendEmail = async () => {
    try {
        const { ROPA, Departments, Processes, User, RopaDocuments } = db.models;
        const ropas = await ropaService.getEmployeeROPAWithDocs(ROPA, Departments, Processes, User, RopaDocuments, {
            [Op.and]: [
                {
                    [Op.or]: [
                        sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                        sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })
                    ]
                } 
            ],
            status: {
                [Op.notIn]: ['Yet to Start', 'Completed'] // Status not equal to 'Completed'
              }
        }, {}, {}, {}, {}, ['id', 'start_date', 'end_date', 'tentative_completion_date', 'status', 'risks', 'progress', 'is_already_performed', 'createdAt', 'updatedAt'], ['id', 'name', 'group_id'], ['id', 'name'], ['firstName', 'lastName', 'email'], {} );

        if (!ropas) {
            throw new Error('Error in fetching policies');
        }
        const currentDate = moment().tz('Asia/Kolkata');
        // console.log("ropas========-->>>", ropas.rows[1]);


        const ropaData = {
            rows: ropas.rows?.map(ropaItem => {
                if (ropaItem.Process !== null) {
                    ropaItem.Department = {};
                    ropaItem.Department.name = `${ropaItem.Process.Department.name} - ${ropaItem.Process.name}`;
                    delete ropaItem.Process.Department;
                    delete ropaItem.Process.User;
                    // console.log("ropaItem-------",ropaItem)
                }

                if (ropaItem.AssignedTo) {
                    ropaItem.AssignedTo.name = `${ropaItem.AssignedTo.firstName} ${ropaItem.AssignedTo.lastName}`;
                    delete ropaItem.AssignedTo.firstName;
                    delete ropaItem.AssignedTo.lastName;
                }

                if (ropaItem.Approver) {
                    ropaItem.Approver.name = `${ropaItem.Approver.firstName} ${ropaItem.Approver.lastName}`;
                    delete ropaItem.Approver.firstName;
                    delete ropaItem.Approver.lastName;
                }

                ropaItem.group_id = ropaItem.Department.group_id;
                delete ropaItem.Department.group_id;

                return ropaItem;
            })
        };


        const emailPromises = ropaData?.rows?.map(ropa => {
            const completionDate = moment(ropa?.tentative_completion_date);
            const daysUntilCompletion = completionDate.diff(currentDate, 'days');
            console.log();
            if (daysUntilCompletion <= 0 ) {
                const subject = `Overdue: ROPA Completion Deadline Passed for :-(${ropa?.Department?.name})`;
                const textTemplate = "ropa_notification_mail.ejs";
                const sendData = {
                    ropaName: `${ropa?.Department?.name}`,
                    name: `${ropa?.AssignedTo?.name}`,
                    completionDate: ropa?.tentative_completion_date,
                    overDueDays: Math.abs(daysUntilCompletion),
                    overdue: true
                    // url: `${process.env.SERVER_IP}/login/${policy?.id}`
                };
                return sendMail(
                    ropa?.AssignedTo?.email,
                    sendData,
                    subject,
                    textTemplate,
                );
            }
            else if (daysUntilCompletion <= 7 ) {
                const subject = `Urgent: Only ${daysUntilCompletion} Days Left to Complete ROPA :-(${ropa?.Department?.name})`;
                const textTemplate = "ropa_notification_mail.ejs";
                const sendData = {
                    ropaName: `${ropa?.Department?.name}`,
                    name: `${ropa?.AssignedTo?.name}`,
                    completionDate: ropa?.tentative_completion_date,
                    daysLeft: daysUntilCompletion,
                    overdue: false
                    // url: `${process.env.SERVER_IP}/login/${policy?.id}`
                };
                return sendMail(
                    ropa?.AssignedTo?.email,
                    sendData,
                    subject,
                    textTemplate,
                );
            }
            else {
                const today = new Date();
                const isMondayOrWednesday = today.getDay() === 1 || today.getDay() === 3 ; // Check if today is Monday (0 = Sunday, 1 = Monday, ... 6 = Saturday)
            
                if (isMondayOrWednesday) {
                    const subject = `Reminder: ROPA Completion - Keep Progressing (${ropa?.Department?.name})`;
                    const textTemplate = "ropa_notification_mail.ejs"; // Use a suitable template for reminders
                    const sendData = {
                        ropaName: `${ropa?.Department?.name}`,
                        name: `${ropa?.AssignedTo?.name}`,
                        completionDate: ropa?.tentative_completion_date,
                        daysLeft: daysUntilCompletion,
                        overdue: false
                        // url: `${process.env.SERVER_IP}/login/${policy?.id}`
                    };
                    return sendMail(
                        ropa?.AssignedTo?.email,
                        sendData,
                        subject,
                        textTemplate,
                    );
                }
            }
        });
        await Promise.all(emailPromises);
    } catch (error) {
        console.log(error);
    }
};




exports.checkAssessmentsTentativeCompletionDateAndSendEmail = async () => {
    try {
        const { Departments, Processes, User, Assessments, CustomerAssessments, Group} = db.models;
        const list = await assessmentService.getListWithMultipleAssociates(CustomerAssessments, Assessments, Departments, Processes, User, 'AssignedTo', User, 'Approver', User, 'Owner', Group,
            {
                [Op.and]: [
                    {
                        [Op.or]: [
                            sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null }),
                            sequelize.where(sequelize.col('CustomerAssessments.entity_id'), { [Op.ne]: null })
                        ]
                    }
                ],
                status: {
                    [Op.notIn]: ['Yet to Start', 'Completed'] // Status not equal to 'Completed'
                },
            },
            {},
            {},
            {},
            {},
            {},
            {},
            {},
            ['id', 'assessment_id', 'risks', 'progress', 'start_date', 'end_date', 'tentative_date', 'status'], ['id', 'type', 'assessment_name', 'key'], ['id', 'name'], ['id', 'name'], ['id', 'firstName', 'lastName', 'email'], ['id', 'firstName', 'lastName', 'email'], ['id', 'firstName', 'lastName', 'email'], ['id', 'name']);

        if (!list) {
            throw new Error('Error in fetching policies');
        }
        const currentDate = moment().tz('Asia/Kolkata');
        // console.log("ropas========-->>>", list.rows[1]);


        const assessmentData = {
            rows: list.rows?.map(assessmentItem => {
                if (assessmentItem.Process !== null) {
                    assessmentItem.Department = {};
                    assessmentItem.Department.name = `Process:- ${assessmentItem?.Process.name}`;
                    delete assessmentItem.Process;
                    // console.log("itemmm-------",assessmentItem)
                }

                if (assessmentItem.AssignedTo) {
                    assessmentItem.AssignedTo.name = `${assessmentItem.AssignedTo.firstName} ${assessmentItem.AssignedTo.lastName}`;
                    delete assessmentItem.AssignedTo.firstName;
                    delete assessmentItem.AssignedTo.lastName;
                }

                if (assessmentItem.Approver) {
                    assessmentItem.Approver.name = `${assessmentItem.Approver.firstName} ${assessmentItem.Approver.lastName}`;
                    delete assessmentItem.Approver.firstName;
                    delete assessmentItem.Approver.lastName;
                }

                if (assessmentItem.Owner) {
                    assessmentItem.Owner.name = `${assessmentItem.Owner.firstName} ${assessmentItem.Owner.lastName}`;
                    delete assessmentItem.Owner.firstName;
                    delete assessmentItem.Owner.lastName;
                }

                // ropaItem.group_id = ropaItem.Department.group_id;
                // delete ropaItem.Department.group_id;

                return assessmentItem;
            })
        };


        const emailPromises = assessmentData?.rows?.map(assessment => {
            const completionDate = moment(assessment?.tentative_date);
            const daysUntilCompletion = completionDate.diff(currentDate, 'days');
            // console.log();
            if (daysUntilCompletion <= 0 ) {
                const subject = `Overdue: Assessment Completion Deadline Passed for :-(${assessment?.Department?.name})`;
                const textTemplate = "assessment_notification_mail.ejs";
                const sendData = {
                    assessmentName: `${assessment?.Department?.name}`,
                    name: `${assessment?.AssignedTo?.name}`,
                    completionDate: assessment?.tentative_date,
                    overDueDays: Math.abs(daysUntilCompletion),
                    overdue: true
                    // url: `${process.env.SERVER_IP}/login/${policy?.id}`
                };
                return sendMail(
                    assessment?.AssignedTo?.email,
                    sendData,
                    subject,
                    textTemplate,
                );
            }
            else if (daysUntilCompletion <= 7 ) {
                const subject = `Urgent: Only ${daysUntilCompletion} Days Left to Complete Assessment for :-(${assessment?.Department?.name})`;
                const textTemplate = "assessment_notification_mail.ejs";
                const sendData = {
                    assessmentName: `${assessment?.Department?.name}`,
                    name: `${assessment?.AssignedTo?.name}`,
                    completionDate: assessment?.tentative_date,
                    daysLeft: daysUntilCompletion,
                    overdue: false
                    // url: `${process.env.SERVER_IP}/login/${policy?.id}`
                };
                return sendMail(
                    assessment?.AssignedTo?.email,
                    sendData,
                    subject,
                    textTemplate,
                );
            }
            else {
                const today = new Date();
                const isMondayOrWednesday = today.getDay() === 1 || today.getDay() === 3 ; // Check if today is Monday (0 = Sunday, 1 = Monday, ... 6 = Saturday)
            
                if (isMondayOrWednesday) {
                    const subject = `Reminder: Assessment Completion - Keep Progressing (${assessment?.Department?.name})`;
                    const textTemplate = "assessment_notification_mail.ejs"; // Use a suitable template for reminders
                    const sendData = {
                        assessmentName: `${assessment?.Department?.name}`,
                        name: `${assessment?.AssignedTo?.name}`,
                        completionDate: assessment?.tentative_date,
                        daysLeft: daysUntilCompletion,
                        overdue: false
                        // url: `${process.env.SERVER_IP}/login/${policy?.id}`
                    };
                    return sendMail(
                        assessment?.AssignedTo?.email,
                        sendData,
                        subject,
                        textTemplate,
                    );
                }
            }
        });
        await Promise.all(emailPromises);
    } catch (error) {
        console.log(error);
    }
};

exports.sendReminderForRequestAssigneeInDSR = async (req, res) => {
    try {
       // console.log('start cron executed successfully')
        const { User, DsrRequest, DataSubject, DsrRequestType, country } = db.models;
        let dataSubjCond = {};
        let dsrReqCondition = {};
               
        let limit = null;
        dsrReqCondition =  {
          //  id: { [Op.in]: [350,320] },
            status: { [Op.in]: ['APPROVED', 'PENDING'] },
            assignee_id: {
                [Op.ne]: null  // Sequelize condition to check for NOT NULL
            },
            createdAt: {
                [Op.lt]: Sequelize.literal("CURRENT_TIMESTAMP - INTERVAL '5 days'") // Subtract 5 days from the current timestamp
            }
        }
                            
        let getRequest = await dsrService.getMultiAssocData(DsrRequest, DsrRequestType, DataSubject, country, User, dsrReqCondition, {}, dataSubjCond, {}, {}, ['id', 'assignee_id', 'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason', 'business_unit', 'status', 'request_type'], ['id', 'flowtype'], ['id', 'first_name', 'last_name', 'relationship', 'email'], ['country_name'], ['firstName', 'lastName', 'email'], limit);
     
        if(getRequest.rows.length > 0){
            for(let key in getRequest.rows){
                if(getRequest.rows[key].User){
                    let emailUser = getRequest.rows[key].User;
                    if (emailUser.email) {
                        const textTemplate = "dsr_mail.ejs";                    
                        const subject = `Action Required: Pending DSR Request ID ${getRequest.rows[key].dsr_id} – Immediate Attention Needed`;
                        let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : "";
                        let contentText = `
                            <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                            <p>This is a courteous reminder that the Data Subject Request (DSR) associated with Request ID ${getRequest.rows[key].dsr_id} remains unresolved and needs your immediate attention. In line with our compliance obligations, it is important that this request is addressed within the designated timeline to maintain alignment with data privacy standards.</p>
                            <p>To view and process the request, please access the GoTrust platform using the link below:</p>
                            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                            <p>We kindly ask that you treat this task as a priority and complete the necessary steps at your earliest convenience.</p>
                            <p>If you have any questions or need further assistance, feel free to reach out.</p>
                            <p>Your prompt  action is greatly appreciated.</p>
                           
                        `;
        
                        const sendData = {
                            content: contentText,
                            
                        };
        
                        if(emailUser.email){
                            await sendMail(
                                emailUser.email,
                                sendData,
                                subject,
                                textTemplate
                            );
                        }  
                    }
                    
                }
            }
        }
            
        console.log('cron executed successfully')
        

    } catch (err) {
        
    }
};
