const db = require('../models/index').sequelize;
const moment = require('moment');
const policyService = require('../services/policy');
const { sendMail } = require('../config/email');
const commonService = require('../services/common');
const ropaService = require('../services/ropa');
const constant = require('../constant/ROPA');
const authConstant = require('../constant/auth');
const sequelize = require('sequelize');
const assessmentService = require('../services/assessment');
const { Op, Sequelize } = require('sequelize');
const dsrService = require('../services/dsr');
const httpStatus = require('http-status');
const vendorService = require('../services/vendor');

exports.checkPolicyExpiryAndSendEmail = async () => {
  try {
    const { Policy, User } = db.models;
    const policies = await policyService.getListAssociateWithoutCount(Policy, User, 'Author', {}, {}, ['id', 'name', 'author_id', 'reviewer_id', 'approver_id', 'renewal_date'], ['id', 'email']);
    if (!policies) {
      throw new Error('Error in fetching policies');
    }
    const currentDate = moment().tz('Asia/Kolkata');

    const emailPromises = policies?.map(policy => {
      const expiryDate = moment(policy?.renewal_date);
      const daysUntilExpiry = expiryDate.diff(currentDate, 'days');

      if (daysUntilExpiry == 45 || daysUntilExpiry == 30 || daysUntilExpiry == 15) {
        const subject = `${policy?.name} Expiring in ${daysUntilExpiry} Days: Hurry up, Renew the Policy!`;
        const textTemplate = 'policy_expire_mail_template.ejs';
        const sendData = {
          policyName: `${policy?.name}`,
          url: `${process.env.SERVER_IP}/login/${policy?.id}`
        };
        return sendMail(policy?.Author?.email, sendData, subject, textTemplate);
      } else if (daysUntilExpiry == 0) {
        const subject = `${policy.name} Expiring today : Hurry up, Renew the Policy!`;
        const textTemplate = 'policy_expire_mail_template.ejs';
        const sendData = {
          policyName: `${policy?.name}`,
          url: `${process.env.SERVER_IP}/login/${policy?.id}`
        };
        return sendMail(policy?.Author?.email, sendData, subject, textTemplate);
      }
    });
    await Promise.all(emailPromises);
  } catch (error) {
    console.log(error);
  }
};

exports.checkRopaTentativeCompletionDateAndSendEmail = async () => {
  try {
    const { ROPA, Departments, Processes, User, RopaDocuments } = db.models;
    const ropas = await ropaService.getEmployeeROPAWithDocs(
      ROPA,
      Departments,
      Processes,
      User,
      RopaDocuments,
      {
        [Op.and]: [
          {
            [Op.or]: [sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null })]
          }
        ],
        status: {
          [Op.notIn]: ['Yet to Start', 'Completed'] // Status not equal to 'Completed'
        }
      },
      {},
      {},
      {},
      {},
      ['id', 'start_date', 'end_date', 'tentative_completion_date', 'status', 'risks', 'progress', 'is_already_performed', 'createdAt', 'updatedAt'],
      ['id', 'name', 'group_id'],
      ['id', 'name'],
      ['firstName', 'lastName', 'email'],
      {}
    );

    if (!ropas) {
      throw new Error('Error in fetching policies');
    }
    const currentDate = moment().tz('Asia/Kolkata');
    // console.log("ropas========-->>>", ropas.rows[1]);

    const ropaData = {
      rows: ropas.rows?.map(ropaItem => {
        if (ropaItem.Process !== null) {
          ropaItem.Department = {};
          ropaItem.Department.name = `${ropaItem.Process.Department.name} - ${ropaItem.Process.name}`;
          delete ropaItem.Process.Department;
          delete ropaItem.Process.User;
          // console.log("ropaItem-------",ropaItem)
        }

        if (ropaItem.AssignedTo) {
          ropaItem.AssignedTo.name = `${ropaItem.AssignedTo.firstName} ${ropaItem.AssignedTo.lastName}`;
          delete ropaItem.AssignedTo.firstName;
          delete ropaItem.AssignedTo.lastName;
        }

        if (ropaItem.Approver) {
          ropaItem.Approver.name = `${ropaItem.Approver.firstName} ${ropaItem.Approver.lastName}`;
          delete ropaItem.Approver.firstName;
          delete ropaItem.Approver.lastName;
        }

        ropaItem.group_id = ropaItem.Department.group_id;
        delete ropaItem.Department.group_id;

        return ropaItem;
      })
    };

    const emailPromises = ropaData?.rows?.map(ropa => {
      const completionDate = moment(ropa?.tentative_completion_date);
      const daysUntilCompletion = completionDate.diff(currentDate, 'days');
      console.log();
      if (daysUntilCompletion <= 0) {
        const subject = `Overdue: ROPA Completion Deadline Passed for :-(${ropa?.Department?.name})`;
        const textTemplate = 'ropa_notification_mail.ejs';
        const sendData = {
          ropaName: `${ropa?.Department?.name}`,
          name: `${ropa?.AssignedTo?.name}`,
          completionDate: ropa?.tentative_completion_date,
          overDueDays: Math.abs(daysUntilCompletion),
          overdue: true
          // url: `${process.env.SERVER_IP}/login/${policy?.id}`
        };
        return sendMail(ropa?.AssignedTo?.email, sendData, subject, textTemplate);
      } else if (daysUntilCompletion <= 7) {
        const subject = `Urgent: Only ${daysUntilCompletion} Days Left to Complete ROPA :-(${ropa?.Department?.name})`;
        const textTemplate = 'ropa_notification_mail.ejs';
        const sendData = {
          ropaName: `${ropa?.Department?.name}`,
          name: `${ropa?.AssignedTo?.name}`,
          completionDate: ropa?.tentative_completion_date,
          daysLeft: daysUntilCompletion,
          overdue: false
          // url: `${process.env.SERVER_IP}/login/${policy?.id}`
        };
        return sendMail(ropa?.AssignedTo?.email, sendData, subject, textTemplate);
      } else {
        const today = new Date();
        const isMondayOrWednesday = today.getDay() === 1 || today.getDay() === 3; // Check if today is Monday (0 = Sunday, 1 = Monday, ... 6 = Saturday)

        if (isMondayOrWednesday) {
          const subject = `Reminder: ROPA Completion - Keep Progressing (${ropa?.Department?.name})`;
          const textTemplate = 'ropa_notification_mail.ejs'; // Use a suitable template for reminders
          const sendData = {
            ropaName: `${ropa?.Department?.name}`,
            name: `${ropa?.AssignedTo?.name}`,
            completionDate: ropa?.tentative_completion_date,
            daysLeft: daysUntilCompletion,
            overdue: false
            // url: `${process.env.SERVER_IP}/login/${policy?.id}`
          };
          return sendMail(ropa?.AssignedTo?.email, sendData, subject, textTemplate);
        }
      }
    });
    await Promise.all(emailPromises);
  } catch (error) {
    console.log(error);
  }
};

exports.checkAssessmentsTentativeCompletionDateAndSendEmail = async () => {
  try {
    const { Departments, Processes, User, Assessments, CustomerAssessments, Group } = db.models;
    const list = await assessmentService.getListWithMultipleAssociates(
      CustomerAssessments,
      Assessments,
      Departments,
      Processes,
      User,
      'AssignedTo',
      User,
      'Approver',
      User,
      'Owner',
      Group,
      {
        [Op.and]: [
          {
            [Op.or]: [sequelize.where(sequelize.col('Department.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('Process.id'), { [Op.ne]: null }), sequelize.where(sequelize.col('CustomerAssessments.entity_id'), { [Op.ne]: null })]
          }
        ],
        status: {
          [Op.notIn]: ['Yet to Start', 'Completed'] // Status not equal to 'Completed'
        }
      },
      {},
      {},
      {},
      {},
      {},
      {},
      {},
      ['id', 'assessment_id', 'risks', 'progress', 'start_date', 'end_date', 'tentative_date', 'status'],
      ['id', 'type', 'assessment_name', 'key'],
      ['id', 'name'],
      ['id', 'name'],
      ['id', 'firstName', 'lastName', 'email'],
      ['id', 'firstName', 'lastName', 'email'],
      ['id', 'firstName', 'lastName', 'email'],
      ['id', 'name']
    );

    if (!list) {
      throw new Error('Error in fetching policies');
    }
    const currentDate = moment().tz('Asia/Kolkata');
    // console.log("ropas========-->>>", list.rows[1]);

    const assessmentData = {
      rows: list.rows?.map(assessmentItem => {
        if (assessmentItem.Process !== null) {
          assessmentItem.Department = {};
          assessmentItem.Department.name = `Process:- ${assessmentItem?.Process.name}`;
          delete assessmentItem.Process;
          // console.log("itemmm-------",assessmentItem)
        }

        if (assessmentItem.AssignedTo) {
          assessmentItem.AssignedTo.name = `${assessmentItem.AssignedTo.firstName} ${assessmentItem.AssignedTo.lastName}`;
          delete assessmentItem.AssignedTo.firstName;
          delete assessmentItem.AssignedTo.lastName;
        }

        if (assessmentItem.Approver) {
          assessmentItem.Approver.name = `${assessmentItem.Approver.firstName} ${assessmentItem.Approver.lastName}`;
          delete assessmentItem.Approver.firstName;
          delete assessmentItem.Approver.lastName;
        }

        if (assessmentItem.Owner) {
          assessmentItem.Owner.name = `${assessmentItem.Owner.firstName} ${assessmentItem.Owner.lastName}`;
          delete assessmentItem.Owner.firstName;
          delete assessmentItem.Owner.lastName;
        }

        // ropaItem.group_id = ropaItem.Department.group_id;
        // delete ropaItem.Department.group_id;

        return assessmentItem;
      })
    };

    const emailPromises = assessmentData?.rows?.map(assessment => {
      const completionDate = moment(assessment?.tentative_date);
      const daysUntilCompletion = completionDate.diff(currentDate, 'days');
      // console.log();
      if (daysUntilCompletion <= 0) {
        const subject = `Overdue: Assessment Completion Deadline Passed for :-(${assessment?.Department?.name})`;
        const textTemplate = 'assessment_notification_mail.ejs';
        const sendData = {
          assessmentName: `${assessment?.Department?.name}`,
          name: `${assessment?.AssignedTo?.name}`,
          completionDate: assessment?.tentative_date,
          overDueDays: Math.abs(daysUntilCompletion),
          overdue: true
          // url: `${process.env.SERVER_IP}/login/${policy?.id}`
        };
        return sendMail(assessment?.AssignedTo?.email, sendData, subject, textTemplate);
      } else if (daysUntilCompletion <= 7) {
        const subject = `Urgent: Only ${daysUntilCompletion} Days Left to Complete Assessment for :-(${assessment?.Department?.name})`;
        const textTemplate = 'assessment_notification_mail.ejs';
        const sendData = {
          assessmentName: `${assessment?.Department?.name}`,
          name: `${assessment?.AssignedTo?.name}`,
          completionDate: assessment?.tentative_date,
          daysLeft: daysUntilCompletion,
          overdue: false
          // url: `${process.env.SERVER_IP}/login/${policy?.id}`
        };
        return sendMail(assessment?.AssignedTo?.email, sendData, subject, textTemplate);
      } else {
        const today = new Date();
        const isMondayOrWednesday = today.getDay() === 1 || today.getDay() === 3; // Check if today is Monday (0 = Sunday, 1 = Monday, ... 6 = Saturday)

        if (isMondayOrWednesday) {
          const subject = `Reminder: Assessment Completion - Keep Progressing (${assessment?.Department?.name})`;
          const textTemplate = 'assessment_notification_mail.ejs'; // Use a suitable template for reminders
          const sendData = {
            assessmentName: `${assessment?.Department?.name}`,
            name: `${assessment?.AssignedTo?.name}`,
            completionDate: assessment?.tentative_date,
            daysLeft: daysUntilCompletion,
            overdue: false
            // url: `${process.env.SERVER_IP}/login/${policy?.id}`
          };
          return sendMail(assessment?.AssignedTo?.email, sendData, subject, textTemplate);
        }
      }
    });
    await Promise.all(emailPromises);
  } catch (error) {
    console.log(error);
  }
};

exports.sendReminderForRequestAssigneeInDSR = async (req, res) => {
  try {
    // console.log('start cron executed successfully')
    const { User, DsrRequest, DataSubject, DsrRequestType, country } = db.models;
    let dataSubjCond = {};
    let dsrReqCondition = {};

    let limit = null;
    dsrReqCondition = {
      //  id: { [Op.in]: [350,320] },
      status: { [Op.in]: ['APPROVED', 'PENDING'] },
      assignee_id: {
        [Op.ne]: null // Sequelize condition to check for NOT NULL
      },
      createdAt: {
        [Op.lt]: Sequelize.literal("CURRENT_TIMESTAMP - INTERVAL '5 days'") // Subtract 5 days from the current timestamp
      }
    };

    let getRequest = await dsrService.getMultiAssocData(
      DsrRequest,
      DsrRequestType,
      DataSubject,
      country,
      User,
      dsrReqCondition,
      {},
      dataSubjCond,
      {},
      {},
      ['id', 'assignee_id', 'dsr_id', 'data_subject_id', 'createdAt', 'assigned_date', 'deadline_date', 'extended', 'workflow_step_id', 'data_discovery', 'reject_reason', 'business_unit', 'status', 'request_type'],
      ['id', 'flowtype'],
      ['id', 'first_name', 'last_name', 'relationship', 'email'],
      ['country_name'],
      ['firstName', 'lastName', 'email'],
      limit
    );

    if (getRequest.rows.length > 0) {
      for (let key in getRequest.rows) {
        if (getRequest.rows[key].User) {
          let emailUser = getRequest.rows[key].User;
          if (emailUser.email) {
            const textTemplate = 'dsr_mail.ejs';
            const subject = `Action Required: Pending DSR Request ID ${getRequest.rows[key].dsr_id} – Immediate Attention Needed`;
            let loginUrl = process.env.FRONTEND_BASE_URL ? process.env.FRONTEND_BASE_URL : '';
            let contentText = `
                            <p>Dear ${emailUser?.firstName} ${emailUser?.lastName},</p>
                            <p>This is a courteous reminder that the Data Subject Request (DSR) associated with Request ID ${getRequest.rows[key].dsr_id} remains unresolved and needs your immediate attention. In line with our compliance obligations, it is important that this request is addressed within the designated timeline to maintain alignment with data privacy standards.</p>
                            <p>To view and process the request, please access the GoTrust platform using the link below:</p>
                            <p><a href="${loginUrl}"><b>${loginUrl}</b></a></p>
                            <p>We kindly ask that you treat this task as a priority and complete the necessary steps at your earliest convenience.</p>
                            <p>If you have any questions or need further assistance, feel free to reach out.</p>
                            <p>Your prompt  action is greatly appreciated.</p>

                        `;

            const sendData = {
              content: contentText
            };

            if (emailUser.email) {
              await sendMail(emailUser.email, sendData, subject, textTemplate);
            }
          }
        }
      }
    }

    console.log('cron executed successfully');
  } catch (err) {}
};

exports.checkRopaRecurrenceDateAndCreateNewVersion = async () => {
  try {
    const { ROPA, Departments, Processes, User, RopaDocuments } = db.models;
    let bulkDbTrans;
    // let dbTrans;
    // const ropas = await commonService.getList(ROPA, { status: 'Completed', is_latest: 'true' }, {});
    const ropas = await ropaService.getEmployeeROPAWithDocs(
      ROPA,
      Departments,
      Processes,
      User,
      RopaDocuments,
      { status: 'Completed', is_latest: 'true' },
      {},
      {},
      {},
      {},
      {},
      ['id', 'name', 'group_id'],
      ['id', 'name'],
      ['firstName', 'lastName', 'email'],
      {}
    );
    if (!ropas || ropas.count === 0) return;

    const currentDate = moment().tz('Asia/Kolkata');
    const newRopa = [];
    ropas.rows.forEach(async ropa => {
      const recurrenceDate = moment(ropa?.recurrence_date);
      const renewalDate = recurrenceDate.diff(currentDate, 'days');
      if (renewalDate === 0) {
        if (ropa.Process !== null) {
          ropa.Department = {};
          ropa.Department.name = `${ropa.Process.Department.name} - ${ropa.Process.name}`;
          delete ropa.Process.Department;
          delete ropa.Process.User;
        }
        if (ropa.AssignedTo) {
          ropa.AssignedTo.name = `${ropa.AssignedTo.firstName} ${ropa.AssignedTo.lastName}`;
          delete ropa.AssignedTo.firstName;
          delete ropa.AssignedTo.lastName;
        }
        if (ropa.Approver) {
          ropa.Approver.name = `${ropa.Approver.firstName} ${ropa.Approver.lastName}`;
          delete ropa.Approver.firstName;
          delete ropa.Approver.lastName;
        }
        newRopa.push({
          department_id: ropa.department_id,
          process_id: ropa.process_id,
          assigned_to: ropa.assigned_to,
          approver: ropa.approver,
          status: 'Yet to Start',
          customer_id: ropa.customer_id,
          group_id: ropa.group_id,
          version: ropa.version + 1,
          parent_id: ropa.parent_id === null ? ropa.id : ropa.parent_id,
          is_latest: true
        });

        const subject = `Action Required: New ROPA Version Created for ${ropa?.Department?.name}`;
        const textTemplate = 'ropa_version_update_mail.ejs';
        const sendData1 = {
          ropaName: `${ropa?.Department?.name}`,
          name: `${ropa?.AssignedTo?.name}`
        };
        const sendData2 = {
          ropaName: `${ropa?.Department?.name}`,
          name: `${ropa?.Approver?.name}`
        };
        sendMail(ropa?.AssignedTo?.email, sendData1, subject, textTemplate);
        sendMail(ropa?.Approver?.email, sendData2, subject, textTemplate);

        const individualTrans = await db.transaction();
        try {
          const updateRopa = await commonService.updateData(ROPA, { is_latest: false }, { id: ropa.id }, individualTrans);
          if (!updateRopa[1]) throw new Error('Failed to update existing ROPA version');

          await individualTrans.commit();
        } catch (err) {
          await individualTrans.rollback();
          console.error('Error updating old ROPA version:', err.message);
        }
      }
    });
    if (newRopa.length > 0) {
      bulkDbTrans = await db.transaction();
      try {
        const bulkAdd = await commonService.bulkAdd(ROPA, newRopa, bulkDbTrans);
        if (!bulkAdd) throw new Error('Failed to create new ROPA versions');

        await bulkDbTrans.commit();
      } catch (err) {
        await bulkDbTrans.rollback();
        console.error('Error creating new ROPA versions:', err.message);
      }
    }
  } catch (error) {
    console.log('=======', error);
  }
};

exports.checkVeaRecurrenceDateAndCreateNewVersions = async () => {
  try {
    const { VendorAssessments, VendorsMapping, Customer, Departments, User, Group } = db.models;

    // Use getListWithMultipleAssociates3 with VendorsMapping support
    const veaList = await vendorService.getListWithMultipleAssociates3(
      VendorAssessments,
      VendorsMapping, // model1 - VendorsMapping
      Customer, // model1_nested - Customer (for nested association)
      'Vendor', // alias1_nested - Vendor (alias for nested Customer)
      Departments, // model2 - Departments
      'Departments', // alias2 - Departments alias
      User, // model4 - User
      'AssignedTo', // alias4
      User, // model5 - User
      'Approver', // alias5
      User, // model6 - User
      'Owner', // alias6
      Group, // model7 - Group
      {
        assessment_type: 'vea',
        status: 'Completed',
        is_latest: true,
        recurrence_date: { [Op.ne]: null }
      },
      {}, // condition1 (VendorsMapping)
      {}, // condition1_nested (Customer/Vendor)
      {}, // condition2 (Departments)
      {}, // condition4 (AssignedTo)
      {}, // condition5 (Approver)
      {}, // condition6 (Owner)
      {}, // condition7 (Group)
      [
        'id',
        'vendor_id',
        'assessment_type',
        'assessment_name',
        'risks',
        'risk_score',
        'progress',
        'start_date',
        'end_date',
        'status',
        'customer_id',
        'department_id',
        'assigned_to',
        'approver',
        'owner_id',
        'version',
        'parent_id',
        'recurrence_date',
        'template_id',
        'entity_id'
      ],
      ['id'], // attributes1 (VendorsMapping)
      ['email', 'company_name'], // attributes1_nested (Customer/Vendor)
      ['id', 'name'], // attributes2 (Departments)
      ['id', 'firstName', 'lastName', 'email'], // attributes4 (AssignedTo)
      ['id', 'firstName', 'lastName', 'email'], // attributes5 (Approver)
      ['id', 'firstName', 'lastName', 'email'], // attributes6 (Owner)
      ['id', 'name'], // attributes7 (Group)
      undefined, // limit
      undefined, // offset
      undefined // order
    );
    console.log('veaList count:', veaList);
    if (!veaList || veaList.count === 0) return;

    const currentDate = moment().tz('Asia/Kolkata');
    const newAssessments = [];
    const emailNotifications = [];

    for (const vea of veaList.rows) {
      const recurrenceDate = moment(vea?.recurrence_date);
      const renewalDate = recurrenceDate.diff(currentDate, 'days');

      if (renewalDate === 0) {
        // Find corresponding VIA for the same vendor
        const correspondingVia = await commonService.findByCondition(
          VendorAssessments,
          {
            vendor_id: vea.vendor_id,
            customer_id: vea.customer_id,
            assessment_type: 'via',
            is_latest: true
          },
          ['id', 'assessment_name', 'customer_id', 'vendor_id', 'department_id', 'assigned_to', 'approver', 'owner_id', 'version', 'parent_id', 'template_id', 'entity_id']
        );

        // Prepare new VEA version
        newAssessments.push({
          assessment_type: 'vea',
          assessment_name: vea.assessment_name,
          customer_id: vea.customer_id,
          vendor_id: vea.vendor_id,
          department_id: vea.department_id,
          assigned_to: vea.assigned_to,
          approver: vea.approver,
          owner_id: vea.owner_id,
          entity_id: vea.entity_id,
          template_id: vea.template_id,
          status: 'Yet to Start',
          version: vea.version + 1,
          parent_id: vea.parent_id === null ? vea.id : vea.parent_id,
          is_latest: true,
          recurrence_date: null // Reset for new version
        });

        // Prepare new VIA version if VIA exists
        if (correspondingVia) {
          newAssessments.push({
            assessment_type: 'via',
            assessment_name: correspondingVia.assessment_name,
            customer_id: correspondingVia.customer_id,
            vendor_id: correspondingVia.vendor_id,
            department_id: correspondingVia.department_id,
            assigned_to: correspondingVia.assigned_to,
            approver: correspondingVia.approver,
            owner_id: correspondingVia.owner_id,
            entity_id: correspondingVia.entity_id,
            template_id: correspondingVia.template_id,
            status: 'Yet to Start',
            version: correspondingVia.version + 1,
            parent_id: correspondingVia.parent_id === null ? correspondingVia.id : correspondingVia.parent_id,
            is_latest: true,
            recurrence_date: null // VIA never has recurrence_date in this flow
          });
        }

        // Prepare email notifications
        const vendorName = vea.Vendor?.company_name || 'Unknown Vendor';
        const departmentName = vea.Departments?.name || 'Unknown Department';

        // Get user details for email
        const assignedUser = await commonService.findByCondition(User, { id: vea.assigned_to }, ['firstName', 'lastName', 'email']);
        const approverUser = await commonService.findByCondition(User, { id: vea.approver }, ['firstName', 'lastName', 'email']);

        // VEA email notifications
        if (assignedUser?.email) {
          emailNotifications.push({
            email: assignedUser.email,
            subject: `Action Required: New VEA Version Created for ${vendorName}`,
            template: 'vea_version_update_mail.ejs',
            data: {
              assessmentName: `${vea.assessment_name} (${vendorName})`,
              name: `${assignedUser.firstName} ${assignedUser.lastName}`,
              departmentName: departmentName
            }
          });
        }

        if (approverUser?.email && approverUser.email !== assignedUser?.email) {
          emailNotifications.push({
            email: approverUser.email,
            subject: `Action Required: New VEA Version Created for ${vendorName}`,
            template: 'vea_version_update_mail.ejs',
            data: {
              assessmentName: `${vea.assessment_name} (${vendorName})`,
              name: `${approverUser.firstName} ${approverUser.lastName}`,
              departmentName: departmentName
            }
          });
        }

        // VIA email notifications if VIA exists
        if (correspondingVia) {
          const viaAssignedUser = await commonService.findByCondition(User, { id: correspondingVia.assigned_to }, ['firstName', 'lastName', 'email']);
          const viaApproverUser = await commonService.findByCondition(User, { id: correspondingVia.approver }, ['firstName', 'lastName', 'email']);

          if (viaAssignedUser?.email) {
            emailNotifications.push({
              email: viaAssignedUser.email,
              subject: `Action Required: New VIA Version Created for ${vendorName}`,
              template: 'via_version_update_mail.ejs',
              data: {
                assessmentName: `${correspondingVia.assessment_name} (${vendorName})`,
                name: `${viaAssignedUser.firstName} ${viaAssignedUser.lastName}`,
                departmentName: departmentName
              }
            });
          }

          if (viaApproverUser?.email && viaApproverUser.email !== viaAssignedUser?.email) {
            emailNotifications.push({
              email: viaApproverUser.email,
              subject: `Action Required: New VIA Version Created for ${vendorName}`,
              template: 'via_version_update_mail.ejs',
              data: {
                assessmentName: `${correspondingVia.assessment_name} (${vendorName})`,
                name: `${viaApproverUser.firstName} ${viaApproverUser.lastName}`,
                departmentName: departmentName
              }
            });
          }
        }

        // Update old versions to is_latest: false
        const individualTrans = await db.transaction();
        try {
          // Update old VEA version
          const updateVea = await commonService.updateData(VendorAssessments, { is_latest: false }, { id: vea.id }, individualTrans);
          if (!updateVea[1]) throw new Error('Failed to update existing VEA version');

          // Update old VIA version if exists
          if (correspondingVia) {
            const updateVia = await commonService.updateData(VendorAssessments, { is_latest: false }, { id: correspondingVia.id }, individualTrans);
            if (!updateVia[1]) throw new Error('Failed to update existing VIA version');
          }

          await individualTrans.commit();
        } catch (err) {
          await individualTrans.rollback();
          console.error('Error updating old assessment versions:', err.message);
        }
      }
    }

    // Bulk create new assessment versions
    if (newAssessments.length > 0) {
      const bulkDbTrans = await db.transaction();
      try {
        const bulkAdd = await commonService.bulkAdd(VendorAssessments, newAssessments, bulkDbTrans);
        if (!bulkAdd) throw new Error('Failed to create new assessment versions');

        await bulkDbTrans.commit();
        console.log(`Created ${newAssessments.length} new assessment versions`);
      } catch (err) {
        await bulkDbTrans.rollback();
        console.error('Error creating new assessment versions:', err.message);
      }
    }

    // Send email notifications
    for (const notification of emailNotifications) {
      try {
        await sendMail(notification.email, notification.data, notification.subject, notification.template);
      } catch (emailError) {
        console.error('Error sending email notification:', emailError);
      }
    }

    console.log('VEA recurrence cron executed successfully');
  } catch (error) {
    console.log('VEA recurrence cron error:', error);
  }
};
