const Joi = require('joi');
const { DEVICE_TYPE } = require('../constant/common');

//Workflow
const workflow = Joi.object({
  flowtype: Joi.string().required(),
  group_id: Joi.number().required()
});

const updateWorkflow = Joi.object({
  flowtype: Joi.string().optional(),
  workflow_status: Joi.string().optional(),
  group_id: Joi.number().optional()
});

//Workflow step
const addWorkflowStep = Joi.object({
  type_id: Joi.number().required(),
  step_title: Joi.string().required(),
  guidance_text: Joi.string().required()
});

const updateWorkflowStep = Joi.object({
  step_title: Joi.string().required(),
  activepieces_automation_id: Joi.string().optional()
});

const sendWhatsUpMessage = Joi.object({
  phone_number: Joi.string().required(),
  source_id: Joi.string().required()
});

const addWorkflowStepTask = Joi.object({
  workflow_id: Joi.number().required(),
  stage_id: Joi.number().required(),
  title: Joi.string().required(),
  guidance_text: Joi.string().optional(),
  start_date: Joi.date().optional(),
  due_date: Joi.date().optional(),
  due_days: Joi.number().optional(),
  department_id: Joi.number().optional(),
  requirement: Joi.string().optional(),
  assignee_id: Joi.array().items(Joi.number().integer().optional()).min(1).optional(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  TaskDocuments: Joi.array().items(Joi.object({})).optional(),
  activepieces_automation_id: Joi.string().optional()
});

const updateWorkflowStepTask = Joi.object({
  stage_id: Joi.number().optional(),
  title: Joi.string().optional(),
  guidance_text: Joi.string().optional(),
  start_date: Joi.date().optional(),
  due_date: Joi.date().optional(),
  department_id: Joi.number().optional(),
  requirement: Joi.string().optional(),
  progress: Joi.string().valid('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'REJECTED').optional(),
  assignee_id: Joi.array().items(Joi.number().integer().optional()).optional(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  TaskDocuments: Joi.array().items(Joi.object({})).optional(),
  activepieces_automation_id: Joi.string().optional(),
  due_days: Joi.number().optional()
});

const updateTaskActivepiecesId = Joi.object({
  activepieces_automation_id: Joi.string().allow(null).optional()
});

const createRequest = Joi.object({
  business_unit: Joi.number().optional(),
  dsr_request_type_id: Joi.number().required(),
  description: Joi.string().optional(),
  is_data_subject: Joi.boolean().required(),
  relationship: Joi.string().required(),
  first_name: Joi.string().required(),
  last_name: Joi.string().required(),
  email: Joi.string().email().required(),
  phone_no: Joi.string().required(),
  dob: Joi.date().required(),
  unique_identification_type: Joi.string().required(),
  unique_identification_number: Joi.string().required(),
  address_1: Joi.string().required(),
  address_2: Joi.string().optional(),
  city: Joi.string().required(),
  state_id: Joi.number().required(),
  postal_code: Joi.number().required(),
  country_id: Joi.number().required(),
  dsr_return_preference: Joi.string().optional(),
  is_internal_request: Joi.boolean().required(),
  third_party_name: Joi.string().optional(),
  third_party_practice_name: Joi.string().optional(),
  third_party_email: Joi.string().optional(),
  third_party_contact_number: Joi.string().optional(),
  joint_party_details: Joi.object({
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().required(),
    phone_no: Joi.string().required(),
    dob: Joi.date().required(),
    unique_identification_type: Joi.string().required(),
    unique_identification_number: Joi.string().required(),
    address_1: Joi.string().required(),
    address_2: Joi.string().optional(),
    city: Joi.string().required(),
    state_id: Joi.number().required(),
    postal_code: Joi.number().required(),
    country_id: Joi.number().required()
  }),
  second_address_1: Joi.string().optional(),
  second_address_2: Joi.string().optional(),
  second_city: Joi.string().optional(),
  second_state_id: Joi.number().optional(),
  second_postal_code: Joi.number().optional(),
  second_country_id: Joi.number().optional(),
  letter_of_authority_doc: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  identification_doc: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional()
});

const createGuestRequest = Joi.object({
  customer_id: Joi.number().required(),
  business_unit: Joi.number().optional(),
  dsr_request_type_id: Joi.number().required(),
  description: Joi.string().optional(),
  is_data_subject: Joi.boolean().required(),
  relationship: Joi.string().required(),
  first_name: Joi.string().required(),
  last_name: Joi.string().required(),
  email: Joi.string().email().required(),
  phone_no: Joi.string().required(),
  dob: Joi.date().required(),
  unique_identification_type: Joi.string().required(),
  unique_identification_number: Joi.string().required(),
  address_1: Joi.string().required(),
  address_2: Joi.string().optional(),
  city: Joi.string().required(),
  state_id: Joi.number().required(),
  postal_code: Joi.number().required(),
  country_id: Joi.number().required(),
  dsr_return_preference: Joi.string().optional(),
  is_internal_request: Joi.boolean().required(),
  third_party_name: Joi.string().optional(),
  third_party_practice_name: Joi.string().optional(),
  third_party_email: Joi.string().optional(),
  third_party_contact_number: Joi.string().optional(),
  joint_party_details: Joi.object({
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().required(),
    phone_no: Joi.string().required(),
    dob: Joi.date().required(),
    unique_identification_type: Joi.string().required(),
    unique_identification_number: Joi.string().required(),
    address_1: Joi.string().required(),
    address_2: Joi.string().optional(),
    city: Joi.string().required(),
    state_id: Joi.number().required(),
    postal_code: Joi.number().required(),
    country_id: Joi.number().required()
  }),
  second_address_1: Joi.string().optional(),
  second_address_2: Joi.string().optional(),
  second_city: Joi.string().optional(),
  second_state_id: Joi.number().optional(),
  second_postal_code: Joi.number().optional(),
  second_country_id: Joi.number().optional(),
  letter_of_authority_doc: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  identification_doc: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional()
});

const createFormGuestRequest = Joi.object({
  // 'g-recaptcha-response': Joi.string().required(),
  customer_id: Joi.string().required(),
  business_unit: Joi.number().optional(),
  dsr_request_type_id: Joi.number().required(),
  description: Joi.string().optional(),
  is_data_subject: Joi.boolean().required(),
  relationship: Joi.string().required(),
  first_name: Joi.string().required(),
  last_name: Joi.string().required(),
  email: Joi.string().email().required(),
  phone_no: Joi.string().required(),
  dob: Joi.date().required(),
  unique_identification_type: Joi.string().required(),
  unique_identification_number: Joi.string().required(),
  address_1: Joi.string().required(),
  address_2: Joi.string().optional(),
  city: Joi.string().required(),
  state_id: Joi.number().required(),
  postal_code: Joi.number().required(),
  country_id: Joi.number().required(),
  dsr_return_preference: Joi.string().optional(),
  is_internal_request: Joi.boolean().required(),
  third_party_name: Joi.string().optional(),
  third_party_practice_name: Joi.string().optional(),
  third_party_email: Joi.string().optional(),
  third_party_contact_number: Joi.string().optional(),
  // joint_party_details: Joi.object({
  //   first_name: Joi.string().required(),
  //   last_name: Joi.string().required(),
  //   email: Joi.string().email().required(),
  //   phone_no: Joi.string().required(),
  //   dob: Joi.date().required(),
  //   unique_identification_type: Joi.string().required(),
  //   unique_identification_number: Joi.string().required(),
  //   address_1: Joi.string().required(),
  //   address_2: Joi.string().optional(),
  //   city: Joi.string().required(),
  //   state_id: Joi.number().required(),
  //   postal_code: Joi.number().required(),
  //   country_id: Joi.number().required(),
  // }),
  joint_party_details_first_name: Joi.string()
    .optional() // Make first name optional
    .default(''), // Default to empty string if not provided

  joint_party_details_last_name: Joi.string()
    .optional() // Make last name optional by default
    .default('') // Default to empty string if not provided
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1), // Check if first_name is a non-empty string
      then: Joi.string().min(1).required(), // If first_name is filled, last_name must be required and non-empty
      otherwise: Joi.optional() // If first_name is not filled, last_name remains optional
    }),

  joint_party_details_email: Joi.string()
    .email()
    .optional()
    .default('') // Default to empty string if not provided
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.string().min(1).required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_phone_no: Joi.number()
    .optional()
    .default(null) // Default to null for number fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.number().required(), // If first_name is filled, phone_no must be required
      otherwise: Joi.optional()
    }),

  joint_party_details_dob: Joi.date()
    .optional()
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.date().required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_unique_identification_type: Joi.string()
    .optional()
    .default('') // Default to empty string for string fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.string().min(1).required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_unique_identification_number: Joi.string()
    .optional()
    .default('') // Default to empty string for string fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.string().min(1).required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_address_1: Joi.string()
    .optional()
    .default('') // Default to empty string for string fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.string().min(1).required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_address_2: Joi.string().optional(),

  joint_party_details_city: Joi.string()
    .optional()
    .default('') // Default to empty string for string fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.string().min(1).required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_state_id: Joi.number()
    .optional()
    .default(null) // Default to null for number fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.number().required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_postal_code: Joi.number()
    .optional()
    .default(null) // Default to null for number fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.number().required(),
      otherwise: Joi.optional()
    }),

  joint_party_details_country_id: Joi.number()
    .optional()
    .default(null) // Default to null for number fields
    .when('joint_party_details_first_name', {
      is: Joi.string().min(1),
      then: Joi.number().required(),
      otherwise: Joi.optional()
    }),

  second_address_1: Joi.string().allow(null, '').optional(),
  second_address_2: Joi.string().allow(null, '').optional(),
  second_city: Joi.string().allow(null, '').optional(),
  second_state_id: Joi.number().allow(null, '').optional(),
  second_postal_code: Joi.number().allow(null, '').optional(),
  second_country_id: Joi.number().allow(null, '').optional(),
  letter_of_authority_doc: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  identification_doc: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional()
});

const guestSendOtp = Joi.object({
  email: Joi.string().email().required()
});

const verifyOtp = Joi.object({
  email: Joi.string().email().required(),
  otp: Joi.number().required()
});
const verifyOtpV2 = Joi.object({
  otp: Joi.number().required()
});

const uploadDocForReqIdenti = Joi.object({
  dsr_data_subject_id: Joi.string().required()
});

const assignRequest = Joi.object({
  assignee_id: Joi.number().required(),
  workflow_step_id: Joi.number().optional()
});

const approvedReject = Joi.object({
  reject_reason: Joi.string().optional(),
  // step: Joi.number().required(),
  status: Joi.string().valid('PENDING', 'APPROVED', 'REJECTED', 'REJECTED_IN_PROGRESS', 'COMPLETED').required()
});

const addTaskDuringOverview = Joi.object({
  activepieces_automation_id: Joi.string().optional(),
  // step: Joi.number().required(),
  workflow_id: Joi.number().required(),
  stage_id: Joi.number().required(),
  title: Joi.string().required(),
  guidance_text: Joi.string().optional(),
  start_date: Joi.date().optional(),
  due_date: Joi.date().optional(),
  department_id: Joi.number().optional(),
  requirement: Joi.string().optional(),
  request_id: Joi.number().required(),
  assignee_id: Joi.array().items(Joi.number().integer().optional()).min(1).required(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  TaskDocuments: Joi.array().items(Joi.object({})).optional()
});

const updateTaskDuringOverview = Joi.object({
  activepieces_automation_id: Joi.string().optional(),
  id: Joi.number().optional(),
  workflow_id: Joi.number().optional(),
  stage_id: Joi.number().required(),
  title: Joi.string().optional(),
  guidance_text: Joi.string().optional(),
  start_date: Joi.date().optional(),
  due_date: Joi.date().optional(),
  due_days: Joi.number().optional(),
  department_id: Joi.number().optional(),
  requirement: Joi.string().optional(),
  progress: Joi.string().valid('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'REJECTED').optional(),
  assignee_id: Joi.array().items(Joi.number().integer().optional()).min(1).optional(),
  request_id: Joi.number().required(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  TaskDocuments: Joi.array().items(Joi.object({})).optional()
});

const deleteDocument = Joi.object({
  url: Joi.string().required()
});

const updateRequest = Joi.object({
  business_unit: Joi.number().optional(),
  dsr_id: Joi.string().optional(),
  data_subject_id: Joi.number().optional(),
  request_type: Joi.number().optional(),
  description: Joi.string().optional(),
  completion_date: Joi.date().optional(),
  status: Joi.boolean().optional(),
  assignee_id: Joi.number().optional(),
  extended: Joi.string().valid('YES', 'NO').optional(),
  workflow_step_id: Joi.number().optional(),
  reject_reason: Joi.string().optional(),
  progress: Joi.string().valid('VERIFIED').optional()
});

const createForm = Joi.object({
  fromURL: Joi.string().optional(),
  formData: Joi.object().required()
});

const createMail = Joi.object({
  subject: Joi.string().required(),
  content: Joi.string().required(),
  logo: Joi.string().optional(),
  bg_color: Joi.string().optional(),
  font_color: Joi.string().optional()
});

const sendGuestSideMail = Joi.object({
  customer_id: Joi.number().required(),
  content: Joi.string().required(),
  dsr_request_id: Joi.number().required(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional()
});

const sendAssigneeSideMail = Joi.object({
  workflow_step_id: Joi.number().optional(),
  subject: Joi.string().required(),
  content: Joi.string().required(),
  dsr_request_id: Joi.number().required(),
  mail_type: Joi.string().valid('EXTERNAL', 'INTERNAL').required(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional(),
  recipients_email: Joi.array().items(Joi.string().optional()).min(1).optional()
});

const registerWebhook = Joi.object({
  url: Joi.string().required(),
  action_name: Joi.string().required()
});

const deleteWebhook = Joi.object({
  url: Joi.string().required()
});
const createFormV2 = Joi.object({
  name: Joi.string().required(),
  busi_unit_id: Joi.number().required(),
  regulation_id: Joi.array().items(Joi.number()).optional(),
  authentication_type: Joi.string().valid('EMAIL', 'WHATSAPP', 'SMS').optional()
});
const createCategory = Joi.object({
  name: Joi.string().required()
});
const addCustomerControls = Joi.object({
  questions: Joi.array()
    .items({
      form_id: Joi.number().required(),
      category_id: Joi.number().allow(null).optional(),
      customer_id: Joi.number().required(),
      title: Joi.string().allow(null).optional(),
      description: Joi.string().allow(null).optional(),
      artifact_type: Joi.string().valid('select', 'radio', 'textarea', 'input', 'checkbox', 'custom_select', 'file', 'date', 'email', 'password', 'number').required(),
      is_attachment: Joi.boolean().required(),
      question: Joi.string().allow(null).optional(),
      fields: Joi.array().items(Joi.object()).allow(null).optional(),
      parent_id: Joi.number().allow(null).optional(),
      extra_input: Joi.boolean().required(),
      extra_input_type: Joi.string().allow(null).optional(),
      extra_input_fields: Joi.array().items(Joi.object()).allow(null).optional(),
      is_optional: Joi.boolean().required(),
      endpoint: Joi.string().allow(null).optional(),
      order: Joi.number().required(),
      rules: Joi.array().items(Joi.object()).allow(null).optional(),
      rule_applied: Joi.string().allow(null).optional()
    })
    .required()
});
const updateCustomControls = Joi.object({
  title: Joi.string().optional(),
  description: Joi.string().optional(),
  artifact_type: Joi.string().valid('select', 'radio', 'textarea', 'input', 'checkbox', 'custom_select', 'file', 'email', 'password', 'number').optional(),
  is_attachment: Joi.boolean().optional(),
  question: Joi.string().optional(),
  fields: Joi.array().items(Joi.object()).optional(),
  rules: Joi.array().items(Joi.object()).allow(null).optional(),
  rule_applied: Joi.string().allow(null).optional()
});
const publishForm = Joi.object({
  url: Joi.string().required()
});

const updateFormContent = Joi.object({
  content: Joi.object().required()
});

const createIncident = Joi.object({
  title: Joi.string().required(),
  reporter_name: Joi.number().optional(),
  designation: Joi.number().optional(),
  handled_by: Joi.number().optional(),
  // your_role_in_this_incident: Joi.array().items(Joi.string().valid('DATA_FIDUCIARY', 'DATA_PROCESSOR')).optional(),
  your_role_in_incident: Joi.array().items(Joi.string().valid('DATA_FIDUCIARY', 'DATA_PROCESSOR')).optional(),
  date_of_incident: Joi.date().optional(),
  date_of_discovery: Joi.date().optional(),
  incident_details: Joi.string().optional(),
  business_unit: Joi.number().allow(null).optional(),
  documents: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().required()
      }).required()
    )
    .optional()
});

const updateIncident = Joi.object({
  affected_pii: Joi.array().items(Joi.string()).optional(),
  no_of_data_records_affected: Joi.number().optional(),
  no_of_data_subjects_affected: Joi.number().optional(),
  severity_level: Joi.string().valid('MAJOR', 'MINOR').optional(),
  did_you_already_contact_the_data_fiduciary: Joi.string().valid('YES', 'NO').optional(),
  applicable_jurisdiction: Joi.array().items(Joi.number()).optional(),
  evaluate_notes: Joi.string().optional(),
  resolution_date: Joi.date().optional(),
  compliance_officer: Joi.number().optional(),
  resolution_notes: Joi.string().optional(),
  status: Joi.string().valid('OPEN', 'CLOSED').optional()
});

const addAction = Joi.object({
  data_breach_id: Joi.number().required(),
  title: Joi.string().required(),
  description: Joi.string().optional(),
  assignee_id: Joi.number().optional(),
  department_id: Joi.number().optional(),
  assigned_date: Joi.date().optional(),
  deadline_date: Joi.date().optional()
});

const addDataBreachNotification = Joi.object({
  data_breach_management_id: Joi.number().required(),
  applicable_jurisdiction_id: Joi.number().required(),
  have_you_filled_up_the_branch_form: Joi.string().valid('YES', 'NO').optional(),
  breach_identification_no: Joi.string().allow('').optional()
});

module.exports = {
  workflow,
  updateWorkflow,
  addWorkflowStep,
  updateWorkflowStep,
  addWorkflowStepTask,
  updateWorkflowStepTask,
  createRequest,
  uploadDocForReqIdenti,
  assignRequest,
  approvedReject,
  addTaskDuringOverview,
  deleteDocument,
  updateTaskDuringOverview,
  updateRequest,
  createForm,
  createMail,
  sendGuestSideMail,
  sendAssigneeSideMail,
  guestSendOtp,
  verifyOtp,
  createGuestRequest,
  createFormGuestRequest,
  registerWebhook,
  deleteWebhook,
  updateTaskActivepiecesId,
  createFormV2,
  createCategory,
  addCustomerControls,
  publishForm,
  updateFormContent,
  verifyOtpV2,
  sendWhatsUpMessage,
  createIncident,
  updateIncident,
  addAction,
  addDataBreachNotification,
  updateCustomControls
};
