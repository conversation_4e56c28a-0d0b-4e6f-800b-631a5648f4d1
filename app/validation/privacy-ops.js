const Joi = require('joi');
const { DEVICE_TYPE } = require('../constant/common');

//Workflow
const customRegulation = Joi.object({
  regulation_ids: Joi.array().items(Joi.number().optional()).required(),
  entity_id: Joi.number().required(),
  customer_id: Joi.number().required()
});

const CustomerBusinessRequirements = Joi.object({
  applicability: Joi.string().valid('YES', 'NO', 'N/A').default('NO'),
  document_id: Joi.number().optional(),
  compliance_status: Joi.string().valid('COMPLIANT', 'PARTIAL_COMPLIANT', 'NON_COMPLIANT', 'N/A').default('NON_COMPLIANT'),
  observation: Joi.array().items(Joi.string().optional()).optional()
});

const addCategoryControlBusiReq = Joi.object({
  category_name: Joi.string().required(),
  category_no: Joi.string().required(),
  control_no: Joi.string().required(),
  control_description: Joi.string().required(),
  references: Joi.array().items(Joi.string()).required(),
  BRs: Joi.array().items(Joi.string()).required(),
  mapping: Joi.array()
    .items(
      Joi.object().pattern(
        Joi.number().required(), // keys must be numbers
        Joi.array().items(Joi.string()).required()
      )
    ) // values must be arrays of strings
    .required()
});

const updateBusiReqArticles = Joi.array().items(
  Joi.object({
    id: Joi.number().required(),
    articles: Joi.array().items(Joi.string()).allow(null).required(),
    is_custom: Joi.boolean().truthy('true').falsy('false').allow(null).required(),
    customer_id: Joi.number().allow(null).required()
  })
);

const createRegulation = Joi.object({
  geography: Joi.string().required(),
  mapping_column_header: Joi.string().required(),
  source: Joi.string().required(),
  authoritative_source: Joi.string().required(),
  version: Joi.string().required(),
  url: Joi.string().uri().required(),
  available: Joi.boolean().required(),
  group_ids: Joi.array().items(Joi.number().integer()).required()
});

const addAction = Joi.object({
  title: Joi.string().required(),
  description: Joi.string().optional(),
  assigned_by: Joi.number().required(),
  assigned_to: Joi.number().required(),
  duty_id: Joi.number().optional(),
  assigned_date: Joi.date().iso().required(), // Enforcing ISO format
  deadline_date: Joi.date().iso().required(),
  regulation_id: Joi.number().allow(null).optional(),
  status: Joi.string().valid('Open', 'Closed').default('Open'),
  custom_busi_requirement_id: Joi.number().allow(null).optional(),
  entity_id: Joi.number().optional(),
  data_breach_id: Joi.number().allow(null).optional(),
  action_type: Joi.string().valid('DPO_RUNBOOK', 'DATA_BREACH_MANAGEMENT').optional()
});

const createDuty = Joi.object({
  tags: Joi.array().items(Joi.number()).optional(), // Ensures `tags` is an array of numbers
  title: Joi.string().required(),
  assignee_id: Joi.array().items(Joi.number()).required(), // Ensures it's an array of numbers
  due_date: Joi.date().iso().required(),
  standard: Joi.string().required(),
  comment: Joi.string().optional(),
  document: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().uri().required()
      })
    )
    .allow(null)
    .optional(),
  regulation_id: Joi.number().allow(null).optional(),
  custom_busi_requirement_id: Joi.number().allow(null).optional(),
  entity_id: Joi.number().optional()
});

const updateDuty = Joi.object({
  title: Joi.string().optional(),
  assignee_id: Joi.array().items(Joi.number()).optional(), // Ensures it's an array of numbers
  status: Joi.string().valid('OPEN', 'COMPLETED').default('OPEN'),
  start_date: Joi.date().iso().allow(null).optional(),
  owner_id: Joi.number().allow(null).optional(),
  due_date: Joi.date().iso().allow(null).optional(),
  frequency: Joi.string().valid('ANNUALLY', 'BI_ANNUAL', 'TRI_MONTHLY', 'MONTHLY').allow(null).optional(),
  standard: Joi.string().allow(null).optional(),
  criteria: Joi.string().allow(null).optional(),
  evidence: Joi.string().allow(null).optional(),
  comment: Joi.string().allow(null).optional(),
  document: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().uri().required()
      })
    )
    .allow(null)
    .optional()
});

const createImprovement = Joi.object({
  tags: Joi.array().items(Joi.number()).optional(),
  title: Joi.string().optional(),
  assignee_id: Joi.array().items(Joi.number()).required(),
  due_date: Joi.date().iso().required(),
  regulation_id: Joi.number().allow(null).optional(),
  comment: Joi.string().allow(null).optional(),
  document: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().uri().required()
      })
    )
    .allow(null)
    .optional(),
  custom_busi_requirement_id: Joi.number().allow(null).optional(),
  entity_id: Joi.number().optional()
});

const updateImprovement = Joi.object({
  title: Joi.string().optional(),
  assignee_id: Joi.array().items(Joi.number()).optional(), // Ensures it's an array of numbers
  status: Joi.string().valid('INPROGRESS', 'COMPLETED').default('INPROGRESS'),
  owner_id: Joi.number().optional(),
  due_date: Joi.date().iso().optional(),
  document: Joi.array()
    .items(
      Joi.object({
        original_name: Joi.string().required(),
        url: Joi.string().uri().required()
      })
    )
    .allow(null)
    .optional(),
  regulation_id: Joi.number().allow(null).optional(),
  origin: Joi.string().allow(null).optional(),
  finding: Joi.string().allow(null).optional(),
  root_cause: Joi.string().allow(null).optional(),
  treatment_plan: Joi.string().allow(null).optional(),
  progress: Joi.string().allow(null).optional(),
  tech_involvement: Joi.string().valid('NO', 'YES').optional(),
  other_area_affected: Joi.string().allow(null).optional(),
  comment: Joi.string().allow(null).optional(),
  management_review: Joi.string().allow(null).optional(),
  management_review_topic: Joi.string().allow(null).optional()
});

const createRisk = Joi.object({
  title: Joi.string().required(),
  description: Joi.string().required(),
  module: Joi.string().valid('DATA_MAPPING', 'ASSESSMENTS', 'VENDOR_RISK_MANAGEMENT', 'OTHER').required(),
  category: Joi.string().valid('LEGAL', 'COMPLIANCE', 'DATA_BREACH', 'OTHER').required(),
  source: Joi.string().valid('INTERNAL_SYSTEM', 'VENDOR', 'CUSTOMER', 'INTERNAL_AUDIT', 'EXTERNAL_AUDIT', 'REGULATORY_OBLIGATION', 'OTHER').required(),
  identified_date: Joi.date().iso().required(), // Enforcing ISO format
  threat: Joi.string().required(),
  vulnerability: Joi.string().required(),
  regulation_id: Joi.number().allow(null).optional(),
  entity_id: Joi.number().required()
});

const updateRisk = Joi.object({
  deadline_date: Joi.date().iso().optional(),
  reminder_date: Joi.date().iso().optional(),
  result: Joi.string().optional(),
  residual_risk_level: Joi.string().valid('CRITICAL', 'HIGH', 'MEDIUM', 'LOW').optional(),
  inherent_risk_level: Joi.string().valid('CRITICAL', 'HIGH', 'MEDIUM', 'LOW').optional(),
  target_risk_level: Joi.string().valid('CRITICAL', 'HIGH', 'MEDIUM', 'LOW').optional(),
  risk_template: Joi.string().valid('DPDPA_COMPLIANCE_RISK_TEMPLATE', 'GDPR_COMPLIANCE_RISK_TEMPLATE', 'CCPA_RISK_ASSESSMENT_TEMPLATE', 'ISO_27001_RISK_TEMPLATE').optional(),
  treatment_plan: Joi.string().optional(),
  treatment_status: Joi.string().valid('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'UNDER REVIEW').optional(),
  approver_id: Joi.number().optional(),
  owner_id: Joi.number().optional()
});

module.exports = {
  customRegulation,
  addAction,
  createDuty,
  updateDuty,
  createImprovement,
  updateImprovement,
  CustomerBusinessRequirements,
  createRisk,
  updateRisk,
  addCategoryControlBusiReq,
  createRegulation,
  updateBusiReqArticles
};
