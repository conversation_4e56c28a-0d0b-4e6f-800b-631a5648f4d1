const routes = require('express').Router();
const homePageController = require('../../controller/homepage');
const ropaController = require('../../controller/ropa');
const authController = require('../../controller/auth');
const onboardingController = require('../../controller/onboarding');
const schema = require('../../validation/auth');
// const policySchema = require('../../validation/policy');
const { verifyAuthToken, verifyUserToken, reqValidator, checkRoleAccess } = require('../../middleware');
const departmentController = require('../../controller/department');
const processController = require('../../controller/process');
const lawController = require('../../controller/law');
const policyController = require('../../controller/policy');
const policyDashboardController = require('../../controller/policyDashboard');
const policyCategoryController = require('../../controller/policyCategory');
const policyCreationController = require('../../controller/policyCreation');
const invoiceController = require('../../controller/invoices');
const auditLogController = require('../../controller/auditLog');



const privacyOpsRopaController = require('../../controller/privacyOpsRopaRepo');
const privacyOpsAssessmentController = require('../../controller/privacyOpsAssessmentRepo');
const privacyOpsDocumentController = require('../../controller/privacyOpsDocumentRepo');
const privacyOpsRegulationController = require('../../controller/regulations');
const privacyOpsActionController = require('../../controller/privacyopsAction'); 
const privacyOpsDutyController = require('../../controller/privacyOpsDuties');
const privacyOpsImprovementController = require('../../controller/privacyOpsImprovement');
const privacyOpsRiskController = require('../../controller/privacyOpsRisk');




const tiaController = require('../../controller/tiaV2');

const imageUpload = require('../../utils/multer');
const { uploadDocuments, uploadMultipleDocuments, deleteDocuments } = require('../../middleware/uploadToS3');
const keycloak = require('../../config/keycloak-config').initKeycloak();

routes.use(keycloak.middleware());
// Home page
routes.get('/regions', keycloak.protect(), verifyAuthToken, homePageController.getRegions);

// login
routes.post('/login', reqValidator(schema.login), authController.loginV2);
routes.post('/change-password', keycloak.protect(), verifyAuthToken, reqValidator(schema.changePassword), authController.changePasswordV2);

// Privacy Control Framework (ROPA)
routes.get('/ropa/list/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getROPAList);
// routes.get('/ropa/start/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.startROPA);
// routes.post('/ropa/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignRopa), ropaController.assignROPA);
// routes.get('/ropa/basic-info/:ropa_level/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getBasicInfo);
// routes.post('/ropa/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfo), ropaController.answerBasicInfo);
// routes.get('/ropa/progress/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getProgress);
// routes.get('/ropa/categories/:ropa_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getCategories);
// routes.get('/ropa/controls/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getControls);

// routes.get('/ropa/download-controls/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getRopaData);

// routes.get('/ropa/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getArtifactTypes);
// routes.post('/ropa/controls/', reqValidator(schema.addCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.addCustomControls);
// routes.put('/ropa/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.updateControls);
// routes.put('/ropa/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.updateFields);
// routes.delete('/ropa/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.deleteCustomControls);
// routes.get('/ropa/collaborator/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getCollaborators);
// routes.post('/ropa/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaborator), ropaController.addCollaborator);
// routes.post('/ropa/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswers), ropaController.createOrUpdateAnswers);
// routes.post('/ropa/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
// routes.patch('/ropa/submit/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.submitROPA);
// routes.post('/ropa/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewROPA), ropaController.reviewROPA);
// routes.patch('/ropa/review/submit/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.submitReview);
// routes.get('/ropa/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getAuditLog);
// routes.post('/ropa/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), ropaController.uploadControls);
// routes.get('/ropa/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), ropaController.getPolicyList);

// routes.get('/ropa-dashboard', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboard);
// routes.get('/ropa-organization-role', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardOrgRole);
// routes.get('/ropa/peronal-data', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardPersonalData);
// routes.get('/ropa/third-party', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardThirdParty);
// routes.get('/ropa/data-system', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardDataSystem);
// routes.get('/ropa/dpia-requirement', keycloak.protect(), verifyAuthToken, ropaController.ropaDpiaRequirement);
// routes.get('/ropa/organisation-role', keycloak.protect(), verifyAuthToken, ropaController.ropaOrgRole);

// // LIA
// routes.get('/lia/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getDepartmentsLIA);
// routes.get('/lia/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getProcessesLIA);
// routes.get('/lia/start/:lia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.startLIA);
// routes.post('/lia/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignLia), liaController.assignLIA);
// routes.get('/lia/progress/:lia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getProgress);
// routes.get('/lia/categories/:lia_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getCategories);
// routes.get('/lia/controls/:lia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getControls);
// routes.get('/lia/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getArtifactTypes);
// routes.post('/lia/controls/', reqValidator(schema.addCustomControlsLia), keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.addCustomControls);
// routes.put('/lia/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.updateControls);
// routes.put('/lia/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.updateFields);
// routes.get('/lia/collaborator/:lia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getliaCollaborators);
// routes.post('/lia/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorLia), liaController.addliaCollaborator);
// routes.post('/lia/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersLia), liaController.createOrUpdateAnswers);
// routes.post('/lia/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
// routes.patch('/lia/submit/:lia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.submitLIA);
// routes.post('/lia/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewLIA), liaController.reviewLIA);
// routes.patch('/lia/review/submit/:lia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.submitReview);
// routes.get('/lia/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, liaController.getAuditLog);
// routes.post('/lia/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), liaController.uploadControls);

// // TIA
// routes.get('/tia/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getDepartmentsTIA);
// routes.get('/tia/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getProcessesTIA);
// routes.get('/tia/start/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.startTIA);
// routes.post('/tia/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignTia), tiaController.assignTIA);
// // routes.get('/tia/basic-info/:tia_level/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getBasicInfo);
// // routes.post('/tia/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfoTia), tiaController.answerBasicInfo);
// routes.get('/tia/categories/:tia_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getCategories);
// routes.get('/tia/controls/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getControls);
// routes.get('/tia/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getArtifactTypes);
// routes.post('/tia/controls/', reqValidator(schema.addCustomControlsTia), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.addCustomControls);
// routes.put('/tia/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.updateControls);
// routes.put('/tia/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.updateFields);
// routes.post('/tia/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorTia), tiaController.addCollaborator);
// routes.post('/tia/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersTia), tiaController.createOrUpdateAnswers);
// routes.post('/tia/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
// routes.patch('/tia/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitTIA);
// routes.post('/tia/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewTIA), tiaController.reviewTIA);
// routes.patch('/tia/review/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitReview);
// routes.get('/tia/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getAuditLog);
// routes.post('/tia/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), tiaController.uploadControls);
// routes.get('/tia/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), tiaController.getPolicyList);

// // PIA
// routes.get('/pia/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getDepartmentsPIA);
// routes.get('/pia/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getProcessesPIA);
// routes.get('/pia/start/:pia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.startPIA);
// routes.post('/pia/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignPia), piaController.assignPIA);
// routes.get('/pia/basic-info/:pia_level/:pia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getBasicInfo);
// routes.post('/pia/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfoPia), piaController.answerBasicInfo);
// routes.get('/pia/categories/:pia_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getCategories);
// routes.get('/pia/controls/:pia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getControls);
// routes.get('/pia/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getArtifactTypes);
// routes.post('/pia/controls/', reqValidator(schema.addCustomControlsPia), keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.addCustomControls);
// routes.put('/pia/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.updateControls);
// routes.put('/pia/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.updateFields);
// routes.post('/pia/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorPia), piaController.addCollaborator);
// routes.post('/pia/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersPia), piaController.createOrUpdateAnswers);
// routes.post('/pia/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
// routes.patch('/pia/submit/:pia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.submitPIA);
// routes.post('/pia/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewPIA), piaController.reviewPIA);
// routes.patch('/pia/review/submit/:pia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.submitReview);
// routes.get('/pia/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, piaController.getAuditLog);
// routes.post('/pia/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), piaController.uploadControls);
// routes.get('/pia/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), piaController.getPolicyList);

// // PDA
// routes.get('/pda/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getDepartmentsPDA);
// routes.get('/pda/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getProcessesPDA);
// routes.get('/pda/start/:pda_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.startPDA);
// routes.post('/pda/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignPda), pdaController.assignPDA);
// routes.get('/pda/basic-info/:pda_level/:pda_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getBasicInfo);
// routes.post('/pda/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfoPda), pdaController.answerBasicInfo);
// routes.get('/pda/categories/:pda_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getCategories);
// routes.get('/pda/controls/:pda_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getControls);
// routes.get('/pda/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getArtifactTypes);
// routes.post('/pda/controls/', reqValidator(schema.addCustomControlsPda), keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.addCustomControls);
// routes.put('/pda/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.updateControls);
// routes.put('/pda/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.updateFields);
// routes.post('/pda/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorPda), pdaController.addCollaborator);
// routes.post('/pda/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersPda), pdaController.createOrUpdateAnswers);
// routes.post('/pda/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
// routes.patch('/pda/submit/:pda_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.submitPDA);
// routes.post('/pda/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewPDA), pdaController.reviewPDA);
// routes.patch('/pda/review/submit/:pda_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.submitReview);
// routes.get('/pda/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, pdaController.getAuditLog);
// routes.post('/pda/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), pdaController.uploadControls);
// routes.get('/pda/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), pdaController.getPolicyList);

// routes.delete('/delete-document', keycloak.protect(), verifyAuthToken, reqValidator(schema.deleteDocumentFromS3), deleteDocuments, ropaController.deleteDocuments);




routes.get('/assessments/tia/list/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getTIAList);
// routes.get('/tia/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getDepartmentsTIA);
// routes.get('/tia/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getProcessesTIA);
routes.get('/assessments/tia/start/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.startTIA);
routes.post('/assessments/tia/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignTia), tiaController.assignTIA);
routes.post('/assessments/tia/reviewer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignTia), tiaController.reviewerTIA);
// routes.get('/tia/basic-info/:tia_level/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getBasicInfo);
// routes.post('/tia/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfoTia), tiaController.answerBasicInfo);
routes.get('/assessments/tia/progress/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getProgress);
routes.get('/assessments/tia/categories/:tia_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getCategories);
routes.get('/assessments/tia/controls/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getControls);
routes.get('/assessments/tia/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getArtifactTypes);
routes.post('/assessments/tia/controls/', reqValidator(schema.addCustomControlsTia), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.addCustomControls);
routes.put('/assessments/tia/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.updateControls);
routes.put('/assessments/tia/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.updateFields);
routes.get('/assessments/tia/collaborator/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.gettiaCollaborators);
routes.post('/assessments/tia/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorTia), tiaController.addtiaCollaborator);
routes.post('/assessments/tia/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersTia), tiaController.createOrUpdateAnswers);
routes.post('/assessments/tia/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
routes.patch('/assessments/tia/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitTIA);
routes.post('/assessments/tia/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewTIA), tiaController.reviewTIA);
routes.patch('/assessments/tia/review/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitReview);
routes.get('/assessments/tia/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getAuditLog);
routes.post('/assessments/tia/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), tiaController.uploadControls);
// routes.get('/tia/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), tiaController.getPolicyList);
routes.delete('/assessments/tia/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.deleteCustomControls);
routes.get('/assessments/tia/download-controls/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getTiaData);
// after mitigation
routes.get('/assessments/tia/mitigation/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getControlsMitigation);
routes.patch('/assessments/tia/mitigation/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.mitigationVEA), tiaController.controlsMitigation);
routes.patch('/assessments/tia/mitigation/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitMitigation);
// routes.get('/assessments/tia/risk-matrix/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.countByRisks);
// routes.delete('/assessments/tia/delete-template', keycloak.protect(), verifyAuthToken, reqValidator(schema.deleteDocumentFromS3), deleteDocuments, veaController.deleteTemplate);
// routes.get('/assessments/tia/download-template/:template_id', keycloak.protect(), verifyAuthToken, veaController.downloadTemplate);
routes.get('/assessments/tia/download-mitigation/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getTiaDataWithMitigation);


//onboarding---->> industryVertical List
routes.get('/industry-vertical', verifyUserToken, onboardingController.industryVertical);
routes.get('/questionnaies', keycloak.protect(), verifyAuthToken, reqValidator(schema.questionnaies, 'query'), onboardingController.getQuestionnaires);
routes.post('/answers', keycloak.protect(), verifyAuthToken, onboardingController.answers);
routes.patch('/answers', keycloak.protect(), verifyAuthToken, onboardingController.updateAnswers);

//Policy - Module
routes.post('/policy', keycloak.protect(), verifyAuthToken, reqValidator(schema.createPolicy), policyController.createPolicy);
routes.put('/policy/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updatePolicy), policyController.updatePolicy);
routes.get('/policy', keycloak.protect(), verifyAuthToken, policyController.getPolicyList);
routes.get('/policy/:id', keycloak.protect(), verifyAuthToken, policyController.policyDetail);
routes.get('/policy/review/:id', keycloak.protect(), verifyAuthToken, policyController.getReview);
routes.patch('/policy/review/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateReview), policyController.updateReview);
// upload document to s3
routes.post('/policy/upload-document/:id', keycloak.protect(), verifyAuthToken, imageUpload.array('files'), uploadMultipleDocuments, policyController.uploadPolicyDocuments);
routes.get('/policy/download-document/:document_id', keycloak.protect(), verifyAuthToken, policyController.downloadPolicyDocuments);
routes.delete('/policy/delete-document', keycloak.protect(), verifyAuthToken, deleteDocuments, policyController.deletePolicyDocuments);

//Policy Dashboard// only for DPO
routes.get('/policy-dashboard/policy', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPolicies);
routes.get('/policy-dashboard/author', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByAuthor);
routes.get('/policy-dashboard/approver', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByApprover);
routes.get('/policy-dashboard/reviewer', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByReviewer);
routes.get('/policy-dashboard/entity', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByEntity);
routes.get('/policy-dashboard/department', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByDepartments);
//Audit Log of Policy
routes.get('/policy/audit-policy/:id', keycloak.protect(), verifyAuthToken, auditLogController.auditPolicy);

//policy categogy
routes.get('/policy-category', keycloak.protect(), verifyAuthToken, policyCategoryController.getCategory);

//law
routes.get('/relevant-law', keycloak.protect(), verifyAuthToken, lawController.getRelevantLaw);


routes.post('/policy-creation/continue-writing', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.continuePolicyCreation);
routes.post('/policy-creation/shorten', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.shortenText);
routes.post('/policy-creation/rewrite', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.rewriteText);
routes.post('/policy-creation/formal', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.formalizeText);
routes.post('/policy-creation/custom-prompt', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationCustomPrompt), policyCreationController.customPrompt);
routes.post('/policy-creation/fix-spelling', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.fixSpelling);
routes.get('/policy-creation/templates', keycloak.protect(), verifyAuthToken, policyCreationController.getTemplates);






// Pribacy Ops

routes.get('/regulations/list', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.regulationListV2);



// keycloak.protect(), verifyAuthToken,










// Department
routes.get('/departments/:group_id', keycloak.protect(), verifyAuthToken, departmentController.getDepartmentList);
routes.post('/departments', keycloak.protect(), verifyAuthToken, reqValidator(schema.department), departmentController.createDepartments);
routes.patch('/departments/:department_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateDepartment), departmentController.updateDepartment)

// Processes
routes.post('/processes', keycloak.protect(), verifyAuthToken, reqValidator(schema.process), processController.createProcesses);
routes.patch('/processes/:process_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateProcess), processController.updateProcess)

//Invoices
routes.get('/invoices', keycloak.protect(), verifyAuthToken, invoiceController.getInvoices);
routes.get('/invoices/:id', keycloak.protect(), verifyAuthToken, invoiceController.invoice);

// Blogs
routes.get('/blogs', homePageController.getBlogs);
routes.get('/blogs/categories', homePageController.getBlogsCategories);
routes.get('/blogs/:blogId', homePageController.getBlog);



module.exports = routes;
