const routes = require('express').Router();
const homePageController = require('../../controller/homepage');
const ropaController = require('../../controller/ropa');
const authController = require('../../controller/auth');
const onboardingController = require('../../controller/onboarding');
const schema = require('../../validation/auth');
// const policySchema = require('../../validation/policy');
const { verifyAuthToken, verifyUserToken, reqValidator, checkRoleAccess } = require('../../middleware');
const departmentController = require('../../controller/department');
const processController = require('../../controller/process');
const lawController = require('../../controller/law');
const policyController = require('../../controller/policy');
const policyDashboardController = require('../../controller/policyDashboard');
const policyCategoryController = require('../../controller/policyCategory');
const policyCreationController = require('../../controller/policyCreation');
const invoiceController = require('../../controller/invoices');
const auditLogController = require('../../controller/auditLog');

const privacyOpsRopaController = require('../../controller/privacyOpsRopaRepo');
const privacyOpsAssessmentController = require('../../controller/privacyOpsAssessmentRepo');
const privacyOpsDocumentController = require('../../controller/privacyOpsDocumentRepo');
const privacyOpsRegulationController = require('../../controller/regulations');
const privacyOpsActionController = require('../../controller/privacyopsAction');
const privacyOpsDutyController = require('../../controller/privacyOpsDuties');
const privacyOpsImprovementController = require('../../controller/privacyOpsImprovement');
const privacyOpsRiskController = require('../../controller/privacyOpsRisk');

const tiaController = require('../../controller/tiaV2');
const multer = require('multer');
const imageUpload = require('../../utils/multer');
const { uploadDocuments, uploadMultipleDocuments, deleteDocuments } = require('../../middleware/uploadToS3');
const keycloak = require('../../config/keycloak-config').initKeycloak();
const upload = multer({ dest: 'uploads/' }); // Store files temporarily in "uploads" folder
const { uploadDocumentsV2 } = require('../../middleware/uploadToBucket');

routes.use(keycloak.middleware());
// Home page
routes.get('/regions', keycloak.protect(), verifyAuthToken, homePageController.getRegions);

// login
routes.post('/login', reqValidator(schema.login), authController.loginV2);
routes.post('/change-password', keycloak.protect(), verifyAuthToken, reqValidator(schema.changePassword), authController.changePasswordV2);

// Privacy Control Framework (ROPA)
routes.get('/ropa/list/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getROPAList);
routes.post('/ropa/automate-ropa/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 1), ropaController.bulkAutomateFilledRopa);
routes.post('/ropa/department/bulk-ropa/upload', keycloak.protect(), verifyAuthToken, checkRoleAccess, upload.array('file', 1), uploadDocumentsV2, ropaController.createDepartmentWithProcessAndSubProcessV2);

// routes.get('/ropa/start/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.startROPA);
// routes.post('/ropa/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignRopa), ropaController.assignROPA);
// routes.get('/ropa/basic-info/:ropa_level/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getBasicInfo);
// routes.post('/ropa/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfo), ropaController.answerBasicInfo);
// routes.get('/ropa/progress/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getProgress);
// routes.get('/ropa/categories/:ropa_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getCategories);
// routes.get('/ropa/controls/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getControls);

// routes.get('/ropa/download-controls/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getRopaData);

// routes.get('/ropa/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getArtifactTypes);
// routes.post('/ropa/controls/', reqValidator(schema.addCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.addCustomControls);
// routes.put('/ropa/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.updateControls);
// routes.put('/ropa/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.updateFields);
// routes.delete('/ropa/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.deleteCustomControls);
// routes.get('/ropa/collaborator/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getCollaborators);
// routes.post('/ropa/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaborator), ropaController.addCollaborator);
// routes.post('/ropa/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswers), ropaController.createOrUpdateAnswers);
// routes.post('/ropa/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
// routes.patch('/ropa/submit/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.submitROPA);
// routes.post('/ropa/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewROPA), ropaController.reviewROPA);
// routes.patch('/ropa/review/submit/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.submitReview);
// routes.get('/ropa/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getAuditLog);
// routes.post('/ropa/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), ropaController.uploadControls);
// routes.get('/ropa/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), ropaController.getPolicyList);

// routes.get('/ropa-dashboard', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboard);
// routes.get('/ropa-organization-role', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardOrgRole);
// routes.get('/ropa/peronal-data', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardPersonalData);
// routes.get('/ropa/third-party', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardThirdParty);
// routes.get('/ropa/data-system', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardDataSystem);
// routes.get('/ropa/dpia-requirement', keycloak.protect(), verifyAuthToken, ropaController.ropaDpiaRequirement);
// routes.get('/ropa/organisation-role', keycloak.protect(), verifyAuthToken, ropaController.ropaOrgRole);

routes.get('/assessments/tia/list/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getTIAList);
// routes.get('/tia/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getDepartmentsTIA);
// routes.get('/tia/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getProcessesTIA);
routes.get('/assessments/tia/start/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.startTIA);
routes.post('/assessments/tia/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignTia), tiaController.assignTIA);
routes.post('/assessments/tia/reviewer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignTia), tiaController.reviewerTIA);
// routes.get('/tia/basic-info/:tia_level/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getBasicInfo);
// routes.post('/tia/basic-info/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfoTia), tiaController.answerBasicInfo);
routes.get('/assessments/tia/progress/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getProgress);
routes.get('/assessments/tia/categories/:tia_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getCategories);
routes.get('/assessments/tia/controls/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getControls);
routes.get('/assessments/tia/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getArtifactTypes);
routes.post('/assessments/tia/controls/', reqValidator(schema.addCustomControlsTia), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.addCustomControls);
routes.put('/assessments/tia/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.updateControls);
routes.put('/assessments/tia/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.updateFields);
routes.get('/assessments/tia/collaborator/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.gettiaCollaborators);
routes.post('/assessments/tia/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorTia), tiaController.addtiaCollaborator);
routes.post('/assessments/tia/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersTia), tiaController.createOrUpdateAnswers);
routes.post('/assessments/tia/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
routes.patch('/assessments/tia/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitTIA);
routes.post('/assessments/tia/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewTIA), tiaController.reviewTIA);
routes.patch('/assessments/tia/review/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitReview);
routes.get('/assessments/tia/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getAuditLog);
routes.post('/assessments/tia/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), tiaController.uploadControls);
// routes.get('/tia/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), tiaController.getPolicyList);
routes.delete('/assessments/tia/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.deleteCustomControls);
routes.get('/assessments/tia/download-controls/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getTiaData);
// after mitigation
routes.get('/assessments/tia/mitigation/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getControlsMitigation);
routes.patch('/assessments/tia/mitigation/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.mitigationVEA), tiaController.controlsMitigation);
routes.patch('/assessments/tia/mitigation/submit/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.submitMitigation);
// routes.get('/assessments/tia/risk-matrix/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.countByRisks);
// routes.delete('/assessments/tia/delete-template', keycloak.protect(), verifyAuthToken, reqValidator(schema.deleteDocumentFromS3), deleteDocuments, veaController.deleteTemplate);
// routes.get('/assessments/tia/download-template/:template_id', keycloak.protect(), verifyAuthToken, veaController.downloadTemplate);
routes.get('/assessments/tia/download-mitigation/:tia_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, tiaController.getTiaDataWithMitigation);

//onboarding---->> industryVertical List
routes.get('/industry-vertical', verifyUserToken, onboardingController.industryVertical);
routes.get('/questionnaies', keycloak.protect(), verifyAuthToken, reqValidator(schema.questionnaies, 'query'), onboardingController.getQuestionnaires);
routes.post('/answers', keycloak.protect(), verifyAuthToken, onboardingController.answers);
routes.patch('/answers', keycloak.protect(), verifyAuthToken, onboardingController.updateAnswers);

//Policy - Module
routes.post('/policy', keycloak.protect(), verifyAuthToken, reqValidator(schema.createPolicy), policyController.createPolicy);
routes.put('/policy/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updatePolicy), policyController.updatePolicy);
routes.get('/policy', keycloak.protect(), verifyAuthToken, policyController.getPolicyList);
routes.get('/policy/:id', keycloak.protect(), verifyAuthToken, policyController.policyDetail);
routes.get('/policy/review/:id', keycloak.protect(), verifyAuthToken, policyController.getReview);
routes.patch('/policy/review/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateReview), policyController.updateReview);
// upload document to s3
routes.post('/policy/upload-document/:id', keycloak.protect(), verifyAuthToken, imageUpload.array('files'), uploadMultipleDocuments, policyController.uploadPolicyDocuments);
routes.get('/policy/download-document/:document_id', keycloak.protect(), verifyAuthToken, policyController.downloadPolicyDocuments);
routes.delete('/policy/delete-document', keycloak.protect(), verifyAuthToken, deleteDocuments, policyController.deletePolicyDocuments);

//Policy Dashboard// only for DPO
routes.get('/policy-dashboard/policy', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPolicies);
routes.get('/policy-dashboard/author', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByAuthor);
routes.get('/policy-dashboard/approver', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByApprover);
routes.get('/policy-dashboard/reviewer', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByReviewer);
routes.get('/policy-dashboard/entity', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByEntity);
routes.get('/policy-dashboard/department', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByDepartments);
//Audit Log of Policy
routes.get('/policy/audit-policy/:id', keycloak.protect(), verifyAuthToken, auditLogController.auditPolicy);

//policy categogy
routes.get('/policy-category', keycloak.protect(), verifyAuthToken, policyCategoryController.getCategory);

//law
routes.get('/relevant-law', keycloak.protect(), verifyAuthToken, lawController.getRelevantLaw);

routes.post('/policy-creation/continue-writing', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.continuePolicyCreation);
routes.post('/policy-creation/shorten', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.shortenText);
routes.post('/policy-creation/rewrite', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.rewriteText);
routes.post('/policy-creation/formal', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.formalizeText);
routes.post('/policy-creation/custom-prompt', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationCustomPrompt), policyCreationController.customPrompt);
routes.post('/policy-creation/fix-spelling', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.fixSpelling);
routes.get('/policy-creation/templates', keycloak.protect(), verifyAuthToken, policyCreationController.getTemplates);

// Pribacy Ops

routes.get('/regulations/list', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.regulationListV2);

// keycloak.protect(), verifyAuthToken,

// Department
routes.get('/departments/:group_id', keycloak.protect(), verifyAuthToken, departmentController.getDepartmentList);
routes.post('/departments', keycloak.protect(), verifyAuthToken, reqValidator(schema.department), departmentController.createDepartments);
routes.patch('/departments/:department_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateDepartment), departmentController.updateDepartment);

// Processes
routes.post('/processes', keycloak.protect(), verifyAuthToken, reqValidator(schema.process), processController.createProcesses);
routes.patch('/processes/:process_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateProcess), processController.updateProcess);

//Invoices
routes.get('/invoices', keycloak.protect(), verifyAuthToken, invoiceController.getInvoices);
routes.get('/invoices/:id', keycloak.protect(), verifyAuthToken, invoiceController.invoice);

// Blogs
routes.get('/blogs', homePageController.getBlogs);
routes.get('/blogs/categories', homePageController.getBlogsCategories);
routes.get('/blogs/:blogId', homePageController.getBlog);

module.exports = routes;
