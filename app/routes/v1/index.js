const routes = require('express').Router();
const homePageController = require('../../controller/homepage');
const assessmentsDashboardController = require('../../controller/assessmentsDashboard');
const ropaController = require('../../controller/ropa');
const liaController = require('../../controller/lia');
const tiaController = require('../../controller/tia');
const piaController = require('../../controller/pia');
const pdaController = require('../../controller/pda');
const vendorController = require('../../controller/vendor');
const viaController = require('../../controller/via');
const veaController = require('../../controller/vea');
const onboardingController = require('../../controller/onboarding');
const schema = require('../../validation/auth');
const dsrSchema = require('../../validation/dsr');
const privacyOpsSchema = require('../../validation/privacy-ops');
// const policySchema = require('../../validation/policy');
const { verifyAuthToken, verifyUserToken, reqValidator, checkRoleAccess, verifyGuestToken } = require('../../middleware');
const limiter = require('../../middleware/rateLimiter');
const departmentController = require('../../controller/department');
const taskController = require('../../controller/task');
const workflowController = require('../../controller/workflow');
const dsrController = require('../../controller/dsr');
const activepiecesController = require('../../controller/activepieces');
const dsrDashboardController = require('../../controller/dsrDashboard');
const dsrFormRepoController = require('../../controller/dsrFormRepo');
const dsrMailTemplateController = require('../../controller/dsrEmailTemplate');
const workflowStepController = require('../../controller/workflowsteps');
const processController = require('../../controller/process');
const lawController = require('../../controller/law');
const policyController = require('../../controller/policy');
const policyDashboardController = require('../../controller/policyDashboard');
const policyCategoryController = require('../../controller/policyCategory');
const policyCreationController = require('../../controller/policyCreation');
const invoiceController = require('../../controller/invoices');
const auditLogController = require('../../controller/auditLog');
const clickHouseController = require('../../controller/click-house');
const privacyOpsRopaController = require('../../controller/privacyOpsRopaRepo');
const privacyOpsAssessmentController = require('../../controller/privacyOpsAssessmentRepo');
const privacyOpsDocumentController = require('../../controller/privacyOpsDocumentRepo');
const privacyOpsRegulationController = require('../../controller/regulations');
const privacyOpsActionController = require('../../controller/privacyopsAction');
const privacyOpsDutyController = require('../../controller/privacyOpsDuties');
const privacyOpsImprovementController = require('../../controller/privacyOpsImprovement');
const privacyOpsRiskController = require('../../controller/privacyOpsRisk');
const privacyOpsDashboard = require('../../controller/privacyOpsDashboard');
const dataBreachManagement = require('../../controller/dataBreachManagement');
const assessmentController = require('../../controller/assessment');
const ropaDashboard = require('../../controller/ropaDashboard');
const imageUpload = require('../../utils/multer');
// const { uploadDocuments, uploadDocumentsV2, uploadMultipleDocuments, deleteDocuments } = require('../../middleware/uploadToS3');
const { uploadDocuments, uploadDocumentsV2, uploadMultipleDocuments, deleteDocuments } = require('../../middleware/uploadToBucket');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' }); // Store files temporarily in "uploads" folder

const keycloak = require('../../config/keycloak-config').initKeycloak();

// core engine
const authController = require('../../controller/auth');
const customerController = require('../../controller/customer');
const groupController = require('../../controller/group');
// const onboardingController = require('../../controller/onboarding');
const dashboardController = require('../../controller/dashboard');
const roleController = require('../../controller/role');
const userController = require('../../controller/user');
const resourceController = require('../../controller/resource');
const tableController = require('../../controller/table');
const projectController = require('../../controller/project');
const supportController = require('../../controller/support');
const grafanaDashboardController = require('../../controller/grafanaDashboard');
const ipController = require('../../controller/ipController');

routes.use(keycloak.middleware());

//ipDetails
routes.get('/ip-info/:ip', ipController.getDetailedIpInfo);

// Home page
routes.get('/regions', keycloak.protect(), verifyAuthToken, homePageController.getRegions);

//******************************** RoPA **************************************
// Privacy Control Framework (ROPA)
routes.post('/ropa/all-answers/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getAllRopaAnswers);
routes.post('/ropa/update/', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateRopa), ropaController.updateRopa);
routes.patch('/ropa/tentative-date/:ropa_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateTentativeDateRopa), ropaController.updateTentativeDate);

routes.get('/ropa/departments/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getDepartmentsROPA);
routes.get('/ropa/processes/:department_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getProcessesROPA);
routes.get('/ropa/start/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.startROPA);
routes.post('/ropa/restart/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.restartROPA);
routes.post('/ropa/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignRopa), ropaController.assignROPA);
routes.get('/ropa/basic-info/:ropa_level/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getBasicInfo);
routes.post('/ropa/basic-info/answer/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.answerBasicInfo), ropaController.answerBasicInfo);
routes.get('/ropa/progress/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getProgress);
routes.get('/ropa/categories/:ropa_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getCategories);
routes.get('/ropa/controls/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getControls);

routes.get('/ropa/download-controls/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getRopaData);
routes.get('/ropa/export', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getExportRopa);

routes.get('/ropa/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getArtifactTypes);
routes.post('/ropa/controls/', reqValidator(schema.addCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.addCustomControls);
routes.put('/ropa/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.updateControls);
routes.put('/ropa/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.updateFields);
routes.delete('/ropa/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.deleteCustomControls);
routes.get('/ropa/collaborator/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getCollaborators);
routes.post('/ropa/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaborator), ropaController.addCollaborator);
routes.post('/ropa/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswers), ropaController.createOrUpdateAnswers);
routes.post('/ropa/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
routes.patch('/ropa/submit/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.submitROPA);
routes.post('/ropa/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewROPA), ropaController.reviewROPA);
routes.patch('/ropa/review/submit/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.submitReview);
routes.get('/ropa/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.getAuditLog);
routes.get('/ropa/download-audit-log/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, ropaController.downloadAuditLog);
routes.post('/ropa/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), ropaController.uploadControls);
routes.get('/ropa/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), ropaController.getPolicyList);
routes.post('/ropa/upload-ropa/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 1), uploadDocumentsV2, ropaController.uploadFilledRopa);
routes.post('/ropa/automate-ropa/:ropa_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 1), ropaController.automateFilledRopa);
// routes.get('/ropa-embedded', ropaController.createEmbeddings);
routes.post('/ropa/department/bulk-ropa/upload', keycloak.protect(), verifyAuthToken, checkRoleAccess, upload.array('file', 1), uploadDocumentsV2, ropaController.createDepartmentWithProcessAndSubProcess);
routes.post('/ropa/change-category', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.categorizationOfControls), ropaController.categorizationOfControls);
routes.get('/ropa-dashboard', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboard);
routes.get('/ropa-organization-role', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardOrgRole);
routes.get('/ropa/peronal-data', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardPersonalData);
routes.get('/ropa/third-party', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardThirdParty);
routes.get('/ropa/data-system', keycloak.protect(), verifyAuthToken, ropaController.ropaDashboardDataSystem);
routes.get('/ropa/dpia-requirement', keycloak.protect(), verifyAuthToken, ropaController.ropaDpiaRequirement);
routes.get('/ropa/organisation-role', keycloak.protect(), verifyAuthToken, ropaController.ropaOrgRole);
routes.delete('/delete-document', keycloak.protect(), verifyAuthToken, reqValidator(schema.deleteDocumentFromS3v2), deleteDocuments, ropaController.deleteDocuments);
routes.get('/ropa/categories-progress/:ropa_id', keycloak.protect(), verifyAuthToken, ropaController.collaboratorProgressV2);
routes.get('/ropa/data', keycloak.protect(), verifyAuthToken, ropaController.getAllRopas);
routes.post('/ropa/reviewer', keycloak.protect(), verifyAuthToken, reqValidator(schema.assignRopa), ropaController.assignReviewerROPA);
routes.get('/ropa-dashboard-status', keycloak.protect(), verifyAuthToken, ropaDashboard.getStatusCounts);
routes.get('/ropa-dashboard-progress', keycloak.protect(), verifyAuthToken, ropaDashboard.getProgressStatsByGroup);
routes.get('/ropa-dashboard-department', keycloak.protect(), verifyAuthToken, ropaDashboard.getRopaStatusByDepartment);
routes.post('/webhook/privacy-notice', ropaController.privacyNoticeWebhook);
routes.post('/privacy-notice', keycloak.protect(), verifyAuthToken, ropaController.addPrivacyNotice);
routes.get('/privacy-notice/:taskId', keycloak.protect(), verifyAuthToken, ropaController.getprivacyNoticeTask);
routes.get('/privacy-notice', keycloak.protect(), verifyAuthToken, ropaController.getPrivacyNotices);
routes.patch('/privacy-notice', keycloak.protect(), verifyAuthToken, ropaController.updatePrivacyNotices);

//******************************** Unified Assessments **************************************
// unified assessment
routes.post('/assessments/assessment-type', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), assessmentController.addCustomeAssessmentType);
routes.get('/assessments/list', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.assessmentList);
routes.post('/assessments/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), assessmentController.uploadControls);
routes.get('/assessments/list-template', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.listTemplates);
routes.get('/assessments/start/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.startAssesment);
routes.post('/assessments/create-assessment', keycloak.protect(), verifyAuthToken, reqValidator(schema.createAssessments2), checkRoleAccess, assessmentController.createAssessments);
routes.get('/assessments/get-category/:level', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getCategories);
routes.get('/assessments/get-controls/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getControls);
routes.get('/assessments/artifact-types', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getArtifactTypes);
routes.post('/assessments/controls', reqValidator(schema.addCustomControlsUnified), keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.addCustomControls);
routes.get('/assessments/tasks-overview/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.taskOverview);
routes.put('/assessments/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.updateControls);
routes.put('/assessments/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.updateFields);
routes.delete('/assessments/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.deleteCustomControls);
routes.get('/assessments/progress/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getProgress);
routes.post('/assessments/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorAssessment), assessmentController.addCollaborator);
routes.get('/assessments/collaborator/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getCollaborators);
routes.post('/assessments/answer', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersAssessment), assessmentController.createOrUpdateAnswers);
routes.patch('/assessments/submit/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.submitASSESSMENT);
routes.post('/assessments/review', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewASSESSMENT), assessmentController.reviewASSESSMENT);
routes.patch('/assessments/review/submit/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.submitReview);
routes.get('/assessments/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getAuditLog);
routes.get('/assessments/download-audit-log/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.downloadAuditLog);
routes.get('/assessments/download-audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.downloadAuditLog);
routes.get('/assessments/download-controls/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getAssessmentData);
routes.get('/assessments/category-types', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.getAssessmentCategories);
routes.get('/assessments/view-template', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentController.viewtemplate);
routes.post('/assessments/reviewer', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignAssessment), assessmentController.reviewerAssessment);
routes.post('/assessments/assign', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignAssessment), assessmentController.assignAssessment);
routes.get('/assessments/categories-progress/:assessment_id', keycloak.protect(), verifyAuthToken, assessmentController.collaboratorProgress);
routes.post('/assessments/automate-assessment/:ass_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 1), assessmentController.automateFilledAssessment);
routes.post('/assessments/bulk-automate/:assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 1), assessmentController.bulkAutomateFilledAssessment);

//list of Assesments (master table)
routes.get('/assessments', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentsDashboardController.assessments);
routes.get('/assessments/task-overview/:entity_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentsDashboardController.taskOverview);
routes.post('/assessments/create', keycloak.protect(), verifyAuthToken, reqValidator(schema.createAssessments), checkRoleAccess, assessmentsDashboardController.createAssessments);
routes.patch('/assessments/update/:assessment_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateAssessments), checkRoleAccess, assessmentsDashboardController.updateAssessments);
routes.get('/assessments/audit/logs', keycloak.protect(), verifyAuthToken, checkRoleAccess, auditLogController.auditAssessments);
//Dashboard
routes.get('/assessments-dashboard/risks', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentsDashboardController.countByRisks);
routes.get('/assessments-dashboard/owner', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentsDashboardController.countByOwner);
routes.get('/assessments-dashboard/name', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentsDashboardController.countByAssessmentName);
routes.get('/assessments-dashboard/list', keycloak.protect(), verifyAuthToken, checkRoleAccess, assessmentsDashboardController.assessmentsList);

//********************************Vendor Risk Mangement**************************************
routes.post('/vrm/create', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createVendor), vendorController.createNewVendor);
routes.post('/vrm/add', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addVendor), vendorController.addVendor);
routes.patch('/vrm/detail/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.updateVendor), vendorController.updateVendor);
routes.get('/vrm/detail/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.getVendorDetails);
routes.get('/vrm/vendor/type', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.vendorType);
routes.get('/vrm/vendor/', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.vendorListForCustomer);
routes.get('/vrm/audit/logs', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.auditVRM);
// routes.get('/vrm/download-audit-log/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.downloadAuditLog);
routes.get('/vrm/download-audit-log/:vendor_assessment_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.downloadAuditLog);
routes.get('/vrm/download-audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.downloadAuditLog);
routes.post('/vrm/alert', keycloak.protect(), verifyAuthToken, reqValidator(schema.sendAlert), vendorController.sendAlert);
routes.get('/vrm/vendor-list', keycloak.protect(), verifyAuthToken, vendorController.getVednorList);
//_________VRM Dashboard___________
routes.get('/vrm-dashboard/key', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.countByStageOrRisk);
routes.get('/vrm-dashboard', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.countByDepartmentOrEntity);

//--------->> Vendor Internal Assessment <<------------------
routes.get('/vrm/via/start/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.startVIA);
routes.post('/vrm/via/assign/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignVia), viaController.assignVIA);
routes.post('/vrm/via/reviewer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignVia), viaController.reviewerVIA);
routes.get('/vrm/via/progress/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getProgress);
routes.get('/vrm/via/categories/:via_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getCategories);
routes.get('/vrm/via/controls/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getControls);
routes.get('/vrm/via/artifact-types/', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getArtifactTypes);
routes.post('/vrm/via/controls/', reqValidator(schema.addCustomControlsVia), keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.addCustomControls);
routes.put('/vrm/via/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.updateControls);
routes.put('/vrm/via/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.updateFields);
routes.get('/vrm/via/collaborator/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getviaCollaborators);
routes.post('/vrm/via/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorVia), viaController.addviaCollaborator);
routes.post('/vrm/via/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersVia), viaController.createOrUpdateAnswers);
routes.post('/vrm/via/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
routes.patch('/vrm/via/submit/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.submitVIA);
routes.post('/vrm/via/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewVIA), viaController.reviewVIA);
routes.patch('/vrm/via/review/submit/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.submitReview);
routes.get('/vrm/via/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getAuditLog);
// routes.get('/vrm/via/download-audit-log/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.downloadAuditLog);
routes.post('/vrm/via/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), viaController.uploadControls);
routes.delete('/vrm/via/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.deleteCustomControls);
routes.get('/vrm/via/download-controls/:via_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, viaController.getViaData);
routes.get('/vrm/via/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.viaList);
routes.get('/vrm/via/categories-progress/:via_id', keycloak.protect(), verifyAuthToken, viaController.collaboratorProgress);

// ----------------->> Vendor External Assessment <<----------------------
routes.get('/vrm/vea/start/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.startVEA);
routes.get('/vrm/vea/progress/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getProgress);
routes.get('/vrm/vea/categories/:vea_level', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getCategories);
routes.get('/vrm/vea/controls/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getControls);
routes.post('/vrm/vea/controls/', reqValidator(schema.addCustomControlsVea), keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.addCustomControls);
routes.put('/vrm/vea/controls/:customer_control_id', reqValidator(schema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.updateControls);
routes.put('/vrm/vea/controls/fields/:customer_control_id', reqValidator(schema.updateFields), keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.updateFields);
// routes.get('/vrm/vea/collaborator/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getveaCollaborators);
// routes.post('/vrm/vea/collaborator', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.addCollaboratorVea), veaController.addveaCollaborator);
routes.post('/vrm/vea/answer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createAnswersVea), veaController.createOrUpdateAnswers);
routes.post('/vrm/vea/answer/upload-document/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
routes.patch('/vrm/vea/submit/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.submitVEA);
routes.post('/vrm/vea/review/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.reviewVEA), veaController.reviewVEA);
routes.patch('/vrm/vea/review/submit/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.submitReview);
routes.get('/vrm/vea/audit-log', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getAuditLog);
// routes.get('/vrm/vea/download-audit-log/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.downloadAuditLog);
routes.post('/vrm/vea/upload-controls', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocumentsV2, veaController.uploadControls);
// routes.get('/vea/policy-list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getPolicyList, 'query'), veaController.getPolicyList);
routes.delete('/vrm/vea/controls/:customer_control_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.deleteCustomControls);
routes.get('/vrm/vea/download-controls/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getVeaData);
routes.get('/vrm/vea/mitigation/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getControlsMitigation);
routes.patch('/vrm/vea/mitigation/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.mitigationVEA), veaController.controlsMitigation);
routes.patch('/vrm/vea/mitigation/submit/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.submitMitigation);
routes.get('/vrm/vea/risk-matrix/:vendor_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.countByRisks);
routes.delete('/vrm/vea/delete-template', keycloak.protect(), verifyAuthToken, reqValidator(schema.deleteDocumentFromS3), deleteDocuments, veaController.deleteTemplate);
routes.get('/vrm/vea/download-template/:template_id', keycloak.protect(), verifyAuthToken, veaController.downloadTemplate);
routes.get('/vrm/vea/download-mitigation/:vea_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.getVeaDataWithMitigation);
routes.get('/vrm/vea/categories-progress/:vea_id', keycloak.protect(), verifyAuthToken, veaController.collaboratorProgress);
//---------->>> Vendor external assessment list <<---------------
routes.get('/vrm/vea/template', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.templateList);
routes.get('/vrm/vea', keycloak.protect(), verifyAuthToken, checkRoleAccess, vendorController.veaList);
routes.get('/vrm/vea/view-template', keycloak.protect(), verifyAuthToken, checkRoleAccess, veaController.viewtemplate);

//onboarding---->> industryVertical List
routes.get('/industry-vertical', onboardingController.industryVertical);
routes.get('/questionnaies', keycloak.protect(), verifyAuthToken, reqValidator(schema.questionnaies, 'query'), onboardingController.getQuestionnaires);
routes.post('/answers', keycloak.protect(), verifyAuthToken, onboardingController.answers);
routes.patch('/answers', keycloak.protect(), verifyAuthToken, onboardingController.updateAnswers);

//Policy - Module
routes.post('/policy', keycloak.protect(), verifyAuthToken, reqValidator(schema.createPolicy), policyController.createPolicy);
routes.patch('/policy/collaborator/:policy_id', keycloak.protect(), verifyAuthToken, policyController.addCollaborator);
routes.put('/policy/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updatePolicy), policyController.updatePolicy);
routes.get('/policy', keycloak.protect(), verifyAuthToken, policyController.getPolicyList);
routes.get('/policy/:id', keycloak.protect(), verifyAuthToken, policyController.policyDetail);
routes.get('/policy/review/:id', keycloak.protect(), verifyAuthToken, policyController.getReview);
routes.patch('/policy/review/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateReview), policyController.updateReview);
// upload document to s3
routes.post('/policy/upload-document/:id', keycloak.protect(), verifyAuthToken, imageUpload.array('files'), uploadMultipleDocuments, policyController.uploadPolicyDocuments);
routes.get('/policy/download-document/:policy_id', keycloak.protect(), verifyAuthToken, policyController.downloadPolicyDocuments);
routes.delete('/policy/delete-document', keycloak.protect(), verifyAuthToken, deleteDocuments, policyController.deletePolicyDocuments);

//Policy Dashboard// only for DPO
routes.get('/policy-dashboard/policy', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPolicies);
routes.get('/policy-dashboard/author', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByAuthor);
routes.get('/policy-dashboard/approver', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByApprover);
routes.get('/policy-dashboard/reviewer', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByReviewer);
routes.get('/policy-dashboard/entity', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByEntity);
routes.get('/policy-dashboard/department', keycloak.protect(), verifyAuthToken, checkRoleAccess, policyDashboardController.countPoliciesByDepartments);
//Audit Log of Policy
routes.get('/policy/audit-policy/:id', keycloak.protect(), verifyAuthToken, auditLogController.auditPolicy);
routes.get('/policy/audit/logs', keycloak.protect(), verifyAuthToken, auditLogController.auditLogs);

//policy categogy
routes.get('/policy-category', keycloak.protect(), verifyAuthToken, policyCategoryController.getCategory);

//law
routes.get('/relevant-law', keycloak.protect(), verifyAuthToken, lawController.getRelevantLaw);
// AI policy creation
routes.post('/policy-creation', keycloak.protect(), verifyAuthToken, reqValidator(schema.startPolicyCreation), policyCreationController.policyCreation);
routes.post('/policy-creation/continue-writing', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.continuePolicyCreation);
routes.post('/policy-creation/shorten', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.shortenText);
routes.post('/policy-creation/rewrite', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.rewriteText);
routes.post('/policy-creation/formal', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.formalizeText);
routes.post('/policy-creation/custom-prompt', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationCustomPrompt), policyCreationController.customPrompt);
routes.post('/policy-creation/fix-spelling', keycloak.protect(), verifyAuthToken, reqValidator(schema.policyCreationAI), policyCreationController.fixSpelling);
routes.get('/policy-creation/templates', keycloak.protect(), verifyAuthToken, policyCreationController.getTemplates);
routes.patch('/policy-creation/save/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.savePolicy), policyCreationController.savePolicy);
routes.patch('/policy-creation/submit/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.submitPolicy), policyCreationController.submitPolicy);

//Workflow
routes.get('/workflow', keycloak.protect(), verifyAuthToken, checkRoleAccess, workflowController.allWorkflowList);
routes.get('/workflow/published', keycloak.protect(), verifyAuthToken, checkRoleAccess, workflowController.allPublishedWorkflowList);
routes.get('/workflow/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, workflowController.getWorkflowById);
routes.post('/workflow', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.workflow), workflowController.createWorkflow);
routes.patch('/workflow/:workflow_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateWorkflow), workflowController.updateWorkflow);
routes.get('/guest-workflow/published', workflowController.allPublishedWorkflowListv2);

//Workflow steps
routes.post('/workflow/steps', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.addWorkflowStep), workflowStepController.createWorkflowSteps);
routes.patch('/workflow/steps/:workflow_step_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateWorkflowStep), workflowStepController.updateWorkflowSteps);
routes.delete('/workflow/steps/:workflow_step_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, workflowStepController.deleteWorkflowSteps);
routes.patch('/workflow/steps/moveup/:workflow_step_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateWorkflowStep), workflowStepController.moveUpSteps);
routes.patch('/workflow/steps/movedown/:workflow_step_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateWorkflowStep), workflowStepController.moveDownSteps);

//Task
routes.get('/workflowsteps/task/:task_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, taskController.getTaskById);
routes.get('/workflowsteps/alltask/:stage_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, taskController.getAllTask);
routes.post('/workflowsteps/task', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.addWorkflowStepTask), taskController.createTask);
routes.patch('/workflowsteps/task/update/activepieces_id/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateTaskActivepiecesId), taskController.updateTaskActivePiecesInfo);
routes.patch('/workflowsteps/task/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateWorkflowStepTask), taskController.updateTask);
routes.delete('/workflowsteps/task/:task_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, taskController.deleteTask);

//activepeacess
routes.post('/activepieces/call-activepieces-webhook', activepiecesController.callActivepiecesWebhook);
routes.post('/activepieces/send-whatsapp-message', keycloak.protect(), reqValidator(dsrSchema.sendWhatsUpMessage), activepiecesController.sendWhatsAppMessage);
routes.post('/activepieces/update-workflow/:id', activepiecesController.updateWorkflow);
routes.post('/activepieces/login', activepiecesController.login);
routes.get('/activepieces/workflows', activepiecesController.getWorkflows);
routes.post('/activepieces/workflows/update/:id', activepiecesController.updateWorkflowStatus);
routes.get('/activepieces/workflows/:id', activepiecesController.getWorkflowById);
routes.post('/activepieces/validate-user', keycloak.protect(), activepiecesController.validateUser);
routes.delete('/dsr/delete-webhook', reqValidator(dsrSchema.deleteWebhook), activepiecesController.deleteWebhook);
routes.post('/dsr/register-webhook', reqValidator(dsrSchema.registerWebhook), activepiecesController.addWebhook);
routes.post('/dsr/send-email', activepiecesController.sendEmail);
routes.post('/dsr/auto-verify-request', activepiecesController.autoVerifyRequest);
routes.post('/dsr/auto-approve-dsr', activepiecesController.autoApprovedDsrAndTask);
routes.post('/dsr/identity-verify-with-db-http', activepiecesController.identityVerifyWithDbHttp);
routes.post('/dsr/identity-verify-with-db-http-v1', activepiecesController.identityVerifyWithDbHttpV1);
routes.post('/dsr/update-automation-task-http', activepiecesController.updateAutomationTask);
routes.post('/dsr/data-eraser-with-db-http', activepiecesController.dataEraserWithDbHttp);
routes.post('/dsr/ocr-verification-for-http', activepiecesController.ocrVerificationForHttp);
routes.post('/dsr/ocr-verification', activepiecesController.ocrVerification);

//request guest
routes.get('/dsr/guest-request', verifyGuestToken, dsrController.getAllGuestrequest);
routes.get('/dsr/guest-request/:id', verifyGuestToken, dsrController.getGuestRequestById);
routes.get('/dsr/guest-request/allinfo/:id', verifyGuestToken, dsrController.getAllInfoByIdForGuest);
routes.post('/dsr/guest-request', limiter, reqValidator(dsrSchema.createGuestRequest), dsrController.createGuestRequest);
routes.post('/dsr/guest-form-request', limiter, reqValidator(dsrSchema.createFormGuestRequest), dsrController.createGuestFormRequest);
routes.post('/dsr/guest-form-request-temp', limiter, reqValidator(dsrSchema.createFormGuestRequest), dsrController.createGuestFormRequestTemp);
routes.post('/dsr/guest-upload-documents', imageUpload.array('files', 10), uploadMultipleDocuments, dsrController.uploadDsrDocuments);
routes.post('/dsr/request/send-guestside-mail', limiter, verifyGuestToken, reqValidator(dsrSchema.sendGuestSideMail), dsrController.send_guestside_mail);
routes.get('/dsr/guest-request/form-view/:request_id', verifyGuestToken, dsrController.viewSubmitForm);

//request
routes.get('/dsr/send-reminder-email-for-request', dsrController.sendReminderForRequestAssigneeInDSR);
routes.get('/dsr/request', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getAllRequestList);
routes.patch('/dsr/request/archive-unarchive/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.archiveAndUnarchiveRequest);
routes.get('/dsr/tas-request', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getTaskRequestList);
routes.get('/dsr/request/export', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.exportAllRequestList);
routes.get('/dsr/request/assignee', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getAllAssigneeList);
routes.get('/dsr/request/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getRequestById);
routes.get('/dsr/request/mail-activity/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getRequestMailActivityById);
routes.get('/dsr/request/approved/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getApprovedReqestById);
routes.get('/dsr/request/allinfo/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getAllInfoById);
routes.post('/dsr/request', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.createRequest), dsrController.createRequest);
routes.post('/dsr/upload-documents', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 10), uploadMultipleDocuments, dsrController.uploadDsrDocuments);
routes.patch('/dsr/request/assign/:dsr_request_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.assignRequest), dsrController.assignRequest);
//dsr AUdit
routes.get('/dsr/request/step-audit/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getStepWiseAuditLog);
routes.get('/dsr/request/audit/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getAuditLog);
routes.get('/dsr/request/download-audit/:req_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.downloadAuditLog);
//dsr dahsboard
routes.get('/dsr-dashboard/count', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countRequest);
routes.get('/dsr-dashboard/request', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countRequestByMonthOrYear);
routes.get('/dsr-dashboard/types', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countByType);
routes.get('/dsr-dashboard/pending', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countByStatusPending);
routes.get('/dsr-dashboard/stage', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countByWorkflowStage);
routes.get('/dsr-dashboard/owners', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countByOwners);
routes.get('/dsr-dashboard/residency', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrDashboardController.countByResidency);
//DSR form Repo
routes.post('/dsr/form/upload-logo/', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, ropaController.uploadDocument);
routes.get('/dsr/form/template', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.getForm);
routes.post('/dsr/form/create', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.createForm), dsrFormRepoController.customForm);
// DSR Email tempolate
routes.get('/dsr/mail/template/:customer_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrMailTemplateController.mailListing);
routes.post('/dsr/mail/create', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.createMail), dsrMailTemplateController.createMail);
routes.patch('/dsr/mail/update/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.createMail), dsrMailTemplateController.updateMail);
routes.delete('/dsr/mail/template/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrMailTemplateController.deleteMail);

routes.patch('/dsr/request/approved-reject/:dsr_request_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.approvedReject), dsrController.approvedRejectRequest);
routes.post('/dsr/request/task/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.addTaskDuringOverview), dsrController.createTask);
routes.post('/dsr/request/task/documents', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 10), uploadMultipleDocuments, dsrController.uploadTaskDocuments);
routes.delete('/dsr/request/task/delete-documents', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.deleteDocument), deleteDocuments, dsrController.deleteTaskDocuments);
routes.patch('/dsr/request/task/:task_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateTaskDuringOverview), dsrController.updateTask);
routes.patch('/dsr/request/:dsr_request_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateRequest), dsrController.updateRequest);
routes.get('/dsr/departments', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.getAllDepartments);
//routes.post('/dsr/request/send-guestside-mail', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.sendGuestSideMail), dsrController.send_guestside_mail);
routes.post('/dsr/request/send-assigneeside-mail', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.sendAssigneeSideMail), dsrController.send_assigneeside_mail);

routes.post('/dsr/request/create-form', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.createFormV2), dsrFormRepoController.createForm);
// routes.get('/dsr/form/categories/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.getCategories );
routes.get('/dsr/form/controls/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.getControls);
routes.get('/dsr/form/template-list', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.getFormv2);
// routes.post('/dsr/request/category/:id', keycloak.protect(), verifyAuthToken,checkRoleAccess, reqValidator(dsrSchema.createCategory),dsrFormRepoController.addCategory);
routes.post('/dsr/request/controls', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.addCustomerControls), dsrFormRepoController.addCustomControls);
routes.delete('/dsr/request/controls/:question_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.deleteCustomControls);
routes.put('/dsr/request/controls/:question_id', reqValidator(dsrSchema.updateCustomControls), keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.updateCustomControls);
routes.delete('/dsr/request/delete-form/:form_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.deleteForm);
// routes.get('/dsr/form/getcontrols/:form_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.getPredefinedControls );
routes.patch('/dsr/request-form/updateFieldOrder/:form_id', dsrFormRepoController.updateFieldOrder);
routes.get('/dsr/request-form/:customer_id/:form_id', dsrFormRepoController.getPublishedForm);
routes.patch('/dsr/form/publish/:form_id', reqValidator(dsrSchema.publishForm), keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrFormRepoController.publishCustomForm);
routes.post('/dsr/request/create-request', dsrController.createRequestV2);
routes.post('/dsr/request/resend-otp', dsrController.dsrFormResendOtp);
routes.patch('/dsr/request/form-content/:form_id', keycloak.protect(), reqValidator(dsrSchema.updateFormContent), verifyAuthToken, checkRoleAccess, dsrFormRepoController.updateFormContent);
routes.post('/dsr/request/verify-otp/:request_id/:form_id', reqValidator(dsrSchema.verifyOtpV2), authController.verifyotpDsrForm);
routes.patch('/dsr/request/check-verification/:request_id/:form_id', authController.checkVerification);
routes.get('/dsr/request/form-view/:request_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.viewSubmitForm);
routes.patch('/dsr/request/mail-read/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dsrController.markMessageRead);
//Data Breach Management.
routes.post('/data-breach-management/create-incident', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.createIncident), dataBreachManagement.createIncident);
routes.get('/data-breach-management', keycloak.protect(), verifyAuthToken, checkRoleAccess, dataBreachManagement.getAllDBMList);
routes.get('/data-breach-management/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dataBreachManagement.getBreachById);
routes.get('/data-breach-dashboard/count', keycloak.protect(), verifyAuthToken, checkRoleAccess, dataBreachManagement.getBreachCount);
routes.get('/data-breach-dashboard/status-distribution', keycloak.protect(), verifyAuthToken, checkRoleAccess, dataBreachManagement.getBreachStatusDistribution);
routes.get('/data-breach-dashboard/severity-distribution', keycloak.protect(), verifyAuthToken, checkRoleAccess, dataBreachManagement.getSeverityDistribution);
routes.post('/data-breach-management/upload-documents', keycloak.protect(), verifyAuthToken, checkRoleAccess, imageUpload.array('files', 10), uploadMultipleDocuments, dataBreachManagement.uploadDocuments);
routes.post('/data-breach-management/add-action', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.addAction), dataBreachManagement.addAction);
routes.patch('/data-breach-management/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.updateIncident), dataBreachManagement.updateIncident);
routes.post('/data-breach-management/add-data-breach-notification', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(dsrSchema.addDataBreachNotification), dataBreachManagement.addDataBreachNotification);
routes.get('/data-breach-management/action/list/:data_breach_id', keycloak.protect(), verifyAuthToken, checkRoleAccess, dataBreachManagement.getActionListing);

// UCF or Privacy Ops
routes.get('/privacy-ops/repo/ropa/count', keycloak.protect(), verifyAuthToken, privacyOpsRopaController.countRopa);
routes.get('/privacy-ops/repo/ropa', keycloak.protect(), verifyAuthToken, privacyOpsRopaController.ROPAList);
// routes.get('/privacy-ops/repo/ropa/view/:ropa_id', keycloak.protect(), verifyAuthToken, privacyOpsRopaController.viewRopa );

routes.get('/privacy-ops/repo/assessment/count', keycloak.protect(), verifyAuthToken, privacyOpsAssessmentController.countAssessment);
routes.get('/privacy-ops/repo/assessment', keycloak.protect(), verifyAuthToken, privacyOpsAssessmentController.assessmentsList);
// routes.get('/privacy-ops/repo/assessment/view/:ropa_id', keycloak.protect(), verifyAuthToken, privacyOpsRopaController.viewRopa );

routes.get('/privacy-ops/repo/document/count', keycloak.protect(), verifyAuthToken, privacyOpsDocumentController.getCount);
routes.get('/privacy-ops/repo/document', keycloak.protect(), verifyAuthToken, privacyOpsDocumentController.getList);
//--------Regulations
// routes.post('/privacy-ops/regulations/upload-controls', imageUpload.array('files', 1), privacyOpsRegulationController.uploadControls);
routes.get('/regulations/list', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.regulationList);
routes.post('/regulations', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.createRegulation), privacyOpsRegulationController.createRegulation);
routes.patch('/regulations/complai', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.customRegulation), privacyOpsRegulationController.complaiRegulationAndBusinessReq);
routes.get('/privacy-ops/regulations/list', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.customerRegulationList);
// routes.get('/privacy-ops/regulations/categories/:regulation_id', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.getControls);
routes.get('/privacy-ops/regulations/controls', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.getControls);
routes.get('/privacy-ops/regulations/busi-req/:control_id', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.getBusinessRequirements);
routes.get('/privacy-ops/regulations/documents/:regulation_id', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.allDocs);
routes.patch('/privacy-ops/regulations/busi-req/:id', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.CustomerBusinessRequirements), privacyOpsRegulationController.updateCustomRequirement);
routes.get('/privacy-ops/regulations/compliance-status/:group_id', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.complianceStatusPerControlPerRegulation);
routes.get('/privacy-ops/regulations/categories', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.getCategories);
routes.get('/privacy-ops/regulations/control-busi-req/:control_id', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.getControlWiseBusiReq);
routes.patch('/privacy-ops/busi-req/articles', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.updateBusiReqArticles), privacyOpsRegulationController.updateBusiReqArticles);
routes.get('/privacy-ops/custom-regulations', keycloak.protect(), verifyAuthToken, privacyOpsRegulationController.getCustomerRegulations);
routes.post('/privacy-ops/regulations/category-control-busi-req/', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.addCategoryControlBusiReq), privacyOpsRegulationController.addCategoryControlBusiReq);
// routes.post('/privacy-ops/authoritative/upload-authoritative',  imageUpload.array('files', 1), privacyOpsRegulationController.uploadAuthoritative);

//-------privacyops actions
routes.post('/privacy-ops/activities/action', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.addAction), privacyOpsActionController.addAction);
routes.get('/privacy-ops/activities/action-list', keycloak.protect(), verifyAuthToken, privacyOpsActionController.listAction);
routes.get('/privacy-ops/activities/action/:id', keycloak.protect(), verifyAuthToken, privacyOpsActionController.detailAction);
routes.get('/privacy-ops/activities/action-count', keycloak.protect(), verifyAuthToken, privacyOpsActionController.countActions);
routes.post('/privacy-ops/activities/upload-documents', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 10), uploadMultipleDocuments, privacyOpsActionController.uploadDocument);

//------------privacyops duties
routes.post('/privacy-ops/activities/duty', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.createDuty), privacyOpsDutyController.createDuty);
routes.patch('/privacy-ops/activities/duty/:id', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.updateDuty), privacyOpsDutyController.updateDuty);
routes.get('/privacy-ops/activities/duty-count', keycloak.protect(), verifyAuthToken, privacyOpsDutyController.countDuties);
routes.get('/privacy-ops/activities/duty/:id', keycloak.protect(), verifyAuthToken, privacyOpsDutyController.detailDuty);
routes.get('/privacy-ops/activities/list-duty', keycloak.protect(), verifyAuthToken, privacyOpsDutyController.listduties);
//-----------privacyops improvement
routes.post('/privacy-ops/activities/improvement', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.createImprovement), privacyOpsImprovementController.createImprovement);
routes.patch('/privacy-ops/activities/improvement/:id', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.updateImprovement), privacyOpsImprovementController.updateImprovement);
routes.get('/privacy-ops/activities/improvement/:id', keycloak.protect(), verifyAuthToken, privacyOpsImprovementController.detailImprovement);
routes.get('/privacy-ops/activities/list-improvement', keycloak.protect(), verifyAuthToken, privacyOpsImprovementController.listImprovement);
routes.get('/privacy-ops/activities/improvement-count', keycloak.protect(), verifyAuthToken, privacyOpsImprovementController.countImprovement);

//------------------privacyops risk
routes.post('/privacy-ops/risk', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.createRisk), privacyOpsRiskController.createRisk);
routes.patch('/privacy-ops/risk/:risk_id', keycloak.protect(), verifyAuthToken, reqValidator(privacyOpsSchema.updateRisk), privacyOpsRiskController.updateRisk);
routes.get('/privacy-ops/risk/:risk_id', keycloak.protect(), verifyAuthToken, privacyOpsRiskController.detailRisk);
routes.get('/privacy-ops/risk-list', keycloak.protect(), verifyAuthToken, privacyOpsRiskController.riskList);

//------------>>privacyops Dashboard

routes.get('/privacy-ops/dashboard/risk-stage', keycloak.protect(), verifyAuthToken, privacyOpsDashboard.getRiskByStage);
routes.get('/privacy-ops/dashboard/risk-category', keycloak.protect(), verifyAuthToken, privacyOpsDashboard.getRiskByCategory);
routes.get('/privacy-ops/dashboard/risk-modules', keycloak.protect(), verifyAuthToken, privacyOpsDashboard.getRiskByModule);
routes.get('/privacy-ops/dashboard/complaince-status', keycloak.protect(), verifyAuthToken, privacyOpsDashboard.getRegulationComplianceBreakdown);

// Department
routes.post('/departments', keycloak.protect(), verifyAuthToken, reqValidator(schema.department), departmentController.createDepartments);
routes.patch('/departments/:department_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateDepartment), departmentController.updateDepartment);
routes.get('/departments/:id', keycloak.protect(), verifyAuthToken, departmentController.departmentList);
routes.delete('/departments/:department_id', keycloak.protect(), verifyAuthToken, departmentController.deleteDepartment);

// Processes
routes.post('/processes', keycloak.protect(), verifyAuthToken, reqValidator(schema.process), processController.createProcesses);
routes.patch('/processes/:process_id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateProcess), processController.updateProcess);
routes.get('/processes/list/:department_id', keycloak.protect(), verifyAuthToken, processController.processList);
routes.delete('/processes/:process_id', keycloak.protect(), verifyAuthToken, processController.deleteProcess);

// core engine

//Project
routes.post('/generate-token', reqValidator(schema.createClientSchema), projectController.createClient);
routes.get('/about-us/:client_name', projectController.aboutUs);

routes.post('/validate-user', reqValidator(schema.validateUser), keycloak.protect(), verifyAuthToken, checkRoleAccess, authController.validateUser);

// generate access Token
routes.post('/generate-access-token', reqValidator(schema.generateAccessToken), authController.generateAccessToken);

// Auth
routes.get('/login', reqValidator(schema.getLogin, 'query'), authController.getLogin);
// routes.post('/login', reqValidator(schema.login), authController.login);
routes.post('/login', keycloak.protect(), verifyAuthToken, authController.login);
// routes.post('/login', reqValidator(schema.login), authController.login);
routes.post('/check-email', authController.checkEmail);
routes.get('/sign-up', reqValidator(schema.getSignup, 'query'), authController.getSignup);
routes.post('/sign-up', keycloak.protect(), verifyUserToken, reqValidator(schema.signUp), authController.signup);
routes.get('/verify-otp', reqValidator(schema.getPage, 'query'), authController.getVerifyOtp);
routes.post('/verify-otp', reqValidator(schema.verifyOtp), authController.verifyOtp);
routes.get('/forgot-password', reqValidator(schema.getPage, 'query'), authController.getForgotPassword);
routes.post('/forgot-password', reqValidator(schema.forgotPassword), authController.forgotPassword);
routes.get('/reset-password', reqValidator(schema.getPage, 'query'), authController.getResetPassword);
routes.post('/reset-password', reqValidator(schema.resetPassword), authController.resetPassword);
routes.get('/change-password', reqValidator(schema.getPage, 'query'), authController.getChangePassword);
routes.post('/change-password', keycloak.protect(), verifyAuthToken, reqValidator(schema.changePassword), authController.changePassword);
routes.post('/resend-credentials', keycloak.protect(), verifyAuthToken, reqValidator(schema.resendCred), authController.resendCredentials);

//guest
routes.post('/guest/send-otp', reqValidator(dsrSchema.guestSendOtp), authController.guestSendOtp);
routes.post('/guest/verify-otp', reqValidator(dsrSchema.verifyOtp), authController.guestVerifyOtp);

//User Profile
routes.get('/profile/:id', keycloak.protect(), verifyAuthToken, userController.profile);
routes.patch('/profile/:id', keycloak.protect(), verifyAuthToken, reqValidator(schema.updateProfile), userController.updateProfile);
// upload document to s3
routes.post('/upload-profile/:id', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), uploadDocuments, userController.uploadProfile);

routes.post('/auth/logout', authController.logoutV2);
routes.get('/state', onboardingController.getState);
routes.get('/country', onboardingController.getCountry);
routes.get('/language', onboardingController.getLanguage);

//Onboarding
routes.patch('/customer/:id', keycloak.protect(), verifyUserToken, reqValidator(schema.createOrganisation), dashboardController.updateOrganisation);
routes.patch('/update-password', keycloak.protect(), verifyUserToken, reqValidator(schema.updatePassword), dashboardController.updatePassword);
routes.post('/onboarding', keycloak.protect(), verifyAuthToken, reqValidator(schema.onboarding), onboardingController.onboarding);

// Dashboard
routes.get('/services', keycloak.protect(), verifyAuthToken, dashboardController.getServices);
routes.get('/ambitions', keycloak.protect(), verifyAuthToken, dashboardController.getAmbitions);
routes.get('/questionnaies', keycloak.protect(), verifyAuthToken, reqValidator(schema.questionnaies, 'query'), dashboardController.getQuestionnaires);
routes.get('/packages', keycloak.protect(), verifyAuthToken, dashboardController.getPackages);
routes.get('/packages/:id', keycloak.protect(), verifyAuthToken, dashboardController.getPackagesDetails);

// Customer
routes.post('/customer/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createCustomer), authController.signup);
routes.get('/customer/list', keycloak.protect(), verifyAuthToken, customerController.getCustomerList);
routes.get('/customer/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, customerController.getCustomerDetail);
routes.put('/customer/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.updateCustomer), customerController.updateCustomer);

// User
routes.post('/user/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createUser), userController.createUser);
routes.get('/user/list', keycloak.protect(), verifyAuthToken, userController.getUserList);
routes.get('/user/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, userController.getUserDetail);
routes.put('/user/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.updateUser), userController.updateUser);

// Role
routes.get('/role/list', keycloak.protect(), verifyAuthToken, reqValidator(schema.getUserList, 'query'), roleController.getOrgRoles);
routes.get('/role/user', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.getRoleUsers, 'query'), roleController.getRoleUsers);
routes.get('/role/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, roleController.getRoleDetails);
routes.put('/role/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.updateRole), roleController.updateRole);
routes.post('/role/', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createRole), roleController.createRole);
routes.post('/role/assign', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.assignRole), roleController.assignRole);

// Group
// routes.get('/groups', keycloak.protect(), verifyAuthToken, reqValidator(schema.getUserList, 'query'), groupController.getOrgGroup);

// company structure
routes.get('/groups', keycloak.protect(), verifyAuthToken, groupController.getOrgGroup);
routes.post('/groups', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createGroup), groupController.createGroup);
routes.patch('/groups/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.editGroup), groupController.editGroup);
routes.post('/business-units', keycloak.protect(), verifyAuthToken, reqValidator(schema.businessUnit), groupController.createBusinessUnit);
routes.delete('/business-units/:id', keycloak.protect(), verifyAuthToken, groupController.deleteBusinessUnit);
routes.get('/groups/company-structure', keycloak.protect(), verifyAuthToken, groupController.getCompanyStructure);
routes.get('/entities/:id', keycloak.protect(), verifyAuthToken, groupController.getEntity);

// Resources
routes.get('/resources', keycloak.protect(), verifyAuthToken, resourceController.resourcesList);
routes.post('/resources', keycloak.protect(), verifyAuthToken, reqValidator(schema.addResource), resourceController.addResource);

// Resources
routes.get('/resources/user', keycloak.protect(), verifyAuthToken, resourceController.userResources);

// Sidebar
routes.get('/sidebar', keycloak.protect(), verifyAuthToken, dashboardController.getSidebar);

// Support System
routes.post('/ticket', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 1), reqValidator(schema.createTicket), supportController.createTicket);
routes.get('/ticket/to-do', keycloak.protect(), verifyAuthToken, supportController.toDoTickets);
routes.get('/ticket/:id', keycloak.protect(), verifyAuthToken, supportController.ticketDetails);
routes.patch('/ticket/:id', keycloak.protect(), verifyAuthToken, imageUpload.array('files', 10), reqValidator(schema.updateTicket), supportController.updateTicket);
routes.get('/tickets/:id', keycloak.protect(), verifyAuthToken, supportController.ticketListing);
// Ticket Dashboard
routes.get('/ticket-dashboard', keycloak.protect(), verifyAuthToken, supportController.dashboard);
routes.get('/tickets-priority/:priority', keycloak.protect(), verifyAuthToken, supportController.priorityWiseTickets);
//Ticket Comments
routes.post('/ticket/comment', keycloak.protect(), verifyAuthToken, reqValidator(schema.createComment), supportController.comment);
//Ticket History
routes.get('/ticket/history/:ticket_id', keycloak.protect(), verifyAuthToken, supportController.ticketHistory);

// click house
routes.get('/unstructured-catalogue', keycloak.protect(), verifyAuthToken, clickHouseController.unstructuredCatalogue);
routes.get('/service-entity', keycloak.protect(), verifyAuthToken, clickHouseController.getDatabaseServiceEntity);
routes.get('/schema-entity', keycloak.protect(), verifyAuthToken, clickHouseController.getDatabaseSchemaEntity);
routes.get('/table-entity', keycloak.protect(), verifyAuthToken, clickHouseController.getTableEntity);
routes.get('/profile-time-series', keycloak.protect(), verifyAuthToken, clickHouseController.getProfileTimeSeries);
routes.get('/column-ner', keycloak.protect(), verifyAuthToken, clickHouseController.getColumnNer);
routes.get('/unstructured-dashboard-data-management', keycloak.protect(), verifyAuthToken, clickHouseController.getDataManagement);
routes.get('/unstructured-dashboard-sanky-graph', keycloak.protect(), verifyAuthToken, clickHouseController.getSankyGraph);
routes.get('/unstructured-dashboard-document', keycloak.protect(), verifyAuthToken, clickHouseController.getDocumentList);
routes.get('/unstructured-dashboard-location', keycloak.protect(), verifyAuthToken, clickHouseController.getLocation);
routes.get('/unstructured-dashboard-file-format', keycloak.protect(), verifyAuthToken, clickHouseController.getFileFormat);
routes.get('/unstructured-dashboard-summary', keycloak.protect(), verifyAuthToken, clickHouseController.getViewSummary);
// click house structured api
routes.get('/structured-catalogue', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredData);
routes.get('/structured-dashboard-view-details', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredViewDetails);
routes.get('/structured-dashboard-element-categories', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredElementCategories);
routes.get('/structured-dashboard-data-management', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredViewDetailsManagement);
routes.get('/structured-dashboard-element-types', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredElementTypes);
routes.get('/structured-dashboard-view-location', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredViewLocation);
routes.get('/structured-dashboard-sensitivity', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredSensitivity);

routes.get('/structured-dashboard-sanky-graph', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredShankyGraph);
routes.get('/structured-dashboard-location', keycloak.protect(), verifyAuthToken, clickHouseController.getStructuredLocation);
routes.get('/unstructured-dashboard-view', keycloak.protect(), verifyAuthToken, clickHouseController.getUnstructuredViewDetails);
routes.get('/unstructured-dashboard-element', keycloak.protect(), verifyAuthToken, clickHouseController.getUnstructuredElements);
routes.get('/unstructured-dashboard-sensitivity', keycloak.protect(), verifyAuthToken, clickHouseController.getUnStructuredSensitivity);
routes.get('/structured-unstructured-data-element', keycloak.protect(), verifyAuthToken, clickHouseController.getUniqueDataType);

routes.get('/profiler-meta', keycloak.protect(), verifyAuthToken, clickHouseController.getProfilerMeta);

routes.put('/column-ner', keycloak.protect(), verifyAuthToken, clickHouseController.updateColumnNer);
routes.put('/unstructured-data', keycloak.protect(), verifyAuthToken, clickHouseController.updateNerUnstructuredData);

routes.get('/data-element', keycloak.protect(), verifyAuthToken, clickHouseController.getDataElement);
routes.post('/data-element', keycloak.protect(), verifyAuthToken, reqValidator(schema.createDataElement), clickHouseController.createDataElement);
routes.put('/data-element', reqValidator(schema.updateDataElement), clickHouseController.updateDataElement);
routes.delete('/data-element', keycloak.protect(), verifyAuthToken, reqValidator(schema.deleteDataElement, 'query'), clickHouseController.deleteDataElement);

// Table
routes.post('/table', reqValidator(schema.getTable), tableController.getTable);

//Invoices
routes.get('/invoices', keycloak.protect(), verifyAuthToken, invoiceController.getInvoices);
routes.get('/invoices/:id', keycloak.protect(), verifyAuthToken, invoiceController.invoice);

// Blogs
routes.get('/blogs', homePageController.getBlogs);
routes.get('/blogs/categories', homePageController.getBlogsCategories);
routes.get('/blogs/:blogId', homePageController.getBlog);

routes.get('/grafana-dashboard-list', keycloak.protect(), verifyAuthToken, grafanaDashboardController.listDashboard);
routes.post('/grafana-dashboard', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.createGrafanaDashboard), grafanaDashboardController.createCustomDashboard);
routes.patch('/grafana-dashboard/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, reqValidator(schema.updateGrafanaDashboard), grafanaDashboardController.updateDashboard);
routes.delete('/grafana-dashboard/:id', keycloak.protect(), verifyAuthToken, checkRoleAccess, grafanaDashboardController.deleteDashboard);
routes.get('/grafana-details', keycloak.protect(), verifyAuthToken, grafanaDashboardController.getdetails);
module.exports = routes;
