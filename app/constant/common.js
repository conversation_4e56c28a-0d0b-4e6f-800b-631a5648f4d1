const order = { ASC: 1, DESC: -1 };

const orderingKeys = {
  ASC: 'ASC',
  DESC: 'DESC'
};

const CLIENT_ONBOARD = {
  client: 'customer'
}



const MESSAGE = {
  FORGOT_PASSWORD_SUBJECT: "Hello GoTrust - Reset Password Request",
  VERIFICATION_CODE: "GoTrust Verification Code:",
  EMAIL_VERIFY:"Verify your email for GoTrust",
  WELCOME: "Welcome to GoTrust",
  GEOTAB_SUPPORT: "Geotab Support",
  ENTERPRISE_QUERY: "Enterprise Query",
  DRIVER_APP_CREDS: "Driver App Credentials",
  ONBOARDED_SUCCESSFULL: "Onboarded Successfully",
  SETUP: "Welcome to GoTrust! Let's Set-Up Your Account"
}

const USER_ROLE = {
  0: "Driver",
  1: "User",
  2: "Data Protection Officer",
  3: "Super Admin",
  4: "Admin",
  5: "Manager",
  6: "Broker",
  7: "Org Admin",
  8: "Basic"
};

const ONBOARDING_STATUS = {
  'USER_CREATED': 'USER_CREATED',
  'OR<PERSON><PERSON>SATION_UPDATED': 'ORGANISATION_UPDATED',
  'EMAIL_VERIFIED': 'EMAIL_VERIFIED',
  'PASSWORD_UPDATED': 'PASSWORD_UPDATED',
  'ONBOARDING_COMPLETED': 'ONBOARDING_COMPLETED',
  'QUESTIONNAIRES_COMPLETED': 'QUESTIONNAIRES_COMPLETED'
}

const DEVICE_TYPE = {
  MPIN_ANDROID: 'MPIN_ANDROID',
  MPIN_IOS: 'MPIN_IOS',
  WEB: 'WEB',
  IOS: 'IOS',
  ANDRIOD: 'ANDROID',
}

const PORTALS = {
  ZEGO: "ZEGO",
  HM: "HM",
  GRIBB: "GRIBB",
};

const LANGAUGE = {
  nl: "nl",
  en: "en",
};




module.exports = { order, orderingKeys, CLIENT_ONBOARD, USER_ROLE, MESSAGE, DEVICE_TYPE ,ONBOARDING_STATUS, PORTALS, LANGAUGE };
