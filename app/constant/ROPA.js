const status = {
    YET_TO_START: 'Yet to Start',
    STARTED: 'Started',
    UNDER_REVIEW: 'Under Review',
    CHANGES_REQUESTED: 'Change Request',
    COMPLETED: 'Completed'
};

const question = {
    "PERSONAL_DATA": 37,
    "SENSITIVE_DATA": 38,
    "DATA_SUBJECTS_CATEGORIES": 2,
    "CHILDREN_DATA_COLLECTION": 3,
    "THIRD_PARTY_VENDORS": 35,
    "CROSS_BORDER_DATA_TRANSFER": 11,
    "LAWFUL_BASIS": 6,
    "DETAILS_OF_THIRD_PARTY": 36,
    "BASIC_INFO_ORG_ROLE": 4
}

const basicInfoDept = {
    "DEPARTMENT_NAME": 1,
    "ENTITY_NAME": 3,
    "DEPARTMENT_HEAD": 5,
    "SPOC": 6,
    "SUBFUNCTION": 7,
    "TENTATIVE_DATE":8
}

const basicInfoProc = {
    "PROCESS_NAME": 2,
    "DEPARTMENT_NAME": 7,
    "DEPARTMENT_HEAD": 10,
    "SPOC": 11,
    "SUBPROCESS": 12
}

const excelQues = {
    "PERSONAL_DATA": "Personally Identifiable Information",
    "SENSITIVE_DATA": "Sensitive Personally Identifiable Information",
    "LAWFUL_BASIS": "What is the lawful basis of the collection of different categories of personal data?",
    "PURPOSE": "What is the purpose of the collection of different categories of personal data?",
    "CATEGORY_OF_DATA": "PLACEHOLDER",
    "MODE_OF_COLLECTION": "How is personal data collected by your department?",
    "PRIVACY_NOTICE": "Is this Data Collection specified in the Privacy Notice",
    "CROSS_BORDER_DATA_TRANSFER": "Is there any cross border data transfer involved?",
    "COUNTRY_NAMES": "PLACEHOLDER",
    "FREQUENCY": "PLACEHOLDER",
    "DATA_PROTECTION_AGREEEMENT": "PLACEHOLDER",
    "PURPOSE_OF_TRANSFER": "PLACEHOLDER",
    "RETENTION_PERIOD_DEFINED": "Is there a retention period defined for physical and electronic documents processed by your department? ",
    "RETENTION_PERIOD": "For how long is the Personal Data stored ?",
    "DATA_DELETED": "PLACEHOLDER",
    "DATA_DESTRUCTION": "What is the data destruction mechanism for physical and digital data after the records reach the end of their defined retention period? ",
    "DATA_SECURITY": "What security measures are in place to protect the data collected/ processed?"
}

module.exports = {
    status,
    question,
    basicInfoDept,
    basicInfoProc,
    excelQues
};
