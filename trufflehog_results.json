{"level":"info-0","ts":"2025-03-20T08:24:27Z","logger":"trufflehog","msg":"running source","source_manager_worker_id":"3CkkY","with_units":true}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/19/8a761b0214274f2661fcaf981e2a64cc9f4493","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/02/efb2c7c33cebe0c26d3c3f44b2d2909cec24d4","line":11}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/16/8ff2f45d876217547e2593cf489baf6764b2be","line":11}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/90/0f99c330a0b1c7dbf74654ff387c81664b9509","line":12}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/7c/4bf43f2176628b105e0431d1b0ca30607d1bf4","line":11}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/8d/98ccd98be2785afe0b1bfc0e9fce5645b016fa","line":12}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/97/fe5fac11bc0b7f42a809e1f02f970d155b60c2","line":11}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/98/8f489d896aed0b262fb044c15c121361cd59ea","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/e8/69f6e8264665868a4f93aab0f2aa27e914f228","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/c7/6daff505990da370320b1d0b59bb298d75cd34","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/f0/7b36c9fd3af2f5e6e2d1a7cde1c96601be99af","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/d2/9b18cfae10e4ef1fbcc1369e16912a5ac85f33","line":16}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/c8/7e9dab9b69182a9595298587a7e42834c3e3dc","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/f3/de676757c7650e1981ee65ea89ae2ad3b475ba","line":11}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/.git/objects/ce/8ba54d86a34669d5d7381456d618fbb5fdeed8","line":12}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":false,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","arn":"arn:aws:iam::************:user/ecr-user","resource_type":"Access key","rotation_guide":"https://howtorotate.com/docs/tutorials/aws/","user_id":"AIDA5FTY7IJ3F5XMMFMZB"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":51}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":221}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":123}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":51}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js","line":61}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":221}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":51}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js","line":61}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js","line":61}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":221}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":131}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":51}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":51}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js","line":61}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":79}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":221}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js","line":61}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":189}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js","line":143}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":189}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":189}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":123}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":230}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.d.ts","line":137}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js","line":163}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":136}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js","line":221}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":150}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":256}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/trace/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":230}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":256}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":218}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":218}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":336}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":34}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":34}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":336}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js","line":129}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js","line":129}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":214}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":214}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":240}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":240}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.d.ts","line":110}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":24}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":240}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":190}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":189}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.d.ts","line":110}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":24}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":24}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":102}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":31}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":200}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":185}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":30}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":256}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":123}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":178}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":170}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":178}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.d.ts","line":177}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":230}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.d.ts","line":177}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js","line":198}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":256}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":230}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":29}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":178}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.d.ts","line":177}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":314}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":123}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":192}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/stable_attributes.js","line":191}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":216}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":339}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/stable_attributes.js","line":338}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js","line":18}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":154}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.d.ts","line":132}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":244}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.d.ts","line":132}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.d.ts","line":132}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/src/experimental_attributes.js","line":31}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@opentelemetry/semantic-conventions/build/esnext/experimental_attributes.js.map","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":114}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"88f9148881ede94ae9dcbf4e1980aa69","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":73}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"0b582e127dd7cf669de16ec36f8056a4","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":92}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"eed33350cbb763726335ef1df74a6591","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":97}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"bf8fa69d15ae8416d534e3025a16d87d","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":96}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"0b51bed24e0842f99744dcf5d79346a6","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@octokit/openapi-types/types.d.ts","line":445}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":924,"DetectorName":"GitHubOauth2","DetectorDescription":"GitHub OAuth2 credentials are used to authenticate and authorize applications to access GitHub's API on behalf of a user or organization. These credentials include a client ID and client secret, which can be used to obtain access tokens for accessing GitHub resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"aa5a315d61ae9438b18d","RawV2":"aa5a315d61ae9438b18d57a7f021a713b1c5a6a199b54cc514735d2d462f","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@octokit/openapi-types/types.d.ts","line":417}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":924,"DetectorName":"GitHubOauth2","DetectorDescription":"GitHub OAuth2 credentials are used to authenticate and authorize applications to access GitHub's API on behalf of a user or organization. These credentials include a client ID and client secret, which can be used to obtain access tokens for accessing GitHub resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"a6db0bec360bb87e9418","RawV2":"a6db0bec360bb87e941857a7f021a713b1c5a6a199b54cc514735d2d462f","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@octokit/openapi-types/types.d.ts","line":97}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":924,"DetectorName":"GitHubOauth2","DetectorDescription":"GitHub OAuth2 credentials are used to authenticate and authorize applications to access GitHub's API on behalf of a user or organization. These credentials include a client ID and client secret, which can be used to obtain access tokens for accessing GitHub resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"aa5a315d61ae9438b18d","RawV2":"aa5a315d61ae9438b18d57a7f021a713b1c5a6a199b54cc514735d2d462f","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@octokit/openapi-types/types.d.ts","line":69}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":924,"DetectorName":"GitHubOauth2","DetectorDescription":"GitHub OAuth2 credentials are used to authenticate and authorize applications to access GitHub's API on behalf of a user or organization. These credentials include a client ID and client secret, which can be used to obtain access tokens for accessing GitHub resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"a6db0bec360bb87e9418","RawV2":"a6db0bec360bb87e941857a7f021a713b1c5a6a199b54cc514735d2d462f","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":87}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"2cb7a40d253621fdfa96f23b96e42ecb","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":130}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":575,"DetectorName":"PostageApp","DetectorDescription":"PostageApp is a service for sending emails via their API. The API keys can be used to send emails and access account information.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"f5735237f7e6f0b467770e28e84c56db","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":575,"DetectorName":"PostageApp","DetectorDescription":"PostageApp is a service for sending emails via their API. The API keys can be used to send emails and access account information.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"f5735237f7e6f0b467770e28e84c56db","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":23}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":455,"DetectorName":"Flickr","DetectorDescription":"Flickr is an image and video hosting service. Flickr API keys can be used to access and modify user data and perform various operations within the Flickr ecosystem.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"3c34c3ac904b6d6f26182807fbb95c5e","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":21}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":455,"DetectorName":"Flickr","DetectorDescription":"Flickr is an image and video hosting service. Flickr API keys can be used to access and modify user data and perform various operations within the Flickr ecosystem.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"d3b2b610171589db68809c3ec3bf2bcb","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":22}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":455,"DetectorName":"Flickr","DetectorDescription":"Flickr is an image and video hosting service. Flickr API keys can be used to access and modify user data and perform various operations within the Flickr ecosystem.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"66c8e8a00ad0a906f632ff99cf490163","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/http.d.ts","line":306}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://abc:<EMAIL>","RawV2":"http://abc:<EMAIL>","Redacted":"http://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":56}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"60c49ff8574d5a47616796ad991463ad","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":53}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"5c920e9829764cbf904b9a59474c1672","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":54}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"12ffe24dcc1478ea0008c60c4ef7118f","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":55}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":1002,"DetectorName":"Box","DetectorDescription":"Box is a service offering various service for secure collaboration, content management, and workflow. Box token can be used to access and interact with this data.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"a9ba953c547585285559d0e05c16e29e","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/http.d.ts","line":67}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://abc:<EMAIL>","RawV2":"http://abc:<EMAIL>","Redacted":"http://abc:********@example.com","ExtraData":null,"StructuredData":null}
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":139}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":726,"DetectorName":"Diffbot","DetectorDescription":"Diffbot is a service that provides APIs for extracting data from web pages. Diffbot API tokens can be used to access these services and extract data from web content.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"8cb8e34af89cb477a5da52e3fd9a13f7","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":25}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":726,"DetectorName":"Diffbot","DetectorDescription":"Diffbot is a service that provides APIs for extracting data from web pages. Diffbot API tokens can be used to access these services and extract data from web content.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"8cb8e34af89cb477a5da52e3fd9a13f7","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":127}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":455,"DetectorName":"Flickr","DetectorDescription":"Flickr is an image and video hosting service. Flickr API keys can be used to access and modify user data and perform various operations within the Flickr ecosystem.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"3c34c3ac904b6d6f26182807fbb95c5e","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":125}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":455,"DetectorName":"Flickr","DetectorDescription":"Flickr is an image and video hosting service. Flickr API keys can be used to access and modify user data and perform various operations within the Flickr ecosystem.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"d3b2b610171589db68809c3ec3bf2bcb","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/JSONStream/test/fixtures/all_npm.json","line":126}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":455,"DetectorName":"Flickr","DetectorDescription":"Flickr is an image and video hosting service. Flickr API keys can be used to access and modify user data and perform various operations within the Flickr ecosystem.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"66c8e8a00ad0a906f632ff99cf490163","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/https.d.ts","line":194}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/url.d.ts","line":129}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/url.d.ts","line":123}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":9}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"ef9c552f17fdd7d0043981aecf72906ddedee5d07d4699f8ee5a23103d6a2a05","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"0443a228db1b8fddd4cade3c9b7d8ac4ea46c9872fcbaa46014f95f6c25f7d97","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"957bb8224e5d1304e97793ad674e5c42426fe0023dcdd937cfc2a5f8ce2e0b6a","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":6}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"345926403c9e3c58d39b4938eee43299966e6984fc1ba3030ad21932d498b8bf","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":7}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"7d5e69080cac84468547796a1123a2db05133dc4da5b4b042f5f1b5f32050cc5","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":8}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"8a9d33e406563f497df418b3c98094b7c0534d393349fd0e94feba7f8e85c006","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":4}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"5b0eae7991817bb58ec9a039fdec38cace47c40f5133e11f553985968dc74af6","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"f95c7a2b7555bf41d54904cedd1be6c1b7b2765eff4e2a8a5911fda2af7fa761","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@sentry/cli/checksums.txt","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":87,"DetectorName":"SentryToken","DetectorDescription":"Sentry is an error tracking service that helps developers monitor and fix crashes in real time. Sentry tokens can be used to access and manage projects and organizations within Sentry.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"1d06e01e7e8bc42def337469626bdcc9693eba4950f225a24b6d4204c7e0a060","RawV2":"","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/url.d.ts","line":32}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://123:<EMAIL>","RawV2":"https://123:<EMAIL>","Redacted":"https://123:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/url.d.ts","line":26}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/aws-sdk/clients/cognitoidentityserviceprovider.d.ts","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":983,"DetectorName":"GCPApplicationDefaultCredentials","DetectorDescription":"GCP Application Default Credentials are used to authenticate and authorize API requests to Google Cloud services.","DecoderName":"PLAIN","Verified":false,"VerificationError":"missing 'type' field in credentials","VerificationFromCache":false,"Raw":"amzn1","RawV2":"amzn1","Redacted":"","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/url.d.ts","line":294}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://123:<EMAIL>","RawV2":"https://123:<EMAIL>","Redacted":"https://123:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/@types/node/url.d.ts","line":123}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/eslint/README.md","line":131}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":8,"DetectorName":"Github","DetectorDescription":"GitHub is a web-based platform used for version control and collaborative software development. GitHub tokens can be used to access and modify repositories and other resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"d1e55d7661d724bf2281c1bfd33cb8f99fe2465f","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/issue-parser/lib/hosts-config.js","line":44}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":190,"DetectorName":"Atlassian","DetectorDescription":"Atlassian provides tools for software development, project management, and content management. Atlassian API keys can be used to access and manage these tools and services.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"mark-up-comments-issues-","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/atlassian/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/issue-parser/README.md","line":14}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":190,"DetectorName":"Atlassian","DetectorDescription":"Atlassian provides tools for software development, project management, and content management. Atlassian API keys can be used to access and manage these tools and services.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"mark-up-comments-issues-","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/atlassian/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/meow/node_modules/hosted-git-info/index.js","line":118}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://user%3An%40me:<EMAIL>","RawV2":"https://user%3An%40me:<EMAIL>","Redacted":"https://user%3An%40me:********@x.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/json5/README.md","line":14}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":924,"DetectorName":"GitHubOauth2","DetectorDescription":"GitHub OAuth2 credentials are used to authenticate and authorize applications to access GitHub's API on behalf of a user or organization. These credentials include a client ID and client secret, which can be used to obtain access tokens for accessing GitHub resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"8e8fa57c7ee1350e3491","RawV2":"8e8fa57c7ee1350e3491feb3c9f670515edf9a88f185301cbd7794ee3e52","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/json5/README.md","line":14}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":924,"DetectorName":"GitHubOauth2","DetectorDescription":"GitHub OAuth2 credentials are used to authenticate and authorize applications to access GitHub's API on behalf of a user or organization. These credentials include a client ID and client secret, which can be used to obtain access tokens for accessing GitHub resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"8e8fa57c7ee1350e3491","RawV2":"8e8fa57c7ee1350e3491b88f20c90bf4659b8ad5cb2a27956005eac2c7e8","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/github/"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/pg-connection-string/README.md","line":20}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup somehost on *******:53: no such host","VerificationFromCache":false,"Raw":"*********************************************","RawV2":"postgres://someuser:somepassword@somehost:381","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/pg-pool/test/connection-strings.js","line":8}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup baz on *******:53: no such host","VerificationFromCache":false,"Raw":"***************************","RawV2":"postgres://foo:bar@baz:1234","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/pg-pool/README.md","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup DBHost on *******:53: no such host","VerificationFromCache":false,"Raw":"************************************","RawV2":"postgres://DBuser:secret@DBHost:5432","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/parse-url/README.md","line":278}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":9,"DetectorName":"Gitlab","DetectorDescription":"GitLab is a web-based DevOps lifecycle tool that provides a Git repository manager providing wiki, issue-tracking, and CI/CD pipeline features. GitLab API tokens can be used to access and modify repository data and other resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"-backup-util-harduino","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/gitlab/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/read-pkg/node_modules/hosted-git-info/index.js","line":118}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://user%3An%40me:<EMAIL>","RawV2":"https://user%3An%40me:<EMAIL>","Redacted":"https://user%3An%40me:********@x.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/tldts/README.md","line":204}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"http: server gave HTTP response to HTTPS client","VerificationFromCache":false,"Raw":"https://user:<EMAIL>:8080","RawV2":"https://user:<EMAIL>:8080/some/path","Redacted":"https://user:********@example.co.uk:8080/some/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/tldts/README.md","line":257}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup secure.example.co.uk on *******:53: no such host","VerificationFromCache":false,"Raw":"https://user:<EMAIL>:443","RawV2":"https://user:<EMAIL>:443/some/path","Redacted":"https://user:********@secure.example.co.uk:443/some/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/README.md","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>:8080","RawV2":"http://user:<EMAIL>:8080/p/a/t/h","Redacted":"http://user:********@host.com:8080/p/a/t/h","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":313}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup -lovemonsterz.tumblr.com: no such host","VerificationFromCache":false,"Raw":"http://user:<EMAIL>","RawV2":"http://user:<EMAIL>/rss","Redacted":"http://user:********@-lovemonsterz.tumblr.com/rss","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":7}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"**************************************","RawV2":"**************************************/path","Redacted":"http://atpass:********@127.0.0.1:8080/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":155}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"*********************************","RawV2":"*********************************","Redacted":"http://atpass:********@127.0.0.1","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":162}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup foo on *******:53: no such host","VerificationFromCache":false,"Raw":"*******************************","RawV2":"*******************************","Redacted":"http://atslash%2F%40:********@foo","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/release-it/test/tasks.js","line":16}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":9,"DetectorName":"Gitlab","DetectorDescription":"GitLab is a web-based DevOps lifecycle tool that provides a Git repository manager providing wiki, issue-tracking, and CI/CD pipeline features. GitLab API tokens can be used to access and modify repository data and other resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"interceptCollaborator","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/gitlab/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/release-it/test/tasks.js","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":9,"DetectorName":"Gitlab","DetectorDescription":"GitLab is a web-based DevOps lifecycle tool that provides a Git repository manager providing wiki, issue-tracking, and CI/CD pipeline features. GitLab API tokens can be used to access and modify repository data and other resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"interceptGitLabPublish","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/gitlab/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":225}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>","RawV2":"http://user:<EMAIL>/vt/lyrs","Redacted":"http://user:********@mt0.google.com/vt/lyrs","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":true,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","resource_type":"Access key"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":6}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":2,"DetectorName":"AWS","DetectorDescription":"AWS (Amazon Web Services) is a comprehensive cloud computing platform offering a wide range of on-demand services like computing power, storage, databases. API keys for AWS can have varying amount of access to these services depending on the IAM policy attached.","DecoderName":"PLAIN","Verified":true,"VerificationFromCache":true,"Raw":"********************","RawV2":"********************:MzdyjJk86k2FXXEUNeU+2IAICWKUG0xxjUO4Ckdg","Redacted":"********************","ExtraData":{"account":"************","resource_type":"Access key"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/parse-path/README.md","line":236}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":9,"DetectorName":"Gitlab","DetectorDescription":"GitLab is a web-based DevOps lifecycle tool that provides a Git repository manager providing wiki, issue-tracking, and CI/CD pipeline features. GitLab API tokens can be used to access and modify repository data and other resources.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"react-native-android-","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/gitlab/","version":"1"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":6}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://123:<EMAIL>","RawV2":"https://123:<EMAIL>","Redacted":"https://123:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup DBHost:5432\",\"RawV2\":\"postgres:: no such host","VerificationFromCache":false,"Raw":"*************************************",\"RawV2\":\"postgres::5432","RawV2":"postgres://DBuser:secret@DBHost:5432\",\"RawV2\":\"postgres::5432","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":19}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"http://abc:<EMAIL>","RawV2":"http://abc:<EMAIL>","Redacted":"http://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup somehost:381\",\"RawV2\":\"postgres:: no such host","VerificationFromCache":false,"Raw":"**********************************************",\"RawV2\":\"postgres::5432","RawV2":"postgres://someuser:somepassword@somehost:381\",\"RawV2\":\"postgres::5432","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"lookup baz:1234\",\"RawV2\":\"postgres:: no such host","VerificationFromCache":false,"Raw":"****************************",\"RawV2\":\"postgres::5432","RawV2":"postgres://foo:bar@baz:1234\",\"RawV2\":\"postgres::5432","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":9}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://user%3An%40me:<EMAIL>","RawV2":"https://user%3An%40me:<EMAIL>","Redacted":"https://user%3An%40me:********@x.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":4}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://123:<EMAIL>","RawV2":"https://123:<EMAIL>","Redacted":"https://123:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":4}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":6}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://123:<EMAIL>","RawV2":"https://123:<EMAIL>","Redacted":"https://123:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://abc:<EMAIL>","RawV2":"https://abc:<EMAIL>","Redacted":"https://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":4}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"http://abc:<EMAIL>","RawV2":"http://abc:<EMAIL>","Redacted":"http://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":4}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"http://abc:<EMAIL>","RawV2":"http://abc:<EMAIL>","Redacted":"http://abc:********@example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":9}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":2}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":4}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://REDACTED:<EMAIL>","RawV2":"https://REDACTED:<EMAIL>","Redacted":"https://REDACTED:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":true,"Raw":"https://username:<EMAIL>","RawV2":"https://username:<EMAIL>","Redacted":"https://username:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":9}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"https://user%3An%40me:<EMAIL>","RawV2":"https://user%3An%40me:<EMAIL>","Redacted":"https://user%3An%40me:********@x.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":21}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"**************************************","RawV2":"**************************************/path","Redacted":"http://atpass:********@127.0.0.1:8080/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"http: server gave HTTP response to HTTPS client","VerificationFromCache":false,"Raw":"https://user:<EMAIL>:8080","RawV2":"https://user:<EMAIL>:8080","Redacted":"https://user:********@example.co.uk:8080","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":19}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>:8080","RawV2":"http://user:<EMAIL>:8080/p/a/t/h","Redacted":"http://user:********@host.com:8080/p/a/t/h","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":19}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>:8080","RawV2":"http://user:<EMAIL>:8080","Redacted":"http://user:********@host.com:8080","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":21}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"**************************************","RawV2":"**************************************","Redacted":"http://atpass:********@127.0.0.1:8080","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":17}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"http: server gave HTTP response to HTTPS client","VerificationFromCache":false,"Raw":"https://user:<EMAIL>:8080","RawV2":"https://user:<EMAIL>:8080/some/path","Redacted":"https://user:********@example.co.uk:8080/some/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":12}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://asdf:<EMAIL>","RawV2":"http://asdf:<EMAIL>","Redacted":"http://asdf:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":11}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://diff:<EMAIL>","RawV2":"http://diff:<EMAIL>","Redacted":"http://diff:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":253}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://diff:<EMAIL>","RawV2":"http://diff:<EMAIL>","Redacted":"http://diff:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":254}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://asdf:<EMAIL>","RawV2":"http://asdf:<EMAIL>","Redacted":"http://asdf:********@www.example.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":10}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>","RawV2":"http://user:<EMAIL>","Redacted":"http://user:********@mt0.google.com","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>:8080","RawV2":"http://user:<EMAIL>:8080/p/a/t/h","Redacted":"http://user:********@host.com:8080/p/a/t/h","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"**************************************","RawV2":"**************************************/path","Redacted":"http://atpass:********@127.0.0.1:8080/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":10}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>","RawV2":"http://user:<EMAIL>/vt/lyrs","Redacted":"http://user:********@mt0.google.com/vt/lyrs","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"http: server gave HTTP response to HTTPS client","VerificationFromCache":false,"Raw":"https://user:<EMAIL>:8080","RawV2":"https://user:<EMAIL>:8080","Redacted":"https://user:********@example.co.uk:8080","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"http: server gave HTTP response to HTTPS client","VerificationFromCache":false,"Raw":"https://user:<EMAIL>:8080","RawV2":"https://user:<EMAIL>:8080/some/path","Redacted":"https://user:********@example.co.uk:8080/some/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":3}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"http://user:<EMAIL>:8080","RawV2":"http://user:<EMAIL>:8080","Redacted":"http://user:********@host.com:8080","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"**************************************","RawV2":"**************************************","Redacted":"http://atpass:********@127.0.0.1:8080","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":5}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":false,"Raw":"*********************************","RawV2":"*********************************","Redacted":"http://atpass:********@127.0.0.1","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":307}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"i/o timeout","VerificationFromCache":false,"Raw":"http://user:<EMAIL>:8000","RawV2":"http://user:<EMAIL>:8000/foo/bar","Redacted":"http://user:********@example.com:8000/foo/bar","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/url/test.js","line":381}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":17,"DetectorName":"URI","DetectorDescription":"This detector identifies URLs with embedded credentials, which can be used to access web resources without explicit user interaction.","DecoderName":"PLAIN","Verified":false,"VerificationError":"dialing local IP addresses is not allowed","VerificationFromCache":true,"Raw":"**************************************","RawV2":"**************************************/path","Redacted":"http://atpass:********@127.0.0.1:8080/path","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/keyv/README.md","line":58}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":895,"DetectorName":"MongoDB","DetectorDescription":"MongoDB is a NoSQL database that uses a document-oriented data model. MongoDB credentials can be used to access and manipulate the database.","DecoderName":"PLAIN","Verified":false,"VerificationError":"context deadline exceeded","VerificationFromCache":false,"Raw":"******************************************","RawV2":"","Redacted":"","ExtraData":{"rotation_guide":"https://howtorotate.com/docs/tutorials/mongo/"},"StructuredData":null}
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/trufflehog_results.json","line":1}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":15,"DetectorName":"PrivateKey","DetectorDescription":"Private keys are used for securely connecting and authenticating to various systems and services. Exposure of private keys can lead to unauthorized access and data breaches.","DecoderName":"PLAIN","Verified":false,"VerificationFromCache":false,"Raw":"-----BEGIN RSA PRIVATE KEY-----\nMIIEogIBAAKCAQEArYxrNYD/iT5CZVpR,ExtraData:null,StructuredData:null}\n{SourceMetadata:{Data:{Filesystem:{file:/src/node_modules/JSONStream/test/fixtures/all_npm.json,line:139}}},SourceID:1,SourceType:15,SourceName:trufflehog - filesystem,DetectorType:726,DetectorName:Diffbot,DetectorDescription:Diffbot is a service that provides APIs for extracting data from web pages. Diffbot API tokens can be used to access these services and extract data from web content.,DecoderName:PLAIN,Verified:false,VerificationFromCache:false,Raw:8cb8e34af89cb477a5da52e3fd9a13f7,RawV2:,Redacted:,ExtraData:null,StructuredData:null}\n{SourceMetadata:{Data:{Filesystem:{file:/src/node_modules/JSONStream/test/fixtures/all_npm.json,line:25}}},SourceID:1,SourceType:15,SourceName:trufflehog - filesystem,DetectorType:726,DetectorName:Diffbot,DetectorDescription:Diffbot is a service that provides APIs for extracting data from web pages. Diffbot API tokens can be used to access these services and extract data from web content.,DecoderName:PLAIN,Verified:false,VerificationFromCache:false,Raw:8cb8e34af89cb477a5da52e3fd9a13f7,RawV2:,Redacted:,ExtraData:null,StructuredData:null}\n{SourceMetadata:{Data:{Filesystem:{file:/src/node_modules/@octokit/openapi-types/types.d.ts,line:1}}},SourceID:1,SourceType:15,SourceName:trufflehog - filesystem,DetectorType:15,DetectorName:PrivateKey,DetectorDescription:Private keys are used for securely connecting and authenticating to various systems and services. Exposure of private keys can lead to unauthorized access and data breaches.,DecoderName:PLAIN,Verified:false,VerificationFromCache:false,Raw:-----BEGIN RSA PRIVATE KEY-----\nMIIEogIBAAKCAQEArYxrNYD/iT5CZVpRJu4rBKmmze3PVmT/gCo2ATUvDvZTPTey\nxcGJ3vvrJXazKk06pN05TN29o98jrYz4cengG3YGsXPNEpKsIrEl8NhbnxapEnM9\nJCMRe0P5JcPsfZlX6hmiT7136GRWiGOUba2X9+HKh8QJVLG5rM007TBER9/z9mWm\nrJuNh+m5l320oBQY/Qq3A7wzdEfZw8qm/mIN0FCeoXH1L6B8xXWaAYBwhTEh6SSn\nZHlO1Xu1JWDmAvBCi0RO5aRSKM8q9QEkvvHP4yweAtK3N8+aAbZ7ovaDhyGz8r6r\nzhU1b8Uo0Z2ysf503WqzQgIajr7Fry7/kUwpgQIDAQABAoIBADwJp80Ko1xHPZDy\nfcCKBDfIuPvkmSW6KumbsLMaQv1aGdHDwwTGv3t0ixSay8CGlxMRtRDyZPib6SvQ\n6OH/lpfpbMdW2ErkksgtoIKBVrDilfrcAvrNZu7NxRNbhCSvN8q0s4ICecjbbVQh\nnueSdlA6vGXbW58BHMq68uRbHkP+k+mM9U0mDJ1HMch67wlg5GbayVRt63H7R2+r\nVxcna7B80J/lCEjIYZznawgiTvp3MSanTglqAYi+m1EcSsP14bJIB9vgaxS79kTu\noiSo93leJbBvuGo8QEiUqTwMw4tDksmkLsoqNKQ1q9P7LZ9DGcujtPy4EZsamSJT\ny8OJt0ECgYEA2lxOxJsQk2kI325JgKFjo92mQeUObIvPfSNWUIZQDTjniOI6Gv63\nGLWVFrZcvQBWjMEQraJA9xjPbblV8PtfO87MiJGLWCHFxmPz2dzoedN+2Coxom8m\nV95CLz8QUShuao6u/RYcvUaZEoYs5bHcTmy5sBK80JyEmafJPtCQVxMCgYEAy3ar\nZr3yv4xRPEPMat4rseswmuMooSaK3SKub19WFI5IAtB/e7qR1Rj9JhOGcZz+OQrl\nT78O2OFYlgOIkJPvRMrPpK5V9lslc7tz1FSh3BZMRGq5jSyD7ETSOQ0c8T2O/s7v\nbeEPbVbDe4mwvM24XByH0GnWveVxaDl51ABD65sCgYB3ZAspUkOA5egVCh8kNpnd\nSd6SnuQBE3ySRlT2WEnCwP9Ph6oPgn+oAfiPX4xbRqkL8q/k0BdHQ4h+zNwhk7+h\nWtPYRAP1Xxnc/F+jGjb+DVaIaKGU18MWPg7f+FI6nampl3Q0KvfxwX0GdNhtio8T\nTj1E+SnFwh56SRQuxSh2gwKBgHKjlIO5NtNSflsUYFM+hyQiPiqnHzddfhSG+/3o\nm5nNaSmczJesUYreH5San7/YEy2UxAugvP7aSY2MxB+iGsiJ9WD2kZzTUlDZJ7RV\nUzWsoqBR+eZfVJ2FUWWvy8TpSG6trh4dFxImNtKejCR1TREpSiTV3Zb1dmahK9GV\nrK9NAoGAbBxRLoC01xfxCTgt5BDiBcFVh4fp5yYKwavJPLzHSpuDOrrI9jDn1oKN\nonq5sDU1i391zfQvdrbX4Ova48BN+B7p63FocP/MK5tyyBoT8zQEk2+vWDOw7H/Z\nu5dTCPxTIsoIwUw1I+7yIxqJzLPFgR2gVBwY1ra/8iAqCj+zeBw=\n-----END RSA PRIVATE KEY-----\n","RawV2":"","Redacted":"-----BEGIN RSA PRIVATE KEY-----\nMIIEogIBAAKCAQEArYxrNYD/iT5CZVpR","ExtraData":null,"StructuredData":null}
{"SourceMetadata":{"Data":{"Filesystem":{"file":"/src/node_modules/is-url/test/index.js","line":68}}},"SourceID":1,"SourceType":15,"SourceName":"trufflehog - filesystem","DetectorType":968,"DetectorName":"Postgres","DetectorDescription":"Postgres connection string containing credentials","DecoderName":"PLAIN","Verified":false,"VerificationError":"i/o timeout","VerificationFromCache":false,"Raw":"postgres://u:<EMAIL>:5702","RawV2":"postgres://u:<EMAIL>:5702","Redacted":"","ExtraData":{"sslmode":"\u003cunset\u003e"},"StructuredData":null}
{"level":"error","ts":"2025-03-20T08:24:45Z","logger":"trufflehog","msg":"encountered errors during scan","errors":["error chunking unit \"/src/node_modules/.bin/JSONStream\": skipping symlink","error chunking unit \"/src/node_modules/.bin/acorn\": skipping symlink","error chunking unit \"/src/node_modules/.bin/browserslist\": skipping symlink","error chunking unit \"/src/node_modules/.bin/chromedriver\": skipping symlink","error chunking unit \"/src/node_modules/.bin/color-support\": skipping symlink","error chunking unit \"/src/node_modules/.bin/conventional-changelog-writer\": skipping symlink","error chunking unit \"/src/node_modules/.bin/conventional-commits-parser\": skipping symlink","error chunking unit \"/src/node_modules/.bin/conventional-recommended-bump\": skipping symlink","error chunking unit \"/src/node_modules/.bin/crc32\": skipping symlink","error chunking unit \"/src/node_modules/.bin/create-jest\": skipping symlink","error chunking unit \"/src/node_modules/.bin/css-beautify\": skipping symlink","error chunking unit \"/src/node_modules/.bin/csv-parser\": skipping symlink","error chunking unit \"/src/node_modules/.bin/cypress\": skipping symlink","error chunking unit \"/src/node_modules/.bin/editorconfig\": skipping symlink","error chunking unit \"/src/node_modules/.bin/ejs\": skipping symlink","error chunking unit \"/src/node_modules/.bin/escodegen\": skipping symlink","error chunking unit \"/src/node_modules/.bin/esgenerate\": skipping symlink","error chunking unit \"/src/node_modules/.bin/eslint\": skipping symlink","error chunking unit \"/src/node_modules/.bin/esparse\": skipping symlink","error chunking unit \"/src/node_modules/.bin/esvalidate\": skipping symlink","error chunking unit \"/src/node_modules/.bin/extract-zip\": skipping symlink","error chunking unit \"/src/node_modules/.bin/fxparser\": skipping symlink","error chunking unit \"/src/node_modules/.bin/get-pkg-repo\": skipping symlink","error chunking unit \"/src/node_modules/.bin/git-raw-commits\": skipping symlink","error chunking unit \"/src/node_modules/.bin/git-semver-tags\": skipping symlink","error chunking unit \"/src/node_modules/.bin/handlebars\": skipping symlink","error chunking unit \"/src/node_modules/.bin/html-beautify\": skipping symlink","error chunking unit \"/src/node_modules/.bin/image-size\": skipping symlink","error chunking unit \"/src/node_modules/.bin/import-local-fixture\": skipping symlink","error chunking unit \"/src/node_modules/.bin/is-docker\": skipping symlink","error chunking unit \"/src/node_modules/.bin/is-in-ci\": skipping symlink","error chunking unit \"/src/node_modules/.bin/is-inside-container\": skipping symlink","error chunking unit \"/src/node_modules/.bin/jake\": skipping symlink","error chunking unit \"/src/node_modules/.bin/jest\": skipping symlink","error chunking unit \"/src/node_modules/.bin/js-beautify\": skipping symlink","error chunking unit \"/src/node_modules/.bin/js-yaml\": skipping symlink","error chunking unit \"/src/node_modules/.bin/jsesc\": skipping symlink","error chunking unit \"/src/node_modules/.bin/json-beautify\": skipping symlink","error chunking unit \"/src/node_modules/.bin/json2yaml\": skipping symlink","error chunking unit \"/src/node_modules/.bin/json5\": skipping symlink","error chunking unit \"/src/node_modules/.bin/loose-envify\": skipping symlink","error chunking unit \"/src/node_modules/.bin/mime\": skipping symlink","error chunking unit \"/src/node_modules/.bin/mkdirp\": skipping symlink","error chunking unit \"/src/node_modules/.bin/nanoid\": skipping symlink","error chunking unit \"/src/node_modules/.bin/node-pre-gyp\": skipping symlink","error chunking unit \"/src/node_modules/.bin/node-which\": skipping symlink","error chunking unit \"/src/node_modules/.bin/nodemon\": skipping symlink","error chunking unit \"/src/node_modules/.bin/nodetouch\": skipping symlink","error chunking unit \"/src/node_modules/.bin/nopt\": skipping symlink","error chunking unit \"/src/node_modules/.bin/parser\": skipping symlink","error chunking unit \"/src/node_modules/.bin/prettier\": skipping symlink","error chunking unit \"/src/node_modules/.bin/rc\": skipping symlink","error chunking unit \"/src/node_modules/.bin/regexp-tree\": skipping symlink","error chunking unit \"/src/node_modules/.bin/release-it\": skipping symlink","error chunking unit \"/src/node_modules/.bin/resolve\": skipping symlink","error chunking unit \"/src/node_modules/.bin/rimraf\": skipping symlink","error chunking unit \"/src/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sentry-cli\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sentry-prune-profiler-binaries\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sequelize\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sequelize-cli\": skipping symlink","error chunking unit \"/src/node_modules/.bin/shjs\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sshpk-conv\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sshpk-sign\": skipping symlink","error chunking unit \"/src/node_modules/.bin/sshpk-verify\": skipping symlink","error chunking unit \"/src/node_modules/.bin/standard-version\": skipping symlink","error chunking unit \"/src/node_modules/.bin/tldts\": skipping symlink","error chunking unit \"/src/node_modules/.bin/tree-kill\": skipping symlink","error chunking unit \"/src/node_modules/.bin/uglifyjs\": skipping symlink","error chunking unit \"/src/node_modules/.bin/update-browserslist-db\": skipping symlink","error chunking unit \"/src/node_modules/.bin/uuid\": skipping symlink","error chunking unit \"/src/node_modules/.bin/xlsx\": skipping symlink","error chunking unit \"/src/node_modules/.bin/yaml2json\": skipping symlink","error chunking unit \"/src/node_modules/@babel/core/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/@babel/helper-compilation-targets/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/@istanbuljs/load-nyc-config/node_modules/.bin/js-yaml\": skipping symlink","error chunking unit \"/src/node_modules/@smithy/middleware-retry/node_modules/.bin/uuid\": skipping symlink","error chunking unit \"/src/node_modules/aws-sdk/node_modules/.bin/uuid\": skipping symlink","error chunking unit \"/src/node_modules/babel-plugin-istanbul/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/conventional-changelog-writer/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/eslint-plugin-import/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/eslint-plugin-node/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/fstream/node_modules/.bin/rimraf\": skipping symlink","error chunking unit \"/src/node_modules/git-semver-tags/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/js-beautify/node_modules/.bin/glob\": skipping symlink","error chunking unit \"/src/node_modules/js-beautify/node_modules/.bin/nopt\": skipping symlink","error chunking unit \"/src/node_modules/jsonwebtoken/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/make-dir/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/meow/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/nodemon/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/read-pkg/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/release-it/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/simple-update-notifier/node_modules/.bin/semver\": skipping symlink","error chunking unit \"/src/node_modules/superagent/node_modules/.bin/mime\": skipping symlink","error chunking unit \"/src/node_modules/tar/node_modules/.bin/mkdirp\": skipping symlink","error chunking unit \"/src/node_modules/tsconfig-paths/node_modules/.bin/json5\": skipping symlink"]}
{"level":"info-0","ts":"2025-03-20T08:24:45Z","logger":"trufflehog","msg":"finished scanning","chunks":74014,"bytes":497150880,"verified_secrets":17,"unverified_secrets":455,"scan_duration":"17.824455911s","trufflehog_version":"3.88.18","verification_caching":{"Hits":266,"Misses":268,"HitsWasted":28,"AttemptsSaved":238,"VerificationTimeSpentMS":243435}}
