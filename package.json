{"devDependencies": {"cypress": "^14.3.3", "eslint": "^9.26.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^17.17.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-security": "^3.0.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "patch-package": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-organize-attributes": "^1.0.0", "release-it": "^19.0.2", "sequelize-cli": "^6.6.3", "standard-version": "^9.5.0", "supertest": "^7.1.0"}, "scripts": {"start": "nodemon server.js", "test": "jest", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org appvin-technologies-pvt-ltd --project node-express ./ && sentry-cli sourcemaps upload --org appvin-technologies-pvt-ltd --project node-express ./", "lint": "eslint .", "format": "prettier --write .", "sonar": "sonar-scanner", "release": "release-it", "postinstall": "patch-package"}, "dependencies": {"@anthropic-ai/sdk": "^0.40.1", "@clickhouse/client": "^1.11.1", "@opentelemetry/auto-instrumentations-node": "^0.34.0", "@opentelemetry/exporter-trace-otlp-http": "^0.34.0", "@opentelemetry/instrumentation-express": "^0.49.0", "@opentelemetry/instrumentation-http": "^0.200.0", "@opentelemetry/instrumentation-pg": "^0.52.0", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-node": "^0.34.0", "@opentelemetry/semantic-conventions": "^1.23.0", "@release-it/keep-a-changelog": "^7.0.0", "@sentry/cli": "^2.44.0", "@sentry/node": "^9.30.0", "@sentry/opentelemetry": "^9.15.0", "@sentry/profiling-node": "^9.15.0", "@xmldom/xmldom": "^0.9.8", "ansi-regex": "^6.1.0", "app-module-path": "^2.2.0", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "braces": "^3.0.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-spawn": "^7.0.6", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "elliptic": "6.6.1", "engine.io": "^6.6.4", "exceljs": "^4.4.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "flat": "^6.0.1", "fs": "^0.0.2", "helmet": "^8.1.0", "html-to-docx": "^1.8.0", "http-proxy-middleware": "^3.0.5", "http-status": "^1.8.1", "https": "^1.0.0", "image-size": "^2.0.2", "joi": "^17.13.3", "json-beautify": "^1.1.1", "jsonwebtoken": "^9.0.2", "keycloak-connect": "^26.1.1", "lodash": "4.17.21", "minio": "^8.0.5", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "nanoid": "5.1.5", "node-cron": "^3.0.3", "node-xlsx": "^0.24.0", "nodemailer": "^7.0.2", "path": "^0.12.7", "pdf-parse": "^1.1.1", "pg": "^8.15.6", "pg-hstore": "^2.3.4", "qs": "^6.14.0", "random-token": "^0.0.8", "react-google-recaptcha": "^3.1.0", "sequelize": "^6.37.7", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "yamljs": "^0.3.0"}, "name": "trustruler-backend", "description": "", "version": "7.3.0", "main": ".eslintrc.js", "author": "", "license": "ISC", "engines": {"node": ">=20.0.0"}, "release-it": {"hooks": {"after:bump": "git push --follow-tags origin development", "after:release": "echo Successfully released ${version} to ${repo.repository}."}, "plugins": {"@release-it/keep-a-changelog": {"preset": "conventionalcommits", "changelogFile": "CHANGELOG.md"}}}, "pnpm": {"overrides": {"xmldom": "@xmldom/xmldom@^0.8.10"}}}