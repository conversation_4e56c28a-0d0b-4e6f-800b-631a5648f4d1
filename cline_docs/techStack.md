## Tech Stack

### Backend Framework
- **Express.js**: A minimal and flexible Node.js web application framework that provides a robust set of features for web and mobile applications.

### Database
- **Sequelize**: A promise-based Node.js ORM for various SQL databases, used for managing database interactions.

### Authentication
- **Keycloak**: An open-source identity and access management solution that provides single sign-on, identity brokering, and user federation.

### API Documentation
- **Swagger**: A tool for documenting RESTful APIs, providing a user-friendly interface for exploring and testing API endpoints.

### Middleware
- **Multer**: A middleware for handling `multipart/form-data`, which is used for uploading files.
- **Rate Limiter**: Middleware to limit repeated requests to public APIs and endpoints.

### Validation
- **Joi**: A powerful schema description language and data validator for JavaScript.

### Testing Libraries
- **Jest**: A delightful JavaScript testing framework with a focus on simplicity.
- **Supertest**: A super-agent driven library that allows you to test HTTP servers in Node.js.

### Other Libraries
- **YAML.js**: A library for parsing and stringifying YAML documents.
- **Swagger UI Express**: A middleware for serving the Swagger UI.

### Deployment
- **Docker**: Containerization platform to package applications and their dependencies into a standardized unit for software development.

### Additional Tools
- **Bcrypt**: A library for hashing passwords securely.
- **Moment.js**: A library for parsing, validating, manipulating, and formatting dates.
- **Random Token**: A library for generating random tokens for various purposes.
