# Port of the server
PORT=4000

# Enivor of the server
NODE_ENV= # 'development' , 'preprod' , 'production' 

#CORS config part for allowed origins
ALLOWED_ORIGINS= #http://localhost:4000,http://127.0.0.1:8080,*
 
 # Database configuration
WRITE_DB_HOST_INFO=
READ_DB_HOST_INFO=
DB_USERNAME_INFO=
DB_PASSWORD_INFO=
DB_DATABASE_INFO=
DB_DIALECT=
DB_PORT=
  
  #Server IP or domain used for redirection from emails to the web app
SERVER_IP= # 'dev.gotrust.tech' , 'preprod.gotrust.tech' , 'portal.gotrust.tech'



USE_MINIO=true # 'true', 'false' ---> if true, the server will use minio for storing files otherwise AWS S3 (if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are set)
 
S3_ENDPOINT= # used only in case of minio // ************
S3_PORT=9000 # used only in case of minio

#### minio S3 OR AWS S3 Credentials  
BUCKET_NAME=go-asset-management-profile-image # bucket name in minio or aws s3
S3_ACCESSKEYID= # Access Key Id fro Minio or AWS S3
S3_SECRETKEY= # Secret Key fro Minio or AWS S3
 


SECRET_KEY= # To veryfy guest (For JWT)
  

 #SMTP Configuration
SMTP_HOST=
SMTP_EMAIL=
# SMTP user credentials
SMTP_USER=
SMTP_PASS=
# SMTP server port (587 for TLS, 465 for SSL, 25 for unencrypted)
SMTP_PORT=587
SMTP_SECURE=false # Set to 'true' if using SSL (port 465)


# Using the email service of Client Portal
USE_CLIENT_PORTAL_EMAIL=false # 'true', 'false
CLIENT_EMAIL= # FROM emailId of client
CLIENT_API_AUTH_URL=https://apis.edelweissfin.com
CLIENT_X_API_KEY= # API Key of client 
API_CLIENT_ID= # client id or username for Basic AUTH
API_CLIENT_SECRET= # client secret or password for the Basic AUTH




 # SSL Configuration
 # Path to the SSL private key file
SSL_PRIV_KEY= #/path/to/your/private.key       # Example: /etc/ssl/private/private.key
# Path to the SSL certificate file
SSL_CERT_KEY= #/path/to/your/certificate.crt  # Example: /etc/ssl/certs/certificate.crt
 


# Sentry Configuration

# The Data Source Name (DSN) for connecting to Sentry
# Replace this with your Sentry project DSN, available in your Sentry project settings.
SENTRY_DSN= #"https://<EMAIL>/0"



 # KEYCLOAK Config
KEYCLOAK_REALM= # Realm name in Keycloak
KEYCLOAK_AUTH_SERVER_URL= https://keycloak-dd.gotrust.tech
KEYCLOAK_RESOURCE= #resource name
KEYCLOAK_REALM_PUBLIC_KEY= # realm public key
KEYCLOAK_CLIENT_ID= # Client ID for the application in Keycloak
KEYCLOAK_CLIENT_SECRET= # Client secret for the application (confidential clients only)
KEYCLOAK_TOKEN_ENDPOINT= # Token endpoint (optional, derived from KEYCLOAK_BASE_URL and KEYCLOAK_REALM if not provided)
KEYCLOAK_USER_ENDPOINT= # User info endpoint to fetch details of logged-in users

# Open Router
OPENROUTER_BASE_URL=""
OPENROUTER_API_KEY=""
OPENROUTER_AI_MODEL="" 
