'use strict';

const db = require('../app/models');
const models = require('../app/models');
const { sequelize, type } = models;

const metadataAddedTable = 'migration_columns_added';
const metadataAlteredTable = 'migration_columns_altered';

const modelKeys = Object.keys(models).filter(key => key !== 'sequelize' && key !== 'Sequelize');

function sanitizeAttribute(attr) {
  const allowedKeys = ['type', 'allowNull', 'defaultValue', 'unique', 'references'];
  const sanitized = {};
  for (const key of allowedKeys) {
    if (attr[key] !== undefined) {
      sanitized[key] = attr[key];
    }
  }
  return sanitized;
}

function getSequelizeColumnType(attr) {
  return attr.type?.key || attr.type?.toString() || '';
}

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, SequelizeCLI) {
    const errorLog = [];
    // return queryInterface.sequelize.transaction(async transaction => {
    const existingTables = await queryInterface.showAllTables();

    // Create metadata tables if they don’t exist
    if (!existingTables.includes(metadataAddedTable)) {
      await queryInterface.createTable(metadataAddedTable, {
        tableName: { type: SequelizeCLI.STRING, allowNull: false },
        columnName: { type: SequelizeCLI.STRING, allowNull: false }
      });
    }

    if (!existingTables.includes(metadataAlteredTable)) {
      await queryInterface.createTable(metadataAlteredTable, {
        tableName: { type: SequelizeCLI.STRING, allowNull: false },
        columnName: { type: SequelizeCLI.STRING, allowNull: false },
        oldType: { type: SequelizeCLI.STRING },
        newType: { type: SequelizeCLI.STRING }
      });
    }

    for (const modelName of modelKeys) {
      const transaction = await queryInterface.sequelize.transaction();
      try {
        const model = models[modelName];
        const tableName = model.getTableName ? model.getTableName() : model.tableName;

        if (!tableName) {
          console.warn(`Skipping model with no table: ${modelName}`);
          await transaction.rollback();
          continue;
        }

        console.log(`🚀 🚀 🚀 🚀 Migrating model: ${modelName}`);
        const tableDefinition = await queryInterface.describeTable(tableName, { transaction });
        const skipColumns = ['id', 'createdAt', 'updatedAt', 'deletedAt'];
        const modelAttrs = model.rawAttributes;
        const addedColumns = [];
        const alteredColumns = [];

        for (const columnName in modelAttrs) {
          try {
            if (skipColumns.includes(columnName)) continue;

            const { fieldName, _modelAttribute, ...modelCol } = modelAttrs[columnName];
            const sanitized = sanitizeAttribute(modelCol);
            const dbCol = tableDefinition[columnName];

            // Case 1: Add missing columns
            if (!dbCol) {
              console.log(`➕ ➕ ➕ ➕ Adding column '${columnName}'`);
              await queryInterface.addColumn(tableName, columnName, sanitized, { transaction });
              addedColumns.push({ tableName, columnName });
              continue;
            }

            // Case 2: Detect changes in type or constraints
            let modelType = getSequelizeColumnType(sanitized).toLowerCase();
            const dbType = dbCol.type.toLowerCase();

            // const needsChange = dbCol.allowNull !== sanitized.allowNull || dbCol.defaultValue !== sanitized.defaultValue || !dbType.includes(modelType);

            //******************************************* */
            // Normalize types and defaults for intelligent comparison
            const normalize = str => str?.toLowerCase?.().trim?.();

            const dbColDefaultValue = dbCol.defaultValue ?? null;
            const modelDefaultValue = sanitized.hasOwnProperty('defaultValue') ? sanitized.defaultValue : null;

            const isDefaultValueEqual = dbColDefaultValue === modelDefaultValue;
            const isAllowNullEqual = dbCol.allowNull === sanitized.allowNull;

            const normalizedDbType = normalize(dbCol.type);
            const normalizedModelType = normalize(getSequelizeColumnType(sanitized));

            const isTextTypeMatch = dbType === 'text' && modelType === 'text';
            const isStringMatch = dbType.startsWith('character varying') && modelType === 'string';
            const isIntegerMatch = normalizedDbType === 'integer' && normalizedModelType === 'integer';

            const isTypeEqual = isTextTypeMatch || isStringMatch || isIntegerMatch || normalizedDbType?.includes(normalizedModelType) || normalizedModelType?.includes(normalizedDbType);

            // Final decision to alter
            const needsChange = !isDefaultValueEqual || !isAllowNullEqual || !isTypeEqual;
            if (dbCol.primaryKey === true) {
              continue;
            }

            //*********************************************** */

            console.log(`🛠 Alter check: '${columnName}' in '${tableName}'`);
            console.log(`🛠 🛠  Need Change: '${needsChange}' in '${tableName}'`);

            if (needsChange) {
              console.log('Column in Database  ', dbCol);
              console.log('Column in Codebase--Model  ', sanitized);
              console.log(`🛠 🛠 🛠 🛠 🛠 🛠 🛠 Changing : '${columnName}' in '${tableName}'`);

              // Safe cast check only for integer
              if (modelType === 'integer' && !dbType.includes('int')) {
                const unsafeQuery = `
                SELECT "${columnName}" FROM "${tableName}"
                WHERE "${columnName}" IS NOT NULL AND "${columnName}" !~ '^[0-9]+$'
                LIMIT 1;
              `;

                const [results] = await queryInterface.sequelize.query(unsafeQuery, { transaction });

                if (results.length > 0) {
                  console.warn(`⚠️ Skipping column '${columnName}' – unsafe to cast to INTEGER`);
                  continue;
                }

                const alterQuery = `
                ALTER TABLE "${tableName}"
                ALTER COLUMN "${columnName}" TYPE INTEGER USING "${columnName}"::INTEGER;
              `;
                await queryInterface.sequelize.query(alterQuery, { transaction });

                alteredColumns.push({
                  tableName,
                  columnName,
                  oldType: dbCol.type,
                  newType: 'INTEGER'
                });
                continue;
              }

              //******************************************** */

              if (modelType === 'string' || modelType === 'text') {
                if (dbType === 'integer') {
                  if (modelType === 'string') modelType = 'VARCHAR';
                  const alterQuery = `
                  ALTER TABLE "${tableName}"
                  ALTER COLUMN "${columnName}" TYPE ${modelType.toUpperCase()} USING "${columnName}"::${modelType.toUpperCase()},
                  ALTER COLUMN "${columnName}" ${sanitized.allowNull === false ? 'SET NOT NULL' : 'DROP NOT NULL'};
                `;
                  await queryInterface.sequelize.query(alterQuery, { transaction });

                  alteredColumns.push({
                    tableName,
                    columnName,
                    oldType: dbCol.type,
                    newType: modelType.toUpperCase()
                  });
                  continue;
                }
              }

              if ((modelType === 'string' || modelType === 'text') && dbType === 'boolean') {
                const alterQuery = `
                ALTER TABLE "${tableName}"
                ALTER COLUMN "${columnName}" TYPE ${modelType.toUpperCase()} USING "${columnName}"::${modelType.toUpperCase()},
                ALTER COLUMN "${columnName}" ${sanitized.allowNull === false ? 'SET NOT NULL' : 'DROP NOT NULL'};
              `;
                await queryInterface.sequelize.query(alterQuery, { transaction });

                alteredColumns.push({
                  tableName,
                  columnName,
                  oldType: dbCol.type,
                  newType: modelType.toUpperCase()
                });
                continue;
              }

              // ************************************************** */

              // Default fallback for other safe type changes
              console.log(`⚙️ ⚙️ <<<-- Changing column -->>>> '${columnName}'`);
              await queryInterface.changeColumn(tableName, columnName, sanitized, { transaction });
              alteredColumns.push({
                tableName,
                columnName,
                oldType: dbCol.type,
                newType: modelType.toUpperCase()
              });
            }
          } catch (err) {
            // console.error(`❌ Error altering '${columnName}' in '${tableName}':`, err.message);
            errorLog.push({ model: modelName, table: tableName, column: columnName, message: err.message });
            continue;
          }
        }

        if (addedColumns.length > 0) {
          await queryInterface.bulkInsert(metadataAddedTable, addedColumns, { transaction });
        }
        if (alteredColumns.length > 0) {
          await queryInterface.bulkInsert(metadataAlteredTable, alteredColumns, { transaction });
        }

        await transaction.commit();
        console.log(`✅ Completed migration for model: ${modelName}`);
        // break;
      } catch (error) {
        console.error(`❌ Failed migration for model '${modelName}':`, error.message);
        errorLog.push({ model: modelName, table: null, column: null, message: error.message });
        await transaction.rollback();
        continue;
      }
    }

    if (errorLog.length > 0) {
      console.log('\n\n\n\n\n\n\n\n🛑 🛑 🛑 ❌ ❌ ❌ Summary of Column Errors Encountered During Migration: ❌ ❌ ❌ 🛑 🛑 🛑 \n\n\n\n ');
      errorLog.forEach(err => {
        console.log(`➡️  Model: ${err.model}, Column: ${err.column}, Table: ${err.table}, Error: ${err.message}`);
      });
    } else {
      console.log('✅ ✅ ✅ ✅ ✅ ✅ All models migrated successfully! ✅ ✅ ✅ ✅ ✅ ✅');
    }
    // });
  },

  async down(queryInterface, Sequelize) {
    let added = [];
    let altered = [];

    try {
      [added] = await queryInterface.sequelize.query(`SELECT tableName, columnName FROM ${metadataAddedTable}`);
    } catch (err) {
      console.warn(`⚠️ Table '${metadataAddedTable}' not found. Skipping added-column rollback.`);
    }

    try {
      [altered] = await queryInterface.sequelize.query(`SELECT tableName, columnName FROM ${metadataAlteredTable}`);
    } catch (err) {
      console.warn(`⚠️ Table '${metadataAlteredTable}' not found. Skipping altered-column rollback.`);
    }

    for (const { tableName, columnName } of added) {
      try {
        await queryInterface.removeColumn(tableName, columnName);
      } catch (err) {
        console.error(`❌ Failed to remove column '${columnName}' from '${tableName}':`, err);
      }
    }

    if (added.length > 0) await queryInterface.dropTable(metadataAddedTable);
    if (altered.length > 0) await queryInterface.dropTable(metadataAlteredTable);

    console.log('🔁 Down migration completed.');
  }
};
