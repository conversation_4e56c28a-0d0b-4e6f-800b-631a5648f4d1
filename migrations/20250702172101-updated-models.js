'use strict';

const db = require('../app/models');
const models = require('../app/models');
const { sequelize, type } = models;

const metadataAddedTable = 'migration_columns_added';
const metadataAlteredTable = 'migration_columns_altered';

const modelKeys = Object.keys(models).filter(key => key !== 'sequelize' && key !== 'Sequelize');

function sanitizeAttribute(attr) {
  const allowedKeys = ['type', 'allowNull', 'defaultValue', 'unique', 'references'];
  const sanitized = {};
  for (const key of allowedKeys) {
    if (attr[key] !== undefined) {
      sanitized[key] = attr[key];
    }
  }
  return sanitized;
}

function getSequelizeColumnType(attr) {
  return attr.type?.key || attr.type?.toString() || '';
}

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, SequelizeCLI) {
    const errorLog = [];
    const existingTables = await queryInterface.showAllTables();

    // Create metadata tables if they don't exist
    if (!existingTables.includes(metadataAddedTable)) {
      await queryInterface.createTable(metadataAddedTable, {
        tableName: { type: SequelizeCLI.STRING, allowNull: false },
        columnName: { type: SequelizeCLI.STRING, allowNull: false }
      });
    }

    if (!existingTables.includes(metadataAlteredTable)) {
      await queryInterface.createTable(metadataAlteredTable, {
        tableName: { type: SequelizeCLI.STRING, allowNull: false },
        columnName: { type: SequelizeCLI.STRING, allowNull: false },
        oldType: { type: SequelizeCLI.STRING },
        newType: { type: SequelizeCLI.STRING }
      });
    }

    for (const modelName of modelKeys) {
      let transaction = await queryInterface.sequelize.transaction();
      let shouldRetryModel = false;

      try {
        const model = models[modelName];
        const tableName = model.getTableName ? model.getTableName() : model.tableName;

        // 1. Get all foreign keys in the database for the table
        const [dbForeignKeys] = await queryInterface.sequelize.query(`
          SELECT
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS referenced_table,
            ccu.column_name AS referenced_column
          FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
          WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = '${tableName}';
        `, { transaction });

        // 2. Get Sequelize associations (e.g., belongsTo, hasMany)
        const definedAssociations = Object.values(model.associations || {});
        const definedForeignKeys = new Set(
          definedAssociations.map(assoc => assoc.foreignKey).filter(Boolean)
        );

        // 3. Drop FKs in DB but not in model associations
        for (const fk of dbForeignKeys) {
          const fkColumn = fk.column_name;
          const constraintName = fk.constraint_name;

          if (!definedForeignKeys.has(fkColumn)) {
            console.warn(`⚠️ Dropping unused foreign key '${constraintName}' (column '${fkColumn}') on table '${tableName}'`);
            try {
              await queryInterface.removeConstraint(tableName, constraintName, { transaction });
            } catch (err) {
              console.error(`❌ Error removing constraint '${constraintName}': ${err.message}`);
              errorLog.push({ model: modelName, table: tableName, column: fkColumn, message: err.message });
            }
          }
        }

        if (!tableName) {
          console.warn(`Skipping model with no table: ${modelName}`);
          await transaction.rollback();
          continue;
        }

        console.log(`🚀 🚀 🚀 🚀 Migrating model: ${modelName}`);
        const tableDefinition = await queryInterface.describeTable(tableName, { transaction });
        const skipColumns = ['id', 'createdAt', 'updatedAt', 'deletedAt'];
        const modelAttrs = model.rawAttributes;
        const addedColumns = [];
        const alteredColumns = [];

        // Process columns
        for (const columnName in modelAttrs) {
          try {
            if (skipColumns.includes(columnName)) continue;

            const { fieldName, _modelAttribute, ...modelCol } = modelAttrs[columnName];
            const sanitized = sanitizeAttribute(modelCol);
            const dbCol = tableDefinition[columnName];

            // Case 1: Add missing columns
            if (!dbCol) {
              console.log(`➕ ➕ ➕ ➕ Adding column '${columnName}'`);
              await queryInterface.addColumn(tableName, columnName, sanitized, { transaction });
              addedColumns.push({ tableName, columnName });
              continue;
            }

            // Case 2: Detect changes in type or constraints
            let modelType = getSequelizeColumnType(sanitized).toLowerCase();
            const dbType = dbCol.type.toLowerCase();

            // Normalize types and defaults for intelligent comparison
            const normalize = str => str?.toLowerCase?.().trim?.();

            const dbColDefaultValue = dbCol.defaultValue ?? null;
            const modelDefaultValue = sanitized.hasOwnProperty('defaultValue') ? sanitized.defaultValue : null;

            const isDefaultValueEqual = dbColDefaultValue === modelDefaultValue;
            const isAllowNullEqual = dbCol.allowNull === sanitized.allowNull;

            const normalizedDbType = normalize(dbCol.type);
            const normalizedModelType = normalize(getSequelizeColumnType(sanitized));

            const isTextTypeMatch = dbType === 'text' && modelType === 'text';
            const isStringMatch = dbType.startsWith('character varying') && modelType === 'string';
            const isIntegerMatch = normalizedDbType === 'integer' && normalizedModelType === 'integer';

            const isTypeEqual = isTextTypeMatch || isStringMatch || isIntegerMatch || normalizedDbType?.includes(normalizedModelType) || normalizedModelType?.includes(normalizedDbType);

            // Final decision to alter
            const needsChange = !isDefaultValueEqual || !isAllowNullEqual || !isTypeEqual;
            if (dbCol.primaryKey === true) {
              continue;
            }

            console.log(`🛠 Alter check: '${columnName}' in '${tableName}'`);
            console.log(`🛠 🛠  Need Change: '${needsChange}' in '${tableName}'`);

            if (needsChange) {
              console.log('Column in Database  ', dbCol);
              console.log('Column in Codebase--Model  ', sanitized);
              console.log(`🛠 🛠 🛠 🛠 🛠 🛠 🛠 Changing : '${columnName}' in '${tableName}'`);

              // Safe cast check only for integer
              if (modelType === 'integer' && !dbType.includes('int')) {
                const unsafeQuery = `
                SELECT "${columnName}" FROM "${tableName}"
                WHERE "${columnName}" IS NOT NULL AND "${columnName}" !~ '^[0-9]+$'
                LIMIT 1;
              `;

                const [results] = await queryInterface.sequelize.query(unsafeQuery, { transaction });

                if (results.length > 0) {
                  console.warn(`⚠️ Skipping column '${columnName}' – unsafe to cast to INTEGER`);
                  continue;
                }

                const alterQuery = `
                ALTER TABLE "${tableName}"
                ALTER COLUMN "${columnName}" TYPE INTEGER USING "${columnName}"::INTEGER;
              `;
                await queryInterface.sequelize.query(alterQuery, { transaction });

                alteredColumns.push({
                  tableName,
                  columnName,
                  oldType: dbCol.type,
                  newType: 'INTEGER'
                });
                continue;
              }

              if (modelType === 'string' || modelType === 'text') {
                if (dbType === 'integer') {
                  if (modelType === 'string') modelType = 'VARCHAR';
                  const alterQuery = `
                  ALTER TABLE "${tableName}"
                  ALTER COLUMN "${columnName}" TYPE ${modelType.toUpperCase()} USING "${columnName}"::${modelType.toUpperCase()},
                  ALTER COLUMN "${columnName}" ${sanitized.allowNull === false ? 'SET NOT NULL' : 'DROP NOT NULL'};
                `;
                  await queryInterface.sequelize.query(alterQuery, { transaction });

                  alteredColumns.push({
                    tableName,
                    columnName,
                    oldType: dbCol.type,
                    newType: modelType.toUpperCase()
                  });
                  continue;
                }
              }

              if ((modelType === 'string' || modelType === 'text') && dbType === 'boolean') {
                const alterQuery = `
                ALTER TABLE "${tableName}"
                ALTER COLUMN "${columnName}" TYPE ${modelType.toUpperCase()} USING "${columnName}"::${modelType.toUpperCase()},
                ALTER COLUMN "${columnName}" ${sanitized.allowNull === false ? 'SET NOT NULL' : 'DROP NOT NULL'};
              `;
                await queryInterface.sequelize.query(alterQuery, { transaction });

                alteredColumns.push({
                  tableName,
                  columnName,
                  oldType: dbCol.type,
                  newType: modelType.toUpperCase()
                });
                continue;
              }

              // Default fallback for other safe type changes
              console.log(`⚙️ ⚙️ <<<-- Changing column -->>>> '${columnName}'`);
              await queryInterface.changeColumn(tableName, columnName, sanitized, { transaction });
              alteredColumns.push({
                tableName,
                columnName,
                oldType: dbCol.type,
                newType: modelType.toUpperCase()
              });
            }
          } catch (err) {
            errorLog.push({ model: modelName, table: tableName, column: columnName, message: err.message });
            continue;
          }
        }

        // Save metadata for added/altered columns
        if (addedColumns.length > 0) {
          await queryInterface.bulkInsert(metadataAddedTable, addedColumns, { transaction });
        }
        if (alteredColumns.length > 0) {
          await queryInterface.bulkInsert(metadataAlteredTable, alteredColumns, { transaction });
        }
       //unique constraint
       const modelUniqueNames = new Set();
        const modelUniqueCols  = [];

        if (model.options?.uniqueKeys) {
          for (const [uName] of Object.entries(model.options.uniqueKeys)) {
            modelUniqueNames.add(uName);
          }
        }
        for (const [attr, def] of Object.entries(model.rawAttributes)) {
          if (def.unique === true) modelUniqueCols.push(attr);
          else if (typeof def.unique === 'string') modelUniqueNames.add(def.unique);
        }

        // 2) collect DB uniques
        const [dbUniques] = await queryInterface.sequelize.query(
          `
          SELECT
            tc.constraint_name,
            STRING_AGG(kcu.column_name, ',' ORDER BY kcu.ordinal_position) AS cols
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
               ON tc.constraint_name = kcu.constraint_name
          WHERE tc.constraint_type = 'UNIQUE'
            AND tc.table_name   = '${tableName}'
            AND tc.table_schema = 'public'
          GROUP BY tc.constraint_name;
        `,
          { transaction }
        );

        // 3) drop orphans
        for (const u of dbUniques) {
          const dbName = u.constraint_name;
          const dbCols = u.cols.split(',');
          const keep =
              modelUniqueNames.has(dbName) ||
              (dbCols.length === 1 && modelUniqueCols.includes(dbCols[0]));
          if (!keep) {
            console.warn(`⚠️  Dropping orphan UNIQUE '${dbName}' on '${tableName}'`);
            await queryInterface.removeConstraint(tableName, dbName, { transaction });
          }
        }

        // 4) add missing composite uniques (your original logic + duplicate prune)
        if (model.options?.uniqueKeys) {
          for (const [keyName, constraint] of Object.entries(model.options.uniqueKeys)) {
            const fields = constraint.fields;
            const part   = fields.map(f => `"${f}"`).join(', ');

            // prune duplicates
            await queryInterface.sequelize.query(
              `
              DELETE FROM "${tableName}"
              WHERE id IN (
                SELECT id FROM (
                  SELECT id,
                         ROW_NUMBER() OVER (
                           PARTITION BY ${part}
                           ORDER BY COALESCE("createdAt", NOW()) ASC
                         ) AS rn
                  FROM "${tableName}"
                ) t WHERE t.rn > 1
              );
            `,
              { transaction }
            );

            // add if missing
            const [exists] = await queryInterface.sequelize.query(
              `SELECT 1 FROM pg_constraint WHERE conname = '${keyName}';`,
              { transaction }
            );
            if (!exists.length) {
              await queryInterface.addConstraint(tableName, {
                type: 'unique',
                name: keyName,
                fields,
                transaction
              });
            }
          }
        }

        // Only commit if transaction hasn't been rolled back

        await transaction.commit();
      } catch (error) {
        console.error(`❌ Failed migration for model '${modelName}':`, error.message);
        errorLog.push({ model: modelName, table: null, column: null, message: error.message });

        // Only rollback if transaction hasn't been finished

        await transaction.rollback();

        continue;
      }
    }

    if (errorLog.length > 0) {
      console.log('\n\n\n\n\n\n\n\n🛑 🛑 🛑 ❌ ❌ ❌ Summary of Column Errors Encountered During Migration: ❌ ❌ ❌ 🛑 🛑 🛑 \n\n\n\n ');
      errorLog.forEach(err => {
        console.log(`➡️  Model: ${err.model}, Column: ${err.column}, Table: ${err.table}, Error: ${err.message}`);
      });
    } else {
      console.log('✅ ✅ ✅ ✅ ✅ ✅ All models migrated successfully! ✅ ✅ ✅ ✅ ✅ ✅');
    }
  },

  async down(queryInterface, Sequelize) {
    let added = [];
    let altered = [];

    try {
      [added] = await queryInterface.sequelize.query(`SELECT tableName, columnName FROM ${metadataAddedTable}`);
    } catch (err) {
      console.warn(`⚠️ Table '${metadataAddedTable}' not found. Skipping added-column rollback.`);
    }

    try {
      [altered] = await queryInterface.sequelize.query(`SELECT tableName, columnName FROM ${metadataAlteredTable}`);
    } catch (err) {
      console.warn(`⚠️ Table '${metadataAlteredTable}' not found. Skipping altered-column rollback.`);
    }

    for (const { tableName, columnName } of added) {
      try {
        await queryInterface.removeColumn(tableName, columnName);
      } catch (err) {
        console.error(`❌ Failed to remove column '${columnName}' from '${tableName}':`, err);
      }
    }

    if (added.length > 0) await queryInterface.dropTable(metadataAddedTable);
    if (altered.length > 0) await queryInterface.dropTable(metadataAlteredTable);

    console.log('🔁 Down migration completed.');
  }
};
