# Use the official Node.js 20 Alpine image as the base image
FROM node:20

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json files
COPY package*.json ./

# Install dependencies
RUN npm install -f

RUN npm install -g npm@latest && rm -rf ~/.cache/Cypress

# Copy the rest of the application code
COPY . .

# # ❌ Clean Cypress cache and global npm junk
# RUN rm -rf ~/.cache/Cypress \
#            /usr/local/lib/node_modules/npm \
#            /usr/lib/node_modules/npm

# # Clean up Cypress cache only
# RUN rm -rf ~/.cache/Cypress

RUN npm install -g npm@latest && rm -rf ~/.cache/Cypress

# Expose port 3000
EXPOSE 3000

# Start the backend server
CMD ["node", "server.js"]
