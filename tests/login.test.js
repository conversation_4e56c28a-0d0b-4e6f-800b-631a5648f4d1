const request = require("supertest");
const app = require("../server"); // Adjust the path to your server file

describe("Login API", () => {
  it("should log in a user successfully", async () => {
    const response = await request(app).post("/api/v1/auth/login").send({
      email: "<EMAIL>",
      password: "V<PERSON><PERSON>@123",
      deviceId: "1234567789675",
      deviceToken: "3455788",
      deviceType: "os",
    });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty("token"); // Adjust based on actual response
  });
});
