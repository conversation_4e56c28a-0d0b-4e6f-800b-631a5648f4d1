<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Create Request Form</title>
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
   <style>.btn-primary{
   background-color: rgb(19 38 80);
   }
   .preview-container {
    display: flex;
    flex-wrap: wrap;
    height: 48px;
    width: 80px;
    gap: 15px;
    margin-top: 20px;
}

   </style> 
</head>
<body>
  <% if (!form_repo_data.content) { %>
    <div style="text-align: center; font-size: 16px; font-weight: bold; padding-top: 20px; font-family: poppins; font-size: 22px;" >
      <p>Form Repository Data not found for the specified customer</p>
    </div>
    <% } else { %>
  <div class="container">
    <!-- <div class="card-header"><%=form_repo_data%></div> -->
    <div class="card my-4">
      <div class="card-header w-100" 
      style="background-color: <%= form_repo_data?.content?.headerBackgroundColor ? form_repo_data.content.headerBackgroundColor : '#FFFFFF' %>;">
                 <div class="row">
      <h2 class="w-50 align-middle mt-2"
      style="color: <%= form_repo_data?.content?.headerTextColor ? form_repo_data.content.headerTextColor : '#000000' %>"
      >
      Data Subject Request Form
    </h2>
    <div class="w-50 ">
      <img alt="Selected file" src="<%= form_repo_data?.content?.logoUrl %>" class="pt-0 mt-0 w-25 h-12" style="float: right">
    </div>
  </div>
  </div>
  <div class="card-body">
    <p class="text-align-center">
      <%=form_repo_data?.content?.paragraphContent%>
    </p>
    <form id="createRequestForm" class="font-normal">
      
        <input type="hidden" name="customer_id" value="<%= customer_id %>" />
      <!-- Is Data Subject -->
      <div class="form-group">
        <label class="form-label">Are you the Data Subject? <span class="form-required">*</span></label>
        <div style="display: flex; align-items: center; gap: 10px;">
          <div>
            <input type="radio" id="yes" name="is_data_subject" value="true" required>
            <label for="yes">Yes</label>
          </div>
          <div>
            <input type="radio" id="no" name="is_data_subject" value="false">
            <label for="no">No</label>
          </div>
        </div>
      </div>
      
      <!-- Request Type -->
      <div class="form-group">
        <label class="form-label">Request Type <span class="form-required">*</span></label>
        <select name="dsr_request_type_id" class="form-control" required>
          <option value="">Select a request type</option>
          <% request_type.forEach(item => { %>
            <option value="<%= item.id %>"><%= item.flowtype %></option>
          <% }); %>
        </select>
      </div>
      
      <!-- Request Description -->
      <div class="form-group">
        <label class="form-label">Request Description</label>
        <textarea name="description" class="form-control" placeholder="Describe your request"></textarea>
      </div>
      
      <!-- Relationship -->
      <div class="form-group">
        <label class="form-label" id="relationship_label">What is your relationship with us?* <span class="form-required">*</span></label>
        <select name="relationship" class="form-control" required>
          <option value="">Select your relationship</option>
          <option value="CUSTOMER">Customer</option>
          <option value="FORMER_CUSTOMER">Former Customer</option>
          <option value="CONTRACTOR">Contractor</option>
          <option value="EMPLOYEE">Employee</option>
          <option value="JOB_APPLICANT">Job Applicant</option>
          <option value="OTHER">Other</option>
        </select>
      </div>
      
      <!-- Business Unit -->
      <div class="form-group">
        <label class="form-label">Business Unit <span class="form-required">*</span></label>
        <select name="business_unit" class="form-control" required>
          <option value="">Select your business</option>
          <% units.forEach(item => { %>
            <option value="<%= item.id %>"><%= item.name %></option>
          <% }); %>
        </select>
      </div>
      
      <!-- Personal Info -->
      <div class="form-group">
        <label class="form-label">First Name <span class="form-required">*</span></label>
        <input type="text" name="first_name" class="form-control" placeholder="John" required>
      </div>
      
      <div class="form-group">
        <label class="form-label">Last Name <span class="form-required">*</span></label>
        <input type="text" name="last_name" class="form-control" placeholder="Doe" required>
      </div>
      
      <div class="form-group">
        <label class="form-label">Email <span class="form-required">*</span></label>
        <input type="email" name="email" class="form-control" placeholder="<EMAIL>" required>
      </div>
      
      <div class="form-group">
        <label class="form-label">Phone Number <span class="form-required">*</span></label>
        <input type="tel" name="phone_no" class="form-control" placeholder="****** 567 8900" required>
      </div>
      
      <div class="form-group">
        <label class="form-label">Date of Birth <span class="form-required">*</span></label>
        <input type="date" name="dob" class="form-control" required>
      </div>
      
      <!-- Unique Identifier -->
      <div class="form-group">
        <label class="form-label">Unique Identifier Type <span class="form-required">*</span></label>
        <select name="unique_identification_type" class="form-control" required>
          <option value="">Select identifier type</option>
          <option value="PASSPORT">Passport</option>
          <option value="DRIVING_LICENSE">Driver's License</option>
          <option value="NATIONAL_ID">National ID</option>
          <option value="OTHER">Others</option>
        </select>
      </div>
      
      <div class="form-group">
        <label class="form-label">Unique Identifier Number <span class="form-required">*</span></label>
        <input type="text" name="unique_identification_number" class="form-control" placeholder="Enter identifier number" required>
      </div>
      
      <!-- Address -->
      <div class="form-group">
        <label class="form-label">House Number & Street <span class="form-required">*</span></label>
        <input type="text" name="address_1" class="form-control" placeholder="123 Main St" required>
      </div>
      
      <div class="form-group">
        <label class="form-label">Address Line 2 <span class="form-required">*</span></label>
        <input type="text" name="address_2" class="form-control" placeholder="Apt 4B">
      </div>
      
      <div class="form-group">
        <label class="form-label">City <span class="form-required">*</span></label>
        <input type="text" name="city" class="form-control" placeholder="New York" required>
      </div>
      
      <div class="form-group">
        <label class="form-label">Country <span class="form-required">*</span></label>
        <select name="country_id" id="country_id" class="form-control" required>
          <option value="">Select country</option>
          <% country.forEach(item => { %>
            <option value="<%= item.id %>"><%= item.country_name %></option>
          <% }); %>          
        </select>
      </div>
      
      <div class="form-group">
        <label class="form-label">State <span class="form-required">*</span></label>
        <select name="state_id" id="state_id" class="form-control" required>
          <option value="">Select state</option>
        </select>
      </div>
      
      <div class="form-group">
        <label class="form-label">Postcode / Zip <span class="form-required">*</span></label>
        <input type="text" name="postal_code" class="form-control" placeholder="10001" required>
      </div>
      
      <!-- Add second address -->
      <div class="space-y-4" id="secondAddressDiv" style="display: none;">
        <label class="text-lg font-semibold"><b>Additional Address Details</b></label>
        
        <div class="form-group">
          <label for="second_address_1">
            Data Subject House Number & Street
            <span class="text-red-500">*</span>
          </label>
          <input type="text" id="second_address_1" name="second_address_1" placeholder="123 Main St" class="form-control" value="">
        </div>
      
        <div class="form-group">
          <label for="second_address_2">Address Line 2</label>
          <input type="text" id="second_address_2" name="second_address_2" placeholder="Apt 4B" class="form-control">
        </div>
      
        <div class="row">
          <div class="form-group col-md-6">
            <label for="second_city">
              Town / City
              <span class="text-red-500">*</span>
            </label>
            <input type="text" id="second_city" name="second_city" placeholder="New York" class="form-control">
          </div>
      
          <div class="form-group col-md-6">
            <label for="second_country_id">
              Country
              <span class="text-red-500">*</span>
            </label>
            <select id="second_country_id" name="second_country_id" class="form-control">
              <option value="" disabled selected>Select country</option>
              <% country.forEach(item => { %>
                <option value="<%= item.id %>"><%= item.country_name %></option>
              <% }); %>  
            </select>
          </div>
        </div>
      
        <div class="row">
          <div class="form-group col-md-6">
            <label for="second_state_id">
              State
              <span class="text-red-500">*</span>
            </label>
            <select id="second_state_id" name="second_state_id" class="form-control">
              <option value="" disabled selected>Select state</option>             
            </select>
          </div>
      
          <div class="form-group col-md-6">
            <label for="second_postal_code">
              Postcode / Zip
              <span class="text-red-500">*</span>
            </label>
            <input type="number" id="second_postal_code" name="second_postal_code" placeholder="10001" class="form-control">
          </div>
        </div>
      </div>

      <div id="joint_party" style="display: none;">
        <!-- Joint Party Details 1 -->
        <div class="p-4 space-y-4">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-2">
              <button type="button" class="btn btn-outline-secondary btn-sm rounded-circle" onclick="handleRemoveJointParty(0)">-</button>
              <span class="h5">Joint Party Details</span>
            </div>
            <hr class="w-75" style="background-color: #E2E2E2;">
          </div>
          
          <!-- First Name and Last Name -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="joint_party_details_first_name" class="form-label">First Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="joint_party_details_first_name" name="joint_party_details_first_name" placeholder="John" >
            </div>
            <div class="col-md-6 form-group">
              <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="joint_party_details_last_name" name="joint_party_details_last_name" placeholder="Doe" >
            </div>
          </div>
      
          <!-- Email and Phone Number -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
              <input type="email" class="form-control" id="joint_party_details_email" name="joint_party_details_email" placeholder="<EMAIL>" >
            </div>
            <div class="col-md-6 form-group">
              <label for="phone_no" class="form-label">Phone Number <span class="text-danger">*</span></label>
              <input type="tel" class="form-control" id="joint_party_details_phone_no" name="joint_party_details_phone_no" placeholder="****** 567 8900" >
            </div>
          </div>
      
          <!-- Date of Birth -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="dob" class="form-label">Date of Birth <span class="text-danger">*</span></label>
              <input type="date" class="form-control" id="joint_party_details_dob" name="joint_party_details_dob" >
            </div>
          </div>
      
          <!-- Unique Identifier Type and Number -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="unique_identification_type" class="form-label">Unique Identifier Type <span class="text-danger">*</span></label>
              <select class="form-control" id="joint_party_details_unique_identification_type" name="joint_party_details_unique_identification_type" >
                <option value="">Select identifier type</option>
                <option value="PASSPORT">Passport</option>
                <option value="DRIVING_LICENSE">Driver's License Number</option>
                <option value="NATIONAL_ID">National Identification Number</option>
                <option value="OTHER">Others</option>
              </select>
            </div>
            <div class="col-md-6 form-group">
              <label for="unique_identification_number" class="form-label">Unique Identifier Number <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="joint_party_details_unique_identification_number" name="joint_party_details_unique_identification_number" placeholder="Enter identifier number" >
            </div>
          </div>
      
          <!-- Address 1 and Address 2 -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="address_1" class="form-label">Data Subject House Number & Street <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="joint_party_details_address_1" name="joint_party_details_address_1" placeholder="123 Main St" >
            </div>
            <div class="col-md-6 form-group">
              <label for="address_2" class="form-label">Address Line 2</label>
              <input type="text" class="form-control" id="joint_party_details_address_2" name="joint_party_details_address_2" placeholder="Apt 4B">
            </div>
          </div>
      
          <!-- City and Country -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="city" class="form-label">Town / City <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="joint_party_details_city" name="joint_party_details_city" placeholder="New York" >
            </div>
            <div class="col-md-6 form-group">
              <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
              <select class="form-control" id="joint_party_details_country_id" name="joint_party_details_country_id" >
                <option value="">Select country</option>
                <% country.forEach(item => { %>
                  <option value="<%= item.id %>"><%= item.country_name %></option>
                <% }); %>   
              </select>
            </div>
          </div>
      
          <!-- State and Postal Code -->
          <div class="row g-3">
            <div class="col-md-6 form-group">
              <label for="state_id" class="form-label">State <span class="text-danger">*</span></label>
              <select class="form-control" id="joint_party_details_state_id" name="joint_party_details_state_id" >
                <option value="">Select state</option>
                
              </select>
            </div>
            <div class="col-md-6 form-group">
              <label for="postal_code" class="form-label">Postcode / Zip <span class="text-danger">*</span></label>
              <input type="number" class="form-control" id="joint_party_details_postal_code" name="joint_party_details_postal_code" placeholder="10001" >
            </div>
          </div>
        </div>
      </div>

      <div class="form-group" id="authority_div" style="display: none;">
        <label for="file-upload" class="form-label">
          Please upload a copy of the updated letter of Authority signed by the Data Subject within the last 6 months or provide a valid Power of Attorney document, if applicable.
          <span class="text-danger">*</span>
        </label>

        <!-- File input for multiple file types -->
        <input type="file" id="authorityInput" multiple accept="application/pdf,.doc,.docx,image/*">

        <button type="button" onclick="uploadAuthorityFiles()">Upload Files</button>

        <!-- Container for file previews -->
        <div class="preview-container" id="authorityPreviewContainer"></div>
      </div>
      

      <div class="form-group d-flex gap-2">
        <!-- First div with toggle action -->
        <div class="max-w-60 h-12 d-flex justify-content-center align-items-center cursor-pointer rounded-lg border border-info p-1" onclick="handleToggleSecondAddressClick()">
          <span id="toggleAddressText">
            <!-- This text would be dynamically updated based on the showSecondAddress state -->
            + Add Address
          </span>
        </div>
      
        <!-- Second div with "Add Joint Party" action -->
        <div class="mx-4 max-w-60 h-12 d-flex justify-content-center align-items-center cursor-pointer rounded-lg border border-info p-1" onclick="handleAddJointPartyClick()">
          <span id="addJointPartyText">+ Add Joint Party</span>
        </div>
      </div>

      <div class="form-group">
        <label for="dsr_return_preference" class="form-label">DSR Request Materials Return Preference</label>
        <div>
          <div class="form-check form-check-inline">
            <input type="radio" id="email" name="dsr_return_preference" value="EMAIL" class="form-check-input">
            <label for="email" class="form-check-label">Email</label>
          </div>
        
          <div class="form-check form-check-inline">
            <input type="radio" id="post" name="dsr_return_preference" value="POST" class="form-check-input">
            <label for="post" class="form-check-label">Post</label>
          </div>
        </div>
      </div>
      
      <!-- Internal Request -->
      <div class="form-group">
        <label class="form-label">[OFFICE USE ONLY] Is this an Internal Request? <span class="form-required">*</span></label>
        <div>
          <div class="form-check form-check-inline">
            <input type="radio" id="yes" name="is_internal_request" value="true" class="form-check-input">
            <label for="yes" class="form-check-label">Yes</label>
          </div>
          <div class="form-check form-check-inline">
            <input type="radio" id="no" name="is_internal_request" value="false" class="form-check-input">
            <label for="no" class="form-check-label">No</label>
          </div>
        </div>
      </div>

        <div class="form-group">
          <label for="file-upload" class="form-label">
            Please Upload your identification and/or Supporting document to progress with your DSR request.
          </label>

          <!-- File input for multiple file types -->
          <br>
          <input class="file-upload" type="file" id="fileInput" multiple accept="application/pdf,.doc,.docx,image/*">
          
          <button type="button" class="btn btn-info m-4" onclick="uploadFiles()">Upload Files</button>

          <!-- Container for file previews -->
          <div class="preview-container" id="previewContainer"></div>
        </div>
        <!-- reCAPTCHA widget -->
        <!-- <div class="w-100 form-group">
          <div class="g-recaptcha" data-sitekey="<%=site_key%>"></div>
        </div> -->
        <br />
      <!-- Submit Button -->
      <div class="w-100">
      <button type="submit" class="btn btn-primary float-right">Submit Request</button>
      </div>
      <br>
      <br>
      <br>
      <br>
      <div style="display: flex; justify-content: center; align-items: center; gap: 8px; padding: 16px; width: 100%;">
        <label>Powered by</label>
        <img alt="Selected file" src="https://dev.gotrust.tech/src/assets/gotrustTitle_light.svg" class="h-12 w-20 object-contain" />
      </div>
      
      
    </form>
  </div>
  </div>
    <div
    class="modal fade"
    id="alertModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="alertModalLabel"
    aria-hidden="true"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <div class="alert alert-success">
            <!-- <button
              type="button"
              class="close"
              data-dismiss="alert"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button> -->
            <strong>Success!</strong>
           
          </div>
          <p className="mb-6 text-center">
            Your Data Subject Request has been successfully submitted. Check your mail box to verify email ID. We will process your request after email verification
            and get back to you shortly.
          </p>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-dismiss="modal"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  </div>

  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- <script src="https://www.google.com/recaptcha/api.js?render=6LehlIkqAAAAAH-tNMvpHXoqJt9MBDAhNMN1pvrI"></script> -->
  <!-- <script src="https://www.google.com/recaptcha/api.js" async defer></script> -->
  <script>
    

    let identificationDoc = [];
    let letterOfAuthorityDoc = [];

    $(document).ready(function() {
      var myModal = new bootstrap.Modal(document.getElementById('alertModal'));
      const myForm = document.getElementById('createRequestForm');

      
      // disable future date for dob
      
     

        $('#createRequestForm').on('submit', function(event) {
            event.preventDefault(); // Prevent the default form submission
            const dobInput = $('input[name="dob"]').val();
        const dob = new Date(dobInput);
        const today = new Date();
        const age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();
        const dayDiff = today.getDate() - dob.getDate();
        if (age < 18 || (age === 18 && (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)))) {
          alert('You must be at least 18 years old.');
          return;
        }
            var formData = $(this).serialize();

            if ($('#second_address_1').val() === '') {
                // Remove the key-value pair for 'second_address_1' from the serialized form data
                formData = formData.replace(/(&?second_address_1=[^&]*)/, '');  // Remove 'second_address_1' entry
            }

            if ($('#second_address_2').val() === '') {
                formData = formData.replace(/(&?second_address_2=[^&]*)/, '');
            }

            if ($('#second_city').val() === '') {
                formData = formData.replace(/(&?second_city=[^&]*)/, '');
            }

            if ($('#second_postal_code').val() === '') {
                formData = formData.replace(/(&?second_postal_code=[^&]*)/, '');
            }

            if ($('#joint_party_details_first_name').val() === '') {
                formData = formData.replace(/(&?joint_party_details_first_name=[^&]*)/, '');  
            }

            if ($('#joint_party_details_last_name').val() === '') {
                formData = formData.replace(/(&?joint_party_details_last_name=[^&]*)/, '');  
            }

            if ($('#joint_party_details_email').val() === '') {
                formData = formData.replace(/(&?joint_party_details_email=[^&]*)/, '');  
            }

            if ($('#joint_party_details_phone_no').val() === '') {
                formData = formData.replace(/(&?joint_party_details_phone_no=[^&]*)/, '');  
            }

            if ($('#joint_party_details_dob').val() === '') {
                formData = formData.replace(/(&?joint_party_details_dob=[^&]*)/, '');  
            }

            if ($('#joint_party_details_unique_identification_type').val() === '') {
                formData = formData.replace(/(&?joint_party_details_unique_identification_type=[^&]*)/, '');  
            }

            if ($('#joint_party_details_unique_identification_number').val() === '') {
                formData = formData.replace(/(&?joint_party_details_unique_identification_number=[^&]*)/, '');  
            }

            if ($('#joint_party_details_address_1').val() === '') {
                formData = formData.replace(/(&?joint_party_details_address_1=[^&]*)/, '');  
            }

            if ($('#joint_party_details_address_2').val() === '') {
                formData = formData.replace(/(&?joint_party_details_address_2=[^&]*)/, '');  
            }

            if ($('#joint_party_details_city').val() === '') {
                formData = formData.replace(/(&?joint_party_details_city=[^&]*)/, '');  
            }

            if ($('#joint_party_details_state_id').val() === '') {
                formData = formData.replace(/(&?joint_party_details_state_id=[^&]*)/, '');  
            }

            if ($('#joint_party_details_postal_code').val() === '') {
                formData = formData.replace(/(&?joint_party_details_postal_code=[^&]*)/, '');  
            }

            if ($('#joint_party_details_country_id').val() === '') {
                formData = formData.replace(/(&?joint_party_details_country_id=[^&]*)/, '');  
            }

            if (identificationDoc.length > 0) {
                // Loop through identificationDoc and append each item to formData
                identificationDoc.forEach((doc, index) => {
                    // Append each item as a separate key-value pair
                    formData += `&identification_doc[${index}][original_name]=${encodeURIComponent(doc.original_name)}`;
                    formData += `&identification_doc[${index}][url]=${encodeURIComponent(doc.url)}`;
                });
            }
    
            if (letterOfAuthorityDoc.length > 0) {
                letterOfAuthorityDoc.forEach((doc, index) => {
                    formData += `&letter_of_authority_doc[${index}][original_name]=${encodeURIComponent(doc.original_name)}`;
                    formData += `&letter_of_authority_doc[${index}][url]=${encodeURIComponent(doc.url)}`;
                });
            }
            // If everything is valid, submit the form via AJAX
            $.ajax({
                type: 'POST',
                url: '<%= base_url %>/api/v1/dsr/guest-form-request',
                data: formData, // Serialize form data
                success: function(response) {
                    // Handle success (e.g., display a message) 
                    console.log(response)                   
                   // let jsonObject = JSON.parse(response);
                    if(response.message){
  
                        myForm.reset();
                        //grecaptcha.reset();
                        myModal.show();
                    }
                },
                error: function(err) {
                    // Handle error
                    console.log('error')
                    console.log(err)
                    let jsonObject = JSON.parse(err.responseText);
                    console.log(jsonObject)
                    if(jsonObject.result.error){
                        alert(jsonObject.result.error)
                    }                    
                   
                }
            });
        });

        // Email validation function
        function validateEmail(email) {
            const re = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
            return re.test(email);
        }

        $('#country_id').on('change', function() {
            var countryId = $(this).val();  // Get the selected country ID
            
            // Check if a country is selected
            if (countryId) {
                // Make the AJAX request to get the states for the selected country
                $.ajax({
                url: '<%= base_url %>/api/v1/state', // Your endpoint to get states for a country
                type: 'GET',
                data: { country_id: countryId },
                success: function(response) {
                    console.log(response.result.rows)
                    // Clear the current state options
                    $('#state_id').html('<option value="">Select state</option>');

                    // Check if the response contains states
                    if (response && response.result && response.result.rows && response.result.rows.length > 0) {
                    // Loop through the states and append them to the state dropdown
                    response.result.rows.forEach(function(state) {
                        $('#state_id').append('<option value="' + state.id + '">' + state.state_name + '</option>');
                    });
                    } else {
                    // If no states are found, show a placeholder message
                    $('#state_id').append('<option value="">No states available</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching states:', error);
                    // Show an error message in case of failure
                    $('#state_id').html('<option value="">Failed to load states</option>');
                }
                });
            } else {
                // If no country is selected, clear the state dropdown
                $('#state_id').html('<option value="">Select state</option>');
            }
        });

        $('#second_country_id').on('change', function() {
            var countryId = $(this).val();  // Get the selected country ID
            
            // Check if a country is selected
            if (countryId) {
                // Make the AJAX request to get the states for the selected country
                $.ajax({
                url: '<%= base_url %>/api/v1/state', // Your endpoint to get states for a country
                type: 'GET',
                data: { country_id: countryId },
                success: function(response) {
                    console.log(response.result.rows)
                    // Clear the current state options
                    $('#second_state_id').html('<option value="">Select state</option>');

                    // Check if the response contains states
                    if (response && response.result && response.result.rows && response.result.rows.length > 0) {
                    // Loop through the states and append them to the state dropdown
                    response.result.rows.forEach(function(state) {
                        $('#second_state_id').append('<option value="' + state.id + '">' + state.state_name + '</option>');
                    });
                    } else {
                    // If no states are found, show a placeholder message
                    $('#second_state_id').append('<option value="">No states available</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching states:', error);
                    // Show an error message in case of failure
                    $('#second_state_id').html('<option value="">Failed to load states</option>');
                }
                });
            } else {
                // If no country is selected, clear the state dropdown
                $('#second_state_id').html('<option value="">Select state</option>');
            }
        });

        $('#joint_party_details_country_id').on('change', function() {
            var countryId = $(this).val();  // Get the selected country ID
            
            // Check if a country is selected
            if (countryId) {
                // Make the AJAX request to get the states for the selected country
                $.ajax({
                url: '<%= base_url %>/api/v1/state', // Your endpoint to get states for a country
                type: 'GET',
                data: { country_id: countryId },
                success: function(response) {
                    console.log(response.result.rows)
                    // Clear the current state options
                    $('#joint_party_details_state_id').html('<option value="">Select state</option>');

                    // Check if the response contains states
                    if (response && response.result && response.result.rows && response.result.rows.length > 0) {
                    // Loop through the states and append them to the state dropdown
                    response.result.rows.forEach(function(state) {
                        $('#joint_party_details_state_id').append('<option value="' + state.id + '">' + state.state_name + '</option>');
                    });
                    } else {
                    // If no states are found, show a placeholder message
                    $('#joint_party_details_state_id').append('<option value="">No states available</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching states:', error);
                    // Show an error message in case of failure
                    $('#joint_party_details_state_id').html('<option value="">Failed to load states</option>');
                }
                });
            } else {
                // If no country is selected, clear the state dropdown
                $('#joint_party_details_state_id').html('<option value="">Select state</option>');
            }
        });

        //are you datasubject change condition 
          // Get the radio buttons and the authority div
          const yesRadio = document.getElementById('yes');
          const noRadio = document.getElementById('no');
          const authorityDiv = document.getElementById('authority_div');
          const relationshipLabel = document.getElementById('relationship_label');

          // Function to toggle the visibility of authority div
          function toggleAuthorityDiv() {
            if (noRadio.checked) {
              authorityDiv.style.display = 'block'; // Show authority_div if "No" is selected
              relationshipLabel.textContent = 'What is your relationship to the data subject?*';
            } else {
              authorityDiv.style.display = 'none'; // Hide authority_div if "Yes" is selected
              relationshipLabel.textContent = 'What is your relationship with us?*';
            }
          }

          // Attach event listeners to the radio buttons
          yesRadio.addEventListener('change', toggleAuthorityDiv);
          noRadio.addEventListener('change', toggleAuthorityDiv);

          // Initial check to set the state of the authority_div
          toggleAuthorityDiv();

    
    });

    function handleAddJointPartyClick() {
      // Toggle visibility of the joint party section
      var jointPartyDiv = document.getElementById('joint_party');
      var addJointPartyText = document.getElementById('addJointPartyText');

      if (jointPartyDiv.style.display === 'none') {
        jointPartyDiv.style.display = 'block';  // Show the joint party form
        addJointPartyText.textContent = '- Remove Joint Party';  // Change button text to '-'
      } else {
        jointPartyDiv.style.display = 'none';  // Hide the joint party form
        addJointPartyText.textContent = '+ Add Joint Party';  // Change button text to '+'
      }
    }


    function handleToggleSecondAddressClick() {
      // Get the toggle button and second address div
      const secondAddressDiv = document.getElementById('secondAddressDiv');
      const toggleAddressText = document.getElementById('toggleAddressText');

      // Toggle the visibility of the second address div
      if (secondAddressDiv.style.display === 'none') {
        secondAddressDiv.style.display = 'block';
        toggleAddressText.textContent = '- Remove Address';

        //$('#secondAddressDiv').html('<option value="">Select state</option>');
      } else {
        secondAddressDiv.style.display = 'none';
        toggleAddressText.textContent = '+ Add Address';
      }
    }
  

  //for file upload for identification
    let selectedFiles = [];

    // Handle file input change event
    document.getElementById('fileInput').addEventListener('change', function(event) {
        selectedFiles = event.target.files;
    });

    // Function to upload files via AJAX
    function uploadFiles() {
       
        if (selectedFiles.length === 0) {
            alert('No files selected for upload!');
            return;  
        }

        let formData = new FormData();

        // Append selected files to FormData
        for (let i = 0; i < selectedFiles.length; i++) {
            formData.append('files', selectedFiles[i]);
        }
        
        // Send files via AJAX
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/backend/api/v1/dsr/guest-upload-documents', true);
        xhr.onload = function() {
            if (xhr.status === 201) {
                let response = JSON.parse(xhr.responseText);
                if (response.success) {
                    identificationDoc = response.result;
                    // displayPreviews(response.result);
                } else {
                    alert('Failed to upload files');
                }
            } else {
                alert('Error uploading files');
            }
        };
        
        xhr.send(formData);
                
    }

    // Function to display file previews (images and documents)
    function displayPreviews(files) {
        const previewContainer = document.getElementById('previewContainer');
        previewContainer.innerHTML = ''; // Clear previous previews

        files.forEach(file => {
            const previewItem = document.createElement('div');
            previewItem.classList.add('preview-item');

            // If the file is an image, display it
            if (file.url.match(/\.(jpg|jpeg|png)$/i)) {
                const img = document.createElement('img');
                img.src = file.url;
                previewItem.appendChild(img);
            }
            // If the file is a document (PDF, DOCX, DOC), display a document icon or name
            else {
                const documentIcon = document.createElement('div');
                documentIcon.classList.add('document-icon');
                documentIcon.innerHTML = '<span>📄</span>'; // You can replace with a custom icon

                const fileName = document.createElement('span');
                fileName.innerText = file.original_name.split('/').pop(); // Get the file name without path

                previewItem.appendChild(documentIcon);
                previewItem.appendChild(fileName);
            }

            // Create delete button
            const deleteButton = document.createElement('button');
            deleteButton.textContent = 'X';
            deleteButton.classList.add('delete-btn');
            deleteButton.onclick = function() {
                deletePreview(previewItem, file.url);
            };

            // previewItem.appendChild(deleteButton);
            previewContainer.appendChild(previewItem);
        });
    }

    // Function to delete a preview (local view) and send delete request
    function deletePreview(previewItem, imageUrl) {
        // Send a delete request to the API if needed
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'YOUR_API_DELETE_URL_HERE', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onload = function() {
            if (xhr.status === 201) {
                // On success, remove the preview from the DOM
                previewItem.remove();
                alert('File deleted');
            } else {
                alert('Error deleting file');
            }
        };

        const data = {
            fileUrl: imageUrl // Send the file URL to delete
        };

        xhr.send(JSON.stringify(data));
    }
  

  // for file upload for authority -->
 
    let authorityFiles = [];

    // Handle file input change event
    document.getElementById('authorityInput').addEventListener('change', function(event) {
        authorityFiles = event.target.files;
    });

    // Function to upload files via AJAX
    function uploadAuthorityFiles() {
       
        if (authorityFiles.length === 0) {
            alert('No files selected for upload!');
            return;  
        }

        let formData = new FormData();

        // Append selected files to FormData
        for (let i = 0; i < authorityFiles.length; i++) {
            formData.append('files', authorityFiles[i]);
        }
        
        // Send files via AJAX
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/backend/api/v1/dsr/guest-upload-documents', true);

        xhr.onload = function() {
            if (xhr.status === 201) {
                let response = JSON.parse(xhr.responseText);
                if (response.success) {
                    letterOfAuthorityDoc = response.result;
                    // displayAuthorityPreviews(response.result);
                } else {
                    alert('Failed to upload files');
                }
            } else {
                alert('Error uploading files');
            }
        };
        
        xhr.send(formData);
                
    }

    // Function to display file previews for authority (images and documents)
    function displayAuthorityPreviews(files) {
        const authorityPreviewContainer = document.getElementById('authorityPreviewContainer');
        authorityPreviewContainer.innerHTML = ''; // Clear previous previews

        files.forEach(file => {
            const previewItem = document.createElement('div');
            previewItem.classList.add('preview-item');

            // If the file is an image, display it
            if (file.url.match(/\.(jpg|jpeg|png)$/i)) {
                const img = document.createElement('img');
                img.src = file.url;
                previewItem.appendChild(img);
            }
            // If the file is a document (PDF, DOCX, DOC), display a document icon or name
            else {
                const documentIcon = document.createElement('div');
                documentIcon.classList.add('document-icon');
                documentIcon.innerHTML = '<span>📄</span>'; // You can replace with a custom icon

                const fileName = document.createElement('span');
                fileName.innerText = file.original_name.split('/').pop(); // Get the file name without path

                previewItem.appendChild(documentIcon);
                previewItem.appendChild(fileName);
            }

            // Create delete button
            const deleteButton = document.createElement('button');
            deleteButton.textContent = 'X';
            deleteButton.classList.add('delete-btn');
            deleteButton.onclick = function() {
                deletePreview(previewItem, file.url);
            };

            previewItem.appendChild(deleteButton);
            authorityPreviewContainer.appendChild(previewItem);
        });
    }

    // Function to delete a preview (local view) and send delete request
    function deletePreview(previewItem, imageUrl) {
        // Send a delete request to the API if needed
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'YOUR_API_DELETE_URL_HERE', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onload = function() {
            if (xhr.status === 201) {
                // On success, remove the preview from the DOM
                previewItem.remove();
                alert('File deleted');
            } else {
                alert('Error deleting file');
            }
        };

        const data = {
            fileUrl: imageUrl // Send the file URL to delete
        };

        xhr.send(JSON.stringify(data));
    }
  </script>
   <% } %>
</body>
</html>


