# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## 1.1.0 (2024-11-30)


### Features

*  Clean HTML string | Add page number to doc ([97af393](https://github.com/DEVTekhsters/trustruler-backend/commit/97af39364a942397f945a35ee0872a03327ca56e))
* Update SPOC of dept/proc ([8797173](https://github.com/DEVTekhsters/trustruler-backend/commit/8797173ce405589bff35e7c1fdde8a52231c527d))


### Bug Fixes

* handle extra_input in transformData function ([79d9b85](https://github.com/DEVTekhsters/trustruler-backend/commit/79d9b851d8519133a6d73f853373b23f6f0371a4))



# Changelog

## [Unreleased]
