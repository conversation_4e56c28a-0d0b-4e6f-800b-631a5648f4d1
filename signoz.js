// signoz.js
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-trace-otlp-http');
const { Resource } = require('@opentelemetry/resources');
const { SemanticResourceAttributes } = require('@opentelemetry/semantic-conventions');
const { PgInstrumentation } = require('@opentelemetry/instrumentation-pg');
const { HttpInstrumentation } = require('@opentelemetry/instrumentation-http');
const { ExpressInstrumentation } = require('@opentelemetry/instrumentation-express');
// const { diag, DiagConsoleLogger, DiagLogLevel } = require('@opentelemetry/api');

// Optional: debug mode
// diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.DEBUG);

const traceExporter = new OTLPTraceExporter({
  url: 'http://148.113.6.50:4318/v1/traces',
});

// const sdk = new NodeSDK({
//   traceExporter,
//   // instrumentations: [getNodeAutoInstrumentations()],
//   instrumentations: [new PgInstrumentation()],
//   resource: new Resource({
//     [SemanticResourceAttributes.SERVICE_NAME]: 'my-node-app',
//   }),
// });

const sdk = new NodeSDK({
  traceExporter,
  instrumentations: [
    //getNodeAutoInstrumentations(), // <-- Captures Express, HTTP, etc.
    new HttpInstrumentation(),
    new ExpressInstrumentation(),
    new PgInstrumentation()        // <-- Specifically for PostgreSQL
  ],
  resource: new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: 'my-node-app',
  }),
});


// Start tracing
sdk.start()
  .then(() => {
    console.log('✅ SigNoz tracing initialized');
  })
  .catch((error) => {
    console.error('❌ Error initializing SigNoz tracing:', error);
  });

// Graceful shutdown
process.on('SIGTERM', () => {
  sdk.shutdown()
    .then(() => console.log('Tracing terminated'))
    .catch((error) => console.log('Error terminating tracing', error))
    .finally(() => process.exit(0));
});
